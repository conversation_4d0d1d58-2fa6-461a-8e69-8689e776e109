
<table border="1" cellpadding="2" cellspacing="0" style="border-collapse: collapse; text-align: left;">
    <tr>
        <th colspan="1">@lang('user_management_module.user_forms.label.report_period')</th>
        <td colspan="1">@lang('user_management_module.user_forms.label.from') {{ date('d/M/Y', strtotime($start_date)) }} @lang('user_management_module.user_forms.label.to') {{ date('d/M/Y', strtotime($end_date)) }}</td>
    </tr>
    
</table>

<br>
@foreach($contracts as $contract)


<table border="1" cellpadding="2" cellspacing="0" style="border-collapse: collapse; text-align: left;">
     <tr>
        <th colspan="1">@lang('user_management_module.user_forms.label.contract_name')</th>
        <td colspan="1">{{ $contract->contract_number }}</td>
    </tr>
    <tr>
        <th colspan="1">@lang('user_management_module.user_forms.label.service_provider')</th>
        <td colspan="1">{{ $contract->serviceProvider->name }}</td>
    </tr>
    <tr>
        <th colspan="1">@lang('user_management_module.user_forms.label.number_of_workforce_members')</th>
        <td colspan="1">{{ count($contract->contractWorkers) }}</td>
    </tr>
</table>

<table border="1" cellpadding="8" cellspacing="0" style="border-collapse: collapse; width: 100%;">
    <thead>
         <tr>
            <th>@lang('user_management_module.user_forms.label.serial')</th>
            <th>@lang('user_management_module.user_forms.label.worker_name')</th>
            <th>@lang('user_management_module.user_forms.label.job_title')</th>
            <th>@lang('user_management_module.user_forms.label.nationality')</th>
            <th>@lang('user_management_module.user_forms.label.admin_level')</th>
            <th>@lang('user_management_module.user_forms.label.phone_number')</th>
            <th>@lang('user_management_module.user_forms.label.working_hours_per_day')</th>
            <th>@lang('user_management_module.user_forms.label.required_monthly_attendance')</th>
            <th>@lang('user_management_module.user_forms.label.required_whole_monthly_attendance')</th>
            <th>@lang('user_management_module.user_forms.label.mandatory_attendance')</th>
            <th>@lang('user_management_module.user_forms.label.salary')</th>
            <th>@lang('user_management_module.user_forms.label.expected_salary')</th>
            <th>@lang('user_management_module.user_forms.label.hour_salary')</th>

            @foreach (\Carbon\Carbon::parse($start_date)->daysUntil($end_date) as $date)
                <th>{{ $date->format('d-M') }}</th>
            @endforeach

            <th>@lang('user_management_module.user_forms.label.total_attendance')</th>
            <th>@lang('user_management_module.user_forms.label.attendance_percent')</th>
            <th>@lang('user_management_module.user_forms.label.net_payable')</th>
            <th>@lang('user_management_module.user_forms.label.total_deduction')</th>

        </tr>
    </thead>
    <tbody>
        @foreach($contract->contractWorkers as $key=>$this_worker)
@php
$per_day_hours=$this_worker->worker->attendance_target ?? 0;
$month_hours=($per_day_hours * $month_days);
$monthly_hours=($per_day_hours * $month_days)* $months_count;

$salary=$this_worker->worker->salary??0;
$final_salary=$salary * $months_count;
$hour_salary=round($final_salary/$monthly_hours,2);

$breakdown = $this_worker->getWorkerAttendance($start_date, $end_date, $this_worker->worker->id,$contract->properties);
$total_minutes = 0;
@endphp


        <tr>
            <td>{{$key+1}}</td>
            <td>{{$this_worker->worker->name??''}}</td>
            <td>{{$this_worker->worker->role??''}}</td>
            <td>
                @if($lang == 'ar')
                {{$this_worker->worker->country->name_ar??''}}
                @else
                {{$this_worker->worker->country->name_en??''}}
                @endif
            </td>
            <td>{{$this_worker->worker->admin_level??''}}</td>
            <td>{{$this_worker->worker->phone??''}}</td>
            <td>{{$per_day_hours}}</td>
            <td>{{$month_hours}}</td>
            <td>{{$monthly_hours}}</td>
            <td>
                @if($this_worker->worker->attendance_mandatory === 1)
                Yes
                @else
                No
                @endif
            </td>
            <td>{{$salary}}</td>
            <td>{{$final_salary}}</td>
            <td>{{$hour_salary}}</td>
            @foreach (\Carbon\Carbon::parse($start_date)->daysUntil($end_date) as $date)
                @php
                    $date_key = $date->format('Y-m-d');
                    $date_hours = $breakdown[$date_key] ?? '00:00';

                    [$h, $m] = explode(':', $date_hours);
                    $total_minutes += ((int)$h * 60) + (int)$m;
                @endphp
                <td>{{ $date_hours }}</td>
            @endforeach
            @php
            $total_hours = floor($total_minutes / 60);
            $total_remaining_minutes = $total_minutes % 60;
            $total_hours_formatted = sprintf('%02d:%02d', $total_hours, $total_remaining_minutes);

            list($h, $m) = explode(':', $total_hours_formatted);
            $timeMinutes = ($h * 60) + $m;
            //dd($timeMinutes);
            $percentage = ($timeMinutes / ($monthly_hours*60)) * 100;
            $percentage = round($percentage, 2);
            $net_payable = round(($percentage / 100) * $final_salary,2);
            $total_deduction = $net_payable-$final_salary;
            @endphp
            <td>{{ $total_hours_formatted }}</td>
            <td>{{ $percentage }}%</td>
            <td>{{ $net_payable }}</td>
            <td>{{ $total_deduction }}</td>
        </tr>
        @endforeach
    </tbody>
</table>

@endforeach

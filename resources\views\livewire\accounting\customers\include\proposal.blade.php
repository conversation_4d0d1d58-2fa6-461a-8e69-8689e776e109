<div class="tab-pane fade show active" id="customer-proposal" role="tabpanel" aria-labelledby="proposal-tab">
    @if($loading)
        <div class="d-flex justify-content-center align-items-center" style="min-height: 200px;">
            <div class="spinner-border text-primary" role="status">
                <span class="sr-only">{{ __('accounting.common.loading') }}</span>
            </div>
        </div>
    @elseif($error)
        <div class="alert alert-danger" role="alert">
            <i class="iconsax icon fs-18 mr-2" icon-name="warning-2"></i>
            {{ $error }}
            <button type="button" class="btn btn-sm btn-outline-danger ms-2" wire:click="fetchProposals">
                {{ __('accounting.common.retry') }}
            </button>
        </div>
    @else
        <div class="card">
            <div class="">
                <div class="card-header py-4 px-3 border-0 d-flex justify-content-between align-items-center">
                    <h6 class="text-capitalize text-osool fw-500 mb-3 mb-sm-0">{{ __('accounting.customers.tabs.proposals') }}</h6>

                    <div class="d-flex gap-10 table-search">
                        <div class="position-relative">
                            <input type="text" class="form-control" placeholder="{{ __('accounting.customers.forms.search_placeholder') }}"
                                   wire:model.live.debounce.300ms="search">
                            <i class="iconsax field-icon fs-18 mr-0" icon-name="search-normal-2"></i>
                        </div>
                        <!-- <button class="btn btn-export text-dark"><i class="iconsax icon fs-22 mr-0" icon-name="upload-1"></i> Export</button> -->
                    </div>
                </div>
            </div>
            <div class="card-body px-0 pt-0">
                <div class="userDatatable projectDatatable project-table bg-white w-100 border-0">
                    <div class="table-responsive">
                        <table class="table mb-0 radius-0 th-osool">
                            <thead>
                                <tr class="userDatatable-header">
                                    <th>
                                        {{ __('accounting.customers.table.headers.proposal') }}
                                    </th>
                                    <th>
                                        <i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i>
                                        {{ __('accounting.customers.table.headers.issue_date') }}
                                    </th>
                                    <th>
                                        <i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i>
                                        {{ __('accounting.customers.table.headers.amount') }}
                                    </th>
                                    <th>
                                        <i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i>
                                        {{ __('accounting.customers.table.headers.status') }}
                                    </th>
                                    <th>
                                        <i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i>
                                        {{ __('accounting.customers.table.headers.action') }}
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="sort-table ui-sortable">
                                @forelse($proposals as $proposal)
                                    <tr class="ui-sortable-handle">
                                        <td>
                                            <div class="userDatatable-inline-title">
                                                <a href="{{ Route::has('accounting.proposals.view') ? route('accounting.proposals.view', $proposal['id']) : '#' }}">
                                                    <span class="fw-400 badge badge-round badge-primary badge-lg badge-outlined">
                                                        {{ $proposal['proposal_number'] ?? $proposal['number'] ?? 'N/A' }}
                                                    </span>
                                                </a>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex userDatatable-content mb-0 align-items-center">
                                                <span>{{ $proposal['issue_date'] ? date('d-m-Y', strtotime($proposal['issue_date'])) : 'N/A' }}</span>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center gap-10">
                                                <img src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSx34eO8RbZlsetN1iAQtxuOLnLOlyJbJmU7Q&amp;s" width="15" />
                                                <span class="text-new-primary">{{ number_format($proposal['amount'] ?? 0, 2) }}</span>
                                            </div>
                                        </td>
                                        <td>
                                            @php
                                                $statusClass = match($proposal['status'] ?? 'draft') {
                                                    'sent' => 'bg-opacity-info text-info',
                                                    'viewed' => 'bg-opacity-warning text-warning',
                                                    'approved' => 'bg-opacity-success text-success',
                                                    'rejected' => 'bg-opacity-danger text-danger',
                                                    default => 'bg-opacity-loss text-loss'
                                                };
                                            @endphp
                                            <span class="badge-new {{ $statusClass }} rounded">
                                                {{ __('accounting.proposals.status.' . ($proposal['status'] ?? 'draft')) }}
                                            </span>
                                        </td>
                                        <td>
                                            <div class="d-inline-block">
                                                <ul class="mb-0 d-flex gap-10">
                                                    <li>
                                                        <a href="javascript:void(0);" wire:click="convertToInvoice({{ $proposal['id'] }})"
                                                           title="{{ __('accounting.customers.actions.convert_to_invoice') }}">
                                                            <i class="iconsax icon text-osool fs-18" icon-name="change-shape-1"></i>
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <a href="{{ route('finance.proposal.view', $proposal['id']) }}">
                                                            <i class="iconsax icon text-osool fs-18" icon-name="eye"></i>
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <a href="{{ route('finance.proposal.edit', $proposal['id']) }}">
                                                            <i class="iconsax icon text-new-primary fs-18" icon-name="edit-1"></i>
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <a href="javascript:void(0);" wire:click="openDeleteModal({{ $proposal['id'] }}, '{{ $proposal['proposal_number'] ?? $proposal['number'] ?? 'N/A' }}')">
                                                            <i class="iconsax icon text-delete fs-18" icon-name="trash"></i>
                                                        </a>
                                                    </li>
                                                </ul>
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="5" class="text-center py-4">
                                            <div class="d-flex flex-column align-items-center">
                                                <i class="iconsax icon fs-48 text-muted mb-2" icon-name="document-text"></i>
                                                <p class="text-muted mb-0">{{ __('accounting.customers.no_proposals_found') }}</p>
                                            </div>
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <div class="card-body pt-0">
                <div class="d-flex justify-content-md-between flex-column flex-md-row justify-content-center align-items-center mt-4 gap-10">
                    <div class="">
                        <ul class="atbd-pagination d-flex justify-content-between">
                            <li>
                                <div class="paging-option">
                                    <div class="dataTables_length d-flex">
                                        <label class="d-flex align-items-center mb-0">
                                            <select aria-controls="proposals_table" wire:model.live.debounce.250ms="perPage" class="custom-select custom-select-sm form-control form-control-sm mx-2" style="min-height: 35px;">
                                                <option value="5">5</option>
                                                <option value="10">10</option>
                                                <option value="25">25</option>
                                                <option value="50">50</option>
                                                <option value="100">100</option>
                                            </select>
                                            <span class="no-wrap"> {{ __('accounting.customers.pagination.entries_per_page') }} </span>
                                        </label>
                                    </div>
                                </div>
                            </li>
                        </ul>
                    </div>
                    <div class="">
                        <div class="user-pagination">
                            <div class="user-pagination new-pagination">
                                <div class="d-flex justify-content-sm-end justify-content-end">
                                    <nav>
                                        <span class="relative z-0 inline-flex rounded-md d-flex new-pagination-section flex-wrap">
                                            <span class="">
                                                @if($currentPage <= 1)
                                                    <span aria-disabled="true" aria-label="{{ __('accounting.pagination.previous') }}">
                                                        <button class="border-0 disabled" aria-hidden="true" disabled="">
                                                            <i class="iconsax icon-bold fs-16 mr-0 rotate-ar-y d-inline-block" icon-name="chevron-left"></i>
                                                        </button>
                                                    </span>
                                                @else
                                                    <button type="button" class="border-0" wire:click="goToPage({{ $currentPage - 1 }})">
                                                        <i class="iconsax icon-bold fs-16 mr-0 rotate-ar-y d-inline-block" icon-name="chevron-left"></i>
                                                    </button>
                                                @endif
                                            </span>

                                            @for($page = max(1, $currentPage - 2); $page <= min($lastPage, $currentPage + 2); $page++)
                                                <span wire:key="paginator-page-{{ $page }}">
                                                    @if($page == $currentPage)
                                                        <button class="border-0 current-page" disabled="">{{ $page }}</button>
                                                    @else
                                                        <button type="button" class="border-0" wire:click="goToPage({{ $page }})">{{ $page }}</button>
                                                    @endif
                                                </span>
                                            @endfor

                                            <span>
                                                @if($currentPage >= $lastPage)
                                                    <button class="border-0 disabled" disabled="">
                                                        <i class="iconsax icon-bold fs-16 mr-0 rotate-ar-y d-inline-block" icon-name="chevron-right"></i>
                                                    </button>
                                                @else
                                                    <button type="button" class="border-0" wire:click="goToPage({{ $currentPage + 1 }})">
                                                        <i class="iconsax icon-bold fs-16 mr-0 rotate-ar-y d-inline-block" icon-name="chevron-right"></i>
                                                    </button>
                                                @endif
                                            </span>
                                        </span>
                                    </nav>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div>
                        <p class="text-sm text-gray-700 leading-5 mb-0">
                            <span>{{ __('accounting.customers.pagination.showing') }}</span>
                            <span class="font-medium">{{ ($currentPage - 1) * $perPage + 1 }}</span>
                            <span>{{ __('accounting.customers.pagination.to') }}</span>
                            <span class="font-medium">{{ min($currentPage * $perPage, $total) }}</span>
                            <span>{{ __('accounting.customers.pagination.of') }}</span>
                            <span class="font-medium">{{ $total }}</span>
                            <span>{{ __('accounting.customers.pagination.results') }}</span>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    @endif
</div>

{{-- Delete Confirmation Modal --}}
@livewire('common.delete-confirm')
<div class="modal-dialog {{-- modal-lg --}}" role="document">
    <div class="modal-content">
        <div class="modal-header">
            <h5 class="modal-title">{{ $tangibledetails['title'] }}</h5>
            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                <i class="iconsax" icon-name="x"></i>
            </button>
        </div>
        <div class="body">
            <form>
                <div class="modal-body">
                    <div class="row mb-2">
                        <div class="col-12">
                            <div class="alert alert-warning" role="alert">
                               @lang('CRMProjects.common.this_tangible_task_related_to_work_order_number') <a href="#" class="alert-link">{{ $tangibledetails['workorder_id'] }}</a>.
                                
                            </div>
                        </div>
                    </div>
                    <div class="row">

                        <div class="form-group col-md-6">
                            <label class="col-form-label">@lang('CRMProjects.common.created_at')</label>
                            <input type="text" class="form-control form-control-light"
                                wire:model.defer="tangibledetails.created_at" readonly>

                        </div>
                        <div class="form-group col-md-6">
                            <label class="col-form-label">@lang('CRMProjects.common.title')</label>
                            <input type="text" class="form-control form-control-light"
                                wire:model.defer="tangibledetails.title" value="" readonly>

                        </div>

                    </div>

                    <div class="row">

                        <div class="form-group col-md-6">
                            <label class="col-form-label"> @lang('CRMProjects.work_order_date')</label>
                            <input type="text" class="form-control form-control-light"
                                wire:model.defer="tangibledetails.startDate" value="" readonly>

                        </div>
                        <div class="form-group col-md-6">
                            <label class="col-form-label">@lang('CRMProjects.work_order_type')</label>
                            @if ($tangibledetails['workorder_type'] == 'preventive')
                                <input type="text" class="form-control form-control-light" value="@lang('CRMProjects.work_order_preventive')"
                                    readonly>
                            @else
                                <input type="text" class="form-control form-control-light" value="@lang('CRMProjects.work_order_corrective')"
                                    readonly>
                            @endif


                        </div>

                    </div>
                    <div class="row">

                        <div class="form-group col-md-12">
                            <label class="col-form-label">@lang('CRMProjects.common.description')</label>
                            <textarea class="form-control form-control-light" id="task-description" rows="3"
                                wire:model.defer="tangibledetails.description" readonly></textarea>
                            @error('description')
                                <span class="text-danger">{{ $message }}</span>
                            @enderror
                        </div>



                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" data-dismiss="modal">@lang('CRMProjects.common.close')</button>

                </div>
            </form>
        </div>
    </div>
</div>

@section('scripts')
@endsection

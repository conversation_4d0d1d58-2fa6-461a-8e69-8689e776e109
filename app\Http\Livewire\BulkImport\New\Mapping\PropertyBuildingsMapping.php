<?php
    namespace App\Http\Livewire\BulkImport\New\Mapping;
    use Livewire\Component;
    use Illuminate\Support\Facades\Log;
    use App\Http\Traits\FunctionsTrait;
    use App\Http\Traits\BulkImportTrait;

    class PropertyBuildingsMapping extends Component{
        use FunctionsTrait, BulkImportTrait;

        public $showMore;
        public $perPage;
        public $countList;
        public $chunkData;
        public $bulkImportDetails;

        public function render(){
            $list = $this->getPaginatedBuildingsList();
            isset($list) && count($list) > 0 ? $this->setCountList($list->total()) : $this->setCountList(0);
            return view('livewire.bulk-import.new.mapping.property-buildings-mapping', compact('list'));
        }

        public function initBuildings() {
            try {
                $buildingsData = isset($this->bulkImportDetails) ? $this->bulkImportDetails->bulkImportTemp->buildings_data : null;
                $array = json_decode($buildingsData, true);
                return collect($array);
            } 
            
            catch (\Throwable $th) {
                Log::error("initBuildings error: ".$th);
            }
        }

        public function getPaginatedBuildingsList() {
            try {
                $buildings = $this->initBuildings();
                return $this->customPagination($buildings, $this->perPage);
            } 
            
            catch (\Throwable $th) {
                Log::error("getPaginatedBuildingsList error: ".$th);
            }
        }

        public function managePerPage() {
            try {
                $number = $this->additionOperation($this->perPage, $this->showMore);
                $this->setPerPage($number);
            } 
            
            catch (\Throwable $th) {
                Log::error("managePerPage error: ".$th);
            }
        }

        public function setPerPage($value) {
            try {
                $this->perPage = $value;
            } 
            
            catch (\Throwable $th) {
                Log::error("setPerPage error: ".$th);
            }
        }

        public function setCountList($value) {
            try {
                $this->countList = $value;
            } 
            
            catch (\Throwable $th) {
                Log::error("setCountList error: ".$th);
            }
        }

        public function manageLoadAll() {
            try {
                $this->setPerPage($this->countList);
            } 
            
            catch (\Throwable $th) {
                Log::error("manageLoadAll error: ".$th);
            }
        }

        public function manageListStatusData() {
            try {
                $buildings = $this->initBuildings();
                $acceptedNumber = 0;
                $refusedNumber = 0;

                if(isset($buildings) && count($buildings) > 0){
                    collect($buildings)->chunk($this->chunkData)->each(function($chunk) use(&$acceptedNumber, &$refusedNumber){
                        foreach($chunk as $data){
                            $response = $this->fullBuildingsValidation($data);

                            if(isset($response) && $response[0]['status'] == 'success'){
                                $acceptedNumber = $acceptedNumber + 1;
                            }

                            else{
                                $refusedNumber = $refusedNumber + 1;
                            }
                        }
                    });
                }

                return [
                    'acceptedNumber' => $acceptedNumber,
                    'refusedNumber' => $refusedNumber
                ];
            } 
            
            catch (\Throwable $th) {
                Log::error("manageListStatusData error: ".$th);
            }
        }
    }
?>
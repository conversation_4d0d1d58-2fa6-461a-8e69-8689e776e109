<?php
namespace App\Http\Livewire\AdvanceContracts;

use Livewire\Component;
use Illuminate\Support\Facades\Log;

class WorkerRow extends Component
{
    public $assigned;
    public $isEditing = false;

    public $salary;
    public $role;
    public $proficiency;
    public $attendanceTarget;

    public $requiredSalary;
    public $selectedRole;

    public $totalOpenDays; // Total open days based on work time frame
    public $selectedProficiency;
    public $adminLevels;
    public $roles;
    public $requiredAttendanceTarget;


    public function mount($assigned, $roles, $adminLevels, $requiredSalary, $selectedRole, $totalOpenDays, $selectedProficiency, $requiredAttendanceTarget)
    {
        $this->assigned = $assigned;
        $this->roles = $roles;
        $this->adminLevels = $adminLevels;
        $this->requiredSalary = $requiredSalary;
        $this->requiredAttendanceTarget = $requiredAttendanceTarget;
        $this->selectedRole = $selectedRole;
        $this->selectedProficiency = $selectedProficiency;
        $this->totalOpenDays = $totalOpenDays; // Set the total open days from the passed parameter
        $this->setEditableFields();
    }

    public function enableEdit()
    {
        $this->isEditing = true;
        $this->setEditableFields();
    }

    public function cancelEdit()
    {
        $this->isEditing = false;
        $this->setEditableFields();
    }

    public function setEditableFields()
    {
        $worker = $this->assigned->worker;
        $this->salary = $worker->salary;
        $this->role = $worker->role;
        $this->proficiency = $worker->admin_level;
        $this->attendanceTarget = $worker->attendance_target;
    }


    public function save()
    {
        $workerId = $this->assigned->worker->id;

        $this->validate([
            'salary' => 'nullable|numeric|min:0',
            'role' => 'nullable|string',
            'proficiency' => 'nullable|string',
            'attendanceTarget' => 'nullable|numeric|min:0|max:24',
        ]);
        
        $worker = $this->assigned->worker;
        $worker->salary = $this->salary;
        $worker->role = $this->role;
        $worker->admin_level = $this->proficiency;
        $worker->attendance_target = $this->attendanceTarget;
        $worker->save();

        $this->assigned->refresh();
        $this->isEditing = false;

        $this->dispatchBrowserEvent('show-toastr', ['type' => 'success', 'message' => __('advance_contracts.workforce_modal.worker_update')]);
    }

   
    public function render()
    {
        return view('livewire.advance-contracts.worker-row');
    }
}

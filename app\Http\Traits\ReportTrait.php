<?php
    namespace App\Http\Traits;
    use App\Http\Helpers\ExportsHelper;
    use App\Http\Helpers\ReportQueryHelper;
    use App\Models\Contracts;
    use App\Models\Exports;
    use App\Models\ManagePpmRequest;
    use App\Models\Notification;
    use App\Models\PpmRequestWorkordersMapping;
    use App\Models\ServiceProvider;
    use App\Models\WorkOrders;
    use Auth;
    use Barryvdh\Snappy\Facades\SnappyPdf;
    use Carbon\Carbon;
    use Helper;
    use Illuminate\Support\Facades\Log;
    use App\Models\Report;
    use Mail;
    use App\Traits\MediaFilesTrait;
    use App\Http\Traits\FunctionsTrait;
    use App\Models\User;

    trait ReportTrait{
        use MediaFilesTrait;
        public function getReportsList($userType, $userId, $projectUserId) {
            // try {
                $reportsList = Report::where('user_id', $userId);

                if ($userType == 'superadmin' || $userType == 'osool_admin') {
                    $reportsList->where('project_user_id', $projectUserId);
                }

                return $reportsList
                ->select('id', 'user_id', 'report_type', 'requested_at', 'report_no', 'filename', 'status', 'generated_at', 'project_user_id', 'start_date', 'end_date')
                ->orderBy('requested_at', 'DESC')
                ->get();
            // }

            // catch (\Throwable $th) {
            //     Log::error("getReportsList error: ".$th);
            // }
        }

        public function getReportInformationsById($id) {
            //try {
                return Report::where('id', $id)
                ->select('filename', 'id')
                ->first();
            //}

            // catch (\Throwable $th) {
            //     Log::error("getReportInformationsById error: ".$th);
            // }
        }

        public function deleteReportById($id) {
            try {
                return Report::where('id', $id)->delete();
            }

            catch (\Throwable $th) {
                Log::error("deleteReportById error: ".$th);
            }
        }

        public function getReportProjectName($lang,$project_id,$user_type,$project_ids) {
            //try {
                Log::error("getReportProjectName userdata error: ".$user_type);

                // Determine the language-specific field name for project names
                $field_name = $lang == 'en' ? 'project_name' : 'project_name_ar';

                // Fetch the project name based on the language
                $project_name = ReportQueryHelper::getProjectNameByIds($project_id, $field_name);

                // Fallback to 'project_name' if Arabic project name is unavailable
                if ($lang != 'en' && empty($project_name)) {
                    $project_name = ReportQueryHelper::getProjectNameByIds($project_id, 'project_name');
                }

                // Check for 'sp_admin' user type and additional project IDs
                if ($user_type == 'sp_admin' && !empty($project_ids)) {
                    $project_name = $lang == 'en'
                        ? ReportQueryHelper::getProjectNameByIds($project_ids, 'project_name')
                        : ReportQueryHelper::getProjectNameArByIds($project_ids);
                }

                return $project_name;
            // }

            // catch (\Throwable $th) {
            //     Log::error("getReportProjectName error: ".$th);
            // }
        }



        public function getReportBuildingIds($project_ids,$user_type,$service_provider_ids,$user_service_provider,$user_building_ids,$user_project_user_id)
        {
            //try {
                Log::error("getReportBuildingIds userdata error: ".$user_type);
                $building_ids =[];

                if($user_type == "sp_admin" || $user_type == "supervisor")

                {
                    $project_user_ids = ReportQueryHelper::getFetchUsersByProjectIds($project_ids);
                    $building_ids = ReportQueryHelper::getContractDetails($user_service_provider, $project_user_ids);
                }
                else
                {
                    $userBuildingIds = [];
                    if ($user_type == 'building_manager' || $user_type == 'building_manager_employee') {
                        $userBuildingIds = $user_building_ids;
                    }
                    $contractIds = ReportQueryHelper::getContractIds($userBuildingIds, $service_provider_ids, $user_type, $user_project_user_id);

                    $building_ids = ReportQueryHelper::getBuildingIdsByContractIds($contractIds);
                }

                return $building_ids;
            // }

            // catch (\Throwable $th) {
            //     Log::error("getReportBuildingIds error: ".$th);
            // }
        }





        public function getReportWorkorders($pageName,$request,$projectUserId,$user)
        {
            //try {

                $serviceProviderIds = $request->service_providers;
                $user_id = $user->id;

                $date_range = explode('-', $request->date_range);
                $start_date = date('Y-m-d', strtotime(str_replace(' ', '', (str_replace('/', '-',$date_range[0])))));
                $end_date = date('Y-m-d', strtotime(str_replace(' ', '', (str_replace('/', '-',$date_range[1])))));

                $startDate = $start_date.' 00:00:00';
                $endDate = $end_date.' 23:59:59';

                $select = array('work_orders.*', 'contracts.contract_number', 'asset_categories.asset_category', 'asset_names.asset_name', 'frequencies_master.title', 'frequencies_master.title_ar','assets.asset_tag', 'assets.asset_number', 'priorities.priority_level' , 'work_orders.created_at as work_order_created_at','property_buildings.building_name');

                if($pageName == 'reports2') {

                    //$select = array('work_orders.*', 'contracts.contract_number', 'asset_categories.asset_category', 'asset_names.asset_name', 'assets.asset_number', 'priorities.priority_level');
                    $workOrdersQuery = WorkOrders::select('work_orders.*')
                    ->leftJoin('contracts', 'contracts.id', '=', 'work_orders.contract_id');

                }
                else
                {
                    $workOrdersQuery = WorkOrders::select($select)
                    ->leftJoin('asset_categories', 'asset_categories.id', '=', 'work_orders.asset_category_id')
                    ->leftJoin('asset_names', 'asset_names.id', '=', 'work_orders.asset_name_id')
                    ->leftJoin('assets', 'assets.id', '=', 'work_orders.asset_number_id')
                    ->leftJoin('priorities', 'priorities.id', '=', 'asset_categories.priority_id')
                    ->leftJoin('contracts', 'contracts.id', '=', 'work_orders.contract_id')
                    ->leftJoin('property_buildings', 'property_buildings.id', '=', 'work_orders.property_id')
                    ->leftJoin('users', 'users.id', '=', 'work_orders.created_by');
                }


                if($pageName !== 'reports2') {
                    $workOrdersQuery->leftJoin('frequencies_master', 'frequencies_master.id', '=', 'work_orders.frequency_id');
                }


                $workOrdersQuery->whereRaw("(IF(`work_orders`.`work_order_type` != 'preventive', work_orders.created_at, work_orders.start_date) BETWEEN '$startDate' AND '$endDate')");

                if (!in_array($user->user_type, ['sp_admin', 'supervisor'])) {
                    $workOrdersQuery->where('work_orders.project_user_id', $projectUserId);
                }

                ReportQueryHelper::applyReportworkorderFilters($workOrdersQuery, $request, $user,$user_id, $projectUserId);


                if($pageName !== 'reports2') {
                    $workOrdersQuery->where('work_orders.is_deleted', '=', 'no');
                }

                if($pageName == 'sendReport' || $pageName == 'reports') {
                    $workOrdersQuery->whereIn('work_orders.work_order_type', $request->maintenance_type);
                }

                $work_orders = $workOrdersQuery->where('work_orders.status', '!=', 5)
                    ->whereIn('work_orders.property_id', $request->building_ids)
                    ->whereIn('work_orders.service_type', $request->service_type)
                    ->whereIn('contracts.service_provider_id', $serviceProviderIds)
                    ->whereIn('work_orders.work_order_type', $request->maintenance_type)
                    ->orderByDesc('work_orders.created_at');
                if($pageName !== 'reports') {
                    $work_orders = $workOrdersQuery->get();
                }

                return $work_orders;
            //}

            // catch (\Throwable $th) {
            //     Log::error("getReportWorkorders error: ".$th);
            // }
        }




        public static function getReportJobRating($maintenanceTypes,$request,$projectUserId,$user)
        {
            //try {
                // Base query for work orders
                $baseQuery = ReportQueryHelper::getReportWorkorders('reports', $request, $projectUserId,$user)
                    ->where('work_orders.status', 4);

                // Apply filters based on maintenance types
                if (in_array('preventive', $maintenanceTypes) && in_array('reactive', $maintenanceTypes)) {
                    $baseQuery->where('work_orders.contract_type', '!=', 'warranty');
                } elseif (in_array('preventive', $maintenanceTypes)) {
                    $baseQuery->where('work_orders.work_order_type', 'preventive');
                } elseif (in_array('reactive', $maintenanceTypes)) {
                    $baseQuery->where('work_orders.work_order_type', 'reactive');
                }

                // Get all relevant work orders
                $workOrders = $baseQuery->get();
                if ($workOrders->isEmpty()) {
                    return 0; // No work orders to process
                }
                $ratings = 0;
                $totalWorkOrders = 0;

                for ($i = 5; $i >= 1; $i--) {
                    $count = $workOrders->where('rating', $i)->count();
                    $ratings += $count * $i;
                    $totalWorkOrders += $count;
                }

                // Calculate overall job rating
                $jobRating = $ratings > 0 ? round(($ratings / ($totalWorkOrders * 5)) * 5, 1) : 0;

                return $jobRating;
            // } catch (\Throwable $e) {
            //     \Log::error("Error in getReportJobRating: " . $e->getMessage());
            //     return 0; // Default value in case of an error
            // }
        }




        public function getReportPMWorkorderscount($request,$project_user_id,$user)
        {
            return $this->getReportWorkordersList($request,$project_user_id,'preventive',$user)->count();
        }


        public function getReportPMWorkordersstatuscount($request,$project_user_id,$user)
        {
            //try {

                    // Fetch the rating statistics
                    $workOrderStatusCountspreventive = $this->getReportWorkordersList($request,$project_user_id,'preventive',$user)->selectRaw('work_orders.status, COUNT(*) as count')
                                                      ->groupBy('status')
                                                      ->get()
                                                      ->keyBy('status')->toArray();

                    $PreventiveWorkorderStatusCounts = [
                      'preventive_open' => $workOrderStatusCountspreventive[1]['count'] ?? 0,
                      'preventive_in_progress' => $workOrderStatusCountspreventive[2]['count'] ?? 0,
                      'preventive_pause' => $workOrderStatusCountspreventive[3]['count'] ?? 0,
                      'preventive_closed' => $workOrderStatusCountspreventive[4]['count'] ?? 0,
                      'preventive_reopen' => $workOrderStatusCountspreventive[6]['count'] ?? 0,
                  ];

                    return $PreventiveWorkorderStatusCounts;
            // }

            // catch (\Throwable $th) {
            //     Log::error("getReportPMWorkorders error: ".$th);
            //    // return $data;
            // }
        }




        public function getReportPMWorkordersData($request, $projectUserId,$user)
    {
            //try {
                // Fetch preventive work orders once
                $workOrders = $this->getReportWorkordersList($request, $projectUserId, 'preventive',$user)->get();

                // Count total work orders
                $totalWorkOrdersCount = $workOrders->count();

                // Count work orders by status
                $statusCounts = $workOrders->groupBy('status')->map(function ($items) {
                    return $items->count();
                });

                // Group work orders by service type and count by status
                $serviceTypeCounts = $workOrders->groupBy('asset_category')->map(function ($items) {
                    return [
                        'preventive_open' => $items->where('status', 1)->count(),
                        'preventive_in_progress' => $items->where('status', 2)->count(),
                        'preventive_pause' => $items->where('status', 3)->count(),
                        'preventive_closed' => $items->where('status', 4)->count(),
                        'preventive_reopen' => $items->where('status', 6)->count(),
                    ];
                });

                return [
                    'total_count' => $totalWorkOrdersCount,
                    'status_counts' => [
                        'preventive_open' => $statusCounts[1] ?? 0,
                        'preventive_in_progress' => $statusCounts[2] ?? 0,
                        'preventive_pause' => $statusCounts[3] ?? 0,
                        'preventive_closed' => $statusCounts[4] ?? 0,
                        'preventive_reopen' => $statusCounts[6] ?? 0,
                    ],
                    'service_type_counts' => $serviceTypeCounts->toArray(),
                ];
            // } catch (\Throwable $th) {
            //     Log::error("Error in getReportPMWorkordersData: " . $th);
            // }
    }

        public function getReportPMWorkordersServicetypes($request,$project_user_id,$user)
        {
            //try {
                    // Fetch the rating statistics
                    $preventiveWorkOrdersServicetype = $this->getReportWorkordersList($request,$project_user_id,'preventive',$user)->selectRaw('asset_category, work_orders.status, COUNT(*) as count')
                                                  ->groupBy('asset_category', 'work_orders.status')
                                                  ->get()
                                                  ->groupBy('asset_category')
                                                  ->map(function ($items) {
                                                      return [
                                                          'preventive_open' => $items->where('status', 1)->sum('count'),
                                                          'preventive_in_progress' => $items->where('status', 2)->sum('count'),
                                                          'preventive_pause' => $items->where('status', 3)->sum('count'),
                                                          'preventive_closed' => $items->where('status', 4)->sum('count'),
                                                          'preventive_reopen' => $items->where('status', 6)->sum('count'),
                                                      ];
                                                  })
                                                  ->toArray();
                    return $preventiveWorkOrdersServicetype;
            // }

            // catch (\Throwable $th) {
            //     Log::error("getReportPMWorkorders error: ".$th);
            //    // return $data;
            // }
        }



        public function getSendReportEmail($filename,$report_id,$file_link,$report_number,$preventive_work_orders_count,$reactive_work_orders_count,$start_date,$end_date,$lang,$user)
        {
            $subject = 'New report has been generated';

            $mail_content['dir'] = $lang=='en' ? 'ltr' : 'rtl';
            $to_name  = $user->name;
            $to_email = $user->email;
            $mail_content['name'] = $user->name;
            $mail_content['subject_en'] = $subject;
            $mail_content['subject_ar'] = $subject;
            $mail_content['from'] =date('d/m/Y', strtotime($start_date));
            $mail_content['to'] =date('d/m/Y', strtotime($end_date));
            $mail_content['total_wo'] =$preventive_work_orders_count+ $reactive_work_orders_count;
            $mail_content['report_file_link'] = $file_link;
            $mail_content['report_no'] = $report_number;
            $sender_email = Helper::getAdminContactMail();
            if ($lang=='en')
            {
                    $mail_content['label_report_no'] ='Report Number' ;
                    $mail_content['label_report_heading'] = 'New report has been generated';
                    $mail_content['label_starting_date'] = 'From';
                    $mail_content['label_ending_date'] =  'To';
                    $mail_content['label_total_no_wo'] =  'Number of work orders';
                    $mail_content['label_report_file'] =  'PDF File';
                    $mail_content['label_download'] =  'Download';
                    $mail_content['label_email_footer_text'] = '*Note: This report is available to be downloaded during the next 24 hours.
                    You can generate new reports anytime';
            }
            else{
                    $mail_content['label_report_no'] ='رقم التقرير' ;
                    $mail_content['label_report_heading'] ='تم إنشاء تقرير جديد ';
                    $mail_content['label_starting_date'] =  'من';
                    $mail_content['label_ending_date'] =  'إلى';
                    $mail_content['label_total_no_wo'] =  'عدد أوامر العمل';
                    $mail_content['label_report_file'] =  'ملف PDF ';
                    $mail_content['label_download'] =  'تنزيل';
                    $mail_content['label_email_footer_text'] =  '*ملاحظة: هذا التقرير متاح للتنزيل خلال ال24 ساعة القادمة فقط.
                    يمكنك انشاء المزيد من التقارير في أي وقت. ';
            }


            //try {
                $rpt = ['generated_at'=>date('Y-m-d H:i:s'), 'status'=>'generated', 'oci_link' => $file_link];
                ReportQueryHelper::editReport($report_id, $rpt);
                $generated = true;
                Log::info('Report Generated stored in the DB');
            // } catch (\Exception $e) {
            //     $rpt = [ 'status'=>'failed'];
            //     $generated = false;
            //     ReportQueryHelper::editReport($report_id, $rpt);
            //     Log::info('Report Failed Stored in the DB');
            // }



            if($generated){
                $notification_msg = $report_number.' Your generated report is ready ';
                $notification_msg_ar = $report_number.'  التقرير الخاص بك جاهز';
                $notificationData = [
                    'user_id'               => $user->id,
                    'message'               => $notification_msg,
                    'message_ar'            => $notification_msg_ar,
                    'section_type'          => 'report',
                    'notification_sub_type' => 'report',
                    'additional_param'      => $filename,
                    'created_at'            => Carbon::now()
                ];
                ReportQueryHelper::createNotification($notificationData);
                //try {
                      Mail::send('mail.reportGenerated', ['mail_content' => $mail_content]
                        , function ($message) use ($to_name, $to_email, $subject, $sender_email, $report_number,$report_id,$user,$filename) {
                        $message->to($to_email, $to_name)
                            ->subject($subject);
                        $message->from($sender_email, 'Osool Team');
                      });

                      Log::info('Email sent');
                //   }
                //   catch (\Exception $e) {
                //       //code to handle the exception
                //       Log::info('Email sending failed');
                //   }
              }
              $headers = array(
                'Content-Type: application/pdf'
              );
        }





        public function getSendNewPPMReportEmail($filename,$report_id,$file_link,$report_number,$preventive_work_orders_count,$reactive_work_orders_count,$start_date,$end_date,$lang,$user)
        {
            $subject = 'New report has been generated';

            $mail_content['dir'] = $lang=='en' ? 'ltr' : 'rtl';
            $to_name  = $user->name;
            $to_email = $user->email;
            $mail_content['name'] = $user->name;
            $mail_content['subject_en'] = $subject;
            $mail_content['subject_ar'] = $subject;
            $mail_content['from'] =date('d/m/Y', strtotime($start_date));
            $mail_content['to'] =date('d/m/Y', strtotime($end_date));
            $mail_content['total_wo'] =$preventive_work_orders_count+ $reactive_work_orders_count;
            $mail_content['report_file_link'] = $file_link;
            $mail_content['report_no'] = $report_number;
            $sender_email = Helper::getAdminContactMail();
            if ($lang=='en')
            {
                    $mail_content['label_report_no'] ='Report Number' ;
                    $mail_content['label_report_heading'] = 'New report has been generated';
                    $mail_content['label_starting_date'] = 'From';
                    $mail_content['label_ending_date'] =  'To';
                    $mail_content['label_total_no_wo'] =  'Number of work orders';
                    $mail_content['label_report_file'] =  'PDF File';
                    $mail_content['label_download'] =  'Download';
                    $mail_content['label_email_footer_text'] = '*Note: This report is available to be downloaded during the next 24 hours.
                    You can generate new reports anytime';
            }
            else{
                    $mail_content['label_report_no'] ='رقم التقرير' ;
                    $mail_content['label_report_heading'] ='تم إنشاء تقرير جديد ';
                    $mail_content['label_starting_date'] =  'من';
                    $mail_content['label_ending_date'] =  'إلى';
                    $mail_content['label_total_no_wo'] =  'عدد أوامر العمل';
                    $mail_content['label_report_file'] =  'ملف PDF ';
                    $mail_content['label_download'] =  'تنزيل';
                    $mail_content['label_email_footer_text'] =  '*ملاحظة: هذا التقرير متاح للتنزيل خلال ال24 ساعة القادمة فقط.
                    يمكنك انشاء المزيد من التقارير في أي وقت. ';
            }


            //try {
                $rpt = ['generated_at'=>date('Y-m-d H:i:s'), 'status'=>'generated', 'oci_link' => $file_link];
                ReportQueryHelper::editReport($report_id, $rpt);
                $generated = true;
                Log::info('Report Generated stored in the DB');
            // } catch (\Exception $e) {
            //     $rpt = [ 'status'=>'failed'];
            //     $generated = false;
            //     ReportQueryHelper::editReport($report_id, $rpt);
            //     Log::info('Report Failed Stored in the DB');
            // }



            if($generated){
                $notification_msg = $report_number.' Your generated report is ready ';
                $notification_msg_ar = $report_number.'  التقرير الخاص بك جاهز';
                $notificationData = [
                    'user_id'               => $user->id,
                    'message'               => $notification_msg,
                    'message_ar'            => $notification_msg_ar,
                    'section_type'          => 'report',
                    'notification_sub_type' => 'report',
                    'additional_param'      => $filename,
                    'created_at'            => Carbon::now()
                ];
                ReportQueryHelper::createNotification($notificationData);
                //try {
                      Mail::send('mail.ppmreportGenerated', ['mail_content' => $mail_content]
                        , function ($message) use ($to_name, $to_email, $subject, $sender_email, $report_number,$report_id,$user,$filename) {
                        $message->to($to_email, $to_name)
                            ->subject($subject);
                        $message->from($sender_email, 'Osool Team');
                      });

                      Log::info('Email sent');
                //   }
                //   catch (\Exception $e) {
                //       //code to handle the exception
                //       Log::info('Email sending failed');
                //   }
              }
              $headers = array(
                'Content-Type: application/pdf'
              );
        }

        public function getPreventiveWorkorderFormatted($request,$project_user_id,$user)
        {
                        ///$workOrders = $this->getReportWorkordersList($request,$project_user_id,'preventive',$user);
                        $asset_category_details_preventive = $this->getReportWorkordersList($request,$project_user_id,'preventive',$user)->groupBy('work_orders.unique_id')->get();
                        foreach($asset_category_details_preventive as $ks => $work_order )
                        {
                            $work_order->pictures = [];
                            $work_order->work_order_history = $this->getReportWorkordersList($request,$project_user_id,'preventive',$user)->where('unique_id', $work_order->unique_id)->get();

                                        if(!empty($work_order->work_order_history))//if(!empty($work_orders))
                                            {
                                                foreach($work_order->work_order_history as $key => $wo)
                                                {
                                                    $wtfs = ReportQueryHelper::getWorkTimeFrameByUserId($wo['project_user_id'] ?? $user->project_user_id);

                                                    // Determine the start time
                                                    $time = $wo['wtf_start_time'] ?: ($wtfs->start_time ?? "00:00:00");

                                                    $wo['created_at'] = $wo['start_date'].' '.$time;

                                                    $wo['picture'] = ReportQueryHelper::getNoCheckListActionsByWorkOrderId($wo['id']);
                                                    $work_order->pictures = $this->getWorkorderImages($wo['picture'],'preventive');
                                                    $service_window = ReportQueryHelper::getPmworkorderServiceWindow($wo);

                                                    $wo['target_date'] = $wo['job_started_at'] == NULL ? $wo['target_date'] : date('Y-m-d H:i:s',strtotime('+'.$service_window.' hours', strtotime($wo['job_started_at'])));

                                                    $wo['status'] = ReportQueryHelper::getWorkorderstatus($wo['status'],$wo['contract_type']);
                                                }
                                            }
                        }

                        return $asset_category_details_preventive;
        }


        public function getReactiveWorkorderFormatted($request,$project_user_id,$user)
        {
                $workOrders = $this->getReportWorkordersList($request,$project_user_id,'reactive',$user)->get();

                foreach ($workOrders as $key => $workOrder) {
                        $workOrder['maintenance_request_details'] = null;
                        $workOrder['tenant_apartment'] = '';
                        if(isset($workOrder['maintanance_request_id']) && $workOrder['maintanance_request_id']!=null){
                            $workOrder['maintenance_request_details'] = ReportQueryHelper::getMaintenanceRequestDetails($workOrder['maintanance_request_id']);
                            $workOrder['tenant_apartment'] = $workOrder['maintenance_request_details']->userDetailsById->apartment ?? null; 
                        }
                        $workOrder['supervisor_names'] = $this->getSupervisorNames($workOrder['supervisor_id']);
                        $workOrder['bm_name_closed_by'] = $this->getUserName($workOrder['closed_by']);
                        $workOrder['bm_name_created_by'] = $this->getUserName($workOrder['created_by']);
                        $workOrder['worker_name'] = $this->getWorkerName($workOrder['worker_id'] , $workOrder['workorder_journey']);
                        $workOrder['status'] = $this->getFormattedStatus($workOrder['status'], $workOrder['contract_type']);
                        $workOrder['pictures'] = $this->getWorkOrderPictures($workOrder['id'], 'reactive');
                        $workOrder['pictures_by_worker_id'] = $this->getWorkOrderPicturesByWorkerId($workOrder['id'], 'reactive');
                        $workOrder['maintenance_request'] = ReportQueryHelper::getMaintenanceRequestDetails($workOrder['maintanance_request_id']);
                        $workOrder['target_date'] = $this->calculateTargetDate($workOrder['asset_category_id'] , $workOrder['contract_number'], $workOrder['target_date'] , $workOrder['job_started_at'], $workOrder['bm_approve_issue']);
                        if($workOrder['priority_id'] != 0){
                            $workOrder['priority_level'] = ReportQueryHelper::getPriorityById($workOrder['priority_id']);
                        }
                    }
                return $workOrders;
        }

private function getMaintenanceRequestDetails($maintanance_request_id): ?array
{

                        if(isset($maintanance_request_id) && $maintanance_request_id !=null){
                            return ReportQueryHelper::getMaintenanceRequestDetails($maintanance_request_id);
                        }
    return null;
}

private function getSupervisorNames(string $supervisorIds): string
{
    $ids = explode(',', $supervisorIds);
    $names = json_decode(ReportQueryHelper::getUserNames($ids), true);
    return implode(',', $names ?? []);
}

private function getUserName(?int $userId): ?string
{
    return $userId ? ReportQueryHelper::getUserNameById($userId) : null;
}

private function getWorkerName($worker_id,$workorder_journey): string
{
    if (!empty($worker_id)) {
        $name = ReportQueryHelper::getUserNameById([$worker_id]);
        return $name ?? '';
    }

    return $workorder_journey !== 'submitted'
        ? 'sp_started_on_behlf_of_worker'
        : 'not_assigned';
}

private function getFormattedStatus($status,$contracttype): string
{
    return ReportQueryHelper::getWorkorderstatus($status, $contracttype);
}

private function getWorkOrderPictures(int $workOrderId, string $type): array
{
    $pictures = $this->getWorkorderImages(ReportQueryHelper::getNoCheckListActionsByWorkOrderId($workOrderId), $type);
    return $pictures['pictures'] ?? [];
}

private function getWorkOrderPicturesByWorkerId(int $workOrderId, string $type): array
{
    $pictures = $this->getWorkorderImages(ReportQueryHelper::getNoCheckListActionsByWorkOrderId($workOrderId), $type);
    return $pictures['pictures_by_worker_id'] ?? [];
}

private function calculateTargetDate($asset_category_id , $contract_number, $target_date , $job_started_at, $bm_approve_issue): ?string
{
    $slaDetails = ReportQueryHelper::getContractAssetCategoriesByAssetCategoryId(
        $asset_category_id,
        $contract_number
    );

    if (empty($slaDetails->priority_id)) {
        return $target_date;
    }

    $priorityDetails = ReportQueryHelper::getContractPriority($slaDetails->priority_id, $contract_number);
    $serviceWindow = $priorityDetails->service_window ?? 0;
    $serviceWindowType = $priorityDetails->service_window_type ?? '';

    return $job_started_at && $bm_approve_issue !== 2
        ? date('Y-m-d H:i:s', strtotime("+{$serviceWindow} {$serviceWindowType}", strtotime($job_started_at)))
        : $target_date;
}



        public function getReportRMWorkorders($request,$project_user_id,$user)
        {
            //try {
                    // Fetch the rating statistics
                    $data['reactive_work_orders'] = ReportQueryHelper::getReportWorkorders('reports', $request, $project_user_id,$user)->where('work_orders.work_order_type', 'reactive');

                    $data['reactive_work_orders_count'] = count($data['reactive_work_orders']->get()->toArray());

                    $workOrderStatusCountsreactive = $data['reactive_work_orders']->selectRaw('work_orders.status, COUNT(*) as count')
                                                    ->groupBy('status')
                                                    ->get()
                                                    ->keyBy('status')->toArray();

                    $data['reactiveWorkOrdersServicetype'] = $data['reactive_work_orders']->selectRaw('asset_category, work_orders.status, COUNT(*) as count')
                        ->groupBy('asset_category', 'work_orders.status')
                        ->get()
                        ->groupBy('asset_category')
                        ->map(function ($items) {
                            return [
                                'reactive_open' => $items->where('status', 1)->sum('count'),
                                'reactive_in_progress' => $items->where('status', 2)->sum('count'),
                                'reactive_pause' => $items->where('status', 3)->sum('count'),
                                'reactive_closed' => $items->where('status', 4)->sum('count'),
                                'reactive_reopen' => $items->where('status', 6)->sum('count'),
                            ];
                        })
                        ->toArray();

                    $data['ReactiveWorkorderStatusCounts'] = [
                      'reactive_open' => $workOrderStatusCountsreactive[1]['count'] ?? 0,
                      'reactive_in_progress' => $workOrderStatusCountsreactive[2]['count'] ?? 0,
                      'reactive_pause' => $workOrderStatusCountsreactive[3]['count'] ?? 0,
                      'reactive_closed' => $workOrderStatusCountsreactive[4]['count'] ?? 0,
                      'reactive_reopen' => $workOrderStatusCountsreactive[6]['count'] ?? 0,
                  ];
                    return $data;
            // }

            // catch (\Throwable $th) {
            //     Log::error("getReportRMWorkorders error: ".$th);
            // }
        }



        public function getGenerateReportPdf($filename,$finalHtml,$report_id)
        {
            gc_collect_cycles();
            // try
            // {
               ini_set('memory_limit', '15G');
               // Load the HTML into the PDF generator
               $pdf = SnappyPdf::loadHTML($finalHtml)
               ->setOption('page-width', 210)
               ->setOption('page-height', 297)
               ->setOption('margin-top', '0mm')
               ->setOption('margin-bottom', '0mm')
               ->setOption('margin-left', '0mm')
               ->setOption('margin-right', '0mm')
               ->setOption('enable-local-file-access', true)
               ->setOption('enable-smart-shrinking', true)
                ->setOption('print-media-type', true)
             ->setOption('encoding', 'UTF-8'); // Ensures proper Arabic rendering
        //    } catch (\Exception $e) {
        //        Log::error('Error during PDF generation: ' . $e->getMessage());
        //    }
                  // Trigger garbage collection after generating PDF
                  gc_collect_cycles();
                    $path = public_path('reports');
                    if(!is_dir($path)) {
                      mkdir($path, 0755, true);
                    }
                    $content = $pdf->download()->getOriginalContent();
                    file_put_contents( $path.'/'.$filename, $content);
                    Log::info('PDF Ready');
                    $headers = array('Content-Type: application/pdf');
                    //push the media file to OCI
                    $ociUrlFile = $this->pushMediasToOCI($this->setModelLog('Report', $report_id),$filename,'reports',null,true);
                    //Set OCI link if exists
                    $file_link = isset($ociUrlFile) ? $ociUrlFile : asset('reports').DIRECTORY_SEPARATOR.$filename;
                    return $file_link;
        }


        public function getChunkpdfhtml($reportHtml)
        {
            $finalHtml = ''; // Initialize an empty string to hold the final HTML
            ini_set('memory_limit', '15G');
                 // // Use regex to split by logical boundaries like sentences or HTML tags
                 $chunks = preg_split('/(?<=[.؟!])\s+/u', $reportHtml); // Split by sentence-ending punctuation
                  foreach ($chunks as $chunk)
                  {
                      //try {
                          $chunkStartTime = microtime(true);
                          // Log::info('Processing chunk: ' . substr($chunk, 0, 50)); // Log first 50 char
                          $finalHtml .= $chunk;
                          $chunkEndTime = microtime(true);
                          Log::info('Processed chunk in ' . ($chunkEndTime - $chunkStartTime) . ' seconds');
                    //   } catch (\Exception $e) {
                    //       Log::error('Error processing chunk: ' . $e->getMessage());
                    //   }
                      // Periodically free memory
                      gc_collect_cycles();
                  }

                  return $finalHtml;
        }




        public function getReportWorkordersCalculation($request,$projectUserId,$user)
        {
            $maintenance_types = $request->maintenance_type;
            if(in_array('preventive',$request->maintenance_type) || !in_array('reactive',$request->maintenance_type))
            {
                $maintenance_types = ['preventive','reactive'];
            }

            //try {

                $serviceProviderIds = $request->service_providers;
                $user_id = $user->id;

                $date_range = explode('-', $request->date_range);

                $startDate = Carbon::createFromFormat('d/m/Y', trim($date_range[0]))->startOfDay()->toDateTimeString();
                $endDate = Carbon::createFromFormat('d/m/Y', trim($date_range[1]))->endOfDay()->toDateTimeString();

                $workOrdersQuery = WorkOrders::select('work_orders.*')
                    ->leftJoin('contracts', 'contracts.id', '=', 'work_orders.contract_id');

                $workOrdersQuery->whereRaw("(IF(`work_orders`.`work_order_type` != 'preventive', work_orders.created_at, work_orders.start_date) BETWEEN '$startDate' AND '$endDate')");

                ReportQueryHelper::applyReportworkorderFilters($workOrdersQuery, $request, $user,$user_id, $projectUserId);


                $allWorkOrders = $workOrdersQuery->whereIn('work_orders.property_id', $request->building_ids)
                    ->whereIn('work_orders.service_type', $request->service_type)
                    ->whereIn('contracts.service_provider_id', $serviceProviderIds)
                    ->whereIn('work_orders.work_order_type', $maintenance_types)
                    ->where('work_orders.status', 4)
                    ->orderByDesc('work_orders.created_at')
                    ->get();
                // Calculate total records
                    $totalRecords = $allWorkOrders->count();

                    // Calculate "on-time" work orders
                    $time = $allWorkOrders
                        ->filter(function ($workOrder) {
                            if ($workOrder->response_time === "On time") {
                                if ($workOrder->sp_approove != 0) {
                                    return true;
                                }

                                $createdAt = $workOrder->work_order_type === "preventive"
                                    ? $workOrder->start_date . ' ' . $workOrder->wtf_start_time
                                    : $workOrder->created_at;

                                $interval = abs(Carbon::parse($workOrder->job_started_at)->timestamp - Carbon::parse($createdAt)->timestamp);
                                return $interval !== 0;
                            }
                            return false;
                        })
                        ->count();

                    // Calculate "pass" work orders
                    $pass = $allWorkOrders->where('pass_fail', 'pass')->count();

                    // Calculate percentages for "response time" and "pass/fail"
                    $responseTimePercentage = $totalRecords > 0 ? ($time / $totalRecords) * 100 : 0;
                    $result['passFailPercentage'] = $totalRecords > 0 ? ($pass / $totalRecords) * 100 : 0;

                    // Round "response time" to one decimal place
                    $result['woCompletedOnTime'] = round($responseTimePercentage, 1);
                    return $result;
            //}

            // catch (\Throwable $th) {
            //     Log::error("getReportWorkordersCalculation error: ".$th);
            // }
        }


        public function getReportWorkordersList($request,$projectUserId,$maintenanceType,$user)
        {
            //try {

                $serviceProviderIds = $request->service_providers;
                $user_id = $user->id;

                $date_range = explode('-', $request->date_range);
                $startDate = Carbon::createFromFormat('d/m/Y', trim($date_range[0]))->startOfDay()->toDateTimeString();
                $endDate = Carbon::createFromFormat('d/m/Y', trim($date_range[1]))->endOfDay()->toDateTimeString();

                $select = array('work_orders.*', 'contracts.contract_number', 'asset_categories.asset_category', 'asset_names.asset_name', 'frequencies_master.title', 'frequencies_master.title_ar','assets.asset_tag', 'assets.asset_number', 'priorities.priority_level' , 'work_orders.created_at as work_order_created_at','property_buildings.building_name');


                    $workOrdersQuery = WorkOrders::select($select)
                    ->leftJoin('asset_categories', 'asset_categories.id', '=', 'work_orders.asset_category_id')
                    ->leftJoin('asset_names', 'asset_names.id', '=', 'work_orders.asset_name_id')
                    ->leftJoin('assets', 'assets.id', '=', 'work_orders.asset_number_id')
                    ->leftJoin('priorities', 'priorities.id', '=', 'asset_categories.priority_id')
                    ->leftJoin('contracts', 'contracts.id', '=', 'work_orders.contract_id')
                    ->leftJoin('property_buildings', 'property_buildings.id', '=', 'work_orders.property_id')
                    ->leftJoin('users', 'users.id', '=', 'work_orders.created_by')
                    ->leftJoin('frequencies_master', 'frequencies_master.id', '=', 'work_orders.frequency_id');

                $workOrdersQuery->whereRaw("(IF(`work_orders`.`work_order_type` != 'preventive', work_orders.created_at, work_orders.start_date) BETWEEN '$startDate' AND '$endDate')");

                ReportQueryHelper::applyReportworkorderFilters($workOrdersQuery, $request, $user,$user_id, $projectUserId);

                $work_orders = $workOrdersQuery->where('work_orders.is_deleted', '=', 'no')
                    ->where('work_orders.status', '!=', 5)
                    ->whereIn('work_orders.property_id', $request->building_ids)
                    ->whereIn('work_orders.service_type', $request->service_type)
                    ->whereIn('contracts.service_provider_id', $serviceProviderIds)
                    ->where('work_orders.work_order_type', $maintenanceType)
                    ->orderByDesc('work_orders.created_at');

                return $work_orders;
            //}

            // catch (\Throwable $th) {
            //     Log::error("getReportWorkorders error: ".$th);
            // }
        }

        public function getWorkorderImages($workorderImages,$maintanceType)
        {
            if($maintanceType == 'preventive')
            {
                $filteredPictures = [];
                if (!empty($workorderImages))
                {
                    // Filter the pictures array
                    $filteredPictures = collect($workorderImages)
                        ->filter(function ($pic) {
                            return !empty($pic) && isset($pic->photos) && !empty(json_decode($pic->photos));
                        })
                        ->take(1) // Limit to one picture
                        ->all();

                    // Assign the filtered pictures to the work order
                    if (!empty($filteredPictures)) {
                        return $filteredPictures;
                    }
                }

                return $filteredPictures;
            }
            else
            {
                $result = ['pictures' => [], 'pictures_by_worker_id' => []];
                if (!empty($workorderImages)) {
                    foreach ($workorderImages as $pic) {
                        if (!empty($pic)) {
                            $picture[] = $pic;

                            if (!empty($pic->worker_id)) {
                                $workerName = ReportQueryHelper::getUserNameById($pic->worker_id);
                                if (!empty($workerName)) {
                                    $pictures_by_worker_id[] = $workerName;
                                }
                            }
                        }
                    }

                    $result['pictures'] = $picture ?? [];
                    $result['pictures_by_worker_id'] = $pictures_by_worker_id ?? [];
            }
            return $result;
            }
        }


        public function getMergeServiceType($PreventiveWorkorderStatusCounts,$ReactiveWorkorderStatusCounts,$preventiveWorkOrdersServicetype,$reactiveWorkOrdersServicetype)
        {
            $result['FinalWorkorderCountsbyStatus'] = array_merge($PreventiveWorkorderStatusCounts,$ReactiveWorkorderStatusCounts);


            $result['WorkOrdersServicetypeTotalReactive'] = [
                'total_open'        => array_sum(array_column($reactiveWorkOrdersServicetype, 'reactive_open')),
                'total_in_progress' => array_sum(array_column($reactiveWorkOrdersServicetype, 'reactive_in_progress')),
                'total_pause'       => array_sum(array_column($reactiveWorkOrdersServicetype, 'reactive_pause')),
                'total_closed'      => array_sum(array_column($reactiveWorkOrdersServicetype, 'reactive_closed')),
                'total_reopen'      => array_sum(array_column($reactiveWorkOrdersServicetype, 'reactive_reopen')),
            ];

            $result['WorkOrdersServicetypeTotalPreventive'] = [
                'total_open'        => array_sum(array_column($preventiveWorkOrdersServicetype, 'preventive_open')),
                'total_in_progress' => array_sum(array_column($preventiveWorkOrdersServicetype, 'preventive_in_progress')),
                'total_pause'       => array_sum(array_column($preventiveWorkOrdersServicetype, 'preventive_pause')),
                'total_closed'      => array_sum(array_column($preventiveWorkOrdersServicetype, 'preventive_closed')),
                'total_reopen'      => array_sum(array_column($preventiveWorkOrdersServicetype, 'preventive_reopen')),
            ];



            $result['WorkOrdersServicetypeTotal'] = ['total_open' => array_sum(array_column($preventiveWorkOrdersServicetype, 'preventive_open')) + array_sum(array_column($reactiveWorkOrdersServicetype, 'reactive_open')),
                                                        'total_in_progress' => array_sum(array_column($preventiveWorkOrdersServicetype, 'preventive_in_progress')) + array_sum(array_column($reactiveWorkOrdersServicetype, 'reactive_in_progress')),
                                                        'total_pause' => array_sum(array_column($preventiveWorkOrdersServicetype, 'preventive_pause')) + array_sum(array_column($reactiveWorkOrdersServicetype, 'reactive_pause')),
                                                        'total_closed' => array_sum(array_column($preventiveWorkOrdersServicetype, 'preventive_closed')) + array_sum(array_column($reactiveWorkOrdersServicetype, 'reactive_closed')),
                                                        'total_reopen' => array_sum(array_column($preventiveWorkOrdersServicetype, 'preventive_reopen')) + array_sum(array_column($reactiveWorkOrdersServicetype, 'reactive_reopen'))];

            // Merging Arrays by Key
            $result['mergedWorkOrdersServiceType'] = array_merge_recursive($preventiveWorkOrdersServicetype, $reactiveWorkOrdersServicetype);
            // Format to Align Data
            foreach ($result['mergedWorkOrdersServiceType'] as $key => $values) {
                $result['mergedWorkOrdersServiceType'][$key] = array_merge([
                    'preventive_open' => 0,
                    'preventive_in_progress' => 0,
                    'preventive_pause' => 0,
                    'preventive_closed' => 0,
                    'preventive_reopen' => 0,
                    'reactive_open' => 0,
                    'reactive_in_progress' => 0,
                    'reactive_pause' => 0,
                    'reactive_closed' => 0,
                    'reactive_reopen' => 0,
                ], $values);
            }

            foreach ($result['mergedWorkOrdersServiceType'] as $key => $val) {
                        $total_sum = $val['preventive_open']+$val['reactive_open']+$val['preventive_pause']+$val['reactive_pause']+$val['preventive_pause']+$val['reactive_pause']+$val['preventive_in_progress']+$val['reactive_in_progress']+$val['preventive_closed']+$val['reactive_closed'];
                        $total_all = $result['WorkOrdersServicetypeTotal']['total_open']+$result['WorkOrdersServicetypeTotal']['total_in_progress']+$result['WorkOrdersServicetypeTotal']['total_pause']+$result['WorkOrdersServicetypeTotal']['total_closed'];
                        $percentage_final = $total_sum > 0 && $total_all > 0 ? ($total_sum * 100) / $total_all : 0;
                        $result['mergedWorkOrdersServiceType'][$key]['percentage'] = round($percentage_final);
            }


            return $result;
        }



        public function getReportSummarryData($request,$lang,$project_user_id,$user,$service_provider_ids,$assigned_to_users,$report_number,$start_date,$end_date)
        {
                        $project_ids = $request->project_ids ?? [];
                        $project_name = $this->getReportProjectName($lang,$user->project_id,$user->user_type,$project_ids);

                        if(in_array('All', $request->building_ids))
                        {
                            $request->building_ids = array_unique($this->getReportBuildingIds($project_ids,$user->user_type,$service_provider_ids,$user->service_provider,$user->building_ids,$user->project_user_id));
                        }

                        $data_reactive = [];
                        $data_preventive = [];

                      $fetchCalculation = $this->getReportWorkordersCalculation($request,$project_user_id,$user);

                      $job_rating = $this->getReportJobRating($request->maintenance_type,$request,$project_user_id,$user);

                      $reactive_job_rating = $this->getReportJobRating(['reactive'],$request,$project_user_id,$user);

                      $preventive_job_rating = $this->getReportJobRating(['preventive'],$request,$project_user_id,$user);


                      //Begin: Preventive Work Orders
                      $getReportPMWorkordersData = $this->getReportPMWorkordersData($request, $project_user_id,$user);

                      if(in_array( 'preventive',$request->maintenance_type))
                      {
                          $data_preventive = $this->getPreventiveWorkorderFormatted($request,$project_user_id,$user);
                      }
                      //End: Preventive Work Orders

                      Log::info($request->maintenance_type);

                      //Begin: Reactive Work Orders
                      $fetchReactiveWorkOrders = $this->getReportRMWorkorders($request,$project_user_id,$user);

                      $getMergeServiceType = $this->getMergeServiceType($getReportPMWorkordersData['status_counts'],$fetchReactiveWorkOrders['ReactiveWorkorderStatusCounts'],$getReportPMWorkordersData['service_type_counts'],$fetchReactiveWorkOrders['reactiveWorkOrdersServicetype']);

                      if(in_array('reactive',$request->maintenance_type))
                      {
                        $data_reactive = $this->getReactiveWorkorderFormatted($request,$project_user_id,$user);
                      }
                      //End: Reactive Work Orders

                  return ['lang'=>$lang, 'assigned_to_users'=>$assigned_to_users, 'final_work_order_counts_by_status'=>$getMergeServiceType['FinalWorkorderCountsbyStatus'], 'user'=>$user, 'preventive_workorders_servicetype'=>$getReportPMWorkordersData['service_type_counts'], 'reactive_workorders_servicetype'=>$fetchReactiveWorkOrders['reactiveWorkOrdersServicetype'], 'merged_workorders_servicetype'=>$getMergeServiceType['mergedWorkOrdersServiceType'], 
                  'workorders_servicetype_total'=>$getMergeServiceType['WorkOrdersServicetypeTotal'],
                  'WorkOrdersServicetypeTotalReactive'=>$getMergeServiceType['WorkOrdersServicetypeTotalReactive'],
                  'WorkOrdersServicetypeTotalPreventive'=>$getMergeServiceType['WorkOrdersServicetypeTotalPreventive'],
                   'data_reactive'=>$data_reactive, 'data_preventive'=>$data_preventive,'start_date'=>date('d/m/Y', strtotime($start_date)), 'end_date'=>date('d/m/Y', strtotime($end_date)),'generated_at'=> date('d/m/Y, h:i A'), 'request_type'=>$request->service_type ,  'maintenance_type'=>$request->maintenance_type, 'reactive_work_orders_count'=> $fetchReactiveWorkOrders['reactive_work_orders_count'], 'preventive_work_orders_count'=> $getReportPMWorkordersData['total_count'], 'project_name'=> $project_name, 'report_no'=>  $report_number,                  'job_rating'=>$job_rating,
                  'reactive_job_rating'=>$reactive_job_rating,
                  'preventive_job_rating'=>$preventive_job_rating,
                  'wo_completed_on_time'=>isset($fetchCalculation['passFailPercentage']) ? number_format((int) $fetchCalculation['passFailPercentage'],0) : 0,'response_time'=>isset($fetchCalculation['woCompletedOnTime']) ? $fetchCalculation['woCompletedOnTime'] : 0];
        }


        public function getExportCalendarData($request,$user,$filename,$report_id,$report_number,$lang)
        {
            if($request->check_export_all == 'export_all')
            {
                $year = date('Y');
                $selectedMonths = [];

                for ($i = 1; $i <= 12; $i++) {
                    $month = str_pad($i, 2, '0', STR_PAD_LEFT);
                    $selectedMonths[] = "$year-$month";
                }
            }
            else
            {
                $selectedMonths = explode(',',$request->months);
            }

            $data = [];


                $building_ids = User::where('id', $user->id)
                ->where('status', 1)
                ->where('is_deleted', 'no')
                ->pluck('building_ids')->first();

                $building_ids = explode(',', $building_ids);

                $asset_categories = explode(',', $user->asset_categories);
                $startDate = '';
                $endDate = '';

                $contractServiceProviders = [];
                $requestMonthName = [];
                $requestMonthNameAr = [];
                $workOrderUniqueId = [];

                foreach ($selectedMonths as $monthStr)
                {
                    $monthDate = Carbon::createFromFormat('Y-m-d', $monthStr . '-01', 'Asia/Riyadh')->startOfMonth(); // Ensure correct month parsing
                    $startOfMonth = $monthDate->copy();
                    $endOfMonth = $monthDate->copy()->endOfMonth();

                    // Get visible calendar range (including previous/next month days)
                    $visibleStart = $startOfMonth->copy()->startOfWeek(Carbon::SUNDAY);
                    $visibleEnd = $endOfMonth->copy()->endOfWeek(Carbon::SATURDAY);
                    if($startDate == '')
                    {
                        $startDate = $startOfMonth->format('Y-m-d');
                    }

                    $endDate = $endOfMonth->format('Y-m-d');

                    if($request->is_export_ppm == 'yes')
                    {
                        // Get work orders for the actual month
                        $workOrders = $this->getCalenderPPMWorkorders($request,$user,$visibleStart,$visibleEnd,$building_ids,$asset_categories,$startOfMonth,$endOfMonth);
                    }
                    else
                    {
                        // Get work orders for the actual month
                        $workOrders = $this->getCalenderPPMReportWorkorders($request,$user,$visibleStart,$visibleEnd,$building_ids,$asset_categories,$startOfMonth,$endOfMonth);
                        
                        // ✅ Now calculate grouped status count AFTER all modifications
                        $statusCounts = clone $workOrders; // Clone to avoid modifying original collection
                        $statusCounts = $statusCounts->groupBy('status')->map->count();

                        // Initialize all statuses (1 to 8) to 0
                        $allStatuses = range(1, 8);
                        $finalCounts = [];

                        foreach ($allStatuses as $status) {
                            $finalCounts[$status] = $statusCounts[$status] ?? 0;
                        }
                    }
                    // Get PM counts for visible calendar days
                    $pmCounts = $workOrders->groupBy(fn ($order) => Carbon::parse($order->start_date)->format('Y-m-d'))
                    ->map(fn ($orders) => $orders->count());
                    $contractServiceProviders = array_merge($contractServiceProviders,
                    $workOrders->map(fn ($workOrder) => $workOrder->contract ? $workOrder->contract->service_provider_id : null)
                    ->filter() // Removes null values
                    ->unique()
                    ->toArray());


                    $workOrderUniqueId = array_merge($workOrderUniqueId,$workOrders->pluck('unique_id')->unique()->toArray());

                    // Generate calendar HTML
                    $calendarHtml = $this->generateCalendarHtml(
                        $visibleStart,
                        $visibleEnd,
                        $pmCounts,
                        $monthDate,
                        $lang
                    );

                    $monthData = self::getTranslateMonthname($monthDate,$lang);
                    $monthname = $lang == 'en' ? $monthData['month_name_en'] : $monthData['month_name_ar'];
                    $requestMonthName = array_merge($requestMonthName,explode(',',$monthDate->format('F Y')));
                    $requestMonthNameAr = array_merge($requestMonthNameAr,explode(',',$monthData['month_name_ar'].$monthDate->format(' Y')));
                    if($request->is_export_ppm == 'yes')
                    {
                        $data[] = [
                            'month' => $monthname.$monthDate->format(' Y'),
                            'calendar' => $calendarHtml,
                            'workOrders' => is_null($workOrders) ? [] : $workOrders
                        ];
                    }
                    else
                    {
                        $data[] = [
                            'month' => $monthname.$monthDate->format(' Y'),
                            'calendar' => $calendarHtml,
                            'workOrders' => is_null($workOrders) ? [] : $workOrders,
                            'statusCounts' => $finalCounts ?? []
                        ];
                    }
                    
                }

                $project_ids = $request->project_ids ?? [];
                $project_name = $this->getReportProjectName($lang,$user->project_id,$user->user_type,$project_ids);
                return ['data'=> $data,'startDate'=> $startDate,'project_name'=> $project_name,'endDate'=> $endDate,'contractServiceProviders'=>array_values(array_unique($contractServiceProviders)),'requestMonthName'=>$requestMonthName,'requestMonthNameAr'=>$requestMonthNameAr,'workOrderUniqueId'=>array_values(array_unique($workOrderUniqueId)),'selectedMonths'=>$selectedMonths];
        }

        public function getTranslateMonthname($monthDate)
        {
                    if($monthDate->format('F') == 'January')
                    {
                        $monthname = 'يناير';
                    }
                    elseif($monthDate->format('F') == 'February')
                    {
                        $monthname = 'فبراير';
                    }
                    elseif($monthDate->format('F') == 'March')
                    {
                        $monthname = 'مارس';
                    }
                    elseif($monthDate->format('F') == 'April')
                    {
                        $monthname = 'أبريل';
                    }
                    elseif($monthDate->format('F') == 'May')
                    {
                        $monthname = 'مايو';
                    }
                    elseif($monthDate->format('F') == 'June')
                    {
                        $monthname = 'يونيو';
                    }
                    elseif($monthDate->format('F') == 'July')
                    {
                        $monthname = 'يوليو';
                    }
                    elseif($monthDate->format('F') == 'August')
                    {
                        $monthname = 'أغسطس';
                    }
                    elseif($monthDate->format('F') == 'September')
                    {
                        $monthname = 'سبتمبر';
                    }
                    elseif($monthDate->format('F') == 'October')
                    {
                        $monthname = 'أكتوبر';
                    }
                    elseif($monthDate->format('F') == 'November')
                    {
                        $monthname = 'نوفمبر';
                    }
                    elseif($monthDate->format('F') == 'December')
                    {
                        $monthname = 'ديسمبر';
                    }

                    return ['month_name_en' => $monthDate->format('F'),'month_name_ar' => $monthname];
        }



        public function getTranslateMonth($monthDate)
        {
            $month = date('F', strtotime($monthDate));
                    if($month == 'January')
                    {
                        $monthname = 'يناير';
                    }
                    elseif($month == 'February')
                    {
                        $monthname = 'فبراير';
                    }
                    elseif($month == 'March')
                    {
                        $monthname = 'مارس';
                    }
                    elseif($month == 'April')
                    {
                        $monthname = 'أبريل';
                    }
                    elseif($month == 'May')
                    {
                        $monthname = 'مايو';
                    }
                    elseif($month == 'June')
                    {
                        $monthname = 'يونيو';
                    }
                    elseif($month == 'July')
                    {
                        $monthname = 'يوليو';
                    }
                    elseif($month == 'August')
                    {
                        $monthname = 'أغسطس';
                    }
                    elseif($month == 'September')
                    {
                        $monthname = 'سبتمبر';
                    }
                    elseif($month == 'October')
                    {
                        $monthname = 'أكتوبر';
                    }
                    elseif($month == 'November')
                    {
                        $monthname = 'نوفمبر';
                    }
                    elseif($month == 'December')
                    {
                        $monthname = 'ديسمبر';
                    }

                    return ['month_name_en' => $month,'month_name_ar' => $monthname];
        }


        public function fetchSpdetailsusingServiceproviderids($contractServiceProvidersIds)
        {
            if(count($contractServiceProvidersIds) > 1)
            {
                return [];
            }

            $result = User::where('status',1)->where('status', 1)
            ->where('is_deleted', 'no')
            ->where('user_type', 'sp_admin')
            ->whereIn('service_provider',$contractServiceProvidersIds)
            ->get()->toArray();

            return $result;
        }



        private function generateCalendarHtml($visibleStart, $visibleEnd, $pmCounts, $targetMonth,$lang) {
            $currentDay = $visibleStart->copy();
            $calendar = '<table class="calendar"><tr>';

            if($lang == 'en')
            {
                $weekname = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
            }
            else
            {
                $weekname = ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];
            }

            // Header row
            foreach ($weekname as $day) {
                $calendar .= "<th>$day</th>";
            }
            $calendar .= '</tr><tr>';

            while ($currentDay->lte($visibleEnd)) {
                $isCurrentMonth = $currentDay->month === $targetMonth->month;
                $dateStr = $currentDay->format('Y-m-d');
                $count = $pmCounts[$dateStr] ?? 0;

                $calendar .= sprintf(
                    '<td class="%s">%s%s</td>',
                    $isCurrentMonth ? 'current-month' : 'other-month',
                    $currentDay->day,
                    $isCurrentMonth ? "<div class='count'>PM: $count</div>" : ''
                );

                if ($currentDay->dayOfWeek === Carbon::SATURDAY) {
                    $calendar .= '</tr><tr>';
                }

                $currentDay->addDay();
            }

            $calendar .= '</tr></table>';
            return $calendar;
        }


        public function getCalenderexportPMcount($request,$user,$visibleStart,$visibleEnd,$building_ids,$asset_categories)
        {
            $pmCounts = WorkOrders::where('work_order_type', 'preventive')
                        ->leftJoin('contracts', 'contracts.id', '=', 'work_orders.contract_id')
                        ->where('work_orders.project_user_id', $user->project_user_id)
                        ->whereBetween('work_orders.start_date', [$visibleStart, $visibleEnd])
                        ->whereIn('work_orders.property_id', $building_ids)
                        ->whereIn('work_orders.asset_category_id', $asset_categories);
                        if(isset($request->buildings))
                        {
                            $pmCounts = $pmCounts->whereIn('work_orders.property_id', explode(',',$request->buildings));

                        }
                        if(isset($request->selected_asset_categories))
                        {
                            $pmCounts = $pmCounts->whereIn('work_orders.asset_category_id', explode(',',$request->selected_asset_categories));
                        }
                        if(isset($request->service_type))
                        {
                            $pmCounts = $pmCounts->whereIn('work_orders.service_type', explode(',',$request->service_type));
                        }
                        if(isset($request->service_providers))
                        {
                            $pmCounts = $pmCounts->whereIn('contracts.service_provider_id', explode(',',$request->service_providers));
                        }
                    $pmCounts = $pmCounts->selectRaw('DATE(work_orders.start_date) as date, COUNT(*) as count')
                        ->groupBy('work_orders.start_date')
                        ->where('work_orders.is_deleted','no')
                        ->pluck('count', 'date');

                        return $pmCounts;
        }




        public function getCalenderPPMWorkorders($request,$user,$visibleStart,$visibleEnd,$building_ids,$asset_categories,$startOfMonth,$endOfMonth)
        {
            // Get work orders for the actual month
            $workOrders = WorkOrders::select('work_orders.work_order_id','work_orders.unique_id','work_orders.created_at','work_orders.description','work_orders.property_id','work_orders.contract_id','work_orders.contract_id','work_orders.work_order_type','work_orders.project_user_id','work_orders.start_date','work_orders.asset_category_id','work_orders.property_id','work_orders.service_type','work_orders.frequency_id','work_orders.asset_name_id','work_orders.wtf_start_time')->with([
                'contract',
                'propertyBuilding.property',
                'frequencyMaster',
                'workTimeFrame',
                'selectedAssetName'])
                ->where('work_orders.work_order_type', 'preventive')
                ->where('work_orders.project_user_id', $user->project_user_id)
                ->whereBetween('work_orders.start_date', [$startOfMonth, $endOfMonth])
                ->whereIn('work_orders.property_id', $building_ids)
                //->whereBetween('work_orders.start_date', [$visibleStart, $visibleEnd])
                ->whereIn('work_orders.asset_category_id', $asset_categories);
                
                if(isset($request->buildings))
                {
                    $workOrders = $workOrders->whereIn('work_orders.property_id', explode(',',$request->buildings));
                }
                if(isset($request->selected_asset_categories))
                {
                    $workOrders = $workOrders->whereIn('work_orders.asset_name_id', explode(',',$request->selected_asset_categories));
                }
                if(isset($request->service_type))
                {
                    $workOrders = $workOrders->whereIn('work_orders.asset_category_id', explode(',',$request->service_type));
                }
                $workOrders = $workOrders->orderBy('work_orders.start_date')
                ->where('work_orders.is_deleted','no')
                ->get();

                return $workOrders;
        }




        public function getCalenderPPMReportWorkorders($request,$user,$visibleStart,$visibleEnd,$building_ids,$asset_categories,$startOfMonth,$endOfMonth)
        {
            // Get work orders for the actual month
            $workOrders = WorkOrders::select('work_orders.work_order_id','work_orders.unique_id','work_orders.created_at','work_orders.description','work_orders.property_id','work_orders.contract_id','work_orders.work_order_type','work_orders.project_user_id','work_orders.start_date','work_orders.asset_category_id','work_orders.property_id','work_orders.service_type','work_orders.frequency_id','work_orders.asset_name_id','work_orders.wtf_start_time','work_orders.status','work_orders.contract_type')->with([
                'contract',
                'propertyBuilding.property',
                'frequencyMaster',
                'workTimeFrame',
                'selectedAssetName',
                'slaAssetCategory',
                'assetCategory'])
                ->join('contracts', 'contracts.id', '=', 'work_orders.contract_id')
                ->where('work_orders.work_order_type', 'preventive')
                ->whereBetween('work_orders.start_date', [$startOfMonth, $endOfMonth]);
             

                if(!empty($user->user_type != 'sp_admin') && !empty($user->user_type != 'supervisor'))
                {
                    $workOrders = $workOrders->where('work_orders.project_user_id', $user->project_user_id);
                }

                if(!empty($user->user_type == 'building_manager_employee') || !empty($user->user_type == 'building_manager'))
                {
                    $workOrders = $workOrders->whereIn('work_orders.property_id', $building_ids)
                                ->whereIn('work_orders.asset_category_id', $asset_categories);
                }
                elseif(!empty($user->user_type == 'sp_admin'))
                {
                        $contract_id = Contracts::where('status', 1)
                                        ->where('is_deleted', 'no')
                                        ->where('service_provider_id', $user->service_provider)
                                        ->pluck('id')
                                        ->toArray();
                        $workOrders = $workOrders->whereIn('work_orders.contract_id', $contract_id)
                        ->where('work_orders.contract_type', 'regular');
                }
                elseif(!empty($user->user_type == 'supervisor'))
                {
                        $workOrders = $workOrders->whereRaw("find_in_set($user->id, work_orders.supervisor_id)")->where('work_orders.contract_type', 'regular');
                }
                else
                {
                    $contract_id = Contracts::where('status', 1)
                                        ->where('is_deleted', 'no')
                                        ->where('service_provider_id', $request->service_provider_id)
                                        ->pluck('id')
                                        ->toArray();
                    $workOrders = $workOrders->whereIn('work_orders.contract_id', $contract_id);
                }


                
                if(isset($request->buildings))
                {
                    $workOrders = $workOrders->whereIn('work_orders.property_id', explode(',',$request->buildings));
                }
                if(isset($request->selected_asset_categories))
                {
                    $workOrders = $workOrders->whereIn('work_orders.asset_name_id', explode(',',$request->selected_asset_categories));
                }
                if(isset($request->service_type))
                {
                    $workOrders = $workOrders->whereIn('work_orders.asset_category_id', explode(',',$request->service_type));
                }
                $workOrders = $workOrders->orderBy('work_orders.start_date')
                ->where('work_orders.is_deleted','no')
                ->get();

                if (!$workOrders->isEmpty()) {
                    foreach ($workOrders as $key => $wo) {
                        $wo->status_text = ReportQueryHelper::getWorkorderstatus($wo->status, $wo->contract_type);
                        $wo->status_text_class = ReportQueryHelper::getWorkorderstatusClass($wo->status, $wo->contract_type);
                    }
                }

                return $workOrders;
        }



        public function getGeneratePPMReportPdf($filename,$finalHtml,$report_id,$is_export_ppm)
        {
            gc_collect_cycles();
            // try
            // {
               ini_set('memory_limit', '15G');
               // Load the HTML into the PDF generator
               

                if ($is_export_ppm !== 'yes') 
                {
                    //This is for ppm report only not for export
                    $pdf = SnappyPdf::loadHTML($finalHtml)
                    ->setOption('page-width', 210)
                    ->setOption('page-height', 297)
                    ->setOption('margin-top', '0mm')
                    ->setOption('margin-bottom', '0mm')
                    ->setOption('margin-left', '0mm')
                    ->setOption('margin-right', '0mm')
                    ->setOption('enable-local-file-access', true)
                    ->setOption('enable-smart-shrinking', true)
                        ->setOption('print-media-type', true)
                    ->setOption('encoding', 'UTF-8')
                    ->setOrientation('landscape'); // Ensures proper Arabic rendering or layout
                }
                else
                {
                    $pdf = SnappyPdf::loadHTML($finalHtml)
               ->setOption('page-width', 210)
               ->setOption('page-height', 297)
               ->setOption('margin-top', '0mm')
               ->setOption('margin-bottom', '0mm')
               ->setOption('margin-left', '0mm')
               ->setOption('margin-right', '0mm')
               ->setOption('enable-local-file-access', true)
               ->setOption('enable-smart-shrinking', true)
                ->setOption('print-media-type', true)
             ->setOption('encoding', 'UTF-8'); // Ensures proper Arabic rendering
                }
        //    } catch (\Exception $e) {
        //        Log::error('Error during PDF generation: ' . $e->getMessage());
        //    }
                  // Trigger garbage collection after generating PDF
                  gc_collect_cycles();
                    $path = public_path('reports');
                    if(!is_dir($path)) {
                      mkdir($path, 0755, true);
                    }
                    $content = $pdf->download()->getOriginalContent();
                    file_put_contents( $path.'/'.$filename, $content);
                    Log::info('PDF Ready');
                    $headers = array('Content-Type: application/pdf');
                    //push the media file to OCI
                    $ociUrlFile = $this->pushMediasToOCI($this->setModelLog('Report', $report_id),$filename,'reports',null,true);
                    //Set OCI link if exists
                    $file_link = isset($ociUrlFile) ? $ociUrlFile : asset('reports').DIRECTORY_SEPARATOR.$filename;
                    return $file_link;
        }



        public function getSendPPMReportEmail($selected_months,$filename,$report_id,$file_link,$report_number,$start_date,$end_date,$lang,$user,$spDataList,$workOrderUniqueId,$requestMonthName,$requestMonthNameAr,$contractServiceProviders)
        {

            //try {
                $rpt = ['start_date' => $start_date, 'end_date' => $end_date,'generated_at'=>date('Y-m-d H:i:s'), 'status'=>'generated', 'oci_link' => $file_link];
                Exports::where('id', $report_id)->update($rpt);
                $generated = true;
                Log::info('PPM Report Generated stored in the DB');
            // } catch (\Exception $e) {
            //     $rpt = [ 'status'=>'failed','start_date' => $start_date, 'end_date' => $end_date];
            //     $generated = false;
            //     Exports::where('id', $report_id)->update($rpt);
            //     Log::info('PPM Report Failed Stored in the DB');
            // }



            if($generated)
            {
                $lastRequest = ManagePpmRequest::latest('id')->first();
                $lastNumber = $lastRequest ? $lastRequest->id : 0;
                $newNumber = $lastNumber + 1;
                $export_id = 'PPM-' . str_pad($newNumber, 6, '0', STR_PAD_LEFT);
                $ppmrequestData = ['export_id'=>$export_id,'lang'=>$lang,'request_by'=>$user->id,'start_date' => $start_date, 'end_date' => $end_date,'status'=>'pending','file_link'=>$file_link,'requested_months'=>json_encode($selected_months),'requested_months_name'=>implode(',',$requestMonthName),'requested_months_name_ar'=>implode(',',$requestMonthNameAr),'created_at'=>Carbon::now(),'file_name'=>$report_number,'export_report_id'=>$report_id,'project_id'=>$user->project_id];

                $ppmRequestId = ManagePpmRequest::insertGetId($ppmrequestData);
                if($ppmRequestId)
                {
                    $insertData = []; // Prepare an array to store data before insertion
                    foreach($workOrderUniqueId as $w => $wd)
                    {
                        $insertData[] = [
                            'ppm_request_id' => $ppmRequestId,
                            'workorder_unique_id' => $wd,
                            'created_at' => now()
                        ];
                    }

                    PpmRequestWorkordersMapping::insert($insertData);
                }

                foreach($spDataList as $k =>$v)
                {
                    $subject = 'PPM Export Request Received';

                    $mail_content['dir'] = $lang=='en' ? 'ltr' : 'rtl';
                    $to_name  = $v['name'];
                    $to_email = $v['email'];
                    $mail_content['lang'] = $lang;
                    $mail_content['requested_by'] = $user->name;
                    $mail_content['subject_en'] = $subject;
                    $mail_content['subject_ar'] = $subject;
                    $mail_content['manage_ppm_report_link'] = '';
                    $mail_content['report_no'] = $export_id;
                    $mail_content['report_date'] = date('Y-m-d h:i A');

                    $sender_email = Helper::getAdminContactMail();
                    if ($lang=='en')
                    {
                            $mail_content['label_report_heading'] = 'PPM Export Request for Approval';
                            $mail_content['label_report_detail_text'] = 'A PPM export report request has been submitted and is pending your approval.Please review the request and take appropriate action.';
                            $mail_content['label_report_no'] ='Export ID';
                            $mail_content['label_requested_by'] ='Requested By';
                            $mail_content['label_requested_on'] ='Requested On';
                            $mail_content['label_link_text'] = 'click here to review and approve request';
                    }
                    else{
                        $mail_content['label_report_heading'] = 'طلب موافقة لخطة الأعمال الوقائية';
                        $mail_content['label_report_detail_text'] = 'تم تقديم طلب تقرير تصدير خطة أعمال الصيانة الوقائية وهو بانتظار موافقتك. يرجى مراجعة الطلب واتخاذ الإجراء المناسب.';
                        $mail_content['label_report_no'] ='معرّف التصدير';
                        $mail_content['label_requested_by'] ='مقدم الطلب';
                        $mail_content['label_requested_on'] ='تاريخ الطلب';
                        $mail_content['label_link_text'] = 'انقر هنا لمراجعة الطلب والموافقة عليه';
                    }


                    //Send Notification To SP Admin
                    $result_array = array('user_id' => $v['id'], 'message' => 'PPM export received, click to view', 'message_ar' => 'تم استلام تصدير خطة الأعمال الوقائية، انقر للعرض', 'section_type' => 'report', 'building_ids' => $v['building_ids'], 'notification_type' => 'create_ppm', 'notification_sub_type' => 'create_ppm', 'additional_param' => $report_id, 'created_by' => $user->id, 'created_at' => date('Y-m-d H:i:s'));
                    Notification::insert($result_array);
                    //try {
                          Mail::send('mail.ppmReportApprovalemail', ['mail_content' => $mail_content]
                            , function ($message) use ($to_name, $to_email, $subject, $sender_email, $report_number,$report_id,$user,$filename) {
                            $message->to($to_email, $to_name)
                                ->subject($subject);
                            $message->from($sender_email, 'Osool Team');
                          });

                          Log::info('Email sent');
                    //   }
                    //   catch (\Exception $e) {
                    //       //code to handle the exception
                    //       Log::info('Email sending failed');
                    //   }
                }


                //Send Email To BMA
                $ppmReuqestData = ManagePpmRequest::where('id',$ppmRequestId)->where('status','pending')->first();
                $this->getSendPPMReportEmailforBM($ppmReuqestData,$user,$lang,$contractServiceProviders);

              }
        }




        public function getSendPPMReportNotificationforBM($ppmReuqestData,$bmaData)
        {
            //Send Notification To BM Admin
            $result_array = array('user_id' => $bmaData->id, 'message' => 'PPM export has an update, click to view', 'message_ar' => 'تم تحديث تصدير خطة الأعمال الوقائية، انقر للعرض', 'section_type' => 'report', 'building_ids' => $bmaData->building_ids, 'notification_type' => 'create_ppm', 'notification_sub_type' => 'create_ppm', 'additional_param' => $ppmReuqestData->id, 'created_by' => Auth::user()->id, 'created_at' => date('Y-m-d H:i:s'));
            Notification::insert($result_array);
        }


        public function getSendPPMReportEmailforBM($ppmReuqestData,$bmaData,$lang,$contractServiceProviders)
        {
            $spnames = '';
            if(count($contractServiceProviders) > 0)
            {
                $spnames = ServiceProvider::whereIn('id',$contractServiceProviders)->pluck('name')  // Get only the 'name' column
                ->implode(',');  // Convert it into a comma-separated string
            }

            $subject = 'PPM Export Request Received';

                    $mail_content['dir'] = $lang=='en' ? 'ltr' : 'rtl';
                    $to_name  = $bmaData->name;
                    $to_email = $bmaData->email;
                    $mail_content['lang'] = $lang;
                    $mail_content['requested_by'] = $spnames != "" ? $spnames : '-';
                    $mail_content['subject_en'] = $subject;
                    $mail_content['subject_ar'] = $subject;
                    $mail_content['manage_ppm_report_link'] = $ppmReuqestData->file_link;
                    $mail_content['report_no'] = $ppmReuqestData->export_id;
                    $mail_content['report_date'] =  date('F Y', strtotime($ppmReuqestData->start_date)).' To '. date('F Y', strtotime($ppmReuqestData->end_date));
                    $sender_email = Helper::getAdminContactMail();
                    if ($lang=='en')
                    {
                            $mail_content['label_report_heading'] = 'Confirmation about Exported PPM';
                            $mail_content['label_report_detail_text'] = 'A request to generate a PPM export has been initiated';
                            $mail_content['label_report_no'] ='Export ID';
                            $mail_content['label_requested_by'] ='Requested For';
                            $mail_content['label_requested_on'] ='Request Period';
                            $mail_content['label_link_text'] = 'click here to view and download exported ppm';
                    }
                    else{
                        $mail_content['label_report_heading'] = 'تأكيد حول تصدير خطة الأعمال الوقائية';
                        $mail_content['label_report_detail_text'] = 'تم بدء طلب لإنشاء تصدير خطة الأعمال الوقائية';
                        $mail_content['label_report_no'] ='معرّف التصدير';
                        $mail_content['label_requested_by'] ='المطلوب لـ';
                        $mail_content['label_requested_on'] ='فترة الطلب';
                        $mail_content['label_link_text'] = 'انقر هنا لعرض وتنزيل تصدير خطة الأعمال الوقائية';
                    }

                    Mail::send('mail.ppmReportApprovalemail', ['mail_content' => $mail_content]
                            , function ($message) use ($to_name, $to_email, $subject, $sender_email) {
                            $message->to($to_email, $to_name)
                                ->subject($subject);
                            $message->from($sender_email, 'Osool Team');
                          });

        }



    public function getPpmListByValues($perPage)
    {

        try {

            if(Auth::user()->user_type == 'building_manager')
            {
                $data = ManagePpmRequest::where('request_by',Auth::user()->id)->where('project_id',Auth::user()->project_id);
            }
            else
            {
                //SP
                $projectIds = ExportsHelper::getPpmenabledsprojects();
                $projectIds = count($projectIds) > 0 ? $projectIds : [0];
                $data = ManagePpmRequest::whereIn('project_id',$projectIds);
            }


            return $data->orderBy('manage_ppm_requests.id','DESC')
            ->with('requestBy','updatedBy')
                ->paginate($perPage, ['*'], 'page');
        } catch (\Throwable $th) {
            Log::error("getPpmListByValues error: " . $th);
        }
    }



    public function fetchPpmRequestName($ppmRequestId) {
        try {
            return ManagePpmRequest::where('export_report_id', $ppmRequestId)->value('export_id');
        }

        catch (\Throwable $th) {
            Log::error("fetchPpmRequestName Error: ".$th);
        }
    }

      public function getExportKPIEmail($fileLink, $fileName, $reportId, $reportNumber, $user, $lang): void
    {
        $subject = 'New KPI export has been generated';

        $mail_content['dir'] = $lang == 'en' ? 'ltr' : 'rtl';
        $to_name = $user->name;
        $to_email = $user->email;
        $mail_content['name'] = $user->name;
        $mail_content['subject_en'] = $subject;
        $mail_content['subject_ar'] = $subject;
        $mail_content['report_file_link'] = $fileLink;
        $mail_content['report_no'] = $reportNumber;
        $sender_email = Helper::getAdminContactMail();

        if ($lang == 'en') {
            $mail_content['label_report_no'] = 'Report Number';
            $mail_content['label_download'] = 'Download';
            $mail_content['label_report_file'] = 'PDF File';
            $mail_content['label_email_footer_text'] = '*Note: This report is available to be downloaded during the next 24 hours.
                    You can generate new reports anytime';
        } else {
            $mail_content['label_report_no'] = 'رقم التقرير';
            $mail_content['label_download'] = 'تنزيل';
            $mail_content['label_report_file'] = 'ملف PDF ';
            $mail_content['label_email_footer_text'] = '*ملاحظة: هذا التقرير متاح للتنزيل خلال ال24 ساعة القادمة فقط.
                    يمكنك انشاء المزيد من التقارير في أي وقت. ';
        }

        $rpt = ['generated_at' => date('Y-m-d H:i:s'), 'status' => 'generated', 'oci_link' => $fileLink];
        ReportQueryHelper::editExports($reportId, $rpt);

        logger('Report Generated stored in the DB');

        Mail::send('mail.kpi-report-generated', ['mail_content' => $mail_content],
            function ($message) use ($to_name, $to_email, $subject, $sender_email, $reportNumber, $reportId, $user, $fileName) {
                $message->to($to_email, $to_name)->subject($subject);
                $message->from($sender_email, 'Osool Team');
            });
    }


    }

?>

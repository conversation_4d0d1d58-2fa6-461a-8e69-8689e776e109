<div>
    <form wire:submit.prevent="save">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalLabel">@lang('lead.convert_lead')</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="show-hide-radio">
                        <div class="col-md-8 mx-auto">
                            @if ($contact_selection == 'new')
                                <div class="row text-center proposal-timeline timeline-new new-contact contact-type">
                                    <div class="col-4 timeline-div">
                                        <div class="h-100">
                                            <div class="timeline-icons text-center active mb-2">
                                                <span class="timeline-dots"></span>
                                                <span
                                                    class="d-center wh-35 rounded-circle mx-auto border-new-primary fw-700 text-new-primary border-2 @if ($contact_created) bg-new-primary @else bg-white bg-opacity-new-primary @endif">
                                                    @if ($contact_created)
                                                        <i class="las la-check text-white fs-20"></i>
                                                    @else
                                                        1
                                                    @endif
                                                </span>
                                            </div>
                                            <span>@lang('lead.contact')</span>
                                        </div>
                                    </div>
                                    <div class="col-4 timeline-div">
                                        <div class="h-100">
                                            <div class="timeline-icons mb-2">
                                                <span class="timeline-dots"></span>
                                                <span
                                                    class="d-center wh-35 rounded-circle mx-auto border-new-primary fw-700 text-new-primary border-2 @if ($account_created) bg-new-primary @else bg-white bg-opacity-new-primary @endif">
                                                    @if ($account_created)
                                                        <i class="las la-check text-white fs-20"></i>
                                                    @else
                                                        2
                                                    @endif
                                                </span>
                                            </div>
                                            <span>@lang('lead.account')</span>
                                        </div>
                                    </div>
                                    <div class="col-4 timeline-div">
                                        <div class="h-100">
                                            <div class="timeline-icons mb-2">
                                                <span class="timeline-dots"></span>
                                                <span
                                                    class="d-center wh-35 bg-white rounded-circle mx-auto bg-opacity-new-primary border-new-primary fw-700 text-new-primary border-2">3</span>
                                            </div>
                                            <span>@lang('lead.deal')</span>
                                        </div>
                                    </div>
                                </div>
                            @endif
                        </div>
                        @if ($multistep_form == 'contact')
                            <div class="form-group">
                                <label for="client_name">@lang('lead.convert_to') <span class="required">*</span></label>
                                <div class="radio-horizontal-list d-flex">
                                    <div class="radio-theme-default custom-radio">
                                        <input class="radio" type="radio" wire:model='contact_selection'
                                            name="contact_type" value="existing" id="active_t1" />
                                        <label for="active_t1">
                                            <span class="radio-text">@lang('lead.existing_contact') </span>
                                        </label>
                                    </div>
                                    <div class="radio-theme-default custom-radio">
                                        <input class="radio" type="radio" wire:model='contact_selection'
                                            name="contact_type" value="new" id="active_t2" />
                                        <label for="active_t2">
                                            <span class="radio-text">@lang('lead.new_contact')</span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        @endif
                        @if ($contact_selection == 'existing')
                            <div class="contact-type existing-contact">
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <label for="client_name">@lang('lead.select_contact') <span
                                                    class="required">*</span></label>
                                            <select class="form-control" wire:model='contact'>
                                                <option value=""></option>
                                                @foreach ($contacts as $item)
                                                    <option value="{{ $item['id'] }}">{{ $item['name'] }}</option>
                                                @endforeach
                                            </select>
                                            @error('contact')
                                                <span class="text-danger">{{ $message }}</span>
                                            @enderror
                                        </div>
                                        <div class="form-group">
                                            <label for="client_name">@lang('lead.account_name') <span
                                                    class="required">*</span></label>
                                            <input type="text" class="form-control" disabled
                                                value="{{ $account_name_disabled }}" />
                                        </div>

                                        <div class="show-hide-radio">
                                            <div class="form-group">
                                                <label for="client_name">@lang('lead.convert_deal_question') <span
                                                        class="required">*</span></label>
                                                <div class="radio-horizontal-list d-flex">
                                                    <div class="radio-theme-default custom-radio">
                                                        <input class="radio" type="radio" wire:model='create_deal'
                                                            name="new_deal" value="yes" id="deal_1" />
                                                        <label for="deal_1">
                                                            <span class="radio-text">@lang('lead.yes')</span>
                                                        </label>
                                                    </div>
                                                    <div class="radio-theme-default custom-radio">
                                                        <input class="radio" type="radio" wire:model='create_deal'
                                                            name="new_deal" value="no" id="deal_2" checked />
                                                        <label for="deal_2">
                                                            <span class="radio-text">@lang('lead.no')</span>
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                            @if ($create_deal == 'yes')
                                                <div class="contact-type deal-yes">
                                                    @include('livewire.leads.modals.deal-creation')
                                                </div>
                                            @endif
                                        </div>
                                    </div>
                                </div>

                                <div class="modal-footer border-0 px-0">
                                    <button type="button" class="btn bg-hold-light text-white"
                                        data-dismiss="modal">@lang('lead.close')</button>
                                    <button type="submit" class="btn bg-new-primary"
                                        onclick="showLoader()">@lang('lead.convert')</button>
                                </div>
                            </div>
                        @endif
                        @if ($contact_selection == 'new')
                            <div class="contact-type new-contact">
                                <div class="tab-content" id="myTabContent">
                                    @if ($multistep_form == 'contact')
                                        <div class="tab-pane fade show active" id="contact_tab" role="tabpanel"
                                            aria-labelledby="home-tab">
                                            <div class="row mt-3">
                                                <div class="col-sm-6">
                                                    <div class="form-group">
                                                        <label>@lang('Name') <small
                                                                class="required">*</small></label>
                                                        <input type="text" wire:model.defer="contact_name"
                                                            class="form-control" placeholder="@lang('Enter Name')">
                                                        @error('contact_name')
                                                            <span class="text-danger">{{ $message }}</span>
                                                        @enderror
                                                    </div>
                                                </div>
                                                <div class="col-sm-6">
                                                    <div class="form-group">
                                                        <label>@lang('Email') <small
                                                                class="required">*</small></label>
                                                        <input class="form-control" placeholder="@lang('Enter Email')"
                                                            wire:model.defer="contact_email" type="email"
                                                            id="email">
                                                        @error('contact_email')
                                                            <span class="text-danger">{{ $message }}</span>
                                                        @enderror
                                                    </div>
                                                </div>
                                                <div class="col-sm-6">
                                                    <div class="form-group">
                                                        <label>@lang('Phone') <small
                                                                class="required">*</small></label>
                                                        <input class="form-control" placeholder="@lang('Enter Phone')"
                                                            wire:model.defer="contact_phone" type="text">
                                                        @error('contact_phone')
                                                            <span class="text-danger">{{ $message }}</span>
                                                        @enderror
                                                        <div class=" text-xs text-danger mt-1">
                                                            @lang('Please use with country code. (ex. +966)')
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-sm-6">
                                                    <div class="form-group">
                                                        <label>@lang('Address') <small
                                                                class="required">*</small></label>
                                                        <input type="text" wire:model.defer="contact_address"
                                                            class="form-control" placeholder="@lang('Enter Address')">
                                                        @error('contact_address')
                                                            <span class="text-danger">{{ $message }}</span>
                                                        @enderror
                                                    </div>
                                                </div>
                                                <div class="col-sm-6">
                                                    <div class="form-group">
                                                        <label>@lang('City') <small
                                                                class="required">*</small></label>
                                                        <input type="text" wire:model.defer="contact_city"
                                                            class="form-control" placeholder="@lang('Enter City')">
                                                        @error('contact_city')
                                                            <span class="text-danger">{{ $message }}</span>
                                                        @enderror
                                                    </div>
                                                </div>
                                                <div class="col-sm-6">
                                                    <div class="form-group">
                                                        <label>@lang('State') <small
                                                                class="required">*</small></label>
                                                        <input type="text" wire:model.defer="contact_state"
                                                            class="form-control" placeholder="@lang('Enter State')">
                                                        @error('contact_state')
                                                            <span class="text-danger">{{ $message }}</span>
                                                        @enderror
                                                    </div>
                                                </div>
                                                <div class="col-sm-6">
                                                    <div class="form-group">
                                                        <label>@lang('Postal Code') <small
                                                                class="required">*</small></label>
                                                        <input type="text" wire:model.defer="contact_postal_code"
                                                            class="form-control" placeholder="@lang('Enter Postal Code')">
                                                        @error('contact_postal_code')
                                                            <span class="text-danger">{{ $message }}</span>
                                                        @enderror
                                                    </div>
                                                </div>
                                                <div class="col-sm-6">
                                                    <div class="form-group">
                                                        <label>@lang('Country') <small
                                                                class="required">*</small></label>
                                                        <input type="text" wire:model.defer="contact_country"
                                                            class="form-control" placeholder="@lang('Enter Country')">
                                                        @error('contact_country')
                                                            <span class="text-danger">{{ $message }}</span>
                                                        @enderror
                                                    </div>
                                                </div>
                                                <div class="col-sm-6">
                                                    <div class="form-group">
                                                        <label>@lang('Assign User')</label>
                                                        <select wire:model.defer="contact_user"
                                                            class="form-control select2">
                                                            <option value = "" selected>@lang('Select')</option>
                                                            @foreach ($users as $item)
                                                                <option value="{{ $item['id'] }}">
                                                                    {{ $item['name'] }}</option>
                                                            @endforeach
                                                        </select>
                                                        @error('contact_user')
                                                            <span class="text-danger">{{ $message }}</span>
                                                        @enderror
                                                    </div>
                                                </div>
                                                <div class="col">
                                                    <div class="form-group">
                                                        <label>@lang('Description') </label>
                                                        <textarea class="form-control" wire:model.defer="contact_description"></textarea>
                                                        @error('contact_description')
                                                            <span class="text-danger">{{ $message }}</span>
                                                        @enderror
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="modal-footer border-0 px-0">
                                                <!-- <button type="button" class="btn bg-hold-light text-white" data-dismiss="modal">Cancel</button> -->
                                                <button type="button" wire:click.prevent='nextStep("account")'
                                                    class="btn bg-new-primary">@lang('lead.next')</button>
                                            </div>
                                        </div>
                                    @endif
                                    @if ($multistep_form == 'account')
                                        <div class="form-group">
                                            <label for="client_name">@lang('lead.convert_to') <span
                                                    class="required">*</span></label>
                                            <div class="radio-horizontal-list d-flex">
                                                <div class="radio-theme-default custom-radio">
                                                    <input class="radio" type="radio"
                                                        wire:model='account_selection' name="account_type"
                                                        value="existing" id="active_t1" />
                                                    <label for="active_t1">
                                                        <span class="radio-text">@lang('lead.existing_account')</span>
                                                    </label>
                                                </div>
                                                <div class="radio-theme-default custom-radio">
                                                    <input class="radio" type="radio"
                                                        wire:model='account_selection' name="account_type"
                                                        value="new" id="active_t2" />
                                                    <label for="active_t2">
                                                        <span class="radio-text">@lang('lead.new_account')</span>
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="tab-pane fade show active" id="account_tab" role="tabpanel"
                                            aria-labelledby="profile-tab">

                                            @if ($account_selection == 'existing')
                                                <div class="row">
                                                    <div class="col-md-12">
                                                        <div class="form-group">
                                                            <label for="client_name">@lang('lead.select_account') <span
                                                                    class="required">*</span></label>
                                                            <select class="form-control" wire:model='account'>
                                                                <option value=""></option>
                                                                @foreach ($accounts as $item)
                                                                    <option value="{{ $item['id'] }}">
                                                                        {{ $item['name'] }}</option>
                                                                @endforeach
                                                            </select>
                                                            @error('contact')
                                                                <span class="text-danger">{{ $message }}</span>
                                                            @enderror
                                                        </div>
                                                    </div>
                                                </div>
                                            @endif

                                            @if ($account_selection == 'new')

                                                <div class="row mt-3">
                                                    <div class="col-6">
                                                        <div class="form-group">
                                                            <label for="name"
                                                                class="form-label">@lang('Name')</label> <span
                                                                class="text-danger">*</span>
                                                            <input class="form-control"
                                                                placeholder="@lang('Enter Name')"
                                                                wire:model.defer="account_name" type="text"
                                                                id="name">
                                                            @error('account_name')
                                                                <span class="text-danger">{{ $message }}</span>
                                                            @enderror
                                                        </div>
                                                    </div>
                                                    <div class="col-6">
                                                        <div class="form-group">
                                                            <label for="email"
                                                                class="form-label">@lang('Email')</label> <span
                                                                class="text-danger">*</span>
                                                            <input class="form-control"
                                                                placeholder="@lang('Enter Email')"
                                                                wire:model.defer="account_email" type="email"
                                                                id="email">
                                                            @error('account_email')
                                                                <span class="text-danger">{{ $message }}</span>
                                                            @enderror
                                                        </div>
                                                    </div>
                                                    <div class="col-6">
                                                        <div class="form-group">
                                                            <label for="phone"
                                                                class="form-label">@lang('Phone')</label> <span
                                                                class="text-danger">*</span>
                                                            <input class="form-control"
                                                                placeholder="@lang('Enter Phone')"
                                                                wire:model.defer="account_phone" type="text">
                                                            @error('account_phone')
                                                                <span class="text-danger">{{ $message }}</span>
                                                            @enderror
                                                            <div class=" text-xs text-danger mt-1">
                                                                @lang('Please use with country code. (ex. +966)')
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-6">
                                                        <div class="form-group">
                                                            <label for="website"
                                                                class="form-label">@lang('Website')</label>
                                                            <input class="form-control"
                                                                placeholder="@lang('Enter Website')"
                                                                wire:model.defer="website" type="text"
                                                                id="website">
                                                            <div class=" text-xs text-danger mt-1">
                                                                @lang('Please enter a valid URL, e.g., https://example.com')
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-6">
                                                        <div class="form-group">
                                                            <label for="billingaddress"
                                                                class="form-label">@lang('Billing Address')</label> <span
                                                                class="text-danger">*</span>

                                                            <input class="form-control"
                                                                placeholder="@lang('Billing Address')"
                                                                wire:model.defer="billing_address" type="text">
                                                            @error('billing_address')
                                                                <span class="text-danger">{{ $message }}</span>
                                                            @enderror
                                                        </div>
                                                    </div>
                                                    <div class="col-6">
                                                        <div class="form-group">
                                                            <div
                                                                class="d-flex justify-content-between align-items-center mb-2">

                                                                <label for="shippingaddress"
                                                                    class="form-label mb-0">@lang('Shipping Address') <span
                                                                        class="text-danger">*</span></label>


                                                                <a class="btn btn-sm align-items-center border p-1"
                                                                    id="copy_billing_to_shipping"
                                                                    data-toggle="tooltip" title="@lang('Same As Billing Address')"
                                                                    data-bs-placement="top"
                                                                    aria-label="Same As Billing Address"><i
                                                                        class="fas fa-copy mr-0"></i></a>
                                                            </div>
                                                            <input class="form-control"
                                                                placeholder="@lang('Shipping Address')"
                                                                wire:model.defer="shipping_address" type="text">
                                                            @error('shipping_address')
                                                                <span class="text-danger">{{ $message }}</span>
                                                            @enderror
                                                        </div>
                                                    </div>
                                                    <div class="col-3">
                                                        <div class="form-group">
                                                            <input class="form-control"
                                                                placeholder="@lang('Billing City')"
                                                                wire:model.defer="billing_city" type="text">
                                                            @error('billing_city')
                                                                <span class="text-danger">{{ $message }}</span>
                                                            @enderror
                                                        </div>
                                                    </div>
                                                    <div class="col-3">
                                                        <div class="form-group">
                                                            <input class="form-control"
                                                                placeholder="@lang('Billing State')"
                                                                wire:model.defer="billing_state" type="text">
                                                            @error('billing_state')
                                                                <span class="text-danger">{{ $message }}</span>
                                                            @enderror
                                                        </div>
                                                    </div>
                                                    <div class="col-3">
                                                        <div class="form-group">
                                                            <input class="form-control"
                                                                placeholder="@lang('Shipping City')"
                                                                wire:model.defer="shipping_city" type="text">
                                                            @error('shipping_city')
                                                                <span class="text-danger">{{ $message }}</span>
                                                            @enderror
                                                        </div>
                                                    </div>
                                                    <div class="col-3">
                                                        <div class="form-group">
                                                            <input class="form-control"
                                                                placeholder="@lang('Shipping State')"
                                                                wire:model.defer="shipping_state" type="text">
                                                            @error('shipping_state')
                                                                <span class="text-danger">{{ $message }}</span>
                                                            @enderror
                                                        </div>
                                                    </div>
                                                    <div class="col-3">
                                                        <div class="form-group">
                                                            <input class="form-control"
                                                                placeholder="@lang('Billing Country')"
                                                                wire:model.defer="billing_country" type="text">
                                                            @error('billing_country')
                                                                <span class="text-danger">{{ $message }}</span>
                                                            @enderror
                                                        </div>
                                                    </div>
                                                    <div class="col-3">
                                                        <div class="form-group">
                                                            <input class="form-control"
                                                                placeholder="@lang('Billing Postal Code')"
                                                                wire:model.defer="billing_postalcode" type="number">
                                                            @error('billing_postalcode')
                                                                <span class="text-danger">{{ $message }}</span>
                                                            @enderror
                                                        </div>
                                                    </div>
                                                    <div class="col-3">
                                                        <div class="form-group">
                                                            <input class="form-control"
                                                                placeholder="@lang('Shipping Country')"
                                                                wire:model.defer="shipping_country" type="text">
                                                            @error('shipping_country')
                                                                <span class="text-danger">{{ $message }}</span>
                                                            @enderror
                                                        </div>
                                                    </div>
                                                    <div class="col-3">
                                                        <div class="form-group">
                                                            <input class="form-control"
                                                                placeholder="@lang('Shipping Postal Code')"
                                                                wire:model.defer="shipping_postalcode" type="number">
                                                            @error('shipping_postalcode')
                                                                <span class="text-danger">{{ $message }}</span>
                                                            @enderror
                                                        </div>
                                                    </div>
                                                    <div class="col-12">
                                                        <h4 class="mb-3 fw-500 fs-18">@lang('Detail')</h4>
                                                    </div>
                                                    <div class="col-6">
                                                        <div class="form-group">
                                                            <label for="user"
                                                                class="form-label">@lang('Assign User')</label>
                                                            <select class="form-control select2-new" id="user"
                                                                wire:model.defer="account_user">
                                                                <option value="" selected>--</option>
                                                                @foreach ($users as $item)
                                                                    <option value="{{ $item['id'] }}">
                                                                        {{ $item['name'] }}</option>
                                                                @endforeach
                                                            </select>
                                                            @error('account_user')
                                                                <span class="text-danger">{{ $message }}</span>
                                                            @enderror
                                                        </div>
                                                    </div>
                                                    <div class="col-6">
                                                        <div class="form-group">
                                                            <label for="type"
                                                                class="form-label">@lang('Type')</label> <span
                                                                class="text-danger">*</span>
                                                            <select class="form-control" id="type"
                                                                wire:model.defer="type">
                                                                <option selected="selected" value="">
                                                                    @lang('Select Type')</option>
                                                                @foreach ($types as $item)
                                                                    <option value="{{ $item['id'] }}">
                                                                        {{ $item['name'] }}</option>
                                                                @endforeach
                                                            </select>
                                                            @error('type')
                                                                <span class="text-danger">{{ $message }}</span>
                                                            @enderror
                                                        </div>
                                                    </div>
                                                    <div class="col-6">
                                                        <div class="form-group">
                                                            <label for="industry"
                                                                class="form-label">@lang('Industry')</label> <span
                                                                class="text-danger">*</span>
                                                            <select class="form-control" id="industry"
                                                                wire:model.defer="industry">
                                                                <option selected="selected" value="">
                                                                    @lang('Select Industry')</option>
                                                                @foreach ($industries as $item)
                                                                    <option value="{{ $item['id'] }}">
                                                                        {{ $item['name'] }}</option>
                                                                @endforeach
                                                            </select>
                                                            @error('industry')
                                                                <span class="text-danger">{{ $message }}</span>
                                                            @enderror
                                                        </div>
                                                    </div>
                                                    <div class="col-6">
                                                        <div class="form-group">
                                                            <label for="document_id"
                                                                class="form-label">@lang('Document')</label>
                                                            <select class="form-control" id="document"
                                                                wire:model.defer="document">
                                                                <option value="" selected>--</option>
                                                                @foreach ($documents as $item)
                                                                    <option value="{{ $item['id'] }}">
                                                                        {{ $item['name'] }}</option>
                                                                @endforeach
                                                            </select>
                                                            @error('document')
                                                                <span class="text-danger">{{ $message }}</span>
                                                            @enderror
                                                        </div>
                                                    </div>
                                                    <div class="col-12">
                                                        <div class="form-group">
                                                            <label for="Description"
                                                                class="form-label">@lang('Description')</label>
                                                            <textarea class="form-control" rows="3" placeholder="@lang('Enter Description')"
                                                                wire:model.defer="account_description" cols="50"></textarea>
                                                            @error('account_description')
                                                                <span class="text-danger">{{ $message }}</span>
                                                            @enderror
                                                        </div>
                                                    </div>
                                                </div>
                                            @endif

                                            <div class="modal-footer border-0 px-0">
                                                <button wire:click.prevent='prevStep("contact")' type="button"
                                                    class="btn bg-hold-light text-white">@lang('lead.previous')</button>
                                                <button wire:click.prevent='nextStep("deal")' type="button"
                                                    class="btn bg-new-primary">@lang('lead.next')</button>
                                            </div>
                                        </div>
                                    @endif
                                    @if ($multistep_form == 'deal')
                                        <div class="tab-pane fade show active" id="deal_tab" role="tabpanel"
                                            aria-labelledby="contact-tab">
                                            <div class="show-hide-radio mt-20">
                                                <div class="form-group">
                                                    <label for="client_name">@lang('lead.convert_deal_question') <span
                                                            class="required">*</span></label>
                                                    <div class="radio-horizontal-list d-flex">
                                                        <div class="radio-theme-default custom-radio">
                                                            <input class="radio" type="radio"
                                                                wire:model='create_deal' name="new_deal"
                                                                value="yes" id="deal_1" />
                                                            <label for="deal_1">
                                                                <span class="radio-text">@lang('lead.yes')</span>
                                                            </label>
                                                        </div>
                                                        <div class="radio-theme-default custom-radio">
                                                            <input class="radio" type="radio"
                                                                wire:model='create_deal' name="new_deal"
                                                                value="no" id="deal_2" checked />
                                                            <label for="deal_2">
                                                                <span class="radio-text">@lang('lead.no')</span>
                                                            </label>
                                                        </div>
                                                    </div>
                                                </div>
                                                @if ($create_deal == 'yes')
                                                    <div class="contact-type deal-yes">
                                                        @include('livewire.leads.modals.deal-creation')
                                                    </div>
                                                @endif
                                            </div>
                                            <div class="modal-footer border-0 px-0">
                                                <button type="button" class="btn bg-hold-light text-white"
                                                    wire:click.prevent='prevStep("account")'>@lang('lead.previous')</button>
                                                <button type="button" class="btn bg-new-primary"
                                                    onclick="showLoader()"
                                                    wire:click.prevent='nextStep("done")'>@lang('lead.submit')</button>
                                            </div>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

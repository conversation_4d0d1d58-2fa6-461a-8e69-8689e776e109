<div class="col-lg-12 mb-3">
    @include('livewire.common.loader')
    <div class="card">
        <div class="">
            <div class="card-header py-4 px-3 border-0 d-flex justify-content-between align-items-center">
                <h6 class="text-capitalize fw-500 mb-3 mb-sm-0 flex-fill"> @lang('CRMProjects.common.documents')
                    ({{ $DocumentsPaginated['total'] }})</h6>
                <div>
                <button wire:click="openCreatedocument"
                        class="btn btn-default btn-primary w-100 no-wrap" type="button">
                    <i class="las la-plus fs-16"></i> @lang('Create')
                </button>
                </div>
            </div>
        </div>
        <div class="card-body px-0 pt-0">
            <div class="userDatatable projectDatatable project-table bg-white w-100 border-0">
                <div class="table-responsive">
                    <table class="table mb-0 radius-0">
                        <thead>
                            <tr class="userDatatable-header">
                               
                                <th>
                                    @lang('CRMProjects.common.subject')
                                </th>
                                <th>
                                    @lang('CRMProjects.common.user')
                                </th>
                                <th>
                                    @lang('CRMProjects.common.type')
                                </th>
                                <th>
                                    @lang('CRMProjects.common.status')
                                </th>
                                <th>
                                    @lang('CRMProjects.template.actions')
                                </th>
                            </tr>
                        </thead>
                        <tbody class="sort-table ui-sortable">
                            @forelse (@$DocumentsPaginated['items']??[] as $item)
                                <tr class="ui-sortable-handle" style="opacity: 1;">
                                   
                                    <td>
                                        <div class="d-flex userDatatable-content mb-0 align-items-center">
                                            <span>{{ $item['subject'] }}</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex userDatatable-content mb-0 align-items-center">
                                           <span>{{ $item['user_name'] }}</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex userDatatable-content mb-0 align-items-center">
                                           <span>{{ $item['type_name'] }}</span>
                                        </div>

                                    </td>
                                    <td>

                             
                                        @switch($item['status'])
                                            @case('accept')
                                                 <small class="py-1 px-2 bg-success rounded text-white">
                                             @lang('CRMProjects.common.accept')
                                             </small>
                                         @break
                                            @case('decline')
                                                 <small class="py-1 px-2 bg-danger rounded text-white">
                                             @lang('CRMProjects.common.decline')
                                        </small>
                                                @break
                                            @case('pending')
                                                 <small class="py-1 px-2 bg-hold rounded text-white">
                                            @lang('CRMProjects.common.pending')
                                        </small>
                                                @break
                                            @case('closed')
                                                 <small class="py-1 px-2 bg-warning rounded text-white">
                                           @lang('CRMProjects.common.closed')

                                        </small>
                                                @break
                                            @default
                                                 <small class="py-1 px-2 bg-draft rounded ">
                                          
                                            {{ __(ucwords(str_replace('_', ' ', $item['status']))) }}
                                        </small>
                                        @endswitch
                                       
                                    </td>
                                    <td>
                                        <div class="d-inline-block">
                                            <ul class="mb-0 d-flex flex-wrap gap-10">
                                                <li>
                                                    <a href="{{ route('documents.document.detail', ['id' => $item['id']]) }}"
                                                        data-toggle="tooltip" data-placement="bottom" title="@lang('document_module.view')"
                                                        href="javascript:void(0);">
                                                        <i class="iconsax icon text-osool fs-18"
                                                            icon-name="eye"></i>
                                                    </a>
                                                </li>
                                                <li>
                                                    <a data-placement="bottom" data-toggle="tooltip" title="@lang('CRMProjects.common.edit')" href="#"
                                                        wire:click.prevent="$emit('editDocument', {{ $item['id'] }})">
                                                        <i class="iconsax icon text-new-primary fs-18"
                                                            icon-name="edit-1"></i>
                                                    </a>
                                                </li>
                                                <li>

                                                    <a data-placement="bottom" data-toggle="tooltip" title="@lang('CRMProjects.common.delete')"
                                                        wire:click.prevent="$emit('openModalDelete', {{ $item['id'] }}, '{{ $item['subject'] }}')"
                                                        href="#">
                                                        <i class="iconsax icon text-delete fs-18" icon-name="trash"></i>
                                                    </a>


                                                </li>
                                            </ul>
                                        </div>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="6">
                                        @include('livewire.sales.common.no-data-tr')
                                    </td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
            @if (@$DocumentsPaginated['items'])
                @livewire('common.paginator', ['totalPages' => ceil(@$DocumentsPaginated['total'] / @$DocumentsPaginated['per_page']), 'currentPage' => \Helper::getCurrentPageFromUrl(@$DocumentsPaginated['next_page_url']), 'functionName' => 'fetchDocuments', 'totalRecords' => @$DocumentsPaginated['total']])
            @endif
        </div>
    </div>
</div>



<div class="modal fade delete" id="deleteDocumentConfirmModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-sm modal-dialog-centered" role="document">
        <div class="modal-content radius-xl">
            <div class="modal-body">
                <div class="text-center">
                                  <h1 class="text-loss mb-4"><i class="las la-exclamation-circle fs-60"></i></h1>
                        <h5 class="mb-3">@lang('CRMProjects.common.are_you_sure')</h5>
                                                <p>@lang('CRMProjects.common.you_are_about_to_delete_document') <strong>{{ $input_name }}</strong> !</p>

                </div>
            </div>
            <div class="modal-footer justify-content-between border-0 gap-10">
                <button type="button" class="btn bg-hold-light text-white flex-fill radius-xl"
                        data-dismiss="modal">@lang('No, Keep It')</button>
                <button type="button"  wire:loading.attr="disabled"
                    wire:loading.class="btn-loading" wire:click="confirmDeleteDocument"
                        class="btn bg-loss flex-fill radius-xl text-white">
                        @lang('Yes, Delete It')
                </button>
            </div>
        </div>
    </div>
</div>





 <div wire:ignore.self  class="modal fade new-popup" id="createDocumentModal" tabindex="-1" role="dialog"
        aria-labelledby="createModalLabel" aria-modal="true">

<div class="modal-dialog" role="document">
    <div class="modal-content radius-xl">
        <div class="modal-header">
            <h6 class="modal-title" id="createModalLabel">@lang('general_sentence.Create_Document')</h6>
            <button  wire:ignore type="button" class="close border-0" data-dismiss="modal" aria-label="Close">
                <span aria-hidden="true">&times;</span> 
              </button>
        </div>

        <form wire:submit.prevent="createDocument">

            <div class="modal-body">
                <div class="form-group">
                    <div class="">
                        <label for="lead_stage_name">@lang('CRMProjects.common.subject')<small class="required">*</small></label>
                        <input type="text" class="form-control" id="lead_stage_name" wire:model.defer="subject">
                        @error('subject')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>
                </div>
                <div class="form-group">
                    <div class="">
                        <label for="lead_stage_name">@lang('CRMProjects.common.project')</label>
                        <input type="text" class="form-control" disabled="" value="{{ $projectData['title'] }}">
                    </div>
                </div>
                <div class="form-group">
                    <div class="row">
                        <div class="col-md-6">
                            <label for="pipeline">@lang('CRMProjects.common.type') <small class="required">*</small></label>
                            <select class="form-control" wire:model.defer="type">
                                <option value="" selected>@lang('Select')</option>
                                @if(isset($documentTypes))
                                @foreach ($documentTypes as $type)
                                    <option value="{{ $type['id'] }}">
                                        {{ $type['name'] }}
                                    </option>
                                @endforeach
                                @endif
                            </select>
                            @error('type')
                                <span class="text-danger">{{ $message }}</span>
                            @enderror
                        </div>
                        <div class="col-md-6">
                            <label for="pipeline">@lang('CRMProjects.common.user') <small class="required">*</small></label>
                            <select class="form-control" wire:model.defer="user_id">
                                <option value="" selected>@lang('Select')</option>
                                @if(isset($users))
                                @foreach ($users as $user)
                                    <option value="{{ $user['id'] }}">
                                        {{ $user['name'] }}
                                    </option>
                                @endforeach
                                @endif
                            </select>
                            @error('user_id')
                                <span class="text-danger">{{ $message }}</span>
                            @enderror
                        </div>
                    </div>
                </div>
                <div class="form-group mb-0">
                    <label for="pipeline">@lang('CRMProjects.common.description') <small class="required">*</small></label>
                    <textarea class="form-control textarea" wire:model.defer="description"></textarea>
                    @error('description')
                        <span class="text-danger">{{ $message }}</span>
                    @enderror
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn bg-loss text-white radius-xl"
                    data-dismiss="modal">@lang('CRMProjects.common.close')</button>
                <button type="submit" class="btn bg-new-primary radius-xl">@lang('CRMProjects.common.submit')</button>
            </div>
        </form>
    </div>
</div>
</div>
 <div wire:ignore.self class="modal fade new-popup" id="editDocument" tabindex="-1" role="dialog"
        aria-labelledby="editModalLabel" aria-modal="true">
<div class="modal-dialog" role="document">
    <div class="modal-content radius-xl">
        <div class="modal-header">
            <h6 class="modal-title" id="editModalLabel">@lang('general_sentence.Edit_Document')</h6>
            <button  wire:ignore type="button" class="close border-0" data-dismiss="modal" aria-label="Close">
                <span aria-hidden="true">&times;</span> 
              </button>
        </div>
        <form wire:submit.prevent="createDocument">
            <div class="modal-body">
                <div class="form-group">
                    <div class="">
                        <label for="lead_stage_name">@lang('CRMProjects.common.subject')<small class="required">*</small></label>
                        <input type="text" class="form-control" wire:model.defer="subject">
                        @error('subject')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>
                </div>
                <div class="form-group">
                    <div class="">
                        <label for="lead_stage_name">@lang('CRMProjects.common.project')</label>
                        <input type="text" class="form-control" disabled="" value="{{ $projectData['title'] }}">
                    </div>
                </div>
                <div class="form-group">
                    <div class="row">
                        <div class="col-md-6">
                            <label for="pipeline">@lang('CRMProjects.common.type') <small class="required">*</small></label>
                            <select class="form-control" wire:model.defer="type">
                                <option value="" selected>@lang('Select')</option>
                                @if(isset($documentTypes))
                                @foreach ($documentTypes as $type)
                                    <option value="{{ $type['id'] }}">
                                        {{ $type['name'] }}
                                    </option>
                                @endforeach
                                @endif
                            </select>
                            @error('type')
                                <span class="text-danger">{{ $message }}</span>
                            @enderror
                        </div>
                        <div class="col-md-6">
                            <label for="pipeline">@lang('CRMProjects.common.user') <small class="required">*</small></label>
                            <select class="form-control" wire:model.defer="user_id">
                                <option value="" selected>@lang('Select')</option>
                                @if(isset($users))
                                @foreach ($users as $user)
                                    <option value="{{ $user['id'] }}">
                                        {{ $user['name'] }}
                                    </option>
                                @endforeach
                                @endif
                            </select>
                            @error('user_id')
                                <span class="text-danger">{{ $message }}</span>
                            @enderror
                        </div>
                    </div>
                </div>
                <div class="form-group mb-0">
                    <label for="pipeline">@lang('CRMProjects.common.description') <small class="required">*</small></label>
                    <textarea class="form-control textarea" wire:model.defer="description"></textarea>
                    @error('description')
                        <span class="text-danger">{{ $message }}</span>
                    @enderror
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn bg-loss text-white radius-xl"
                    data-dismiss="modal">@lang('CRMProjects.common.close')</button>
                <button type="submit" class="btn bg-new-primary radius-xl">@lang('CRMProjects.save-changes')</button>
            </div>
        </form>
    </div>
</div>
</div>
<script src="/js/livewire/error-messages.js"></script>
<script src="/js/livewire/manage-loader.js"></script>
<script src="/js/livewire/manage-modals.js"></script>
<script src="/js/livewire/manage-select2.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function () {
        Livewire.on('openCreateDocumentModal', (projectId) => {
            // You can optionally do something with projectId here
            $('#createDocumentModal').modal('show');
        });
    });
    window.addEventListener('show-delete-modal', () => {
        $('#deleteDocumentConfirmModal').modal('show');
    });

    window.addEventListener('close-confirm-modal', () => {
        $('#deleteDocumentConfirmModal').modal('hide');
    });

    window.addEventListener('reloadPage', function () {
    location.reload();  // Reload the page
    });
</script>

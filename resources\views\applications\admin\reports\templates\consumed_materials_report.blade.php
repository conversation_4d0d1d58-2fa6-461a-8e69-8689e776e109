
<table border="1" cellpadding="2" cellspacing="0" style="border-collapse: collapse; text-align: left;">
    <tr>
        <th colspan="1">@lang('user_management_module.user_forms.label.report_period')</th>
        <td colspan="1">@lang('user_management_module.user_forms.label.from') {{ date('d/M/Y', strtotime($start_date)) }} @lang('user_management_module.user_forms.label.to') {{ date('d/M/Y', strtotime($end_date)) }}</td>
    </tr>
</table>

<br>
@foreach($contracts as $contract)


<table border="1" cellpadding="2" cellspacing="0" style="border-collapse: collapse; text-align: left;">
     <tr>
        <th colspan="1">@lang('user_management_module.user_forms.label.contract_name')</th>
        <td colspan="1">{{ $contract->contract_number }}</td>
    </tr>
    <tr>
        <th colspan="1">@lang('user_management_module.user_forms.label.service_provider')</th>
        <td colspan="1">{{ $contract->serviceProvider->name }}</td>
    </tr>
     <tr>
        <th colspan="1">@lang('user_management_module.user_forms.label.number_of_materials_included')</th>
        <td colspan="1">{{count($contract->getUsableItems)}}</td>
    </tr>
</table>

<table border="1" cellpadding="8" cellspacing="0" style="border-collapse: collapse; width: 100%;">
    <thead>
         <tr>
            <th>@lang('user_management_module.user_forms.label.serial')</th>
            <th>@lang('user_management_module.user_forms.label.material_name')</th>
            <th>@lang('user_management_module.user_forms.label.unit')</th>
            <th>@lang('user_management_module.user_forms.label.open_stock')</th>
            <th>@lang('user_management_module.user_forms.label.cost_per_unit')</th>
            @foreach (\Carbon\Carbon::parse($start_date)->daysUntil($end_date) as $date)
                <th>{{ $date->format('d-M') }}</th>
            @endforeach

            <th>@lang('user_management_module.user_forms.label.quantity_used')</th>
            <th>@lang('user_management_module.user_forms.label.total_consumed_cost')</th>
            <th>@lang('user_management_module.user_forms.label.remaining_stock')</th>
            <th>@lang('user_management_module.user_forms.label.remaining_stock_cost')</th>
            <th>@lang('user_management_module.user_forms.label.reorder_level')</th>
            <th>@lang('user_management_module.user_forms.label.reorder_warning')</th>

        </tr>
    </thead>
    <tbody>
        @foreach($contract->getUsableItems as $key=>$this_service)
        @php
            $item_id=$this_service->item_id;
            $total_consumption = 0;
            $breakdown = $contract->getAllWorkerOrders($start_date, $end_date,$item_id);
            $open_stock=$this_service->open_stock;
            $cost=$this_service->price;
            $low_stock=$this_service->low_stock;
        @endphp
        <tr>
            <td>{{$key+1}}</td>
            <td>{{$this_service->getItem()->name}}</td>
            <td>{{$this_service->getItem()->unit}}</td>
            <td>{{$open_stock}}</td>
            <td>{{$cost}}</td>
            @foreach (\Carbon\Carbon::parse($start_date)->daysUntil($end_date) as $date)
                @php
                    $date_key = $date->format('Y-m-d');
                    $date_service_price = $breakdown[$date_key] ?? 0;
                    $total_consumption += $date_service_price;
                @endphp
                <td>{{ $date_service_price }}</td>
            @endforeach
            @php
            $total_consumption_cost=$total_consumption*$cost;
            $remaning_stock=$open_stock-$total_consumption;
            $remaning_stock_cost=$remaning_stock*$cost;
            @endphp
            <td>{{ $total_consumption }}</td>
            <td>{{ $total_consumption_cost }}</td>
            <td>{{ $remaning_stock }}</td>
            <td>{{ $remaning_stock_cost }}</td>
            <td>{{ $low_stock }}</td>
            <td> 
                @if($remaning_stock < $low_stock)
                🔴 Low  
                @else
                ✅ OK
                @endif
            </td>
        </tr>
        @endforeach
    </tbody>
</table>

@endforeach
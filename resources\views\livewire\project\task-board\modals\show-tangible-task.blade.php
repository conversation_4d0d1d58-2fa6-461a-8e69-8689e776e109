<div wire:ignore.self class="modal fade" id="show-tangible-task-modal" tabindex="-1" role="dialog"
    aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel">@lang('CRMProjects.common.view_task')</h5>
                <button wire:ignore type="button" class="close border-0" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <!-- <form> -->

                <div class="row">
                      <div class="form-group col-md-3">
                        <label class="col-form-label"> @lang('Title')</label>
                        <p>{{ $taskDetails['title'] ?? null }}</p>


                    </div>
                    <div class="form-group col-md-3">
                        <label class="col-form-label">  @lang('CRMProjects.work_order_date')</label>
                        <p>{{ $taskDetails['start_date'] ?? null }}</p>


                    </div>
                  

                    <div class="form-group col-md-3">
                        <label class="col-form-label">@lang('Priority')</label>
                        <p>{{ $taskDetails['priority'] ?? null }}</p>

                    </div>

                    <div class="form-group col-md-3">
                        <label class="col-form-label">@lang('CRMProjects.milestone')</label>
                        <p>{{ $taskDetails['milestone'] ?? null }}</p>

                    </div>
               
                    <div class="form-group col-md-12">
                        <label class="col-form-label">@lang('Description')</label>
                        <p>{{ $taskDetails['description'] ?? null }}</p>


                    </div>
                    {{-- <div class="form-group col-md-12">
                            <div class="form-group">
                                <label for="user" class="form-label">@lang('Assign User')</label>
                                <ul class="list-unstyled d-flex flex-wrap">
                                    @foreach ($taskDetails['assign_to'] as $user)
                                        <li class="d-flex align-items-center mr-3 mb-2">
                                            <img src="{{ $user['avatar'] }}" alt="{{ $user['name'] }}" width="32" height="32" class="rounded-circle mr-2">
                                            <span>{{ $user['name'] }}</span>
                                        </li>
                                    @endforeach
                                </ul>
                                




                            </div>
                        </div> --}}

                    <div class="form-group col-md-12">
                        <div class="">


                            <div class="atbd-tab tab-horizontal">
                                <ul class="nav nav-tabs nav-fill col-12" id="pills-tab" role="tablist">
                                    <li class="nav-item">

                                        <a class="nav-link {{ $activeTab === 'comments' ? 'active' : '' }}"
                                            style="    cursor: pointer;"
                                            wire:click="$set('activeTab', 'comments')">@lang('CRMProjects.common.comments')</a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link {{ $activeTab === 'files' ? 'active' : '' }}"
                                            style="    cursor: pointer;"
                                            wire:click="$set('activeTab', 'files')">@lang('CRMProjects.common.attachment')</a>
                                    </li>

                                    <li class="nav-item">
                                        <a class="nav-link {{ $activeTab === 'subtask' ? 'active' : '' }}"
                                            style="    cursor: pointer;" wire:click="$set('activeTab', 'subtask')">@lang('CRMProjects.common.sub_task')</a>
                                    </li>
                             {{--        <li class="nav-item">
                                        <a class="nav-link {{ $activeTab === 'members' ? 'active' : '' }}"
                                            style="    cursor: pointer;"
                                            wire:click="$set('activeTab', 'members')">@lang('CRMProjects.common.members')</a>
                                    </li>
 --}}
                                </ul>
                                <div class="tab-content col-12">
                                    @if ($activeTab === 'comments')
                                        <div class="tab-pane fade show active">
                                            <livewire:project.task-board.partials.taskboard-comments :taskboard-id="$taskDetails['id_task'] ?? null"
                                                :project-id="$itemId" :comments="$taskDetails['comments'] ?? []" :key="'comments-' . $taskDetails['id_task'] ?? 'new'" />
                                        </div>
                                    @elseif ($activeTab === 'files')
                                        <div class="tab-pane fade show active">
                                            <livewire:project.task-board.partials.taskboard-attachment :taskboard-id="$taskDetails['id_task'] ?? null"
                                                :project-id="$itemId" :key="$taskDetails['id_task'] ?? 'new'" />

                                        </div>
                                    @elseif ($activeTab === 'subtask')
                                        <div class="tab-pane fade show active">
                                            <livewire:project.task-board.partials.taskboard-subtasks :taskboard-id="$taskDetails['id_task'] ?? null"
                                                :project-id="$itemId" :key="'subtask-' . $taskDetails['id_task'] ?? 'new'" />
                                        </div>
                               {{--      @elseif ($activeTab === 'members')
                                        <div class="tab-pane fade show active">
                                            <livewire:project.task-board.partials.taskboard-assign :taskboard-id="$taskDetails['id_task'] ?? null"
                                                :project-id="$itemId" :assignedUsers="$taskDetails['users'] ?? []" :key="'members-' . $taskDetails['id_task'] ?? 'new'" />
                                        </div> --}}
                                    @endif
                                </div>

                            </div>
                        </div>
                    </div>
                </div>
                <!-- </form> -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn bg-new-primary text-white"
                    data-dismiss="modal">@lang('CRMProjects.common.close')</button>

            </div>
        </div>
    </div>
    @push('scripts')
  <script>
            document.addEventListener('livewire:load', function () {
        let selectassign_to = $('#assign_to');
        selectassign_to.select2();
       
    });
    </script>
    @endpush
</div>


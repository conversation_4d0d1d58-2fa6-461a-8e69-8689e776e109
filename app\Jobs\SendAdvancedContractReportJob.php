<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Models\WorkOrders;
use App\Models\Contracts;
use Auth;
use Carbon\Carbon;
use DateTime;
use DB;
use Log;
use Mail;
use PDF2;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\Border;
use Illuminate\Http\Request;
use App\Models\User;
use Helper;
use Response;
use Storage;
use ArPHP\I18N\Arabic;
use \App;
use Session;
use PDF;
use App\Http\Helpers\ReportQueryHelper;
use App\Models\Report;
use App\Traits\MediaFilesTrait;
use Mccarlosen\LaravelMpdf\Facades\LaravelMpdf;
use App\Exports\AdvanceContract\MonthlyCostReport;
use Maatwebsite\Excel\Facades\Excel;

class SendAdvancedContractReportJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    use MediaFilesTrait;
    /**
     * Create a new job instance.
     *
     * @return void
     */
    private $session_data;
    private $logged_user;
    private $filename;
    private $report_number;
    private $lang;
    private $root;
    private $project_image_link;
    private $link_start;
    private $report_id;
    private $project_user_id;
    private $report;


    public function __construct($session_data,$logged_user, $report_number, $filename,$lang ,$root, $project_image_link ,$link_start,$report_id, $project_user_id, $report)
    {
        $this->session_data = $session_data;
        $this->logged_user = $logged_user;
        $this->report_number = $report_number;
        $this->filename = $filename;
        $this->lang = $lang;
        $this->root = $root;
        $this->project_image_link = $project_image_link;
        $this->link_start = $link_start;
        $this->report_id = $report_id;
        $this->project_user_id = $project_user_id;
        $this->report = $report;
    }




    public function handle()
    {
        Log::info("Advanced Report Job Started ".$this->report);
        Log::info($this->session_data);

        $request = json_decode(json_encode($this->session_data), FALSE);

        $contract_ids = $request->contract_ids;
        $months = $request->months;
        if (strpos($months, ' to ') !== false) {
            list($startStr, $endStr) = explode(' to ', $months);
        } else {
            $startStr = $endStr = $months;
        }

        $start_date = date('Y-m-d', strtotime("first day of $startStr"));
        $end_date   = date('Y-m-d', strtotime("last day of $endStr"));


        $language   = $request->language ?? '0';
        $lang       = $language == 1 ? 'ar' : 'en';

        app()->setLocale($lang);

        $user               = $this->logged_user;
        $report_number      = $this->report_number;
        $filename           = $this->filename;
        $project_image_link = $this->project_image_link;
        $link_start         = $this->link_start;
        $report_id          = $this->report_id;
        $project_user_id    = $this->project_user_id;



        $contracts=[];

        if($this->report === 'monthly_cost_report'){
            $contracts = Contracts::whereIn('id', $contract_ids)
                ->with(['contractServiceKpis.assetCategory', 'serviceProvider'])
                ->get();
            $html = view('applications.admin.reports.templates.monthly_cost_report', [
                'contracts' => $contracts,
                'start_date'=> $start_date,
                'end_date'=> $end_date,
                'filename'=>$filename,
                'report_id'=>$report_id
            ])->render();
        }elseif($this->report === 'consumed_materials_report'){
            $contracts = Contracts::whereIn('id', $contract_ids)
                ->get();
            $html = view('applications.admin.reports.templates.consumed_materials_report', [
                'contracts' => $contracts,
                'start_date'=> $start_date,
                'end_date'=> $end_date,
                'filename'=>$filename,
                'report_id'=>$report_id
            ])->render(); 
        }elseif($this->report === 'attendance_cost_report'){
            $start = new DateTime($start_date);
            $end = new DateTime($end_date);  
            $interval = $start->diff($end);
            $months_count = ($interval->y * 12) + $interval->m + 1;
            $month_days=30;
            $contracts = Contracts::whereIn('id', $contract_ids)
                    ->with(['contractWorkers.worker']) 
                    ->get();
            $html = view('applications.admin.reports.templates.attendance_cost_report', [
                'contracts' => $contracts,
                'start_date'=> $start_date,
                'end_date'=> $end_date,
                'filename'=>$filename,
                'report_id'=>$report_id,
                'lang'=>$lang,
                'months_count'=>$months_count,
                'month_days'=>$month_days
            ])->render();
        }

        $reader = IOFactory::createReader('Html');
        $spreadsheet = $reader->loadFromString($html);
        if($lang === 'ar'){
            $spreadsheet->getActiveSheet()->setRightToLeft(true);
        }
        $filePath = public_path('reports/'.$filename);
        $writer = IOFactory::createWriter($spreadsheet, 'Xlsx');
        $writer->save($filePath);
        $ociUrlFile = $this->pushMediasToOCI(
          $this->setModelLog('Report', $report_id),
          $filename,
          'reports',
          null,
          true
        );

        if(isset($ociUrlFile)){
          $file_link = $ociUrlFile;
        }else{
          $file_link = asset('reports').DIRECTORY_SEPARATOR.$filename;
        }

        $subject = 'New report has been generated';
        if ($lang=='en')
        {
        $mail_content['dir'] = 'ltr';
        }
        else{
        $mail_content['dir'] = 'rtl';
        }
        $user = $user;
        $to_name  = $user->name;
        $to_email = $user->email;
        $mail_content['name'] = $user->name;
        $mail_content['filename'] = $filename;
        $mail_content['subject_en'] = $subject;
        $mail_content['subject_ar'] = $subject;
        $mail_content['from'] =date('d/m/Y', strtotime($start_date));
        $mail_content['to'] =date('d/m/Y', strtotime($end_date));
        $mail_content['total_wo'] =count($contracts);
        $mail_content['report_file_link'] = $file_link;
        $mail_content['report_no'] = $report_number;
        $sender_email = \Helper::getAdminContactMail();

        if ($lang=='en')
        {
        $mail_content['label_report_no'] ='Report Number' ;
        $mail_content['label_report_heading'] = 'New report has been generated';
        $mail_content['label_starting_date'] = 'From';
        $mail_content['label_ending_date'] =  'To';
        $mail_content['label_total_no_wo'] =  'Number of Contracts';
        $mail_content['label_report_file'] = 'Excel File';
        $mail_content['label_download'] =  'Download';
        $mail_content['label_email_footer_text'] = '*Note: This report is available to be downloaded during the next 24 hours.
        You can generate new reports anytime';
        }
        else{
        $mail_content['label_report_no'] ='رقم التقرير' ;
        $mail_content['label_report_heading'] ='تم إنشاء تقرير جديد ';
        $mail_content['label_starting_date'] =  'من';
        $mail_content['label_ending_date'] =  'إلى';
        $mail_content['label_total_no_wo'] =  'عدد العقود';
        $mail_content['label_report_file'] =  'ملف Excel';
        $mail_content['label_download'] =  'تنزيل';
        $mail_content['label_email_footer_text'] =  '*ملاحظة: هذا التقرير متاح للتنزيل خلال ال24 ساعة القادمة فقط.
        يمكنك انشاء المزيد من التقارير في أي وقت. ';
        }

        $headers = array(
        'Content-Type: application/xlsx'
        );

        try {
        $rpt = ['generated_at'=>date('Y-m-d H:i:s'), 'status'=>'generated', 'oci_link' => $file_link];
        ReportQueryHelper::editReport($report_id, $rpt);
        $generated = true;
        } catch (\Exception $e) {
        $rpt = [ 'status'=>'failed'];
        $generated = false;
        ReportQueryHelper::editReport($report_id, $rpt);
        }
        if($generated){
        $notification_msg = $report_number.' Your generated report is ready ';
        $notification_msg_ar = $report_number.'  التقرير الخاص بك جاهز';
        $notificationData = [
        'user_id' => $user->id,
        'message' => $notification_msg,
        'message_ar' => $notification_msg_ar,
        'section_type' => 'report',
        'notification_sub_type' => 'report',
        'additional_param' => $filename,
        'created_at' => Carbon::now(),
        ];
        ReportQueryHelper::createNotification($notificationData);
        try {
        Mail::send('mail.reportGenerated', ['mail_content' => $mail_content]
        ,function ($message) use ($to_name, $to_email, $subject, $sender_email, $report_number,$report_id,$user,$filename) {
        $message->to($to_email, $to_name)
          ->subject($subject);
        $message->from($sender_email,'Osool Team');
        });
        }
        catch (\Exception $e) {
        }
        }

    }
}

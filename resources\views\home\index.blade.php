<?php
// phpinfo();
?>
<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}"
    dir="{{ (app()->getLocale()=='ar' ? 'rtl' : 'ltr') }}">

<head>
  <meta charset="UTF-8">
  @if(App::getLocale()=='en')
  <title lang="en">Osool </title>
  @else (App::getLocale()=='ar')
  <title lang="ar">أصول </title>
  @endif
  <meta name="description" lang="en" content="Osool is your technical partner in following up on maintenance contracts and managing the operational system. Osool enables facilities and properties management teams to manage maintenance and property operations, link with operational service providers, follow up on maintenance contract work in one platform, and serve the final beneficiary of the property.">
  <meta name="description" lang="ar" content="أصول هو شريكك التقني في متابعة عقود الصيانة وإدارة المنظومة التشغيلية. يمكن أصول فرق إدارة المرافق والأملاك من إدارة عمليات الصيانة والعقار والربط مع مقدمي الخدمات التشغيلية ومتابعة أعمال عقود الصيانة في منصة واحدة وخدمة المستفيد النهائي من العقار.">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta http-equiv="X-UA-Compatible" content="ie=edge">
  <meta property="og:image" content="{{ asset('img/img-share.jpg') }}">
  <meta property="og:image:secure_url" content="{{ asset('img/img-share.jpg') }}">
  <meta property="og:image:type" content="image/jpg">
  <meta property="og:image:width" content="1200">
  <meta property="og:image:height" content="630">
  <link rel="shortcut icon" href="{{ asset('home/image/favicon/favicon-32x32.png') }}" type="image">

  <!-- Bootstrap, fonts & icons  -->
  @if(App::getLocale()=='en')
  <link rel="stylesheet" href="{{ asset('home/css/bootstrap.css') }}">
  @else (App::getLocale()=='ar')
  <link rel="stylesheet" href="{{ asset('home/css/bootstrap-rtl.css') }}">
  @endif
  <link rel="stylesheet" href="{{ asset('home/css/bootstrap.css') }}">
  <link rel="stylesheet" href="{{ asset('home/fonts/icon-font/css/style.css') }}">
  <link rel="stylesheet" href="{{ asset('home/fonts/typography-font/typo.css') }}">
  <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.15.4/css/all.css" integrity="sha384-DyZ88mC6Up2uqS4h/KRgHuoeGwBcD4Ng9SiP4dIRy0EXTlnuz47vAwmeGwVChigm" crossorigin="anonymous">
  <link href="https://fonts.googleapis.com/css2?family=Karla:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
  <link href="https://fonts.googleapis.com/css2?family=Gothic+A1:wght@400;500;700;900&display=swap" rel="stylesheet">
  <link href="https://fonts.googleapis.com/css2?family=Work+Sans:wght@400;500;600;700;800;900&display=swap" rel="stylesheet">
  <link href="https://fonts.googleapis.com/css2?family=Rubik:wght@400;500;600;700;800;900&display=swap" rel="stylesheet">
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700;800;900&display=swap" rel="stylesheet">
  <link href="https://fonts.googleapis.com/css2?family=Nunito:wght@400;600;700;800;900&display=swap" rel="stylesheet">
  <!-- Plugin'stylesheets  -->
  <link rel="stylesheet" href="{{ asset('home/plugins/aos/aos.min.css') }}">
  <link rel="stylesheet" href="{{ asset('home/plugins/fancybox/jquery.fancybox.min.css') }}">
  <link rel="stylesheet" href="{{ asset('home/plugins/nice-select/nice-select.min.css') }}">
  <link rel="stylesheet" href="{{ asset('home/plugins/slick/slick.min.css') }}">
  <!-- Vendor stylesheets  -->
  @if(App::getLocale()=='en')
  <link rel="stylesheet" href="{{ asset('home/css/main.css') }}">
  @else (App::getLocale()=='ar')
  <link rel="stylesheet" href="{{ asset('home/css/main-rtl.css') }}">
  @endif
  <link rel="stylesheet" href="{{ asset('css/scss/new-style.css') }}">

  <link rel="stylesheet" href="//cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css">

  <style>
    .swal-footer {
      text-align: center !important;
    }
    .mt-10
    {
      margin-top:10px !important;
    }
    .required {
    vertical-align: top;
    color: red !important;
}

            .nav-link {
            color: #6c757d;
            text-decoration: none;
            padding: 10px 15px;
            transition: color 0.3s ease;
        }

        .nav-link.active p,
        .nav-link.active .nav-icon,
        .nav-link.active .chevron i {
            color: var(--primary) !important;
        }

        .nav-icon,
        .chevron i {
            color: #6c757d;
            transition: color 0.3s ease;
        }
  </style>
  <!-- Custom stylesheet -->
<!-- Start of  Zendesk Widget script -->
<!-- <script id="ze-snippet" src="https://static.zdassets.com/ekr/snippet.js?key=5dcd3800-b341-400a-b410-ac5c928e42f5"> </script> -->
<!-- End of  Zendesk Widget script -->
</head>
@php
$lang_path=resource_path('lang/'.App::getLocale());
$translations=collect(File::allFiles($lang_path))->flatMap(function ($file)use($lang_path) {
    return [
        ($translation = $file->getBasename('.php')) => trans($translation),
    ];
})->toJson();
@endphp
<script type="text/javascript">
  window.baseUrl="{{URL::to('/')}}";
  window.current_locale="{{App::getLocale()}}";
  window.translations = {!! $translations !!};
  //console.log(window.current_locale) ;
</script>
<body data-theme-mode-panel-active data-theme="light" class="ltr">
  <div class="site-wrapper overflow-hidden position-relative">
    <!-- Site Header -->
    <!-- Preloader -->
    <!-- <div id="loading">
    <div class="preloader">

   </div>
   </div>    -->
    <!--Site Header Area -->
    <header class="site-header site-header--menu-right landing-1-menu site-header--absolute site-header--sticky bg-white shadow-sm">
      <div class="container-fluid">
        <nav class="navbar site-navbar">
          <!-- Brand Logo-->
          <div class="brand-logo">
            <a href="{{ url('/') }}">
              <!-- light version logo (logo must be black)-->
              <img src="{{ asset('home/image/home/<USER>') }}" alt="" class="light-version-logo">
              <!-- Dark version logo (logo must be White)-->
              <img src="{{ asset('home/image/home/<USER>') }}" alt="" class="dark-version-logo">
            </a>
          </div>
          <div class="menu-block-wrapper">
            <div class="menu-overlay"></div>
            <nav class="menu-block" id="append-menu-header">
              <div class="mobile-menu-head">
                <img src="{{ asset('home/image/home/<USER>') }}" alt="" class="">
                <div class="go-back">
                  <i class="fa fa-angle-left"></i>
                </div>
                <div class="current-menu-title"></div>
                <div class="mobile-menu-close">&times;</div>
              </div>
              <ul class="site-menu-main">
                <li class="nav-item">
                  <a href="{{ url('/') }}#menu1" class="nav-link-item"> {{__('landing_page.menu.wahts_osool')}}</a>
                </li>
                <li class="nav-item">
                  <a href="{{ url('/') }}#menu2" class="nav-link-item">{{__('landing_page.menu.osool_advantage')}} </a>
                </li>
                <li class="nav-item">
                  <a href="{{ url('/') }}#menu3" class="nav-link-item"> {{__('landing_page.menu.beneficiaries_of_osool')}}</a>
                </li>
                <li class="nav-item">
                  <a href="{{ url('/') }}#menu4" class="nav-link-item"> {{__('landing_page.menu.contact_us')}}</a>
                </li>

                @if(\Illuminate\Support\Facades\Auth::check())
                      @if(\Illuminate\Support\Facades\Auth::user()->isServiceProviderAdmin() && \Illuminate\Support\Facades\Auth::user()->status != 1)
                          @include('layouts.partials.psp-head-menu')
                      @else
                          <li class="d-flex align-items-center">
                  <span class="nav-link-item no-hover">
                   <a class="btn bg-db text-white focus-reset lan-btn" href="{{ route('admin.dashboard') }}">
            {{__('dashboard.blank_dashboard.dashboard')}}
                  </a>
                  </span>
                          </li>
                      @endif
                @else
                      <li class="nav-item mt-sm-0 mt-3 mt-sm-0">
                  <span class="nav-link-item no-hover pr-0">
                  <a href="{{ route('psp-registration.index') }}"
                     class="btn btn-border-lb btn-hover-slide position-relative overflow-hidden"><span
                          class="rounded d-block position-relative">{{ __('psp_registration.common.nav_menu.register_as_vendor') }}</span></a>
                </span>
                      </li>
                      <li class="nav-item mt-sm-0 mt-3 mt-sm-0">
                  <span class="nav-link-item no-hover pr-0">
                  <a href="javascript:void(0);"
                     class="btn btn-border-lb btn-hover-slide position-relative overflow-hidden" data-bs-toggle="modal"
                     data-bs-target="#osool-popup"><span
                          class="rounded d-block position-relative"> {{__('landing_page.menu.get_started')}}</span></a>
                </span>
                      </li>
                      <li class="d-flex align-items-center">
                  <span class="nav-link-item no-hover">
                   <a class="btn bg-db text-white focus-reset lan-btn" href="{{ url('login') }}">
            {{__('landing_page.menu.login')}}
                  </a>
                  </span>
                      </li>
                @endif

                <li class="nav-item">
                    @if (App::getLocale()=='en')
                    <a href="{{route('changeLanguage',"ar")}}" class="nav-link-item lang-btn"><span class="rounded d-block">{{__('landing_page.menu.en_ar')}}</span></a>
                    @elseif (App::getLocale()=='ar')
                    <a href="{{route('changeLanguage',"en")}}" class="nav-link-item lang-btn"><span class="rounded d-block">{{__('landing_page.menu.en_ar')}}</span></a>
                    @endif

                </li>
              </ul>
            </nav>
          </div>

          <!-- mobile menu trigger -->
          <div class="mobile-menu-trigger">
            <span></span>
          </div>
          <!--/.Mobile Menu Hamburger Ends-->
        </nav>
      </div>
    </header>
    <!-- navbar- -->
    <!-- Hero Area -->
    <div class="hero-area-l1  position-relative background-property" id="menu1" style="background: url({{ asset('home/image/landing-1/hero-bg1.png') }});">
      <div class="container">
        <div class="row justify-content-center align-items-center">
          <div class="col-xxl-5 col-xl-6 col-lg-6 col-md-10 order-lg-1 order-1 text-center" data-aos="fade-right" data-aos-delay="500" data-aos-duration="1000">
            <div class="content">
              <h2>{{__('landing_page.menu.your_first_stop_for_asset_mng')}}</h2>
              <p>{{__('landing_page.menu.whether_you_are_an')}}</p>
              <div class="l1-create-acc-btn">
                <a href="#" class="btn btn-style-02"  data-bs-toggle="modal" data-bs-target="#osool-popup">{{__('landing_page.menu.request_free_demo')}}<i class="fas fa-angle-right"></i></a>
              </div>
            </div>
          </div>
          <div class="col-xxl-7 col-xl-5 col-lg-6 col-md-8 order-lg-1 order-0" data-aos="fade-left" data-aos-delay="700" data-aos-duration="1000">
            <div class="hero-image-group-l1">
              <div class="image-1">
                    @if (App::getLocale()=='en')
                    <img class="w-100" src="{{ asset('home/image/home/<USER>') }}" alt="image">
                    @elseif (App::getLocale()=='ar')
                    <img class="w-100" src="{{ asset('home/image/home/<USER>') }}" alt="image">
                    @endif
              </div>

            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- Features Area -->
    <div class="feature-area-l1" id="menu2">
      <div class="container pb-5 b-b-dotted no-b-mobile">
        <div class="row feature-l1-items justify-content-center">
          <h3 class="text-center text-grey fw-bold mb-5">{{__('landing_page.menu.manage_the_operational')}} </h3>
          <div class="col-lg-4 col-md-6 col-sm-11 col-xs-12" data-aos="fade-up" data-aos-duration="800" data-aos-once="true">
            <div class="content h-100 text-center">
              <img src="{{ asset('home/image/home/<USER>') }}" alt="image">
              <h5>{{__('landing_page.menu.integrated_dashboard')}}</h5>
              <!-- <p>With lots of unique blocks, you can easily build a page without coding. Build your next website
                within few minutes</p> -->
            </div>
          </div>
          <div class="col-lg-4 col-md-6 col-sm-11 col-xs-12" data-aos="fade-up" data-aos-duration="800" data-aos-once="true">
            <div class="content h-100 text-center">
              <img src="{{ asset('home/image/home/<USER>') }}" alt="image">
              <h5>  {{__('landing_page.menu.all_date_in')}}  </h5>
              <!-- <p>With lots of unique blocks, you can easily build a page without coding. Build your next website
                within few minutes</p> -->
            </div>
          </div>
          <div class="col-lg-4 col-md-6 col-sm-11 col-xs-12" data-aos="fade-up" data-aos-duration="800" data-aos-once="true">
            <div class="content h-100 text-center">
              <img src="{{ asset('home/image/home/<USER>') }}" alt="image">
              <h5> {{__('landing_page.menu.facilities_management')}} </h5>
              <!-- <p>With lots of unique blocks, you can easily build a page without coding. Build your next website
                within few minutes</p> -->
            </div>
          </div>
        </div>
      </div>
    </div>




    <div class="home-tabs1 section-padding-100" id="menu3">
      <div class="container">
        <div class="tabs-heading">
        <h2 class="mb-4"> {{__('landing_page.menu.ossol_provides_multiple')}} </h2>
        <p class="fs-4 mb-5"> {{__('landing_page.menu.osool_is_your')}} </p>
        </div>
        <ul class="nav nav-tabs mb-5 flex-lg-nowrap" id="myTab" role="tablist">

  <li class="nav-item flex-fill" role="presentation">
    <button class="nav-link active" id="contact-tab" data-bs-toggle="tab" data-bs-target="#tab-1" type="button" role="tab" aria-controls="tab-1" aria-selected="false">
      <h5 class="min-h-48"> {{__('landing_page.menu.owners_association')}} </h5>
      <p class="fs-45"> {{__('landing_page.menu.communication_with_your')}}   </p>
    </button>
  </li>
  <li class="nav-item flex-fill" role="presentation">
    <button class="nav-link" id="profile-tab" data-bs-toggle="tab" data-bs-target="#tab-2" type="button" role="tab" aria-controls="tab-2" aria-selected="false">
      <h5 class="min-h-48">  {{__('landing_page.menu.operation_and_maintenance')}}  </h5>
      <p class="fs-45">  {{__('landing_page.menu.floow_up_daily')}}   </p>
    </button>
  </li>
  <li class="nav-item flex-fill" role="presentation">
    <button class="nav-link" id="home-tab" data-bs-toggle="tab" data-bs-target="#tab-3" type="button" role="tab" aria-controls="tab-1" aria-selected="true">
      <h5 class="min-h-48">  {{__('landing_page.menu.facilites_manager')}} </h5>
      <p class="fs-45"> {{__('landing_page.menu.manage_facilities')}} </p>
    </button>
  </li>
  <li class="nav-item flex-fill" role="presentation">
    <button class="nav-link" id="contact-tab" data-bs-toggle="tab" data-bs-target="#tab-4" type="button" role="tab" aria-controls="tab-3" aria-selected="false">
      <h5 class="min-h-48">  {{__('landing_page.menu.real_estate')}}  </h5>
      <p class="fs-45"> {{__('landing_page.menu.follow_the_efficiency')}} </p>
    </button>
  </li>
</ul>
<div class="tab-content" id="myTabContent">


  <div class="tab-pane fade show active radius-20 p-5" id="tab-1" role="tabpanel" aria-labelledby="contact-tab">

    <div class="row">
    <div class="col-md-6">
      <h2 class="mb-5"> {{__('landing_page.menu.owners_association')}} </h2>
      <p class="fs-3 mb-5">
        {{__('landing_page.menu.osool_provides_owners')}}
      </p>
      <a href="{{ route('home.learnmore') }}" class="c-l-b fs-4 plane-arrow-btn radius-50 px-5 py-2 border b-c-lb overflow-hidden mb-4"><span> {{__('landing_page.menu.know_more')}} <span class="px-10">&nbsp;</span><img src="{{ asset('home/image/home/<USER>') }}" alt="" width="30" /></span></a>
    </div>
    <div class="col-md-6">
      <img src="{{ asset('home/image/home/<USER>') }}" class="w-100">
    </div>
  </div>

  </div>


  <div class="tab-pane fade radius-20 p-5" id="tab-2" role="tabpanel" aria-labelledby="home-tab">
    <div class="row">
    <div class="col-md-6">
      <h2 class="mb-5"> {{__('landing_page.menu.operation_and_maintenance')}} </h2>
      <p class="fs-3 mb-5">
        {{__('landing_page.menu.osool_helps_operation')}}
      </p>

    </div>
    <div class="col-md-6">
      <img src="{{ asset('home/image/home/<USER>') }}" class="w-100">
    </div>
  </div>

  </div>
  <div class="tab-pane fade radius-20 p-5" id="tab-3" role="tabpanel" aria-labelledby="profile-tab">
    <div class="row">
    <div class="col-md-6">
      <h2 class="mb-5"> {{__('landing_page.menu.facilites_manager')}} </h2>
      <p class="fs-3 mb-5">
      {{__('landing_page.menu.assign_corrective')}}
      </p>

    </div>
    <div class="col-md-6">
      <img src="{{ asset('home/image/home/<USER>') }}" class="w-100">
    </div>
  </div>
  </div>
  <div class="tab-pane fade radius-20 p-5" id="tab-4" role="tabpanel" aria-labelledby="contact-tab">

    <div class="row">
    <div class="col-md-6">
      <h2 class="mb-5"> {{__('landing_page.menu.real_estate')}} </h2>
      <p class="fs-3 mb-5">
     {{__('landing_page.menu.osool_provide')}}
      </p>

    </div>
    <div class="col-md-6">
      <img src="{{ asset('home/image/home/<USER>') }}" class="w-100">
    </div>
  </div>

  </div>
</div>
      </div>
    </div>
    <!-- Contact form Area -->
    <div class="contact-form-area-l1 bg-mirage position-relative" id="menu4">
      <div class="contact-area-l-image-group">
        <div class="image-1 d-none d-md-block" data-aos="fade-up" data-aos-delay="500" data-aos-duration="1000">
          <img class="w-100" src="{{ asset('home/image/landing-1/h1-cta-blur-shape-1.png') }}" alt="image">
        </div>
        <div class="image-2 d-none d-md-block" data-aos="fade-right" data-aos-delay="800" data-aos-duration="1000">
          <img class="w-100 spin" src="{{ asset('home/image/landing-1/h1-cta-blur-shape-2.png') }}" alt="image">
        </div>

      </div>
      <div class="container position-relative">
        <div class="row justify-content-center">
          <div class="col-lg-6">
            <div class="section__heading text-center">
              <h2> {{__('landing_page.menu.contact_us')}} </h2>
            </div>
          </div>
        </div>
        <div class="row justify-content-center">
          <div class="col-xxl-8 col-lg-9 col-md-12 col-sm-10" data-aos="fade-up" data-aos-delay="500" data-aos-duration="1000">
            <div class="contact-form-l1">
             <form id="contact_form_2" action="{{ route('contact.save') }}" method="post">
                @csrf
                <input type="hidden" id="form_name2" value="contact_form" />
                <div class="row">

                  <div class="col-md-12">
                    <div class="form-group">
                      <label for="name">
                         {{__('landing_page.menu.name')}}
                      </label>
                      <input type="text" name="name" placeholder="{{__('landing_page.menu.enter_your_name')}}" id="name" class="form-control ">
                    </div>
                  </div>
                  <div class="col-md-6">
                    <div class="form-group">
                      <label for="email">
                        {{__('landing_page.menu.email')}}
                      </label>
                      <input type="text" name="email" placeholder="{{__('landing_page.menu.enter_your_email')}}" id="email2" class="form-control ">
                    </div>
                  </div>
                  <div class="col-md-6">
                    <div class="form-group">
                      <label for="email">
                        {{__('landing_page.menu.phone_number')}}
                      </label>
                      <input type="text" name="phone" placeholder="{{__('landing_page.menu.enter_your_phone')}}" id="phone2" class="form-control ">
                    </div>
                  </div>


                  <div class="col-md-12">
                    <div class="form-group">
                      <label for="name">
                         {{__('landing_page.menu.manage_description')}}
                      </label>
                      <textarea name="message" id="message" class="form-control form-message" placeholder="{{__('landing_page.menu.enter_your_message_description')}} "></textarea>
                    </div>
                  </div>
                  <div class="col-md-4 mt-3">
                    <div class="form-group border mt-2 p-1 radius-15">
                      <label for="captcha" class="captcha d-flex align-items-center">
                      <button type="button" class="px-3 py-2 rounded-circle btn-danger" class="reload" id="reload">
                                &#x21bb;
                            </button> &nbsp;&nbsp;&nbsp;
                      <span>{{ $data['math']}} </span> <b class="ms-4"> = </b>

                      </label>
                    </div>
                  </div>
                  <div class="col-md-6 mt-3">
                    <div class="form-group mt-10">
                          <input id="captcha" type="text" class="form-control" placeholder="{{__('landing_page.menu.enter_sum_of_numbers')}}" name="captcha">
                          @error('captcha')
                          <div class="alert alert-danger mt-1 mb-1">{{ $message }}</div>
                          @enderror
                    </div>
                  </div>

                  <!-- <div class="col-md-6">
                    <div class="form-group">
                      <label for="name">
                        <div class="captcha">
                            <span>{{ $data['math']}}</span>
                            <button type="button" class="btn btn-danger" class="reload" id="reload">
                                &#x21bb;
                            </button>
                        </div>
                      </label>
                          <input id="captcha" type="text" class="form-control" placeholder="أدخل رمز التحقق" name="captcha">
                          @error('captcha')
                          <div class="alert alert-danger mt-1 mb-1">{{ $message }}</div>
                          @enderror
                      </div>
                  </div> -->

                </div>
                <div class="w-100 mt-5">
                  <div class="col-md-6 m-auto mt-5">
                <button type="submit" class="w-100 btn get-free-demo-btn btn-style-02"> {{__('landing_page.menu.submit')}}</button>
              </div>
              </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- Footer Area -->
    <footer class="footer-area position-relative">
      <div class="curve-image-l1">
        <img src="{{ asset('home/image/landing-1/white-shape.svg') }}" alt="image" class="w-100">
      </div>
      <div class="container">
        <div class="row justify-content-center footer-l1-area-items">
          <div class="col-xl-12 text-center">
            <ul class="list-unstyled list-inline">
              <li class="list-inline-item"><a href="#" class="px-2 fs-5 tex-primary"><img src="{{ asset('home/image/home/<USER>') }}"></a></li>
              <!-- <li class="list-inline-item"><a href="#" class="px-2 fs-5 tex-primary">ﺟﻤﻴﻊ اﻟﺤﻘﻮق ﻣﺤﻔﻮﻇﺔ ﻟﺄﺻﻮل  <?= date('Y'); ?> ® </a></li> -->
            </ul>
            <ul class="list-unstyled list-inline mb-5">
              <li class="list-inline-item"><a href="#menu1" class="px-2 fs-5 tex-primary">{{__('landing_page.menu.wahts_osool')}}</a></li>
              <li class="list-inline-item"><a href="#menu2" class="px-2 fs-5 tex-primary">{{__('landing_page.menu.osool_advantage')}}</a></li>
              <li class="list-inline-item"><a href="#menu3" class="px-2 fs-5 tex-primary">{{__('landing_page.menu.beneficiaries_of_osool')}}</a></li>
              <li class="list-inline-item"><a href="#menu4" class="px-2 fs-5 tex-primary">{{__('landing_page.menu.contact_us')}}</a></li>
            </ul>
          </div>
          <div class="col-xl-12 text-center">
            <div class="d-sm-flex justify-content-sm-between">
              <a class="fs-5 text-primary d-block">{{__('landing_page.learnmore.copyright')}} <?= date('Y'); ?> ® </a>
              <a href="{{ route('home.privacypolicy',App::getLocale()) }}" class="fs-5 text-primary d-block">
                {{__('landing_page.learnmore.privacy_policy')}}
              </a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  </div>

<!-- Modal -->
<div class="modal fade" id="osool-popup" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-md">
    <div class="modal-content radius-20">
      <div class="success-div w-100 h-100 p-5 h-100 w-100 position-absolute bg-white radius-20 text-center">
        <div class="d-flex h-100 align-items-center">
          <div class="w-100">
        <div class="col-md-6 m-auto">
         <img src="{{ asset('home/image/home/<USER>') }}" class="m-auto mb-5">
       </div>
       <h2 class="mb-3">{{__('landing_page.popup.data_sent_success')}}</h2>
       <p class="">{{__('landing_page.popup.we_wil_reach_soon')}}</p>
       <div class="m-auto mt-5 mb-4">
          <a class="w-100 btn bg-lb radius-10 py-4 text-white close_final_modal"   data-bs-dismiss="modal" aria-label="Close">{{__('landing_page.popup.okay')}}</a>
        </div>
      </div>
      </div>
      </div>
      <div class="modal-header px-4">
        <div>
        <h5 class="modal-title c-l-b" id="exampleModalLabel">{{__('landing_page.menu.request_free_demo')}}</h5>
        <p class="mb-0">{{__('landing_page.popup.to_get_a_free_demo')}}</p>
        </div>
        <button type="button" class="btn-close radius-10" data-bs-dismiss="modal" aria-label="Close"><i class="fas fa-times"></i></button>
      </div>
      <div class="modal-body site-form px-4">
        <form id="contact_form" action="{{ route('contact.save') }}" method="post">
                @csrf
                <input type="hidden" id="form_name" value="top_form" />
                <div class="row">
                  <div class="col-md-12">
                    <div class="form-group mb-3">
                      <label for="name" class="mb-1">
                         {{__('landing_page.menu.name')}}<small class="required">*</small>
                      </label>
                    <input type="text" name="name" placeholder="{{__('landing_page.menu.enter_your_name')}}" id="full_name" class="form-control radius-10">
                    </div>
                    <div class="form-group mb-3">
                      <label for="email" class="mb-1">
                        {{__('landing_page.popup.company_entity')}}
                      </label>
                       <input type="text" name="dept" placeholder="{{__('landing_page.popup.enter_name_of_company')}}" id="dept" class="form-control  radius-10">
                    </div>
                    <div class="form-group mb-3">
                      <label for="email" class="mb-1">
                        {{__('landing_page.popup.filed_of_company')}}
                      </label>
                      <input type="text" name="entry_area" placeholder="{{__('landing_page.popup.enter_company_field_of_work')}}" id="entry_area" class="form-control  radius-10">
                    </div>
                    <div class="form-group mb-3">
                      <label for="email" class="mb-1">
                        {{__('landing_page.menu.phone_number')}}<small class="required">*</small>
                      </label>
                      <input type="text" name="phone" placeholder="05xxxxxxxx" id="phone" class="form-control  radius-10">
                    </div>
                    <div class="form-group">
                      <label for="name" class="mb-1">
                         {{__('landing_page.menu.email')}}<small class="required">*</small>
                      </label>
                      <input type="text" name="email" placeholder="<EMAIL>" id="email" class="form-control  radius-10">
                    </div>
                  </div>

                  <div class="col-md-12 mt-3">
                    <div class="d-flex">
                    <div class="form-group mt-2 me-2 radius-15">
                      <label for="captcha" class="captcha2 d-flex align-items-center form-control radius-10">
                      <button type="button" class="px-2 py-2 rounded-circle reload" id="reload2">
                                &#x21bb;
                            </button> &nbsp;&nbsp;&nbsp;
                      <span>{{ $data['math']}} </span> <b class="ms-2"> = </b>

                      </label>
                    </div>

                    <div class="form-group mt-2 flex-fill">
                          <input id="captcha2" type="text" class="form-control radius-10" placeholder="{{__('landing_page.menu.enter_sum_of_numbers')}}" name="captcha">
                          @error('captcha')
                          <div class="alert alert-danger mt-1 mb-1">{{ $message }}</div>
                          @enderror
                    </div>
                  </div>
                </div>
                </div>
                <div class="w-100">
                 <div class="m-auto mt-5 mb-4">
                   <!-- data-bs-toggle="modal" data-bs-target="#osool-popup" -->
                   <button type="submit" class="w-100 btn bg-lb radius-10 py-4 text-white btn-pop"> {{__('landing_page.menu.submit')}}
                   </button>
                 </div>
              </div>
        </form>
      </div>
    </div>
  </div>
</div>

<!-- SP ticket Submit-->
<span class="help-ticket cursor-pointer rounded-circle d-flex justify-content-center align-items-center"  data-bs-toggle="modal" data-bs-target="#support-popup"><i class="fas fa-question-circle"></i></span>


<!-- Modal -->
<div class="modal fade" id="support-popup" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header direction-ltr">
        <h1 class="modal-title fs-5" id="exampleModalLabel">Submit Ticket</h1>
        <button type="button" class="btn-close d-flex align-items-center justify-content-center" data-bs-dismiss="modal" aria-label="Close"><i class="fas fa-times"></i></button>
      </div>
      <div class="modal-body">
         <script charset="utf-8" type="text/javascript" src="//js-eu1.hsforms.net/forms/embed/v2.js"></script>
          <script>
          hbspt.forms.create({
          region: "eu1",
          portalId: "144520605",
          formId: "18a015d9-9ce6-4b53-b322-57234d986ce3"
          });
          </script>
      </div>
    </div>
  </div>
</div>
<!-- SP ticket Submit-->




  <!-- Vendor Scripts -->
  <script src="{{ asset('home/js/vendor.min.js') }}"></script>
  <!-- Plugin's Scripts -->
  <script src="{{ asset('home/plugins/fancybox/jquery.fancybox.min.js') }}"></script>
  <script src="{{ asset('home/plugins/nice-select/jquery.nice-select.min.js') }}"></script>
  <script src="{{ asset('home/plugins/aos/aos.min.js') }}"></script>
  <script src="{{ asset('home/plugins/slick/slick.min.js') }}"></script>
  <script src="https://porjoton.netlify.app/mekanic/js/waypoints.min.js"></script>
  <script src="{{ asset('home/plugins/counter-up/jquery.counterup.min.js') }}"></script>
  <script src="{{ asset('home/plugins/isotope/isotope.pkgd.min.js') }}"></script>
  <script src="{{ asset('home/plugins/isotope/packery.pkgd.min.js') }}"></script>
  <script src="{{ asset('home/plugins/isotope/image.loaded.js') }}"></script>
  <script src="{{ asset('home/plugins/menu/menu.js') }}"></script>
  <!-- Activation Script -->

<script src="{{asset('js/jquery.validate.js')}}"></script>
<script src="https://unpkg.com/sweetalert/dist/sweetalert.min.js"></script>
<script src="//cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>
<script src="{{asset('js/admin/jquery-validate-method-add.js')}}"></script>

<script src="https://unpkg.com/sweetalert/dist/sweetalert.min.js"></script>

<script src="{{asset('js/validator_additional.js')}}"></script>
  <script src="{{ asset('home/js/custom.js') }}"></script>
  <script type="text/javascript">
    $(document).ready(function(){
      {{ Session::forget('token'); }}
      $('.btn-pop').click(function(){
        //$('.success-div').slideToggle();
      });
    });
    $(".menu-overlay").click(function(){
      $(".mobile-menu-trigger").removeClass("active");
    });
    //alert('test')
$("#contact_form").validate({
    ignore: "input[type=hidden]",
    rules: {
        name: {
            required: true,
            minlength: 2,
            maxlength: 150,
            //lettersandspace: true,
        },

        email: {
            required: true,
            email: true,
            maxlength: 50,

        },
        phone: {
            required: true,
            //number: true,
            minlength: 9,
            maxlength: 15,
        },
        captcha: {
            required: true,
        }

    },
    messages: {

        name: {
            required: translations.general_sentence.validation.This_field_is_required,
            minlength: translations.general_sentence.validation.Please_enter_at_least_2_characters,
            maxlength: translations.general_sentence.validation.Please_enter_no_more_than_50_characters,
            //lettersandspace: true,
        },

        email: {
            required: translations.general_sentence.validation.This_field_is_required,
            email: translations.general_sentence.validation.Email_format_not_Valid,
            maxlength: translations.general_sentence.validation.Please_enter_no_more_than_50_characters,

        },
        phone: {
            required: translations.general_sentence.validation.This_field_is_required,
            //number:translations.general_sentence.validation.Please_enter_a_valid_number,
            minlength: translations.general_sentence.validation.Please_enter_at_least_9_characters,
            maxlength: translations.general_sentence.validation.Please_enter_no_more_than_15_characters,

        },
        captcha: {
            required: translations.general_sentence.validation.This_field_is_required,
        },
    },
    errorPlacement: function (error, element) {
        error.addClass("invalid-feedback");
        if (element.attr("name") == "city_id") {
            error.appendTo($("#city_option_erro"));
        } else if (element.attr("name") == "country_id") {
            error.appendTo($("#country_id_error"));
        } else {
            error.insertAfter(element);
        }
    },
    highlight: function (element, errorClass, validClass) {
        $(element).addClass("is-invalid");
    },
    unhighlight: function (element, errorClass, validClass) {
        $(element).removeClass("is-invalid");
        var elem = $(element);
        if (elem.hasClass("select2-offscreen")) {
            $("#s2id_" + elem.attr("id") + " ul").removeClass(errorClass);
        } else {
            elem.removeClass(errorClass);
        }
    },
    submitHandler: function (form) {
        //$.LoadingOverlay("show");
        //alert('ad');
        //form.submit();
        var _token = $("input[name='_token']").val();
        var name = $("#full_name").val();
        var dept = $("#dept").val();
        var email = $("#email").val();
        var phone = $("#phone").val();
        var entry_area = $("#entry_area").val();
        var form_name = $("#form_name").val();
        var captcha = $("#captcha2").val();


        $.ajax({
              url: "{{ route('contact.save') }}",
              type:'POST',
              dataType:"json",
              data: {_token:_token,
                name:name,
                dept:dept,
                email:email,
                phone:phone,
                entry_area:entry_area,
                form_name:form_name,
                captcha:captcha
              },
              success: function(data)
              {
                  if(data.status)
                  {

                  //location.reload();
                  //toastr.success(translations.general_sentence.validation.Success,data.success, {timeOut: 5000});
                    $('.success-div').slideToggle();

                    $("#full_name,#dept,#email,#phone,#entry_area,#captcha2").val('');
                    $.ajax({
                        type: 'GET',
                        url: "{{ route('home.reloadcaptcha') }}",
                        success: function (data) {
                            $(".captcha2 span").html(data.captcha);
                        }
                    });

                    console.log('asasa')
                  }
                  else
                  {
                      printErrorMsgswl2(data.error);
                      return false;
                  }
              }
          });
    },
});

$('#reload').click(function () {
        $.ajax({
            type: 'GET',
            url: "{{ route('home.reloadcaptcha') }}",
            success: function (data) {
                $(".captcha span").html(data.captcha);
            }
        });
    });

$('#reload2').click(function () {
        $.ajax({
            type: 'GET',
            url: "{{ route('home.reloadcaptcha') }}",
            success: function (data) {
                $(".captcha2 span").html(data.captcha);
            }
        });
    });

$("#contact_form_2").validate({
    ignore: "input[type=hidden]",
    rules: {
        name: {
            required: true,
            minlength: 2,
            maxlength: 150,
            //lettersandspace: true,
        },

        email: {
            required: true,
            email: true,
            maxlength: 50,

        },
        phone: {
            required: true,
            //number: true,
            minlength: 9,
            maxlength: 15,
        },

        captcha: {
            required: true,
        }

    },
    messages: {

        name: {
            required: translations.general_sentence.validation.This_field_is_required,
            minlength: translations.general_sentence.validation.Please_enter_at_least_2_characters,
            maxlength: translations.general_sentence.validation.Please_enter_no_more_than_50_characters,
            //lettersandspace: true,
        },

        email: {
            required: translations.general_sentence.validation.This_field_is_required,
            email: translations.general_sentence.validation.Email_format_not_Valid,
            maxlength: translations.general_sentence.validation.Please_enter_no_more_than_50_characters,

        },
        phone: {
            required: translations.general_sentence.validation.This_field_is_required,
            //number:translations.general_sentence.validation.Please_enter_a_valid_number,
            minlength: translations.general_sentence.validation.Please_enter_at_least_9_characters,
            maxlength: translations.general_sentence.validation.Please_enter_no_more_than_15_characters,

        },

        captcha: {
          required: translations.general_sentence.validation.This_field_is_required,
        }
    },
    errorPlacement: function (error, element) {
        error.addClass("invalid-feedback");
        if (element.attr("name") == "city_id") {
            error.appendTo($("#city_option_erro"));
        } else if (element.attr("name") == "country_id") {
            error.appendTo($("#country_id_error"));
        } else {
            error.insertAfter(element);
        }
    },
    highlight: function (element, errorClass, validClass) {
        $(element).addClass("is-invalid");
    },
    unhighlight: function (element, errorClass, validClass) {
        $(element).removeClass("is-invalid");
        var elem = $(element);
        if (elem.hasClass("select2-offscreen")) {
            $("#s2id_" + elem.attr("id") + " ul").removeClass(errorClass);
        } else {
            elem.removeClass(errorClass);
        }
    },
    submitHandler: function (form) {
        //$.LoadingOverlay("show");
        //alert('ad');
        //form.submit();
        var _token = $("input[name='_token']").val();
        var name = $("#name").val();
        //var dept = $("#dept").val();
        var email = $("#email2").val();
        var phone = $("#phone2").val();
        var message = $("#message").val();
        var form_name = $("#form_name2").val();
        var captcha = $("#captcha").val();


        $.ajax({
              url: "{{ route('contact.save') }}",
              type:'POST',
              dataType:"json",
              data: {_token:_token,
                name:name,
                message:message,
                email:email,
                phone:phone,
                form_name:form_name,
                captcha:captcha
              },
              success: function(data)
              {
                  if(data.status)
                  {

                  //location.reload();
                  //toastr.success(translations.general_sentence.validation.Success,data.success, {timeOut: 5000});
                  $("#name,#phone2,#email2,#message,#captcha").val('');
                  $.ajax({
                      type: 'GET',
                      url: "{{ route('home.reloadcaptcha') }}",
                      success: function (data) {
                          $(".captcha span").html(data.captcha);
                      }
                  });
                  swal({
                    title: "ﺗﻢ إرﺳﺎل ﺑﻴﺎﻧﺎﺗﻚ ﺑﻨﺠﺎح!",
                    text: "ﺳﻨﺘﻮاﺻﻞ ﻣﻌﻚ ﻗﺮﻳﺒﺎ",
                    icon: "success",
                    button: "حسنا",
                  });
                  }
                  else
                  {
                      printErrorMsgswl(data.error);
                      return false;
                  }
              },
              error: function (jqXHR, textStatus, errorThrown) {
                            console.log('jqXHR: ' + jqXHR);
                            console.log('ERRORS: ' + textStatus);
                            console.log('errorThrown: ' + errorThrown);
                    },
          });
    },
});

$('.close_final_modal').click(function() {
    location.reload();
});

function printErrorMsgswl(msg) {
  console.log(msg);
    for (var i in msg) {
      if(msg[i] == "validation.captcha")
      {
        msg[i] = "يرجى إدخال رمز التحقق بصورة صحيحة";

      }
      $("#captcha").val('');
      $.ajax({
            type: 'GET',
            url: "{{ route('home.reloadcaptcha') }}",
            success: function (data) {
                $(".captcha span").html(data.captcha);
            }
        });
        //toastr.error(translations.general_sentence.validation.warning, msg[i], { timeOut: 5000 });

        swal({
                    title: translations.general_sentence.validation.warning,
                    text: msg[i],
                    icon: "error",
                    button: "حسنا",
                  });
        return false;
    }
}


function printErrorMsgswl2(msg) {
  console.log(msg);
    for (var i in msg) {
      if(msg[i] == "validation.captcha")
      {
        msg[i] = "يرجى إدخال رمز التحقق بصورة صحيحة";

      }
      $("#captcha2").val('');
      $.ajax({
            type: 'GET',
            url: "{{ route('home.reloadcaptcha') }}",
            success: function (data) {
                $(".captcha2 span").html(data.captcha);
            }
        });
        //toastr.error(translations.general_sentence.validation.warning, msg[i], { timeOut: 5000 });

        swal({
                    title: translations.general_sentence.validation.warning,
                    text: msg[i],
                    icon: "error",
                    button: "حسنا",
                  });
        return false;
    }
}


// function printErrorMsg(msg)
//        {
//             $.each( msg, function( key, value )
//             {
//                 toastr.error(translations.general_sentence.validation.warning,value, {timeOut: 5000});
//             });
//        }

  </script>
  <script type="text/javascript">
    $(".mobile-menu-trigger").click(function(){
      $(this).toggleClass("active");
    });
    $(".menu-block .nav-link-item").click(function(){
      $(".mobile-menu-trigger").removeClass("active");
    });
    $(document).ready(function () {
        // Toggle dropdown menu
        $('.user-dropdown-trigger').on('click', function (e) {
            e.stopPropagation();
            $('.user-dropdown-menu').toggleClass('active');
        });

        // Close dropdown when clicking outside
        $(document).on('click', function (e) {
            if (!$(e.target).closest('.user-dropdown-wrapper').length) {
                $('.user-dropdown-menu').removeClass('active');
            }
        });

        // Handle mobile menu integration
        $('.mobile-menu-trigger').on('click', function () {
            if (window.innerWidth <= 768) {
                $('.user-dropdown-menu').removeClass('active');
            }
        });
    });

  </script>
</body>

</html>

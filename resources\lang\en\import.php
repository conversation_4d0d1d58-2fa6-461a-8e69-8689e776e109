<?php
    /*
    |--------------------------------------------------------------------------
    | Industry Types
    |--------------------------------------------------------------------------
    |
    | language translate for bulk import project assets pages
    | English verion file
    |
    */

    return [
        'import_project_assets' => 'Bulk Import',
        'import_btn' => 'Save',
        'import_your_xl_file' => 'Select Excel file',
        'drop_file_or' => 'Drop File or',
        'browse_file' =>'Browse',
        'failres_bulk_import' => 'Invalid Excel file or sheets',
        'excel_wrong_format' => 'Excel wrong file format!',
        'excel_imported_success' => 'Your Excel sheets has been read successfully',
        'bulk_import_modal_question' => 'To continue proceeding with this Excel to the project?',
        'bulk_import_modal_yes' => 'Yes',
        'bulk_import_modal_no' => 'No',
        'are_you_sure' => 'Are you sure?',
        'file_invalid_mime' => 'The file must be a file of type:  csv, xlsx, xlsm, xlsb, xl, xls!',
        'well_done' => 'Well done!',
        'completed_out_of' => 'completed out of',
        'in_progress' => 'In progress..',
        'file_downloded' => "The file has been successfully imported",
        "view" => "View data",
        "upload_file" => "Import the file",
        "column_mapping" => "Column mapping",
        "confirmation" => "Import confirmation",
        "done" => "Done",
        "import_completed" => "Import completed",
        "users" => "Users list",
        "name" => "User Name",
        "type" => "User Type",
        "email" => "Email",
        "mobile" => "Mobile Number",
        "assigned_buid" => "Assigned Building",
        "department" => "Department",
        "empty_users" => "No user found in file.",
        "user_draft" => "Draft User",
        "prioriy_level_list" => "Priorities Levels List",
        "priority_name" => "Priority Name",
        "service_window" => "Service Window",
        "service_window_type" => "Service Window Type",
        "response_time" => 'Response Time',
        "response_time_type" => 'Response Time Type',
        "empty_priorities" => "No priorities found in file.",
        "asset_categories_list" => "Services List",
        "service_type" => "Service Type",
        "priority" => "Priority",
        "empty_asset_categories" => "No services found in file.",
        "name2" => "Name",
        "properties_list" => "Properties List",
        "city" => "City",
        "region" => "Region",
        "property_name" => "Property Name",
        "location" => "Location",
        "gps_latitude" => "Latitude",
        "gps_longtitude" => "Longitude",
        "empty_properties" => 'No properties found in file.',
        "property_building_list" => "Properties Building List",
        "zone" => "Zone",
        "unit" => "Unit",
        "unit_type" => "Unit Type",
        "empty_property_building" => 'No buildings found in file.',
        "assets_list" => "Assets List",
        "asset_name" => "Asset Name",
        "asset_symbol" => "Asset Symbol",
        "asset_number" => "Asset Number",
        "empty_assets" => 'No assets found in file.',
        "next" => "Next",
        "reset" => "Reset",
        "modal_title" => "Import the file?",
        "desc_modal" => "Are you sure you want to confirm the file importation?",
        "confirm" => "Confirm",
        "close" => "Close",
        "importing" => "Importing",
        "text_importing" => "Your Excel file is being imported..",
        'excel_imported_error' => 'We have faced problems when trying to upload your Excel file',
        "users_count" => "Imported Users",
        "prioriy_level_count" => "Imported Priority Levels",
        "asset_categories" => "Imported Services",
        "properties" => "Imported Properties",
        "property_building" => "Imported Properties Building",
        "assets" => "Imported Assets",
        "users_id" => "User Identifier",
        "status" => "Status",
        "prioriy_level_id" => "Priority Levels Identifier",
        "asset_id" => "Asset Identifier",
        "property_id" => "Property Identifier",
        "changed_default" => "Changed to default",
        "desc_imported" => "You have imported ",
        "users_small" => "users",
        'loading' => 'Loading data..',
        "priorities_levels_small" => "priority levels",
        'asset_categories_small' => "services",
        "properties_small" => "properties",
        "property_building_small" => "properties building",
        "assets_small" => "assets",
        "empty_row" => "Empty row",
        "template" => "Download template",
        "index_no" => "Index of row N°",
        "index" => "Index N°",
        "service" => "Service",
        "accepted" => "Accepted",
        "ignored" => "Ignored",
        "missing" => "Missing value",
        'action' => 'Action',
        'user_id' => 'User ID',
        'wait_insert' => "Waiting for insertion",
        'wait_update' => "Waiting for update",
        'wait_exist' => "Row found",
        'supports' => 'Supports',
        'start_importing' => 'Start Importing',
        'previous' => 'Previous',
        'osool' => 'Osool',
        'filename_title' => 'File Name',
        'size' => 'Size: ',
        'required_file' => 'You must select the file to proceed to the next step!',
        'result' => 'Result',
        'type_title' => 'Result type',
        "view2" => "View",
        "validation" => "Validation Result",
        'nothing_check' => 'No thing to check',
        'select_users' => 'If you want to insert the list of users found in the Excel file, you must select this box',
        'set_email' => 'If you selected this box, the email will be chosen as the primary key of the inserted user',
        'set_mobile' => 'If you selected this box, the mobile number will be chosen as the primary key of the inserted user',
        'loading2' => 'Loading',
        'set_result' => 'Please select the type of imported result you want to see on the next page',
        'desc_popup_view' => 'Consider correcting these points for correct insertion',
        'select_priorities_level' => 'If you want to insert the list of priorities levels found in the Excel file, you must select this box',
        'set_priority_name' => 'If you selected this box, the priority name will be chosen as the primary key of the inserted priority',
        'no_users' => '0 users imported',
        'no_priorities' => '0 prioroties imported',
        'finish' => 'Finish',
        'title_popup' => 'Well done! Data has been imported',
        'text_popup' => 'You’ll find the inserted data inside each module',
        "confirm_message" => "Confirmation",
        'select_services' => 'If you want to insert the list of services found in the Excel file, you must select this box',
        "service_name" => "Service Name",
        'set_service_name' => 'If you selected this box, the service name will be chosen as the primary key of the inserted service',
        'no_services' => '0 services imported',
        'no_records' => '0 Records Imported',
        'displaying' => 'Displaying',
        'question_first_step' => 'You choose to select a file with those informations: ',
        'extension_title' => 'Extension: ',
        'select_properties' => 'If you want to insert the list of properties found in the Excel file, you must select this box',
        'set_property_name' => 'If you selected this box, the property name will be chosen as the primary key',
        'select_properties_building' => 'If you want to insert the list of properties building found in the Excel file, you must select this box',
        'download_regions_cities' => 'Download Region & City',
        'select_assets' => 'If you want to insert the list of assets found in the Excel file, you must select this box',
        'data_purchase' => 'Date Of Purchase',
        'manif_name' => 'Manufacturer Name',
        'model_number' => 'Model Number',
        'damage_status' => 'Damage Status',
        'asset_status' => 'Asset Status',
        'date_of_status' => 'Date of Status',
        'damage_date' => 'Damage Date',
        'optional' => 'Optional',
        'optional_text' => 'The attributes presented in orange color are optional to be entered',
        'set_asset' => 'If you selected this box, the asset number and the property name will be chosen as the primary key at the same time',
        'file_importation' => 'File importation',
        'file_name' => 'Name: ',
        'ready_insert' => 'Ready for insert',
        'ready_update' => 'Ready for update',
        'active' => 'Active',
        'in_active' => 'Inactive',
        'view_users' => 'View users',
        'view_priorities_levels' => 'View priorities level',
        'view_services' => 'View services',
        'view_properties' => 'View properites',
        'view_properties_building' => 'View properites building',
        'view_assets' => 'View assets',
        'double_check' => 'Once you\'ve finished selecting your modules, please double-check that you\'ve chosen everything necessary.',
        'empty_excel_file' => 'You cannot proceed due to no list of data found. (Imported an empty Excel file)',
        'show_notification' => 'Show notification',
        'notification' => 'Notification',
        'file' => 'File',
        'excel_file_icon' => 'Excel file icon',
        'Delete' => 'Delete',
        'not_exist_company_id' => 'You have entered an-existing company ID in the project',
        'company_id' => 'SP Company ID',
        'notification_company_id' => 'Please double-check that you have entered a correct service provider company identifier. If not, the required user will not be inserted.',
        'incorrect_company_id' => 'The company ID must contains digits only',
        'priority_will_not_insert' => 'The property should have at least one unit',
        'property_type' => 'Property Type',
        'complex_name' => 'Complex Name',
        'building_name' => 'Building Name',
        'building_tag' => 'Building Tag',
        'project_id_not_exits' => 'You have selected an invalid project',
        'service_provider_id' => 'Service Provider ID',
        'building_count' => 'Building Count',
        'loading3' => 'Loading..',
      

        #---- new validation
        'required_user_type' => 'You have entered an empty user type.',
        'incorrect_user_type' => 'You have entered an invalid user type. The available user types are: <b>Project Owner Admin, Project Owner Employee, Building Manager Admin, Building Manager Employee, Service Provider Admin, Service Provider Supervisor, Service Provider Worker, Tenant.</b>',
        'required_name' => 'You have entered an empty name.',
        'required_email' => 'You have entered an empty email.',
        'incorrect_email' => "You have entered an invalid email address.",
        'incorrect_mobile_type' => 'You have entered an invalid mobile number type.',
        'incorrect_mobile_length' => "You have entered an invalid mobile number length: must contains 9 digits.",
        'required_priority_name' => 'You have entered an empty priority name.',
        'required_service_window' => 'You have entered an empty service window.',
        'incorrect_service_window_number' => "You have entered an invalid service window: must contains digits only.",
        'required_service_window_type' => 'You have entered an empty service window type.',
        'incorrect_service_window_type' => "You have entered an invalid service window type. The available service window types are: <b>Minutes, Hours, Days.</b>",
        'required_response_type' => 'You have entered an empty response time.',
        'incorrect_response_time_number' => "You have entered an invalid response time: must contains digits only.",
        'required_response_time_type' => 'You have entered an empty response time type.',
        'incorrect_response_time_type' => "You have entered an invalid response time type. The available response time types are: <b>Minutes, Hours, Days.</b>",
        'required_service_name' => 'You have entered an empty service name.',
        'required_service_priority' => 'You have entered an empty priority.',
        'incorrect_priority' => 'You have entered a priority that does not exist.',
        'required_service_type' => 'You have entered an empty service type.',
        'incorrect_service_type' => 'You have entered an invalid service type. The available service types are: <b>Hard, Soft.</b>',
        'required_region' => 'You have entered an empty region.',
        'incorrect_region' => 'You have entered an invalid region.',
        'required_city' => 'You have entered an empty city.',
        'incorrect_city' => 'You have entered an invalid city.',
        'required_property_type' => 'You have entered an empty property type.',
        'incorrect_property_type' => "You have entered an invalid property type. The available property types are: <b>Complex, Building.</b>",
        'required_property_name' => 'You have entered an empty property name.',
        'incorrect_property_name' => 'You have entered an invalid property name',
        'required_building_count' => 'You have entered an empty building count.',
        'incorrect_building_count' => 'You have entered an invalid building count: must contains digits only.',
        'null_building_count' => 'You have entered an invalid building count: must be greater than 0.',
        'required_latitude' => 'You have entered an empty latitude.',
        'incorrect_latitude' => 'You have entered an invalid latitude format.',
        'required_longtitude' => 'You have entered an empty longitude.',
        'incorrect_longtitude' => 'You have entered an invalid longitude format.',
        'required_building_name' => 'You have entered an empty building name.',
        'required_zone' => 'You have entered an empty zone.',
        'required_unit' => 'You have entered an empty unit.',
        'required_unit_type' => 'You have entered an empty unit type.',
        'invalid_service_type' => 'You have entered an invalid service type.',
        'required_asset_name' => 'You have entered an empty asset name.',
        'required_asset_symbole' => 'You have entered an empty asset symbol.',
        'required_asset_number' => 'You have entered an empty asset number.',
        'incorrect_asset_number' => "You have entered an invalid asset number: minimum 3 to 45 characters are allowed.",
        'incorrect_date_purchase' => 'You have entered an invalid purchase date format : the correct format is dd/mm/YYYY.',
        'incorrect_asset_status' => 'You have entered an invalid asset status. The available status are : <b>New, Used, Damaged, Abandoned, Disposed, Donated, Escrowed, For Sale, In Repair, In Service, In Storage, In Use, Leased, Leased (Expired), Missing, N/A, Out Of Service, Owned, Owned And Leased, Pipeline, Salvaged, Sold, Strolen, Sub-Leased, Sub Let, Under Contract or Unknown.</b>',
        'incorrect_date_damage' => 'You have entered an invalid status date format : the correct format is dd/mm/YYYY.',
        'required_worker' => 'The worker id is required',
        'not_auth' => 'You are not authorized to use this service',
        'bug' => 'An unexpected bug was found. We will resolve this issue as soon as possible. Thank you for understanding',
        'service_provider_company' => 'Service Provider Company',
        'hq_location' => 'HQ Location',
        'reopen_count' => 'Reopen Count',
        'overaill_text' => 'Overall Rating',
        'average_rate' => 'Average Rate',
        'on_time_exec' => 'On time Execution',
        'on_time_resp' => 'On time Response Rate',
        'closed_work_orders' => 'Closed Work Orders',
        'wo_ratings' => 'Workers Ratings',
        'wo_history_text' => 'Closed Workorders Rating History',
        'filter_by' => 'Filter by',
        'wo_id' => 'Workorder ID',
        'supervisor_assign' => 'Supervisor Assigned',
        'wo_assign' => 'Worker Assigned',
        'response_time' => 'Response Time',
        'ex_time' => 'Execution Time',
        'wo_ratings2' => 'Worker Ratings',
        'job_ratings' => 'Job Ratings',
        'reopns' => 'Reopens',
        'on_time' => 'On time',
        'late' => 'Late',
        'no' => 'No',
        'yes' => 'Yes', 
        'workers' => 'Workers',
        'search_worker' => 'Search Worker',
        'clear_section' => 'Clear section',
        'pass' => 'Pass',
        'fail' => 'Fail',
        'prop' => 'Property',
        'sup' => 'Supervisors',
        'contract' => 'Contract',
        'search_con' => 'Search Contracts',
        'view_details' => 'View Details',
        'closed_wo_rating' => 'Closed Job Rating',
        'pending' => 'Pending',
        'skipped' => 'Skipped',
        'check_disabled_smart' => 'Smart Assign enabled, you can’t select a permanent worker but manually changeable from the work order details.',
        'selected_worker' => 'Selected Worker',
        'make_your_decision' => 'Smart Assign feature enabled and a permanent selected worker previously, select how to continue:',
        'use_smart_assign_feature' => 'Use the smart assign criteria',
        'keeep_same_worker' => 'Keep the same worker',
        '404_picture' => '404 picture',
        'no_project_found' => 'Sorry! the project you are looking for doesn\'t exist.',
        'return_previous' => 'Return to previous',
        'upload' => 'Upload',
        'map' => 'Map',
        'validate' => 'Validate',
        'cancel' => 'Cancel',
        'confirm_question_bulk_import' => 'Are you sure you want to upload this file?',
        'users_label' => 'Users',
        'filesize_title' => 'File Size',
        'sheet' => 'Sheet',
        'count' => 'Count',
        'connected_user' => 'Connected User',
        'priorities_label' => 'Priorities',
        'services_label' => 'Services',
        'properties_label' => 'Properties',
        'property_buildings_label' => 'Property Buildings',
        'assets_label' => 'Assets',
        'description' => 'Description',
        'service_provider_label' => 'Service Provider',
        'confirm_question_select_sheets' => 'Are you sure you selected all needed sheets?',
        'select_one_sheet' => 'You must select at least one sheet to proceed to next stage!',
        'validation' => 'Validation',
        'load_more' => 'Load more',
        'accepted' => 'Accepted',
        'view_errors' => 'View errors',
        'load_all' => 'Load all',
        'total' => 'Total',
        'refused' => 'Refused',
        'show_more' => 'Show more >>',
        'show_less' => 'Show less <<',
        'incorrect_asset_number_type' => 'You have entered an invalid asset number: must contains digits only.',
        'confirm_map_question' => 'Are you sure you want to proceed? this action cannot be undone!',
        'bulk_import_progress' => 'Bulk import progress',
        'hey' => 'Hey',
        'view_logs_progress' => 'View your progress logs',
        'users_sheet' => 'Users sheet',
        'priorities_sheet' => 'Priorities sheet',
        'services_sheet' => 'Services sheet',
        'properties_sheet' => 'Properties sheet',
        'buildngs_sheet' => 'Buildings sheet',
        'assets_sheet' => 'Assets sheet',
        'complete' => 'Complete',
        'value_will_be_skipped' => 'This value will be skipped and will be inserted as nullable entrie',
        'delete' => 'Delete',
        'error_type' => 'Error type',
        'identifier' => 'Identifier',
        'value' => 'Value',
        'errors' => 'Errors',
        'status_type' => 'Not inserted',
        'empty_errors' => 'No errors found in sheet.',
        'front_step' => 'Front validation',
        'system_step' => 'System validation',
        'template_text' => 'Template',
        'download' => 'Download',
        'regions_cities' => 'Regions & Cities',
        'system_errors' => 'System errors',
        'inserted_rows' => 'Inserted rows',
        'errors_list' => 'Errors list',
        'users_errors_text' => 'This users list was not included due to system validation issues.',
        'users_inserted_text' => 'This users list was inserted successfully without any issues.',
        'poa' => 'Project Owner Admin',
        'poe' => 'Project Owner Employee',
        'bma' => 'Building Manager Admin',
        'bme' => 'Building Manager Employee',
        'spa' => 'Service Provider Admin',
        'sps' => 'Service Provider Supervisor',
        'spw' => 'Service Provider Worker',
        'tenant' => 'Tenant',
        'ud' => 'User Draft',
        'phone' => 'Phone',
        'type2' => 'Type',
        'proceed' => 'Proceed',
        'question_delete_sheet' => 'You want to delete this list?',
        'users_not_deleted' => 'Cannot delete the users list',
        'users_deleted_successfully' => 'The users list is deleted successfully',
        'bulk_import_errors_not_deleted' => 'The users list is deleted but we cannot delete the errors list',
        'inserted_list_not_updated' => 'The users list is deleted but we cannot update the inserted list',
        'delete_inserted_users' => 'Delete inserted users',
        'finish_insertion' => 'Finish insertion',
        'data_created' => 'Data has been inserted',
        'manage_data_text' => 'You can manage the new inserted data any time',
        'go_dashboard' => 'Go to dashboard',
        'delete_inserted_priorities' => 'Delete inserted priorities',
        'priorities_inserted_text' => 'This priorities list was inserted successfully without any issues.',
        'priorties_errors_text' => 'This priorities list was not included due to system validation issues.',
        'priorities_not_deleted' => 'Cannot delete the priorities list',
        'priorities_deleted_successfully' => 'The priorities list is deleted successfully',
        'services_inserted_text' => 'This services list was inserted successfully without any issues.',
        'services_errors_text' => 'This services  list was not included due to system validation issues.',
        'error' => 'Error',
        'delete_inserted_services' => 'Delete inserted services',
        'services_not_deleted' => 'Cannot delete the services list',
        'services_deleted_successfully' => 'The services list is deleted successfully',
        'delete_inserted_properties' => 'Delete inserted properties',
        'properties_inserted_text' => 'This properties list was inserted successfully without any issues.',
        'properties_not_deleted' => 'Cannot delete the properties list',
        'properties_deleted_successfully' => 'The properties list is deleted successfully',
        'more' => '>>',
        'less' => '<<',
        'complex' => 'Complex',
        'building' => 'Building',
        'delete_inserted_buildings' => 'Delete inserted buildings',
        'buildings_inserted_text' => 'This buildings list was inserted successfully without any issues.',
        'property_text' => 'Property',
        'rooms_count' => 'Rooms Count',
        'buildings_errors_text' => 'This buildings list was not included due to system validation issues.',
        'rooms_type_floor_not_deleted' => 'Cannot delete the rooms type floor list',
        'rooms_type_not_deleted' => 'Cannot delete the rooms type list',
        'buildings_not_deleted' => 'Cannot delete the property buildings list',
        'buildings_deleted_successfully' => 'The buildings list is deleted successfully',
        'delete_inserted_assets' => 'Delete inserted assets',
        'assets_inserted_text' => 'This assets list was inserted successfully without any issues.',
        'assets_errors_text' => 'This assets list was not included due to system validation issues.',
        'asset_names_not_deleted' => 'Cannot delete the asset names list',
        'assets_not_deleted' => 'Cannot delete the assets list',
        'assets_deleted_successfully' => 'The assets list is deleted successfully',
        'affectation' => 'Affectation',
        'affectation_text' => 'By applying this action, and after closing this modal, you cannot change it again!',
        'admin' => 'Admin',
        'company' => 'Company',
        'supervisor' => 'Supervisor',
        'building_manager_label' => 'Building Manager Admin',
        'service_provider_admin_label' => 'Service Provider Admin',
        'service_provider_supervisor_label' => 'Service Provider Supervisor',
        'affect' => 'Affect',
        'apply' => 'Apply',
        'not_available' => 'Not Available',
        'affectation_error' => 'Please make sure you have actually completed the affectation procedure correctly and then try again later!',
        'import_data_in_progress' => 'We\'re in the process of importing your data. This might take a few moments especially for larger datasets, it could take up to 5 minutes or more. Thanks for your patience!',
        'data_issues' => 'Data issues',
        'data_issue_displayed' => 'This data issue was displayed in the mapping view',
    ];
?>
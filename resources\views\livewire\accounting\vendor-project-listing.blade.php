<div>
    @if($showFullView)
    <div class="contents crm">
        <div class="container-fluid">
            <div class="col-lg-12">
                <div class="row justify-content-sm-between align-items-center justify-content-center my-3 flex-sm-row flex-column">
                    <div class="page-title-wrap p-0">
                        <div class="page-title d-flex justify-content-between">
                            <div class="page-title__left justify-content-sm-between align-items-center justify-content-center">
                                <div class="user-member__title mr-sm-25 ml-0">
                                    <h4 class="text-capitalize fw-500 breadcrumb-title fs-16">
                                        {{ __('vendors.projects.vendor_projects') }}
                                    </h4>
                                </div>
                            </div>
                        </div>
                        <div>
                            <ul class="atbd-breadcrumb nav">
                                <li class="atbd-breadcrumb__item">
                                    <a href="{{ route('admin.dashboard') }}">{{ __('vendors.common.dashboard') }}</a>
                                    <span class="breadcrumb__seperator">
                                        <span class="la la-angle-right"></span>
                                    </span>
                                </li>
                                <li class="atbd-breadcrumb__item">
                                    <a href="{{ route('finance.vendors') }}">{{ __('vendors.common.vendors') }}</a>
                                    <span class="breadcrumb__seperator">
                                        <span class="la la-angle-right"></span>
                                    </span>
                                </li>
                                <li class="atbd-breadcrumb__item">
                                    <a>{{ __('vendors.projects.projects') }}</a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endif

    <div class="card radius-xl">
        <div class="card-header py-4 px-3 border-0 d-flex justify-content-between align-items-center">
            <h6 class="text-capitalize text-osool fw-500 mb-3 mb-sm-0">{{ __('vendors.projects.projects') }}</h6>
            
            <div class="d-flex gap-10 table-search">
                <div class="position-relative">
                    <input type="text" class="form-control" placeholder="{{ __('vendors.projects.search_placeholder') }}" wire:model.debounce.300ms="search">
                    <i class="iconsax field-icon fs-18 mr-0" icon-name="search-normal-2"></i>
                </div>
            </div>
        </div>

        <div class="card-body px-0 pt-0">
            @if($loading)
                <div class="text-center py-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="sr-only">{{ __('vendors.common.loading') }}</span>
                    </div>
                </div>
            @elseif($error)
                <div class="text-center py-5">
                    <i class="iconsax icon fs-48 text-danger mb-3" icon-name="info-circle"></i>
                    <h6 class="text-danger">{{ $error }}</h6>
                    <button wire:click="fetchProjects" class="btn btn-primary mt-3">
                        {{ __('vendors.buttons.retry') }}
                    </button>
                </div>
            @else
                <div class="userDatatable projectDatatable project-table bg-white w-100 border-0">
                    <div class="table-responsive">
                        <table class="table mb-0 radius-0 th-osool">
                            <thead>
                                <tr class="userDatatable-header">
                                    <th wire:click="sortBy('name')" style="cursor: pointer;">
                                        <i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i>
                                        {{ __('vendors.projects.name') }}
                                        @if($sortField === 'name')
                                            <i class="las la-sort-{{ $sortDirection === 'asc' ? 'up' : 'down' }}"></i>
                                        @endif
                                    </th>
                                    <th wire:click="sortBy('stage')" style="cursor: pointer;">
                                        <i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i>
                                        {{ __('vendors.projects.stage') }}
                                        @if($sortField === 'stage')
                                            <i class="las la-sort-{{ $sortDirection === 'asc' ? 'up' : 'down' }}"></i>
                                        @endif
                                    </th>
                                    <th wire:click="sortBy('start_date')" style="cursor: pointer;">
                                        <i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i>
                                        {{ __('vendors.projects.start_date') }}
                                        @if($sortField === 'start_date')
                                            <i class="las la-sort-{{ $sortDirection === 'asc' ? 'up' : 'down' }}"></i>
                                        @endif
                                    </th>
                                    <th wire:click="sortBy('end_date')" style="cursor: pointer;">
                                        <i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i>
                                        {{ __('vendors.projects.end_date') }}
                                        @if($sortField === 'end_date')
                                            <i class="las la-sort-{{ $sortDirection === 'asc' ? 'up' : 'down' }}"></i>
                                        @endif
                                    </th>
                                    <th>
                                        <i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i>
                                        {{ __('vendors.projects.description') }}
                                    </th>
                                    <th>
                                        <i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i>
                                        {{ __('vendors.projects.action') }}
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="sort-table ui-sortable">
                                @forelse($projects as $project)
                                <tr class="ui-sortable-handle">
                                    <td>
                                        <div class="userDatatable-inline-title">
                                            <span>{{ $project['name'] ?? '-' }}</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="userDatatable-inline-title">
                                            @php
                                                $stage = strtolower($project['stage'] ?? '');
                                                $badgeClass = match($stage) {
                                                    'completed' => 'success',
                                                    'in progress', 'inprogress' => 'primary',
                                                    'onhold', 'on hold' => 'warning',
                                                    'planning' => 'info',
                                                    default => 'secondary'
                                                };
                                            @endphp
                                            <span class="badge badge-{{ $badgeClass }}">
                                                {{ $project['stage'] ?? '-' }}
                                            </span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex userDatatable-content mb-0 align-items-center">
                                            <span class="no-wrap">{{ isset($project['start_date']) ? date('d-m-Y', strtotime($project['start_date'])) : '-' }}</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex userDatatable-content mb-0 align-items-center">
                                            <span class="no-wrap">{{ isset($project['end_date']) ? date('d-m-Y', strtotime($project['end_date'])) : '-' }}</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="userDatatable-inline-title">
                                            <span class="max-td" title="{{ $project['description'] ?? '' }}">
                                                {{ Str::limit($project['description'] ?? '', 80) }}
                                            </span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-inline-block">
                                            <ul class="mb-0 d-flex gap-10">
                                                <li>
                                                    <a href="javascript:void(0);" wire:click="viewProject({{ $project['id'] }})">
                                                        <i class="iconsax icon text-osool fs-18" icon-name="eye"></i>
                                                    </a>
                                                </li>
                                                <li>
                                                    <a href="javascript:void(0);" wire:click="editProject({{ $project['id'] }})">
                                                        <i class="iconsax icon text-new-primary fs-18" icon-name="edit-1"></i>
                                                    </a>
                                                </li>
                                                <li>
                                                    <a href="javascript:void(0);" wire:click="openDeleteModal({{ $project['id'] }}, '{{ $project['name'] }}')">
                                                        <i class="iconsax icon text-delete fs-18" icon-name="trash"></i>
                                                    </a>
                                                </li>
                                            </ul>
                                        </div>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="6" class="text-center py-5">
                                        <div class="d-flex flex-column align-items-center">
                                            <i class="iconsax icon fs-48 text-muted mb-3" icon-name="folder-2"></i>
                                            <h6 class="text-muted">{{ __('vendors.projects.no_projects_found') }}</h6>
                                        </div>
                                    </td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            @endif
        </div>

        @if($total > 0)
        <div class="card-body pt-0">
            <div class="d-flex justify-content-md-between flex-column flex-md-row justify-content-center align-items-center mt-4 gap-10">
                <div class="">
                    <ul class="atbd-pagination d-flex justify-content-between">
                        <li>
                            <div class="paging-option">
                                <div class="dataTables_length d-flex">
                                    <label class="d-flex align-items-center mb-0">
                                        <select wire:model="perPage" class="custom-select custom-select-sm form-control form-control-sm mx-2" style="min-height: 35px;">
                                            <option value="5">5</option>
                                            <option value="10">10</option>
                                            <option value="25">25</option>
                                            <option value="50">50</option>
                                            <option value="100">100</option>
                                        </select>
                                        <span class="no-wrap"> {{ __('vendors.pagination.entries_per_page') }} </span>
                                    </label>
                                </div>
                            </div>
                        </li>
                    </ul>
                </div>

                <div class="">
                    <div class="user-pagination">
                        <div class="user-pagination new-pagination">
                            <div class="d-flex justify-content-sm-end justify-content-end">
                                <nav>
                                    <span class="relative z-0 inline-flex rounded-md d-flex new-pagination-section flex-wrap">
                                        @if($pagination->current_page > 1)
                                            <span>
                                                <button type="button" class="border-0" wire:click="previousPage">
                                                    <i class="iconsax icon-bold fs-16 mr-0 rotate-ar-y d-inline-block" icon-name="chevron-left"></i>
                                                </button>
                                            </span>
                                        @else
                                            <span>
                                                <button class="border-0 disabled" disabled>
                                                    <i class="iconsax icon-bold fs-16 mr-0 rotate-ar-y d-inline-block" icon-name="chevron-left"></i>
                                                </button>
                                            </span>
                                        @endif

                                        @php
                                            $start = max(1, $pagination->current_page - 2);
                                            $end = min($pagination->last_page, $pagination->current_page + 2);
                                        @endphp

                                        @if($start > 1)
                                            <span>
                                                <button type="button" class="border-0" wire:click="gotoPage(1)">1</button>
                                            </span>
                                            @if($start > 2)
                                                <span>
                                                    <button class="border-0 disabled" disabled>...</button>
                                                </span>
                                            @endif
                                        @endif

                                        @for($i = $start; $i <= $end; $i++)
                                            @if($i == $pagination->current_page)
                                                <span>
                                                    <button class="border-0 current-page" disabled>{{ $i }}</button>
                                                </span>
                                            @else
                                                <span>
                                                    <button type="button" class="border-0" wire:click="gotoPage({{ $i }})">{{ $i }}</button>
                                                </span>
                                            @endif
                                        @endfor

                                        @if($end < $pagination->last_page)
                                            @if($end < $pagination->last_page - 1)
                                                <span>
                                                    <button class="border-0 disabled" disabled>...</button>
                                                </span>
                                            @endif
                                            <span>
                                                <button type="button" class="border-0" wire:click="gotoPage({{ $pagination->last_page }})">{{ $pagination->last_page }}</button>
                                            </span>
                                        @endif

                                        @if($pagination->current_page < $pagination->last_page)
                                            <span>
                                                <button type="button" class="border-0" wire:click="nextPage">
                                                    <i class="iconsax icon-bold fs-16 mr-0 rotate-ar-y d-inline-block" icon-name="chevron-right"></i>
                                                </button>
                                            </span>
                                        @else
                                            <span>
                                                <button class="border-0 disabled" disabled>
                                                    <i class="iconsax icon-bold fs-16 mr-0 rotate-ar-y d-inline-block" icon-name="chevron-right"></i>
                                                </button>
                                            </span>
                                        @endif
                                    </span>
                                </nav>
                            </div>
                        </div>
                    </div>
                </div>

                <div>
                    <p class="text-sm text-gray-700 leading-5 mb-0">
                        <span>{{ __('vendors.pagination.showing') }}</span>
                        <span class="font-medium">{{ ($pagination->current_page - 1) * $pagination->per_page + 1 }}</span>
                        <span>{{ __('vendors.pagination.to') }}</span>
                        <span class="font-medium">{{ min($pagination->current_page * $pagination->per_page, $pagination->total) }}</span>
                        <span>{{ __('vendors.pagination.of') }}</span>
                        <span class="font-medium">{{ $pagination->total }}</span>
                        <span>{{ __('vendors.pagination.results') }}</span>
                    </p>
                </div>
            </div>
        </div>
        @endif
    </div>

    {{-- Delete Confirmation Modal --}}
    @livewire('common.delete-confirm')
</div>

@push('scripts')
<script>
    // Toast notification handler
    window.addEventListener('show-toastr', event => {
        toastr.options = {
            "closeButton": true,
            "progressBar": true,
            "positionClass": "toast-top-right",
            "timeOut": "3000"
        };

        if (event.detail.type === 'success') {
            toastr.success(event.detail.message);
        } else if (event.detail.type === 'error') {
            toastr.error(event.detail.message);
        }
    });
</script>
@endpush

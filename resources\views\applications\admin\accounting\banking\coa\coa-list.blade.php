@extends('layouts.app')
@section('styles')
@endsection
@section('content')
        <div class="contents crm">
           <div class="container-fluid">
            <div class="col-lg-12">
    <div class="row justify-content-sm-between align-items-center justify-content-center my-3 flex-sm-row flex-column">
        <div class="page-title-wrap p-0">
            <div class="page-title d-flex justify-content-between">
                <div class="page-title__left justify-content-sm-between align-items-center justify-content-center">
                    <div class="user-member__title mr-sm-25 ml-0">
                        <h4 class="text-capitalize fw-500 breadcrumb-title fs-16">
                            Manage Charts of Accounts
                        </h4>
                    </div>
                </div>
            </div>
            <div>
                <ul class="atbd-breadcrumb nav">
                    <li class="atbd-breadcrumb__item">
                        <a>Dashboard</a>
                        <span class="breadcrumb__seperator">
                            <span class="la la-angle-right"></span>
                        </span>
                    </li>
                    <li class="atbd-breadcrumb__item">
                        <a>Manage Charts of Accounts</a>
                    </li>
                </ul>
            </div>
        </div>

        <div class="d-flex gap-10 breadcrumb_right_icons">
            <div class="d-flex gap-10 breadcrumb_right_icons">

                    <button class="btn btn-white btn-default text-center svg-20 wh-45">
                        <i class="iconsax icon text-new-primary fs-22 colorRed mr-0" icon-name="setting-1"></i>
                    </button>

                    <button class="btn btn-default btn-primary w-100 no-wrap" type="button" aria-expanded="false"  data-toggle="modal" data-target="#create-vendor"><i class="las la-plus fs-16"></i>Create</button>

            </div>
        </div>
        <!--====End Design for Export PDF===-->
    </div>
</div>


<div class="">
    <div class="card mb-3" data-select2-id="108">
            <div class="card-body" data-select2-id="107">
                <form wire:submit.prevent="applyFilters" class="fs-14">
                    <div class="row">
                        <div class="col-md-3 col-6">
                            <label for="" class="text-osool">Start Date</label>
                            <div class="position-relative">
                                <input type="text" class="form-control datepicker" id="start_date" aria-describedby="emailHelp" placeholder="Enter Start Date" />
                                 <i class="iconsax field-icon" icon-name="calendar-search"></i>
                            </div>
                        </div>
                        <div class="col-md-3 col-6">
                            <label for="" class="text-osool">End Date</label>
                            <div class="position-relative">
                                <input type="text" class="form-control datepicker" id="start_date" aria-describedby="emailHelp" placeholder="Enter Start Date" />
                                 <i class="iconsax field-icon" icon-name="calendar-search"></i>
                            </div>
                        </div>

                        <div class="col-md-3 mt-md-0 mt-3">
                            <label for="" class="d-md-block d-none">&nbsp;</label>
                            <div class="d-flex gap-10">
                                <button type="submit" class="btn bg-opacity-new-primary btn-sm text-new-primary radius-md px-5">
                                    Apply
                                </button>
                                <button type="button" class="btn bg-opacity-loss btn-sm text-loss wh-45 radius-md">
                                    <i class="iconsax mr-0 fs-18" icon-name="rotate-left"></i>
                                    <!-- Reset -->
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        <div class="d-inline-block mb-3">
            <ul class="nav nav-tabs site-pills bg-white p-1 rounded" id="pills-tab" role="tablist">
                <li class="nav-item" role="presentation">
                    <a class="nav-link rounded active" href="#"> Assets </a>
                </li>
                <li class="nav-item" role="presentation">
                    <a class="nav-link rounded text_black" href="#"> Liabilities </a>
                </li>
                <li class="nav-item" role="presentation">
                    <a class="nav-link rounded text_black" href="#"> Equity </a>
                </li>
                <li class="nav-item" role="presentation">
                    <a class="nav-link rounded text_black" href="#"> Income </a>
                </li>
                <li class="nav-item" role="presentation">
                    <a class="nav-link rounded text_black" href="#"> Costs of Goods Sold </a>
                </li>
                <li class="nav-item" role="presentation">
                    <a class="nav-link rounded text_black" href="#"> Expenses </a>
                </li>
            </ul>
        </div>
    <div class="card mb-3">
        <div class="">
            <div class="card-header py-4 px-3 border-0 d-flex justify-content-between align-items-center">
                <h6 class="text-capitalize text-osool fw-500 mb-3 mb-sm-0">Assets</h6>

                <!-- <div class="d-flex gap-10 table-search">
                    <div class="position-relative">
                    <input type="text" class="form-control" placeholder="Search">
                    <i class="iconsax field-icon fs-18 mr-0" icon-name="search-normal-2"></i>
                </div>
                     <button class="btn btn-export text-dark"><i class="iconsax icon fs-22 mr-0" icon-name="upload-1"></i> Export</button>
                </div> -->
            </div>
        </div>
        <div class="card-body px-0 pt-0">
            <div class="userDatatable projectDatatable project-table bg-white w-100 border-0">
                <div class="table-responsive">
                    <table class="table mb-0 radius-0 th-osool">
                        <thead>
                            <tr class="userDatatable-header">
                                 <th>
                                   CODE
                                </th>
                                <th>
                                   <i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i>  Name
                                </th>
                                <th>
                                   <i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i>  Type
                                </th>
                                <th>
                                    <i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i> Parent Account Type
                                </th>
                                <th>
                                   <i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i>  Balance
                                </th>
                                <th>
                                   <i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i>  Status
                                </th>
                                <th>
                                    Action
                                </th>
                            </tr>
                        </thead>
                        <tbody class="sort-table ui-sortable">
                            <tr class="ui-sortable-handle">
                                <td>
                                    <div class="d-flex userDatatable-content mb-0 align-items-center">
                                        <span>1011</span>
                                    </div>
                                    
                                </td>
                                <td>
                                    <div class="d-flex userDatatable-content mb-0 align-items-center">
                                       <a href="#"> <span>Checking Account</span> </a>
                                    </div>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center gap-10"><span class="">Current Asset</span>
                                </div>
                                </td>
                                <td>
                                    <div class="d-flex userDatatable-content mb-0 align-items-center">
                                        <span>1</span>
                                    </div>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center gap-10">
                                    <img src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSx34eO8RbZlsetN1iAQtxuOLnLOlyJbJmU7Q&s" width="15"><span class="text-new-primary">25000</span>
                                </td>
                                <td>
                                    <span class="badge-new bg-opacity-win text-win rounded">Enabled</span>
                                </td>
                                <td>
                                    <div class="d-inline-block">
                                        <ul class="mb-0 d-flex gap-10">
                                            <li>
                                                <a href="" title="Transaction Summery">
                                                    <i class="iconsax icon text-osool fs-18" icon-name="bar-graph-4"></i>
                                                </a>
                                            </li>
                                            <li>
                                                <a href="javascript:void(0);" data-toggle="modal" data-target="#edit-account" wire:click="openEditModal(588)">
                                                    <i class="iconsax icon text-new-primary fs-18" icon-name="edit-1"></i>
                                                </a>
                                            </li>
                                            <li>
                                                <a href="javascript:void(0);" data-toggle="modal" data-target="#delete-vendor">
                                                    <i class="iconsax icon text-delete fs-18" icon-name="trash"></i>
                                                </a>
                                            </li>
                                            <li>
                                                <a href="javascript:void(0);" data-toggle="modal" data-target="#create-vendor">
                                                    <i class="iconsax icon text-delete fs-18" icon-name="eye"></i>
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                </td>
                            </tr>
                            <tr class="ui-sortable-handle">
                                <td>
                                    <div class="d-flex userDatatable-content mb-0 align-items-center">
                                        <span>1011</span>
                                    </div>
                                    
                                </td>
                                <td>
                                    <div class="d-flex userDatatable-content mb-0 align-items-center">
                                       <a href="#"> <span>Checking Account</span> </a>
                                    </div>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center gap-10"><span class="">Current Asset</span>
                                </div>
                                </td>
                                <td>
                                    <div class="d-flex userDatatable-content mb-0 align-items-center">
                                        <span>1</span>
                                    </div>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center gap-10">
                                    <img src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSx34eO8RbZlsetN1iAQtxuOLnLOlyJbJmU7Q&s" width="15"><span class="text-new-primary">25000</span>
                                </td>
                                <td>
                                    <span class="badge-new bg-opacity-loss text-loss rounded">Disabled</span>
                                </td>
                                <td>
                                    <div class="d-inline-block">
                                        <ul class="mb-0 d-flex gap-10">
                                            <li>
                                                <a href="" title="Transaction Summery">
                                                    <i class="iconsax icon text-osool fs-18" icon-name="bar-graph-4"></i>
                                                </a>
                                            </li>
                                            <li>
                                                <a href="javascript:void(0);" data-toggle="modal" data-target="#edit-account" wire:click="openEditModal(588)">
                                                    <i class="iconsax icon text-new-primary fs-18" icon-name="edit-1"></i>
                                                </a>
                                            </li>
                                            <li>
                                                <a href="javascript:void(0);" data-toggle="modal" data-target="#delete-vendor">
                                                    <i class="iconsax icon text-delete fs-18" icon-name="trash"></i>
                                                </a>
                                            </li>
                                            <li>
                                                <a href="javascript:void(0);" data-toggle="modal" data-target="#create-vendor">
                                                    <i class="iconsax icon text-delete fs-18" icon-name="eye"></i>
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>




        <div class="card-body pt-0">
<div class="d-flex justify-content-md-between flex-column flex-md-row justify-content-center align-items-center mt-4 gap-10">
    <div class="">
        <ul class="atbd-pagination d-flex justify-content-between">
            <li>
                <div class="paging-option">
                    <div class="dataTables_length d-flex">
                        <label class="d-flex align-items-center mb-0">
                            <select aria-controls="workorder_table" wire:model.live.debounce.250ms="perPage" class="custom-select custom-select-sm form-control form-control-sm mx-2" style="min-height: 35px;">
                                <option value="5">5</option>
                                <option value="10">10</option>
                                <option value="25">25</option>
                                <option value="50">50</option>
                                <option value="100">100</option>
                            </select>
                           <span class="no-wrap"> Entries Per Page </span>
                        </label>
                    </div>
                </div>
            </li>
        </ul>
    </div>
    <div class="">
        <div class="user-pagination">
            <div class="user-pagination new-pagination">
                <div class="d-flex justify-content-sm-end justify-content-end">
                    <nav>
                        <span class="relative z-0 inline-flex rounded-md d-flex new-pagination-section flex-wrap">
    <span class="">
        <span aria-disabled="true" aria-label="&amp;laquo; Previous">
            <button class="border-0 disabled" aria-hidden="true" disabled="">
                <i class="iconsax icon-bold fs-16 mr-0 rotate-ar-y d-inline-block" icon-name="chevron-left"></i>
            </button>
        </span>
    </span>

    <span wire:key="paginator-page-1-page1">
        <button class="border-0 current-page" disabled="">1</button>
    </span>
    <span wire:key="paginator-page-1-page2">
        <button type="button" class="border-0">2</button>
    </span>
    <span wire:key="paginator-page-1-page3">
        <button type="button" class="border-0">3</button>
    </span>
    <span wire:key="paginator-page-1-page4">
        <button type="button" class="border-0">4</button>
    </span>
    <span wire:key="paginator-page-1-page5">
        <button type="button" class="border-0">5</button>
    </span>
    <span wire:key="paginator-page-1-page6">
        <button type="button" class="border-0">6</button>
    </span>
    <span wire:key="paginator-page-1-page7">
        <button type="button" class="border-0">7</button>
    </span>
    <span wire:key="paginator-page-1-page8">
        <button type="button" class="border-0">8</button>
    </span>
    <span wire:key="paginator-page-1-page9">
        <button type="button" class="border-0"> 9 </button>
    </span>

    <span>
        <button type="button" class="border-0">
            <i class="iconsax icon-bold fs-16 mr-0 rotate-ar-y d-inline-block" icon-name="chevron-right"></i>
        </button>
    </span>
</span>

                    </nav>
                </div>
            </div>
        </div>
    </div>
    <div>
        <p class="text-sm text-gray-700 leading-5 mb-0">
                        <span>Showing</span>
                        <span class="font-medium">1</span>
                        <span>to</span>
                        <span class="font-medium">6</span>
                        <span>of</span>
                        <span class="font-medium">52</span>
                        <span>results</span>
                    </p>
    </div>
</div>
        </div>
    </div>


    <div class="card mb-3">
        <div class="">
            <div class="card-header py-4 px-3 border-0 d-flex justify-content-between align-items-center">
                <h6 class="text-capitalize text-osool fw-500 mb-3 mb-sm-0">Liabilities</h6>

                <!-- <div class="d-flex gap-10 table-search">
                    <div class="position-relative">
                    <input type="text" class="form-control" placeholder="Search">
                    <i class="iconsax field-icon fs-18 mr-0" icon-name="search-normal-2"></i>
                </div>
                     <button class="btn btn-export text-dark"><i class="iconsax icon fs-22 mr-0" icon-name="upload-1"></i> Export</button>
                </div> -->
            </div>
        </div>
        <div class="card-body px-0 pt-0">
            <div class="userDatatable projectDatatable project-table bg-white w-100 border-0">
                <div class="table-responsive">
                    <table class="table mb-0 radius-0 th-osool">
                        <thead>
                            <tr class="userDatatable-header">
                                 <th>
                                   CODE
                                </th>
                                <th>
                                   <i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i>  Name
                                </th>
                                <th>
                                   <i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i>  Type
                                </th>
                                <th>
                                    <i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i> Parent Account Type
                                </th>
                                <th>
                                   <i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i>  Balance
                                </th>
                                <th>
                                   <i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i>  Status
                                </th>
                                <th>
                                    Action
                                </th>
                            </tr>
                        </thead>
                        <tbody class="sort-table ui-sortable">
                            <tr class="ui-sortable-handle">
                                <td>
                                    <div class="d-flex userDatatable-content mb-0 align-items-center">
                                        <span>1011</span>
                                    </div>
                                    
                                </td>
                                <td>
                                    <div class="d-flex userDatatable-content mb-0 align-items-center">
                                       <a href="#"> <span>Checking Account</span> </a>
                                    </div>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center gap-10"><span class="">Current Asset</span>
                                </div>
                                </td>
                                <td>
                                    <div class="d-flex userDatatable-content mb-0 align-items-center">
                                        <span>1</span>
                                    </div>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center gap-10">
                                    <img src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSx34eO8RbZlsetN1iAQtxuOLnLOlyJbJmU7Q&s" width="15"><span class="text-new-primary">25000</span>
                                </td>
                                <td>
                                    <span class="badge-new bg-opacity-win text-win rounded">Enabled</span>
                                </td>
                                <td>
                                    <div class="d-inline-block">
                                        <ul class="mb-0 d-flex gap-10">
                                            <li>
                                                <a href="" title="Transaction Summery">
                                                    <i class="iconsax icon text-osool fs-18" icon-name="bar-graph-4"></i>
                                                </a>
                                            </li>
                                            <li>
                                                <a href="javascript:void(0);" data-toggle="modal" data-target="#edit-account" wire:click="openEditModal(588)">
                                                    <i class="iconsax icon text-new-primary fs-18" icon-name="edit-1"></i>
                                                </a>
                                            </li>
                                            <li>
                                                <a href="javascript:void(0);" data-toggle="modal" data-target="#delete-vendor">
                                                    <i class="iconsax icon text-delete fs-18" icon-name="trash"></i>
                                                </a>
                                            </li>
                                            <li>
                                                <a href="javascript:void(0);" data-toggle="modal" data-target="#create-vendor">
                                                    <i class="iconsax icon text-delete fs-18" icon-name="eye"></i>
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                </td>
                            </tr>
                            <tr class="ui-sortable-handle">
                                <td>
                                    <div class="d-flex userDatatable-content mb-0 align-items-center">
                                        <span>1011</span>
                                    </div>
                                    
                                </td>
                                <td>
                                    <div class="d-flex userDatatable-content mb-0 align-items-center">
                                       <a href="#"> <span>Checking Account</span> </a>
                                    </div>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center gap-10"><span class="">Current Asset</span>
                                </div>
                                </td>
                                <td>
                                    <div class="d-flex userDatatable-content mb-0 align-items-center">
                                        <span>1</span>
                                    </div>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center gap-10">
                                    <img src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSx34eO8RbZlsetN1iAQtxuOLnLOlyJbJmU7Q&s" width="15"><span class="text-new-primary">25000</span>
                                </td>
                                <td>
                                    <span class="badge-new bg-opacity-loss text-loss rounded">Disabled</span>
                                </td>
                                <td>
                                    <div class="d-inline-block">
                                        <ul class="mb-0 d-flex gap-10">
                                            <li>
                                                <a href="" title="Transaction Summery">
                                                    <i class="iconsax icon text-osool fs-18" icon-name="bar-graph-4"></i>
                                                </a>
                                            </li>
                                            <li>
                                                <a href="javascript:void(0);" data-toggle="modal" data-target="#edit-account" wire:click="openEditModal(588)">
                                                    <i class="iconsax icon text-new-primary fs-18" icon-name="edit-1"></i>
                                                </a>
                                            </li>
                                            <li>
                                                <a href="javascript:void(0);" data-toggle="modal" data-target="#delete-vendor">
                                                    <i class="iconsax icon text-delete fs-18" icon-name="trash"></i>
                                                </a>
                                            </li>
                                            <li>
                                                <a href="javascript:void(0);" data-toggle="modal" data-target="#create-vendor">
                                                    <i class="iconsax icon text-delete fs-18" icon-name="eye"></i>
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>




        <div class="card-body pt-0">
<div class="d-flex justify-content-md-between flex-column flex-md-row justify-content-center align-items-center mt-4 gap-10">
    <div class="">
        <ul class="atbd-pagination d-flex justify-content-between">
            <li>
                <div class="paging-option">
                    <div class="dataTables_length d-flex">
                        <label class="d-flex align-items-center mb-0">
                            <select aria-controls="workorder_table" wire:model.live.debounce.250ms="perPage" class="custom-select custom-select-sm form-control form-control-sm mx-2" style="min-height: 35px;">
                                <option value="5">5</option>
                                <option value="10">10</option>
                                <option value="25">25</option>
                                <option value="50">50</option>
                                <option value="100">100</option>
                            </select>
                           <span class="no-wrap"> Entries Per Page </span>
                        </label>
                    </div>
                </div>
            </li>
        </ul>
    </div>
    <div class="">
        <div class="user-pagination">
            <div class="user-pagination new-pagination">
                <div class="d-flex justify-content-sm-end justify-content-end">
                    <nav>
                        <span class="relative z-0 inline-flex rounded-md d-flex new-pagination-section flex-wrap">
    <span class="">
        <span aria-disabled="true" aria-label="&amp;laquo; Previous">
            <button class="border-0 disabled" aria-hidden="true" disabled="">
                <i class="iconsax icon-bold fs-16 mr-0 rotate-ar-y d-inline-block" icon-name="chevron-left"></i>
            </button>
        </span>
    </span>

    <span wire:key="paginator-page-1-page1">
        <button class="border-0 current-page" disabled="">1</button>
    </span>
    <span wire:key="paginator-page-1-page2">
        <button type="button" class="border-0">2</button>
    </span>
    <span wire:key="paginator-page-1-page3">
        <button type="button" class="border-0">3</button>
    </span>
    <span wire:key="paginator-page-1-page4">
        <button type="button" class="border-0">4</button>
    </span>
    <span wire:key="paginator-page-1-page5">
        <button type="button" class="border-0">5</button>
    </span>
    <span wire:key="paginator-page-1-page6">
        <button type="button" class="border-0">6</button>
    </span>
    <span wire:key="paginator-page-1-page7">
        <button type="button" class="border-0">7</button>
    </span>
    <span wire:key="paginator-page-1-page8">
        <button type="button" class="border-0">8</button>
    </span>
    <span wire:key="paginator-page-1-page9">
        <button type="button" class="border-0"> 9 </button>
    </span>

    <span>
        <button type="button" class="border-0">
            <i class="iconsax icon-bold fs-16 mr-0 rotate-ar-y d-inline-block" icon-name="chevron-right"></i>
        </button>
    </span>
</span>

                    </nav>
                </div>
            </div>
        </div>
    </div>
    <div>
        <p class="text-sm text-gray-700 leading-5 mb-0">
                        <span>Showing</span>
                        <span class="font-medium">1</span>
                        <span>to</span>
                        <span class="font-medium">6</span>
                        <span>of</span>
                        <span class="font-medium">52</span>
                        <span>results</span>
                    </p>
    </div>
</div>
        </div>
    </div>


    <div class="card mb-3">
        <div class="">
            <div class="card-header py-4 px-3 border-0 d-flex justify-content-between align-items-center">
                <h6 class="text-capitalize text-osool fw-500 mb-3 mb-sm-0">Equity</h6>

                <!-- <div class="d-flex gap-10 table-search">
                    <div class="position-relative">
                    <input type="text" class="form-control" placeholder="Search">
                    <i class="iconsax field-icon fs-18 mr-0" icon-name="search-normal-2"></i>
                </div>
                     <button class="btn btn-export text-dark"><i class="iconsax icon fs-22 mr-0" icon-name="upload-1"></i> Export</button>
                </div> -->
            </div>
        </div>
        <div class="card-body px-0 pt-0">
            <div class="userDatatable projectDatatable project-table bg-white w-100 border-0">
                <div class="table-responsive">
                    <table class="table mb-0 radius-0 th-osool">
                        <thead>
                            <tr class="userDatatable-header">
                                 <th>
                                   CODE
                                </th>
                                <th>
                                   <i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i>  Name
                                </th>
                                <th>
                                   <i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i>  Type
                                </th>
                                <th>
                                    <i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i> Parent Account Type
                                </th>
                                <th>
                                   <i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i>  Balance
                                </th>
                                <th>
                                   <i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i>  Status
                                </th>
                                <th>
                                    Action
                                </th>
                            </tr>
                        </thead>
                        <tbody class="sort-table ui-sortable">
                            <tr class="ui-sortable-handle">
                                <td>
                                    <div class="d-flex userDatatable-content mb-0 align-items-center">
                                        <span>1011</span>
                                    </div>
                                    
                                </td>
                                <td>
                                    <div class="d-flex userDatatable-content mb-0 align-items-center">
                                       <a href="#"> <span>Checking Account</span> </a>
                                    </div>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center gap-10"><span class="">Current Asset</span>
                                </div>
                                </td>
                                <td>
                                    <div class="d-flex userDatatable-content mb-0 align-items-center">
                                        <span>1</span>
                                    </div>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center gap-10">
                                    <img src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSx34eO8RbZlsetN1iAQtxuOLnLOlyJbJmU7Q&s" width="15"><span class="text-new-primary">25000</span>
                                </td>
                                <td>
                                    <span class="badge-new bg-opacity-win text-win rounded">Enabled</span>
                                </td>
                                <td>
                                    <div class="d-inline-block">
                                        <ul class="mb-0 d-flex gap-10">
                                            <li>
                                                <a href="" title="Transaction Summery">
                                                    <i class="iconsax icon text-osool fs-18" icon-name="bar-graph-4"></i>
                                                </a>
                                            </li>
                                            <li>
                                                <a href="javascript:void(0);" data-toggle="modal" data-target="#edit-account" wire:click="openEditModal(588)">
                                                    <i class="iconsax icon text-new-primary fs-18" icon-name="edit-1"></i>
                                                </a>
                                            </li>
                                            <li>
                                                <a href="javascript:void(0);" data-toggle="modal" data-target="#delete-vendor">
                                                    <i class="iconsax icon text-delete fs-18" icon-name="trash"></i>
                                                </a>
                                            </li>
                                            <li>
                                                <a href="javascript:void(0);" data-toggle="modal" data-target="#create-vendor">
                                                    <i class="iconsax icon text-delete fs-18" icon-name="eye"></i>
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                </td>
                            </tr>
                            <tr class="ui-sortable-handle">
                                <td>
                                    <div class="d-flex userDatatable-content mb-0 align-items-center">
                                        <span>1011</span>
                                    </div>
                                    
                                </td>
                                <td>
                                    <div class="d-flex userDatatable-content mb-0 align-items-center">
                                       <a href="#"> <span>Checking Account</span> </a>
                                    </div>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center gap-10"><span class="">Current Asset</span>
                                </div>
                                </td>
                                <td>
                                    <div class="d-flex userDatatable-content mb-0 align-items-center">
                                        <span>1</span>
                                    </div>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center gap-10">
                                    <img src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSx34eO8RbZlsetN1iAQtxuOLnLOlyJbJmU7Q&s" width="15"><span class="text-new-primary">25000</span>
                                </td>
                                <td>
                                    <span class="badge-new bg-opacity-loss text-loss rounded">Disabled</span>
                                </td>
                                <td>
                                    <div class="d-inline-block">
                                        <ul class="mb-0 d-flex gap-10">
                                            <li>
                                                <a href="" title="Transaction Summery">
                                                    <i class="iconsax icon text-osool fs-18" icon-name="bar-graph-4"></i>
                                                </a>
                                            </li>
                                            <li>
                                                <a href="javascript:void(0);" data-toggle="modal" data-target="#edit-account" wire:click="openEditModal(588)">
                                                    <i class="iconsax icon text-new-primary fs-18" icon-name="edit-1"></i>
                                                </a>
                                            </li>
                                            <li>
                                                <a href="javascript:void(0);" data-toggle="modal" data-target="#delete-vendor">
                                                    <i class="iconsax icon text-delete fs-18" icon-name="trash"></i>
                                                </a>
                                            </li>
                                            <li>
                                                <a href="javascript:void(0);" data-toggle="modal" data-target="#create-vendor">
                                                    <i class="iconsax icon text-delete fs-18" icon-name="eye"></i>
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>




        <div class="card-body pt-0">
<div class="d-flex justify-content-md-between flex-column flex-md-row justify-content-center align-items-center mt-4 gap-10">
    <div class="">
        <ul class="atbd-pagination d-flex justify-content-between">
            <li>
                <div class="paging-option">
                    <div class="dataTables_length d-flex">
                        <label class="d-flex align-items-center mb-0">
                            <select aria-controls="workorder_table" wire:model.live.debounce.250ms="perPage" class="custom-select custom-select-sm form-control form-control-sm mx-2" style="min-height: 35px;">
                                <option value="5">5</option>
                                <option value="10">10</option>
                                <option value="25">25</option>
                                <option value="50">50</option>
                                <option value="100">100</option>
                            </select>
                           <span class="no-wrap"> Entries Per Page </span>
                        </label>
                    </div>
                </div>
            </li>
        </ul>
    </div>
    <div class="">
        <div class="user-pagination">
            <div class="user-pagination new-pagination">
                <div class="d-flex justify-content-sm-end justify-content-end">
                    <nav>
                        <span class="relative z-0 inline-flex rounded-md d-flex new-pagination-section flex-wrap">
    <span class="">
        <span aria-disabled="true" aria-label="&amp;laquo; Previous">
            <button class="border-0 disabled" aria-hidden="true" disabled="">
                <i class="iconsax icon-bold fs-16 mr-0 rotate-ar-y d-inline-block" icon-name="chevron-left"></i>
            </button>
        </span>
    </span>

    <span wire:key="paginator-page-1-page1">
        <button class="border-0 current-page" disabled="">1</button>
    </span>
    <span wire:key="paginator-page-1-page2">
        <button type="button" class="border-0">2</button>
    </span>
    <span wire:key="paginator-page-1-page3">
        <button type="button" class="border-0">3</button>
    </span>
    <span wire:key="paginator-page-1-page4">
        <button type="button" class="border-0">4</button>
    </span>
    <span wire:key="paginator-page-1-page5">
        <button type="button" class="border-0">5</button>
    </span>
    <span wire:key="paginator-page-1-page6">
        <button type="button" class="border-0">6</button>
    </span>
    <span wire:key="paginator-page-1-page7">
        <button type="button" class="border-0">7</button>
    </span>
    <span wire:key="paginator-page-1-page8">
        <button type="button" class="border-0">8</button>
    </span>
    <span wire:key="paginator-page-1-page9">
        <button type="button" class="border-0"> 9 </button>
    </span>

    <span>
        <button type="button" class="border-0">
            <i class="iconsax icon-bold fs-16 mr-0 rotate-ar-y d-inline-block" icon-name="chevron-right"></i>
        </button>
    </span>
</span>

                    </nav>
                </div>
            </div>
        </div>
    </div>
    <div>
        <p class="text-sm text-gray-700 leading-5 mb-0">
                        <span>Showing</span>
                        <span class="font-medium">1</span>
                        <span>to</span>
                        <span class="font-medium">6</span>
                        <span>of</span>
                        <span class="font-medium">52</span>
                        <span>results</span>
                    </p>
    </div>
</div>
        </div>
    </div>

    
</div>



</div>


<div class="modal fade" id="delete-vendor" tabindex="-1" role="dialog" aria-labelledby="deleteUserModalLabel" aria-hidden="true">
                    <div class="modal-dialog modal-sm" role="document">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="deleteUserModalLabel">Delete Vendor</h5>
                                <button type="button" class="close" data-dismiss="modal" aria-label="{{ __('user_management_module.modal.close') }}">
                                    <span aria-hidden="true">&times;</span>
                                </button>
                            </div>
                            <div class="modal-body">
                                <div class="text-center">
                                     <i class="iconsax icon text-loss fs-60" icon-name="warning-triangle"></i>
                                    <p class="mt-4">Are you sure you want to delete <br> the vendor <strong id="deleteUserName"> Abdul Rehman ?</strong></p>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                                <form id="deleteUserForm"  method="POST">
                                    <button type="submit" class="btn btn-danger">Yes, Delete It</button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>





<div class="modal fade new-modal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true" id="create-vendor">
<div class="modal-dialog radius-xl modal-md" role="document">
    <div class="modal-content radius-xl">
        <div class="modal-header">
            <h6 class="modal-title" id="exampleModalLabel">Create New Account</h6>
            <button wire:ignore="" type="button" class="close" data-dismiss="modal" aria-label="Close">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-x">
                    <line x1="18" y1="6" x2="6" y2="18"></line>
                    <line x1="6" y1="6" x2="18" y2="18"></line>
                </svg>
            </button>
        </div>
        <form wire:submit.prevent="create">
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-12">
                        <div class="form-group">
                            <label for="name" class="form-label">Name</label> <span class="text-danger">*</span>
                            <input class="form-control" placeholder="Enter Name" wire:model.defer="name" type="text" id="name" />
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="email" class="form-label">Code</label> <span class="text-danger">*</span>
                            <input class="form-control" placeholder="Enter Code" type="number" id="code" />
                        </div>
                    </div>
                     <div class="col-md-6">
                        <div class="form-group">
                            <label for="email" class="form-label">Code</label> <span class="text-danger">*</span>
                            <select class="form-control select" required="required" id="account_type" name="sub_type">
                                <option selected="selected" value="">Please select</option>
                                <optgroup label="Assets">
                                    <optgroup label="Current Assets">
                                        <option value="326">Cash & Cash Equivalents</option>
                                        <option value="327">Accounts Receivable</option>
                                        <option value="328">Inventory</option>
                                        <option value="328">Prepaid Expenses</option>
                                    </optgroup> 
                                    <optgroup label="Non-Current Assets:">
                                        <option value="326">Property, Plant & Equipment (PPE)</option>
                                        <option value="327">Intangible Assets</option>
                                        <option value="328">Long-term Investments</option>
                                    </optgroup> 
                                </optgroup>
                                <optgroup label="Liabilities">
                                    <optgroup label=" Current Liabilities:">
                                        <option value="326">Accounts Payable</option>
                                        <option value="327">Short-term Loans</option>
                                        <option value="328">Accrued Expenses</option>
                                    </optgroup> 
                                    <optgroup label="Non-Current Liabilities:">
                                        <option value="326">Long-term Loans</option>
                                        <option value="327">Bonds Payable</option>
                                        <option value="328">Lease Liabilities</option>
                                    </optgroup> 
                                </optgroup>
                                <optgroup label="Equity">
                                    <option value="333">Capital</option>
                                    <option value="333">Retained Earnings</option>
                                    <option value="333">Common Stock</option>
                                    <option value="333">Preferred Stock</option>
                                </optgroup>
                                <optgroup label="Revenue">
                                    <option value="334">Sales Revenue</option>
                                    <option value="334">Service Revenue</option>
                                    <option value="335">Other Revenue</option>
                                </optgroup>
                                <optgroup label="Costs of Goods Sold">
                                    <option value="336">Costs of Goods Sold</option>
                                </optgroup>
                                <optgroup label="Expenses">
                                    <option value="337"> Cost of Goods Sold (COGS)</option>
                                    <option value="338"> Salaries & Wages</option>
                                    <option value="337">    Rent Expense</option>
                                    <option value="338"> Administrative Expenses</option>
                                    <option value="337">Depreciation & Amortization</option>
                                </optgroup>
                            </select>

                        </div>
                    </div>
                    <div class="col-md-6 mt-2">
                        <div class="form-group">
                            <label for="phone" class="form-label">Is Enable</label>
                            <label class="switch">      
                                <label class="switch"><input type="checkbox" name="fields[services_assets_enabled]" id="services_assets_toggle" class="optional-checkbox" checked=""><span class="slider round"></span></label>
                            </label>
                        </div>
                    </div>
                    <div class="col-md-12">
                        <div class="form-group">
                            <label for="website" class="form-label">Description</label>
                            <textarea class="form-control textarea" placeholder="Enter Description"></textarea>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn bg-hold-light text-white radius-xl" data-dismiss="modal">
                    Cancel
                </button>
                <button type="submit" class="btn bg-new-primary radius-xl">
                    Create
                </button>
            </div>
        </form>
    </div>
</div>
</div>


<div class="modal fade new-modal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true" id="edit-account">
<div class="modal-dialog radius-xl modal-md" role="document">
    <div class="modal-content radius-xl">
        <div class="modal-header">
            <h6 class="modal-title" id="exampleModalLabel">Create New Account</h6>
            <button wire:ignore="" type="button" class="close" data-dismiss="modal" aria-label="Close">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-x">
                    <line x1="18" y1="6" x2="6" y2="18"></line>
                    <line x1="6" y1="6" x2="18" y2="18"></line>
                </svg>
            </button>
        </div>
        <form wire:submit.prevent="create">
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="name" class="form-label">Name</label> <span class="text-danger">*</span>
                            <input class="form-control" placeholder="Enter Name" wire:model.defer="name" type="text" id="name" />
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="email" class="form-label">Code</label> <span class="text-danger">*</span>
                            <input class="form-control" placeholder="Enter Code" type="number" id="code" />
                        </div>
                    </div>
                    <div class="col-md-6 mt-2">
                        <div class="form-group">
                            <label for="phone" class="form-label">Is Enable</label>
                            <label class="switch">      
                                <label class="switch"><input type="checkbox" name="fields[services_assets_enabled]" id="services_assets_toggle" class="optional-checkbox" checked=""><span class="slider round"></span></label>
                            </label>
                        </div>
                    </div>
                    <div class="col-md-12">
                        <div class="form-group">
                            <label for="website" class="form-label">Description</label>
                            <textarea class="form-control textarea" placeholder="Enter Description"></textarea>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn bg-hold-light text-white radius-xl" data-dismiss="modal">
                    Cancel
                </button>
                <button type="submit" class="btn bg-new-primary radius-xl">
                    Create
                </button>
            </div>
        </form>
    </div>
</div>
</div>



</div>

@endsection

@section('scripts')
<script type="text/javascript">
    $(".datepicker").datepicker();
    $("#account_type").select2();
    $(document).ready(function() {
      // Toggle active class and manage dropdown on button click
      $('.filter-button').on('click', function (e) {
        e.stopPropagation(); // Prevent the click from bubbling up to the document
        $(this).toggleClass('active'); // Toggle the 'active' class
        $(this).closest('.dropdown').find(".dropdown-menu").toggleClass('show'); // Toggle the Bootstrap dropdown
      });

      // Remove active class and close dropdown when clicking outside
      $(document).on('click', function (e) {
        if (!$(e.target).closest('.dropdown').length) {
          // If the click is outside the dropdown, remove the 'active' class and close the dropdown
          $('.filter-button').removeClass('active');
          $('.dropdown-menu').removeClass('show');
          $('.filter-button').attr('aria-expanded', 'false');
        }
      });

      // Prevent dropdown from closing when clicking inside the dropdown menu
      $('.dropdown-menu').on('click', function (e) {
        e.stopPropagation();
      });
    });
</script>
@endsection
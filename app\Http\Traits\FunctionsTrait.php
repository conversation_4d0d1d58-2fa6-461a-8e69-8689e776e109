<?php
    namespace App\Http\Traits;
    use Illuminate\Support\Facades\Log;
    use Illuminate\Support\Facades\Crypt;
    use Illuminate\Support\Facades\Storage;
    use Illuminate\Support\Facades\Session;
    use Illuminate\Support\Facades\Hash;
    use Illuminate\Support\Facades\Route;
    use Illuminate\Support\Str;
    use Illuminate\Support\Facades\Cache;
    use Illuminate\Support\Facades\Request;
    use Intervention\Image\Facades\Image;
    use Illuminate\Pagination\LengthAwarePaginator;
    use SimpleSoftwareIO\QrCode\Facades\QrCode;
    use ArPHP\I18N\Arabic;
    use Carbon\Carbon;
    use DateTime;
    use App;
    use Auth;
    use App\Enums\Language;

    trait FunctionsTrait{
        public function valueIsRequired($value){
            try {
                return (empty($value) || is_null($value));
            } 
            
            catch (\Throwable $th) {
                Log::error("valueIsRequired error: ".$th);
            }
        }

        public function decryptCryptedString($string) {
            try {
                return Crypt::decryptString($string);
            } 
            
            catch (DecryptException $th) {
                Log::error("decryptCryptedString Error: ".$th);
            }
        }

        public function encryptDecryptedString($string) {
            try {
                return Crypt::encryptString($string);
            } 
            
            catch (\Throwable $th) {
                Log::error("encryptDecryptedString Error: ".$th);
            }
        }

        public function getCurrentDate() {
            try {
                return date('d-m-Y');
            } 
            
            catch (\Throwable $th) {
                Log::error("getCurrentDate Error: ".$th);
            }
        }

        public function getCurrentDateWithCarbon() {
            try {
                return Carbon::now()->toDateString();
            } 
            
            catch (\Throwable $th) {
                Log::error("getCurrentDateWithCarbon Error: ".$th);
            }
        }

        public function explodeDataFromField($field) {
            try {
                return explode(',', $field);
            } 
            
            catch (\Throwable $th) {
                Log::error("explodeDataFromField Error: ".$th);
            }
        }

        public function implodeDataFromField($field) {
            try {
                return implode(',', $field);
            } 
            
            catch (\Throwable $th) {
                Log::error("implodeDataFromField Error: ".$th);
            }
        }

        public function getReportsPath() {
            try {
                return public_path('reports');
            } 
            
            catch (\Throwable $th) {
                Log::error("getReportsPath Error: ".$th);
            }
        }

        public function getCurrentTime() {
            try {
                return time();
            } 
            
            catch (\Throwable $th) {
                Log::error("getCurrentTime Error: ".$th);
            }
        }
        
        public function setDataToJsonEncode($data) {
            try {
                return json_encode($data);
            } 
            
            catch (\Throwable $th) {
                Log::error("setDataToJsonEncode Error: ".$th);
            }
        }

        public function getCountOfItemsInsideArray($array) {
            try {
                return count($array);
            } 
            
            catch (\Throwable $th) {
                Log::error("getCountOfItemsInsideArray Error: ".$th);
            }
        }

        public function changeDateFormat($format, $date) {
            try {
                return date($format, strtotime($date)); 
            } 
            
            catch (\Throwable $th) {
                Log::error("changeDateFormat Error: ".$th);
            }
        }

        public function generateRandomNumber($min, $max) {
            try {
                return random_int($min, $max);
            } 
            
            catch (\Throwable $th) {
                Log::error("generateRandomNumber Error: ".$th);
            }
        }

        public function generateNumber2() {
            try {
                return rand(1000, 9999);
            } 
            
            catch (\Throwable $th) {
                Log::error("generateNumber2 Error: ".$th);
            }
        }

        public function getCurrentDateTime() {
            try {
                return date('Y-m-d H:i:s');
            } 
            
            catch (\Throwable $th) {
                Log::error("getCurrentDateTime error: ".$th);
            }
        }

        public function changeLinkFormat($old, $new, $value) {
            try {
                return str_replace($old, $new, $value);
            } 
            
            catch (\Throwable $th) {
                Log::error("changeLinkFormat Error: ".$th);
            }
        }

        public function arabicConversion($value) {
            try {
                $arabic = new Arabic();
                $p = $arabic->arIdentify($value);

                for ($i = count($p)-1; $i >= 0; $i-=2) {
                    try{
                        $utf8ar = $arabic->utf8Glyphs(substr($value, $p[$i-1], $p[$i] - $p[$i-1]));
                        $value = substr_replace($value, $utf8ar, $p[$i-1], $p[$i] - $p[$i-1]);
                    }

                    catch (\Throwable $th) {
                        Log::error("inside arabicConversion Error: ".$th);
                    }
                }

                return $value;
            } 
            
            catch (\Throwable $th) {
                Log::error("arabicConversion Error: ".$th);
            }
        }

        public function storeFolder($path, $permission, $state) {
            try {
                return mkdir($path, $permission, $state);
            } 
            
            catch (\Throwable $th) {
                Log::error("storeFolder Error: ".$th);
            }
        }

        public function changeLanguageFunction($value) {
            try {
                if($value == 'ar'){
                    try {
                        App::setLocale('ar');
                        app()->setLocale('ar');
                    }

                    catch (\Throwable $th) {
                        Log::error("inside changeLanguageFunction Error: ".$th);
                    }
                }

                else{
                    try {
                      App::setLocale('en');
                    }

                    catch (\Throwable $th) {
                        Log::error("inside changeLanguageFunction Error: ".$th);
                    }
                } 
            }
            
            catch (\Throwable $th) {
                Log::error("changeLanguage Error: ".$th);
            }
        }

        public function getCountListOfData($list) {
            try {
                return count($list);
            } 
            
            catch (\Throwable $th) {
                Log::error("getCountListOfData Error: ".$th);
            }
        }

        public function generateDateTime($value) {
            try {
                $dStart = new DateTime($value);
                $currentDate = $this->getCurrentDateTime();
                $dEnd  = new DateTime($currentDate); 
                return $dStart->diff($dEnd)->format('%r%a');
            } 
            
            catch (\Throwable $th) {
                Log::error("generateDateTime Error: ".$th);
            }
        }

        public function generateTime($value, $hours) {
            try {
                // Convert inputs to DateTime objects
                $date1 = new DateTime($value);
                $currentDate = $this->getCurrentDateTime();
                $date2 = new DateTime($currentDate);
        
                // Calculate the difference in hours
                $diff = $date2->diff($date1);
        
                // Ensure $hours and $diff->h are numeric
                $newhours = (int)$hours - (int)$diff->h;
        
                return $newhours;
            } 
            catch (\Throwable $th) {
                Log::error("generateTime Error: ".$th);
            }
        }        

        public function addMinutesToDate($date, $added_time) {
            try {
                $dateObject = Carbon::parse($date);
                return $dateObject->addMinutes($added_time);
            } 
            
            catch (\Throwable $th) {
                Log::error("addMinutesToDate Error: ".$th);
            }
        }

        public function getCurrentDateTimeWithCarbon() {
            try {
                return Carbon::now();
            } 
            
            catch (\Throwable $th) {
                Log::error("getCurrentDateTimeWithCarbon Error: ".$th);
            }
        }

        public function hundleApiBug($exception){
            try {
                Log::error(json_encode($exception));
            } catch (\Throwable $th) {
                Log::error("hundleApiBug Error: ".$th);
            }
        }

        public function convertDataToObject($code, $message_systeme, $data){
            try {
                return array(
                    "ResultCode" => $code,
                    "ResultMessage" => $message_systeme,
                    "ResultObject" => $data
                );
            }
            catch (\Throwable $th) {
                Log::error("convertDataToObject Error: ".$th);
            }
        }

        public function getResultMessage($message_systeme){
            try {
                return array(
                    "text_en" => $message_systeme->text_en,
                    "text_ar" => $message_systeme->text_ar,
                );
            }
            catch (\Throwable $th) {
                Log::error("getResultMessage Error: ".$th);
            }
        }

        public function hashValue($value) {
            try {
                return Hash::make($value);
            } 
            
            catch (\Throwable $th) {
                Log::error("hashValue Error: ".$th);
            }
        }

        public function createDataSession($key, $value) {
            try {
                return Session::put($key, $value);
            } 
            
            catch (\Throwable $th) {
                Log::error("createDataSession Error: ".$th);
            }
        }

        public function getDataSession($key) {
            try {
                return Session::get($key); 
            } 
            
            catch (\Throwable $th) {
                Log::error("getDataSession error: ".$th);
            }
        }

        public function clearDataSession($key) {
            try {
                return Session::forget($key); 
            } 
            
            catch (\Throwable $th) {
                Log::error("clearSession Error: ".$th);
            }
        }

        public function cryptToken($token) {
            try {
                return Crypt::encrypt([
                    'token' => $token
                ]);
            } 
            
            catch (\Throwable $th) {
                Log::error("cryptProjectId Error: ".$th);
            }
        }

        public function decryptToken($token) {
            try {
                return Crypt::decrypt($token);
            } 
            
            catch (\Throwable $th) {
                Log::error("decryptToken Error: ".$th);
            }
        }

        public function convertValueToInt($value) {
            try {
                return (int) $value;
            } 
            
            catch (\Throwable $th) {
                Log::error("convertValueToInt Error: ".$th);
            }
        }

        public function substrictionOperation($value1, $value2) {
            try {
                return $value1 - $value2;
            } 
            
            catch (\Throwable $th) {
                Log::error("substrictionValues Error: ".$th);
            }
        }

        public function divisionOperation($value1, $value2) {
            try {
                return $value1 / $value2;
            } 
            
            catch (\Throwable $th) {
                Log::error("divisionOperation Error: ".$th);
            }
        }

        public function multiplicationOperation($value1, $value2) {
            try {
                return $value1 * $value2;
            } 
            
            catch (\Throwable $th) {
                Log::error("multiplicationOperation Error: ".$th);
            }
        } 

        public function clearBulkImportFileSession() {
            try {
                $this->clearDataSession("users");
                $this->clearDataSession("priorities");
                $this->clearDataSession("services");
                $this->clearDataSession("properties");
                $this->clearDataSession("propertiesBuilding");
                $this->clearDataSession("assets");
            } 
            
            catch (\Throwable $th) {
                Log::error("clearBulkImportFileSession Error: ".$th);
            }
        }

        public function cryptImportToken($id_project, $batch) {
            try {
                return Crypt::encrypt([
                    'id_project' => $id_project,
                    'batch' => $batch
                ]);
            } 
            
            catch (\Throwable $th) {
                Log::error("cryptImportToken Error: ".$th);
            }
        }

        public function cryptTempBulkToken($projectId, $batch, $tempId) {
            try {
                return Crypt::encrypt([
                    'projectId' => $projectId,
                    'batch' => $batch,
                    'tempId' => $tempId
                ]);
            } 
            
            catch (\Throwable $th) {
                Log::error("cryptTempBulkToken Error: ".$th);
            }
        }

        public function createFileInStorage($filename, $folder) {
            try {
                return Storage::disk('public')->put($folder, $filename);
            } 
            
            catch (\Throwable $th) {
                Log::error("createFileInStorage Error: ".$th);
            }
        }

        public function deleteFileFromStorage($filename) {
            try {
                return Storage::disk('public')->delete($filename);
            } 
            
            catch (\Throwable $th) {
                Log::error("deleteFileFromStorage Error: ".$th);
            }
        }

        public function getJsonCount($value) {
            try {
                return count($value);
            } 
            
            catch (\Throwable $th) {
                Log::error("getJsonCount Error: ".$th);
            }
        }

        public function cryptViewData($tempId, $type, $projectId) {
            try {
                return Crypt::encrypt([
                    'tempId' => $tempId,
                    'type' => $type,
                    'projectId' => $projectId
                ]);
            } 
            
            catch (\Throwable $th) {
                Log::error("cryptViewData Error: ".$th);
            }
        }

        public function customPagination($items, $perPage, $page = null, $options = []) {
            try {
                $page = $page ?: (LengthAwarePaginator::resolveCurrentPage() ?: 1);
                $items = collect($items);
                return new LengthAwarePaginator($items->forPage($page, $perPage), $items->count(), $perPage, $page, $options);
            } 
            
            catch (\Throwable $th) {
                Log::error("customPagination Error: ".$th);
            }
        }

        public function manageArrayData($array, $column) {
            try {
                $uniqueIds = [];
                $finalResult = [];
                
                if(!is_null($array)){
                    foreach ($array as $item) {
                        if ($item[$column] !== null && !in_array($item[$column], $uniqueIds)) {
                            $uniqueIds[] = $item[$column];
                            $finalResult[] = $item;
                        }
                    }
                }
    
                return $finalResult;
            } 
            
            catch (\Throwable $th) {
                Log::error("manageArrayData error: ".$th);
            }
        }

        public function getCurrentDateWithChangedFormat($format) {
            try {
                return date($format);
            } 
            
            catch (\Throwable $th) {
                Log::error("getCurrentDateWithChangedFormat Error: ".$th);
            }
        }

        public function getCurrentDateTimeWithCarbonBeforeDays($value) {
            try {
                return $this->getCurrentDateTimeWithCarbon()->subDays($value);
            } 
            
            catch (\Throwable $th) {
                Log::error("getCurrentDateTimeWithCarbonBeforeDays Error: ".$th);
            }
        }

        public function getAuthenticatedUser() {
            try {
                return Auth::user();
            } 
            
            catch (\Throwable $th) {
                Log::error("getAuthenticatedUser error: ".$th);
            }
        }

        public function getLocalLanguage() {
            try {
                return App::getLocale();
            } 
            
            catch (\Throwable $th) {
                Log::error("getLocalLanguage Error: ".$th);
            }
        }

        public function setDateTimeFormatByLanguage($language, $value) {
            try {
                return $language == 'en' ? date('h:i a • d M Y',strtotime($value)) : date('d M Y • h:i a',strtotime($value));
            } 
            
            catch (\Throwable $th) {
                Log::error("setDateTimeFormatByLanguage Error: ".$th);
            }
        }

        public function goToRouteName($route) {
            try {
                return redirect()->route($route);
            } 
            
            catch (\Throwable $th) {
                Log::error("goBack Error: ".$th);
            }
        }


        function pushKeyValuePair(&$array, $key, $value) {
            try {
                $newElement = [$key => $value];
                return array_push($array, $newElement);
            }catch (\Throwable $th) {
                Log::error("pushKeyValuePair Error: ".$th);
            }
        }
        

        public function getDecodedJson($data){
            try {
                return json_decode($data);
            } 
            
            catch (\Throwable $th) {
                Log::error("getDecodedJson Error: ".$th);
            }
        }

        public function checkUserPrivileges($privilegeName = '', $privilegeSectionName = '', $shouldBeTrue = true, $user) {
            try {
                $res = [
                    'success' => true,
                    'message' => '',
                    'redirect_url' => null,
                ];

                $userTypes = '';

                if ($this->isRouteSpecificRestriction('property.create', ['building_manager', 'building_manager_employee', 'supervisor', 'sp_admin'], $user)) {
                    $res['success'] = false;
                    $res['redirect_url'] = redirect()->route('admin.dashboard')
                        ->with('message', 'You are not authorized to access that page.')
                        ->with('type', 'warning');
                    return $res;
                }

                $userTypes = $userTypes ? (is_array($userTypes) ? $userTypes : [$userTypes]) : ['admin_employee', 'building_manager', 'building_manager_employee', 'supervisor', 'sp_admin'];

                if ($privilegeSectionName === 'view' && $user->user_type === 'sp_admin' && $privilegeName === "property") {
                    $res['success'] = true;
                    return $res;
                }

                if ($privilegeSectionName === 'no_view' && $user->user_type === 'sp_admin' && $privilegeName === "property") {
                    $res['success'] = false;
                    return $res;
                }

                $privilegesArr = isset($user->user_privileges) ? json_decode($user->user_privileges, true)[$privilegeSectionName] ?? [] : [];

                if (in_array($user->user_type, $userTypes) && isset($privilegesArr) && in_array($privilegeSectionName, array_keys($privilegesArr))) {
                    $res['success'] = $shouldBeTrue ? in_array($privilegeName, $privilegesArr) : !in_array($privilegeName, $privilegesArr);
                    $res['message'] = $user->user_type . ' privilege_name=' . $privilegeName . ', privileges_section_name=' . $privilegeSectionName;
                }

                if ($user->user_type === "building_manager" && $privilegeSectionName === 'tenant') {
                    if (!isset($privilegesArr['tenant'])) {
                        $res['success'] = false;
                    }
                }

                return $res;
            } 
            
            catch (\Throwable $th) {
                Log::error("checkUserPrivileges Error: ".$th);
            }
        }

        public function isRouteSpecificRestriction($routeName, $userTypes, $user){
            try {
                return Route::currentRouteName() === $routeName && in_array($user->user_type, $userTypes);
            } 
            
            catch (\Throwable $th) {
                Log::error("isRouteSpecificRestriction Error: ".$th);
            }
        }
      
        public function mergeCollections($collection1, $collection2) {
            try {
                return $collection1->merge($collection2);
            } 
            
            catch (\Throwable $th) {
                Log::error("mergeCollections Error: ".$th);
            }
        }

        public function convertStringToDeciaml($value, $number, $seperate = '') {
            try {
                return number_format($value, $number, '', $seperate);
            } 

            catch (\Throwable $th) {
                Log::info("convertStringToDeciaml error: ".$th);
            }
        }

        public function setSortByCollection($collection, $value) {
            try {
                return $collection->sortByDesc($value)->values()->all();
            } 
            
            catch (\Throwable $th) {
                Log::error("setSortByCollection Error: ".$th);
            }
        }

        public function getChartFromString($value, $number) {
            try {
                return substr($value, $number);
            } 
            
            catch (\Throwable $th) {
                Log::error("getChartFromString Error: ".$th);
            }
        }

        public function isNumericValue($value) {
            try {
                return is_numeric($value);
            } 
            
            catch (\Throwable $th) {
                Log::error("isNumericValue Error: ".$th);
            }
        }

        public function getStringLength($value) {
            try {
                return strlen($value);
            } 
            
            catch (\Throwable $th) {
                Log::error("getStringLength Error: ".$th);
            }
        }

        public function getFileExtension($file) {
            try {
                return $file->getClientOriginalExtension();
            } 
            
            catch (\Throwable $th) {
                Log::error("getFileExtension Error: ".$th);
            }
        }

        public function getFileName($file) {
            try {
                return $file->getClientOriginalName();
            } 
            
            catch (\Throwable $th) {
                Log::error("getFileName Error: ".$th);
            }
        }

        public function getFileFromPublicStorage($file) {
            try {
                return Storage::disk('public')->url($file);
            } 
            
            catch (\Throwable $th) {
                Log::error("getFileFromPublicStorage Error: ".$th);
            }
        }

        public function putFileIntoOciStorage($location, $file) {
            try {
                return Storage::disk('oci')->put($location, $file);
            } 
            
            catch (\Throwable $th) {
                Log::error("putFileIntoOciStorage Error: ".$th);
            }
        }

        public function getBaseNameFromPath($path) {
            try {
                return basename($path);
            } 
            
            catch (\Throwable $th) {
                Log::error("getBaseNameFromPath Error: ".$th);
            }
        }

        public function getFileFromStorage($file) {
            try {
                return Storage::disk('public')->get($file);
            } 
            
            catch (\Throwable $th) {
                Log::error("getFileFromStorage Error: ".$th);
            }
        }

        public function getFileFromOciStorage($file) {
            try {
                return Storage::disk('oci')->url($file);
            } 
            
            catch (\Throwable $th) {
                Log::error("getFileFromPublicStorage Error: ".$th);
            }
        }

        public function checkFileExistInOciStorage($file) {
            try {
                return Storage::disk('oci')->exists($file);
            } 
            
            catch (\Throwable $th) {
                Log::error("checkFileExistInOciStorage Error: ".$th);
            }
        }

        public function generateUid(){
            try {
                return Str::uuid();
            } 
            
            catch (\Throwable $th) {
                Log::error("generateUid Error: ".$th);
            }
        }

        public function filterArray($array) {
            try {
                return array_unique($array);
            } 
            
            catch (\Throwable $th) {
                Log::error("filterArray Error: ".$th);
            }
        }

        public function isValidEmail($email) {
            try {
                return filter_var($email, FILTER_VALIDATE_EMAIL);
            } 
            
            catch (\Throwable $th) {
                Log::error("isValidEmail Error: ".$th);
            }
        }

        public function validatePhoneNumber($phone) {
            try {
                return preg_match('/^\d{9}$/', $phone);
           } 
           
            catch (\Throwable $th) {
                Log::error("validatePhoneNumber Error: ".$th);
            }
        }

        public function checkIfNumber($value) {
            try {
                return is_numeric($value);
            } 
            
            catch (\Throwable $th) {
                Log::error("checkIfNumber error: ".$th);
            }
        }

        public function checkIfFloat($value) {
            try {
                return is_numeric($value) && strpos($value, '.');
            } 
            
            catch (\Throwable $th) {
                Log::error("checkIfFloat error: ".$th);
            }
        }

        public function checkIfValidDate($value) {
            try {
               return preg_match('/^\d{2}\/\d{2}\/\d{4}$/', $value);
            } 
            
            catch (\Throwable $th) {
                Log::error("checkIfValidDate Error: ".$th);
            }
        }

        public function changeDateFormatByCarbon($format, $date) {
            try {
                $newDate = Carbon::createFromFormat('d/m/Y', $date);
                return $newDate->format($format);
            } 
            
            catch (\Throwable $th) {
                Log::error("changeDateFormatByCarbon Error: ".$th);
            }
        }

        public function generateQrCode($format, $size, $errorCorrection, $data) {
            try {
                return QrCode::format($format)->size($size)->errorCorrection($errorCorrection)->generate($data);
            } 
            
            catch (\Throwable $th) {
                Log::error("generateQrCode error: ".$th);
            }
        }

        public function secondGenerateQrCode($size, $data) {
            try {
                return QrCode::size($size)->generate("".url('/')."/maintenance/need-help/".$data."");
            } 
            
            catch (\Throwable $th) {
                Log::error("secondGenerateQrCode error: ".$th);
            }
        }

        public function checkIfDataExistInArray($array, $searchedItem, $column) {
            try {
                return $array->contains($column, $searchedItem);
            } 
            
            catch (\Throwable $th) {
                Log::error("checkIfDataExistInArray error: ".$th);
            }
        }

        public function roundToIntNumber($value) {
            try {
                return round($value);
            } 
            
            catch (\Throwable $th) {
                Log::error("roundToIntNumber error: ".$th);
            }
        }

        public function numberFormatValue($value, $int) {
            try {
                return number_format($value, $int);
            } 
            
            catch (\Throwable $th) {
                Log::error("numberFormatValue error: ".$th);
            }
        }

        public function calculPourcentage($value, $number) {
            try {
                return number_format((float) $value, $number, '.', '');
            } 
            
            catch (\Throwable $th) {
                Log::error("calculPourcentage error: ".$th);
            }
        }

        public function addHoursToDate($date, $hours) {
            try {
                $formattedDate = $date = Carbon::parse($date);
                return $formattedDate->addHours($hours);
            } 
            
            catch (\Throwable $th) {
                Log::error("addHoursToDate error: ".$th);
            }
        }

        public function calculUsersLocation($workers, $longitude, $latitude) {
            try {
                $allDistance = [];
                $result = [];

                foreach ($workers as $row) {
                    $worker = $this->getLastWorkerLocationByValues('worker_id', $row['worker_id']);

                    if(isset($worker)){
                        $distance = \DB::select("
                        SELECT (
                            6371 * acos(
                                cos(radians(?)) * cos(radians(?)) * cos(radians(?) - radians(?)) +
                                sin(radians(?)) * sin(radians(?))
                            )
                        ) AS distance
                        FROM worker_locations
                        WHERE worker_id = ?", [
                            $latitude, 
                            $longitude, 
                            $worker->latitude, 
                            $worker->longitude, 
                            $worker->latitude, 
                            $worker->longitude, 
                            $row['worker_id'],
                        ]);

                        array_push($allDistance, ['distance' => $distance[0]->distance, 'workerId' => $row['worker_id']]);
                    }
                }

                if(isset($allDistance) && count($allDistance) > 0){
                    $collection = collect($allDistance);

                    if(isset($collection) && $collection->count() == 1){
                        $result = [
                            'distance' => $collection[0]['distance'],
                            'workerId' => $collection[0]['workerId']
                        ];
                    }
                    
                    elseif(isset($collection) && $collection->count() == 0){
                        $result = [];
                    }

                    else{
                        $collection = $collection->sortByDesc('distance');
                        $firstLocation = $collection[0]['distance'];
                        $secondLocation = $collection[1]['distance'];

                        if($firstLocation == $secondLocation){
                            $result = [];
                        }

                        elseif($firstLocation > $secondLocation){
                            $result = [
                                'distance' => $firstLocation,
                                'workerId' => $collection[0]['workerId']
                            ];
                        }

                        else{
                            $result = [
                                'distance' => $secondLocation,
                                'workerId' => $collection[1]['workerId']
                            ];
                        }
                    }
                }

                else{
                    $result = []; 
                }

                return $result;
            } 
            
            catch (\Throwable $th) {
                Log::error("calculUsersLocation error: ".$th);
            }
        }

        public function getPartFromString($string, $from, $to) {
            try {
                return substr($string, $from, $to);
            } 
            
            catch (\Throwable $th) {
                Log::error("getPartFromString error: ".$th);
            }
        }
        
        public function hasDataSession($key) {
            try {
                return Session::has($key); 
            } 
            
            catch (\Throwable $th) {
                Log::error("hasDataSession error: ".$th);
            }
        }

        public function getNewWorkOrdersRoutesList() {
            try {
                return [
                    'all' => 'workorder.openWorkOrdersList',
                    'deleted' => 'workorder.workorders.list',
                    'open' => 'workorder.openWorkOrdersList',
                    'in-progress' => 'workorder.openWorkOrdersList',
                    'spare-parts' => 'workorder.openWorkOrdersList',
                    'under-evaluation' => 'workorder.openWorkOrdersList',
                    'closed' => 'workorder.openWorkOrdersList',
                    'pending' => 'workorder.openWorkOrdersList',
                    'request' => 'workorder.openWorkOrdersList'
                ];
            } 
            
            catch (\Throwable $th) {
                Log::error("getNewWorkOrdersRoutesList error: ".$th);
            }
        }

        public function getCurrentIpAddress() {
            try {
                //return $_SERVER['HTTP_CLIENT_IP'] ?? $_SERVER['HTTP_X_FORWARDED_FOR'] ?? $_SERVER['HTTP_X_FORWARDED'] ?? $_SERVER['HTTP_FORWARDED_FOR'] ?? $_SERVER['HTTP_FORWARDED'] ?? $_SERVER['REMOTE_ADDR'] ?? 'UNKNOWN';
                $ipAddress = Request::ip();
                return $ipAddress ?: 'UNKNOWN';
            } 
            
            catch (\Throwable $th) {
                Log::error("getCurrentIpAddress error: ".$th);
            }
        }

        public function getLanguageDate($timestamp, $lang) {
            try {
                $days = [];
                $months = [];
                
                switch ($lang) {
                    case Language::English->value:
                        $days = [
                            'Sunday' => 'Sunday',
                            'Monday' => 'Monday',
                            'Tuesday' => 'Tuesday',
                            'Wednesday' => 'Wednesday',
                            'Thursday' => 'Thursday',
                            'Friday' => 'Friday',
                            'Saturday' => 'Saturday'
                        ];
    
                        $months = [
                            'January' => 'January',
                            'February' => 'February',
                            'March' => 'March',
                            'April' => 'April',
                            'May' => 'May',
                            'June' => 'June',
                            'July' => 'July',
                            'August' => 'August',
                            'September' => 'September',
                            'October' => 'October',
                            'November' => 'November',
                            'December' => 'December'
                        ];
                    break;
                    
                    case Language::Arabic->value:
                        $days = [
                            'Sunday' => 'الأحد',
                            'Monday' => 'الاثنين',
                            'Tuesday' => 'الثلاثاء',
                            'Wednesday' => 'الأربعاء',
                            'Thursday' => 'الخميس',
                            'Friday' => 'الجمعة',
                            'Saturday' => 'السبت'
                        ];
    
                        $months = [
                            'January' => 'يناير',
                            'February' => 'فبراير',
                            'March' => 'مارس',
                            'April' => 'أبريل',
                            'May' => 'مايو',
                            'June' => 'يونيو',
                            'July' => 'يوليو',
                            'August' => 'أغسطس',
                            'September' => 'سبتمبر',
                            'October' => 'أكتوبر',
                            'November' => 'نوفمبر',
                            'December' => 'ديسمبر'
                        ];
                    break;
                }
    
                $dayOfWeek = $this->changeDateFormat('l', $timestamp);
                $dayOfMonth = $this->changeDateFormat('d', $timestamp);
                $month = $this->changeDateFormat('F', $timestamp);
                $year = $this->changeDateFormat('Y', $timestamp);
                $time = $this->changeDateFormat('H:i', $timestamp);
                $dayOfWeekFormatted = $days[$dayOfWeek];
                $monthFormatted = $months[$month];
                return $lang == Language::English->value ? "$dayOfWeekFormatted $dayOfMonth $monthFormatted $year" : "$dayOfWeekFormatted $dayOfMonth $monthFormatted $year";
            } 
            
            catch (\Throwable $th) {
                Log::error("getLanguageDate error: ".$th);
            }
        }
         public function searchInCollection($collection, $key, $value) {
            try {
                return collect($collection)->firstWhere($key, $value);
            } 

            catch (\Throwable $th) {
                Log::info("mergeCollections Error: ".$th);
            }
        }

        public function additionOperation($value1, $value2) {
            try {
                return $value1 + $value2;
            } 

            catch (\Throwable $th) {
                Log::info("additionOperation error: ".$th);
            }
        } 

        public function fullGenerateQrCodeFunction($min, $max) {
            try {
                $randomNumber = $this->generateRandomNumber($min, $max);
                $qrCode = $this->generateQrCode('png', 100, 'H', $randomNumber) ?? null;
                $base64 = base64_encode($qrCode) ?? null;
                $dataUrl = ('data:image/png;base64,' . $base64) ?? null;
                return [
                    'randomNumber' => $randomNumber,
                    'dataUrl' => $dataUrl
                ];
            } 
            
            catch (\Throwable $th) {
                Log::info("fullGenerateQrCodeFunction error: ".$th);
            }
        }

        public function removeLastCaractersFromString($string, $start, $length) {
            try {
                return Str::substr($string, $start, $length);
            } 
            
            catch (\Throwable $th) {
                Log::info("removeLastCaractersFromString error: ".$th);
            }
        }

        public function clearDataCache($key) {
            try {
                return Cache::forget($key); 
            } 
            
            catch (\Throwable $th) {
                Log::error("clearDataCache error: ".$th);
            }
        }

        public function createDataCacheForEver($key, $value) {
            try {
                return Cache::forever($key, $value);
            } 
            
            catch (\Throwable $th) {
                Log::error("createDataCacheForEver error: ".$th);
            }
        }

        public function getDataCache($key) {
            try {
                return Cache::get($key); 
            } 
            
            catch (\Throwable $th) {
                Log::error("getDataCache error: ".$th);
            }
        }

        public function generateUniqueKey() {
            try {
                return bin2hex(random_bytes(32));
            } 
            
            catch (\Throwable $error) {
                Log::error("generateUniqueKey error: ".$error);
            }
        }
    }
?>
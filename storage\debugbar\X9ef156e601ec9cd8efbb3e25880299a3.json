{"__meta": {"id": "X9ef156e601ec9cd8efbb3e25880299a3", "datetime": "2025-07-31 15:40:59", "utime": 1753965659.371691, "method": "GET", "uri": "/language/en", "ip": "127.0.0.1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 1, "messages": [{"message": "[15:40:59] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\laragon\\www\\Osool-B2G\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1753965659.32307, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753965658.896361, "end": 1753965659.371734, "duration": 0.4753727912902832, "duration_str": "475ms", "measures": [{"label": "Booting", "start": 1753965658.896361, "relative_start": 0, "end": 1753965659.300485, "relative_end": 1753965659.300485, "duration": 0.40412378311157227, "duration_str": "404ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1753965659.300495, "relative_start": 0.40413379669189453, "end": 1753965659.371735, "relative_end": 1.1920928955078125e-06, "duration": 0.07124018669128418, "duration_str": "71.24ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 32481904, "peak_usage_str": "31MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET language/{locale}", "middleware": "web", "controller": "App\\Http\\Controllers\\Admin\\LanguageController@changeLanguage", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "changeLanguage", "file": "<a href=\"phpstorm://open?file=C:\\laragon\\www\\Osool-B2G\\app\\Http\\Controllers\\Admin\\LanguageController.php&line=12\">\\app\\Http\\Controllers\\Admin\\LanguageController.php:12-39</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.00437, "accumulated_duration_str": "4.37ms", "statements": [{"sql": "select * from `users` where `id` = 7368 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["7368"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 340}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Middleware\\CheckSuperLogin.php", "line": 23}], "duration": 0.00437, "duration_str": "4.37ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "osool_dev_db2", "start_percent": 0, "width_percent": 100}]}, "models": {"data": {"App\\Models\\User": 1}, "count": 1}, "livewire": {"data": [], "count": 0}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "82Y3S2Ii8XAEUfJqJwoJcxv4amqMyZ5Eyd3DAmvL", "captcha_answer": "32", "_previous": "array:1 [\n  \"url\" => \"http://osool-b2g.test/language/en\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "7368", "plain_user_password": "123456", "locale": "en", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/language/en", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-243983710 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-243983710\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1536149281 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1536149281\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1843382561 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"245 characters\">http://osool-b2g.test/finance/customers/view/eyJpdiI6IkpmblRWUlpld1JISW9McFBsTlpwanc9PSIsInZhbHVlIjoiYkp1QkYxaldDM2thby92bXVvaURyUT09IiwibWFjIjoiN2E5ODE4ZTFlYmQyYThjNTYwMzY1NTgxMGE5MThiODdlMDBiMTJiMDJhODdjYmQ4ZTY3NzY1ZDI5YTI1ZGU2MSIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"95 characters\">en-US,en;q=0.9,ar;q=0.8,bg;q=0.7,es;q=0.6,fr;q=0.5,he;q=0.4,ms;q=0.3,pt;q=0.2,it;q=0.1,pl;q=0.1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"409 characters\">XSRF-TOKEN=eyJpdiI6InJjaDhiVnNUMDdzU2tXNko1d3NEQnc9PSIsInZhbHVlIjoiNHlrbC8zK0g4SzA1ZnVXSEtWRW1zUWVuSkVuODY3bERMUHJTd1crQ1RYQVcvdXh6dGgvaHl0MFdUNDVhdHk2YTc2N3VNT2xuTUk1dGhuWExqWjg2dEYxTzVsTEpOYWxTZ2VNL3ptam9JN3k5ZnlxY0N6V2NzQldTM0oreDFIS3YiLCJtYWMiOiI2NjA4NGM0MzFkZjk1NWY5YTAyNTVkNmU5NzNlZDUyMWU1MTNhNzdlMmQxNDY5MTkxYjdiMjkyOWUxYWUxMzNkIiwidGFnIjoiIn0%3D; osool_session=NkZoyueTkDMdtsgMTEySUxpj4MAXBaB5vd7zDBDo</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1843382561\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-487417863 data-indent-pad=\"  \"><span class=sf-dump-note>array:39</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>REDIRECT_STATUS</span>\" => \"<span class=sf-dump-str title=\"3 characters\">200</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_DNT</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"245 characters\">http://osool-b2g.test/finance/customers/view/eyJpdiI6IkpmblRWUlpld1JISW9McFBsTlpwanc9PSIsInZhbHVlIjoiYkp1QkYxaldDM2thby92bXVvaURyUT09IiwibWFjIjoiN2E5ODE4ZTFlYmQyYThjNTYwMzY1NTgxMGE5MThiODdlMDBiMTJiMDJhODdjYmQ4ZTY3NzY1ZDI5YTI1ZGU2MSIsInRhZyI6IiJ9</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"95 characters\">en-US,en;q=0.9,ar;q=0.8,bg;q=0.7,es;q=0.6,fr;q=0.5,he;q=0.4,ms;q=0.3,pt;q=0.2,it;q=0.1,pl;q=0.1</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"409 characters\">XSRF-TOKEN=eyJpdiI6InJjaDhiVnNUMDdzU2tXNko1d3NEQnc9PSIsInZhbHVlIjoiNHlrbC8zK0g4SzA1ZnVXSEtWRW1zUWVuSkVuODY3bERMUHJTd1crQ1RYQVcvdXh6dGgvaHl0MFdUNDVhdHk2YTc2N3VNT2xuTUk1dGhuWExqWjg2dEYxTzVsTEpOYWxTZ2VNL3ptam9JN3k5ZnlxY0N6V2NzQldTM0oreDFIS3YiLCJtYWMiOiI2NjA4NGM0MzFkZjk1NWY5YTAyNTVkNmU5NzNlZDUyMWU1MTNhNzdlMmQxNDY5MTkxYjdiMjkyOWUxYWUxMzNkIiwidGFnIjoiIn0%3D; osool_session=NkZoyueTkDMdtsgMTEySUxpj4MAXBaB5vd7zDBDo</span>\"\n  \"<span class=sf-dump-key>PATH</span>\" => \"<span class=sf-dump-str title=\"1017 characters\">C:\\Program Files\\Parallels\\Parallels Tools\\Applications;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\Git\\cmd;C:\\laragon\\bin\\php\\php-8.3.16-Win32-vs16-x64;C:\\ProgramData\\ComposerSetup\\bin;C:\\laragon\\bin\\composer;C:\\laragon\\bin\\git\\bin;C:\\laragon\\bin\\git\\cmd;C:\\laragon\\bin\\git\\mingw64\\bin;C:\\laragon\\bin\\git\\usr\\bin;C:\\laragon\\bin\\mysql\\mysql-8.4.3-winx64\\bin;C:\\laragon\\bin\\ngrok;C:\\laragon\\bin\\nodejs\\node-v22;C:\\laragon\\bin\\php\\php-8.3.16-Win32-vs16-x64;C:\\laragon\\bin\\python\\python-3.13;C:\\laragon\\bin\\python\\python-3.13\\Scripts;C:\\laragon\\usr\\bin;C:\\Users\\<USER>\\AppData\\Local\\Yarn\\config\\global\\node_modules\\.bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin</span>\"\n  \"<span class=sf-dump-key>SystemRoot</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\WINDOWS</span>\"\n  \"<span class=sf-dump-key>COMSPEC</span>\" => \"<span class=sf-dump-str title=\"27 characters\">C:\\WINDOWS\\system32\\cmd.exe</span>\"\n  \"<span class=sf-dump-key>PATHEXT</span>\" => \"<span class=sf-dump-str title=\"53 characters\">.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC</span>\"\n  \"<span class=sf-dump-key>WINDIR</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\WINDOWS</span>\"\n  \"<span class=sf-dump-key>SERVER_SIGNATURE</span>\" => \"\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">Apache/2.4.62 (Win64) OpenSSL/3.0.15 PHP/8.3.16</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  \"<span class=sf-dump-key>SERVER_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"2 characters\">80</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"31 characters\">C:/laragon/www/Osool-B2G/public</span>\"\n  \"<span class=sf-dump-key>REQUEST_SCHEME</span>\" => \"<span class=sf-dump-str title=\"4 characters\">http</span>\"\n  \"<span class=sf-dump-key>CONTEXT_PREFIX</span>\" => \"\"\n  \"<span class=sf-dump-key>CONTEXT_DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"31 characters\">C:/laragon/www/Osool-B2G/public</span>\"\n  \"<span class=sf-dump-key>SERVER_ADMIN</span>\" => \"<span class=sf-dump-str title=\"17 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"41 characters\">C:/laragon/www/Osool-B2G/public/index.php</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">50944</span>\"\n  \"<span class=sf-dump-key>REDIRECT_URL</span>\" => \"<span class=sf-dump-str title=\"12 characters\">/language/en</span>\"\n  \"<span class=sf-dump-key>GATEWAY_INTERFACE</span>\" => \"<span class=sf-dump-str title=\"7 characters\">CGI/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>QUERY_STRING</span>\" => \"\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"12 characters\">/language/en</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1753965658.8964</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1753965658</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-487417863\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-185780671 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">82Y3S2Ii8XAEUfJqJwoJcxv4amqMyZ5Eyd3DAmvL</span>\"\n  \"<span class=sf-dump-key>osool_session</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-185780671\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1705024074 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 12:40:59 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"245 characters\">http://osool-b2g.test/finance/customers/view/eyJpdiI6IkpmblRWUlpld1JISW9McFBsTlpwanc9PSIsInZhbHVlIjoiYkp1QkYxaldDM2thby92bXVvaURyUT09IiwibWFjIjoiN2E5ODE4ZTFlYmQyYThjNTYwMzY1NTgxMGE5MThiODdlMDBiMTJiMDJhODdjYmQ4ZTY3NzY1ZDI5YTI1ZGU2MSIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Ijh5aEhhTGNsdTRUQ1V1OW5VTGRiSEE9PSIsInZhbHVlIjoibzVJS0U2N0dreXVvbGN5RmU0M2Y3SFVrOWIwYmZPUGdmK0NubEl5WUpQbDJwb2xWeWNzZzV2QXoxUG1QanBtY0VraTBhSlIzc3l1K25zQXkwRDJ1aU1mZ21vU255SnZNd2hLR3hHN2E4K29GaFN2RGpQRy9QaU0zMEw4U2prb2siLCJtYWMiOiIyMmMzMzI4MjcxNTE2MzQxOWUyNTMyNzU3NmIxYjY0ZjRkOTFiZTk3NmQ1ZWMwMzdiY2I4NGM1MDU0Zjg1NDlkIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 14:40:59 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"441 characters\">osool_session=eyJpdiI6ImoydTE5L1Rna2FpSFREVU5UTk5yN3c9PSIsInZhbHVlIjoiajRla3poaVg2UVYvWnJUc3o4S3hsR2JOWFZiczNRUVdPMGZpZ1JQVklWU21vcy96Vzd4cFUvVlcwZHZjS3ZkaWVZTEZBWWxHNW9iOWEvSk1nRW9XUCtlZVZPd0ttK0lDb3l2M29hcWh2eW55b3NVdDFpN0Z3Y0p1ZjJCMEhFek0iLCJtYWMiOiI1NTgyNjZkNTA2Y2ExYzgzNTU3NzUyNzU5OWQzY2UyYjUyZDVhNWQ2YjRhMGYyOTgzMjk0Mzk4MDg5ZTE4ZTc1IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 14:40:59 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Ijh5aEhhTGNsdTRUQ1V1OW5VTGRiSEE9PSIsInZhbHVlIjoibzVJS0U2N0dreXVvbGN5RmU0M2Y3SFVrOWIwYmZPUGdmK0NubEl5WUpQbDJwb2xWeWNzZzV2QXoxUG1QanBtY0VraTBhSlIzc3l1K25zQXkwRDJ1aU1mZ21vU255SnZNd2hLR3hHN2E4K29GaFN2RGpQRy9QaU0zMEw4U2prb2siLCJtYWMiOiIyMmMzMzI4MjcxNTE2MzQxOWUyNTMyNzU3NmIxYjY0ZjRkOTFiZTk3NmQ1ZWMwMzdiY2I4NGM1MDU0Zjg1NDlkIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 14:40:59 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"413 characters\">osool_session=eyJpdiI6ImoydTE5L1Rna2FpSFREVU5UTk5yN3c9PSIsInZhbHVlIjoiajRla3poaVg2UVYvWnJUc3o4S3hsR2JOWFZiczNRUVdPMGZpZ1JQVklWU21vcy96Vzd4cFUvVlcwZHZjS3ZkaWVZTEZBWWxHNW9iOWEvSk1nRW9XUCtlZVZPd0ttK0lDb3l2M29hcWh2eW55b3NVdDFpN0Z3Y0p1ZjJCMEhFek0iLCJtYWMiOiI1NTgyNjZkNTA2Y2ExYzgzNTU3NzUyNzU5OWQzY2UyYjUyZDVhNWQ2YjRhMGYyOTgzMjk0Mzk4MDg5ZTE4ZTc1IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 14:40:59 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1705024074\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1054374466 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">82Y3S2Ii8XAEUfJqJwoJcxv4amqMyZ5Eyd3DAmvL</span>\"\n  \"<span class=sf-dump-key>captcha_answer</span>\" => <span class=sf-dump-num>32</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"33 characters\">http://osool-b2g.test/language/en</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>7368</span>\n  \"<span class=sf-dump-key>plain_user_password</span>\" => \"<span class=sf-dump-str title=\"6 characters\">123456</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1054374466\", {\"maxDepth\":0})</script>\n"}}
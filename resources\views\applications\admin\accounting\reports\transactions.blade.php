@extends('layouts.app')
@section('styles')
<style type="text/css">

</style>
@endsection
@section('content')
<div class="contents crm">
    <div class="container-fluid">
        <div class="col-lg-12">
            <div class="row justify-content-sm-between align-items-center justify-content-center my-3 flex-sm-row flex-column">
                <div class="page-title-wrap p-0">
                    <div class="page-title d-flex justify-content-between">
                        <div class="page-title__left justify-content-sm-between align-items-center justify-content-center">
                            <div class="user-member__title mr-sm-25 ml-0">
                                <h4 class="text-capitalize fw-500 breadcrumb-title fs-16">
                                    Transaction Summary
                                </h4>
                            </div>
                        </div>
                    </div>
                    <div>
                        <ul class="atbd-breadcrumb nav">
                            <li class="atbd-breadcrumb__item">
                                <a>Dashboard</a>
                                <span class="breadcrumb__seperator">
                                    <span class="la la-angle-right"></span>
                                </span>
                            </li>
                            <li class="atbd-breadcrumb__item">
                                <a>Report</a>
                                <span class="breadcrumb__seperator">
                                    <span class="la la-angle-right"></span>
                                </span>
                            </li>
                            <li class="atbd-breadcrumb__item">
                                <a>Transaction Summary</a>
                            </li>
                        </ul>
                    </div>
                </div>

                <div class="d-flex gap-10 breadcrumb_right_icons">
                    <div class="d-flex gap-10 breadcrumb_right_icons">
                        <button class="btn btn-white btn-default text-center svg-20 px-2 wh-45">
                            <i class="iconsax icon text-new-primary fs-22 colorRed mr-0" icon-name="download-1"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>


        <div class="table-responsive">

            <div class="card mb-3">
                <div class="card-body">
                    <form wire:submit.prevent="applyFilters" class="fs-14">
                        <div class="d-flex flex-wrap gap-10">

                            <div class="flex-fill">
                                <label for="start_Month" class="text-osool fw-600">Start Month</label>
                                <div class="position-relative">
                                    <input type="text" class="form-control datepicker" id="start_Month" type="month" placeholder="Select Month" />
                                    <i class="iconsax field-icon" icon-name="calendar-search"></i>
                                </div>
                            </div>

                            <div class="flex-fill">
                                <label for="end_Month" class="text-osool fw-600">End Month</label>
                                <div class="position-relative">
                                    <input type="text" class="form-control datepicker" id="end_Month" type="month" placeholder="Select Month" />
                                    <i class="iconsax field-icon" icon-name="calendar-search"></i>
                                </div>
                            </div>

                            <div class="flex-fill">
                                <label for="select_account" class="text-osool fw-600">Account</label>
                                <select class="form-control select2-new" id="select_account">
                                    <option selected="selected" value="">Select Account</option>
                                    <option value="strip-paypal">Stripe / Paypal</option>
                                    <option value="27">cash</option>
                                    <option value="113">Mohammed Firdaus0</option>
                                </select>
                            </div>
                            <div class="flex-fill" data-select2-id="106">
                                <label for="" class="text-osool fw-600">Category</label>
                                <select class="form-control select2-new">
                                    <option selected="selected" value="">Select Category</option>
                                    <option value="Invoice">Invoice</option>
                                    <option value="Bill">Bill</option>
                                </select>
                            </div>

                            <div class="flex-fill">
                                <label for="" class="d-md-block d-none">&nbsp;</label>
                                <div class="d-flex gap-10 justify-content-end">
                                    <button class="btn btn-white btn-default text-center svg-20 px-2 wh-45">
                                        <i class="bg-inprogress bg-opacity-loss btn btn-sm d-flex fs-18 icon iconsax radius-md text-white wh-45" data-toggle="tooltip" data-placement="top" title="Apply" icon-name="search-normal-2"></i>
                                    </button>
                                    <button class="btn btn-white btn-default text-center svg-20 px-2 wh-45">
                                        <i class="bg-secondary bg-opacity-loss btn btn-sm d-flex fs-18 icon iconsax radius-md text-white wh-45" data-toggle="tooltip" data-placement="top" title="Reset" icon-name="trash"></i>
                                    </button>

                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <div id="printableArea">
                <div class="row">
                    <div class="col-4 pl-0">
                        <input type="hidden" value="All Category Transaction Report of Jun-2025 to Jan-2025" id="filename" />
                        <div class="card p-4 mb-4 h-110">
                            <h5 class="report-text gray-text mb-0">Report :</h5>
                            <h6 class="report-text mb-0">Transaction Summary</h6>
                        </div>
                    </div>

                    <div class="col-4">
                        <div class="card p-4 mb-4 h-110">
                            <h5 class="report-text gray-text mb-0">Duration :</h5>
                            <h6 class="report-text mb-0">Jun-2025 to Jan-2025</h6>
                        </div>
                    </div>

                    <div class="col-4 pr-0">
                        <div class="card p-4 mb-4 h-110">
                            <h6 class="report-text gray-text mb-0">Mohammed Firdaus0 - Saudi National Bank</h6>
                            <h5 class="report-text mb-0">101K ﷼</h5>
                        </div>
                    </div>
                </div>
            </div>
        </div>


        <div class="card">
            <div class="">
                <div class="card-header py-4 px-3 border-0 d-flex justify-content-between align-items-center">
                    <div class="dataTable-dropdown d-flex align-center gap-20">
                        <label class="mb-0">
                            <select class="dataTable-selector form-control select2-new px-3 min-h-40">
                                <option value="5" selected="">5</option>
                                <option value="10">10</option>
                                <option value="15">15</option>
                                <option value="20">20</option>
                                <option value="25">25</option>
                            </select>
                        </label>
                        <div>
                            Entries per page
                        </div>
                    </div>


                    <div class="d-flex gap-10 table-search">
                        <div class="position-relative">
                            <input type="text" class="form-control" placeholder="Search">
                            <i class="iconsax field-icon fs-18 mr-0" icon-name="search-normal-2"></i>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card-body px-0 pt-0 pb-0">
                <div class="userDatatable projectDatatable project-table bg-white w-100 border-0">
                    <div class="table-responsive">
                        <table class="table mb-0 radius-0 th-osool">
                            <thead>
                                <tr class="userDatatable-header">
                                    <th>
                                        Date
                                    </th>
                                    <th>
                                        <i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i> Account
                                    </th>
                                    <th>
                                        <i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i> Type
                                    </th>
                                    <th>
                                        <i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i> Category
                                    </th>
                                    <th>
                                        <i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i> Description
                                    </th>
                                    <th>
                                        <i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i> Amount
                                    </th>
                                    <th>
                                        Action
                                    </th>

                                </tr>
                            </thead>
                            <tbody class="sort-table ui-sortable">
                                <tr class="ui-sortable-handle" style="opacity: 1;">
                                    <td>
                                        <div class="d-flex userDatatable-content mb-0 align-items-center">
                                            <span>09-09-2025</span>
                                        </div>
                                    </td>

                                    <td>
                                        <div class="d-flex userDatatable-content mb-0 align-items-center">
                                            <span>Saudi National Bank Mohammed Firdaus0</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex userDatatable-content mb-0 align-items-center">
                                            <span>Payment</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex userDatatable-content mb-0 align-items-center">
                                            <span>Test</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex userDatatable-content mb-0 align-items-center">
                                            <span>Hello this is a payment</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center gap-10">
                                            <img src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSx34eO8RbZlsetN1iAQtxuOLnLOlyJbJmU7Q&s" width="15"><span class="text-new-primary">1k</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-inline-block">
                                            <ul class="mb-0 d-flex flex-wrap gap-10">
                                                <li>
                                                    <a href="javascript:void(0);" data-toggle="modal" data-target="#revenue-details">
                                                        <i class="iconsax icon text-osool fs-18" icon-name="eye"></i>
                                                    </a>
                                                </li>
                                                <li>
                                                    <a href="javascript:void(0);" data-toggle="modal" data-target="#create-revenue">
                                                        <i class="iconsax icon text-new-primary fs-18" icon-name="edit-1"></i>
                                                    </a>
                                                </li>
                                                <li>
                                                    <a href="javascript:void(0);" wire:click="openDeleteModal(588, 'Beyond Tech1 Copy test task')">
                                                        <i class="iconsax icon text-delete fs-18" icon-name="trash"></i>
                                                    </a>
                                                </li>
                                                <li>
                                                    <a href="javascript:void(0);">
                                                        <i class="iconsax icon text-osool fs-18 mr-0" icon-name="download-1"></i>
                                                    </a>
                                                </li>
                                            </ul>
                                        </div>
                                    </td>
                                </tr>

                                <tr class="ui-sortable-handle" style="opacity: 1;">
                                    <td>
                                        <div class="d-flex userDatatable-content mb-0 align-items-center">
                                            <span>23-09-2025</span>
                                        </div>
                                    </td>

                                    <td>
                                        <div class="d-flex userDatatable-content mb-0 align-items-center">
                                            <span>Saudi National Bank Mohammed Firdaus0</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex userDatatable-content mb-0 align-items-center">
                                            <span>Payment</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex userDatatable-content mb-0 align-items-center">
                                            <span>Test</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex userDatatable-content mb-0 align-items-center">
                                            <span>Hello</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center gap-10">
                                            <img src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSx34eO8RbZlsetN1iAQtxuOLnLOlyJbJmU7Q&s" width="15"><span class="text-new-primary">100k</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-inline-block">
                                            <ul class="mb-0 d-flex flex-wrap gap-10">
                                                <li>
                                                    <a href="javascript:void(0);" data-toggle="modal" data-target="#revenue-details">
                                                        <i class="iconsax icon text-osool fs-18" icon-name="eye"></i>
                                                    </a>
                                                </li>
                                                <li>
                                                    <a href="javascript:void(0);" data-toggle="modal" data-target="#create-revenue">
                                                        <i class="iconsax icon text-new-primary fs-18" icon-name="edit-1"></i>
                                                    </a>
                                                </li>
                                                <li>
                                                    <a href="javascript:void(0);" wire:click="openDeleteModal(588, 'Beyond Tech1 Copy test task')">
                                                        <i class="iconsax icon text-delete fs-18" icon-name="trash"></i>
                                                    </a>
                                                </li>
                                                <li>
                                                    <a href="javascript:void(0);">
                                                        <i class="iconsax icon text-osool fs-18 mr-0" icon-name="download-1"></i>
                                                    </a>
                                                </li>
                                            </ul>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>


            <div class="card-body pt-0">
                <div class="d-flex justify-content-md-between flex-column flex-md-row justify-content-center align-items-center mt-4 gap-10">
                    <div>
                        <p class="text-sm text-gray-700 leading-5 mb-0">
                            <span>Showing</span>
                            <span class="font-medium">1</span>
                            <span>to</span>
                            <span class="font-medium">2</span>
                            <span>of</span>
                            <span class="font-medium">2</span>
                            <span>Entries</span>
                        </p>
                    </div>
                </div>
            </div>
        </div>


    </div>



</div>




<!-- Modal -->
<div class="modal fade" id="create-revenue" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel">Create Revenue</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="form-group col-md-6">
                        <label for="date" class="form-label">Date</label><span class="text-danger">*</span>
                        <div class="form-icon-user">
                            <input class="form-control datepicker" required="required" placeholder="Select Date" name="date" type="text" id="date" />
                        </div>
                    </div>
                    <div class="form-group col-md-6">
                        <label for="amount" class="form-label">Amount</label><span class="text-danger">*</span>
                        <div class="form-icon-user">
                            <input class="form-control" required="required" placeholder="Enter Amount" step="0.01" min="0" name="amount" type="number" id="amount" />
                        </div>
                    </div>
                    <div class="form-group col-md-6">
                        <label for="account_id" class="form-label">Account</label><span class="text-danger">*</span>
                        <select class="form-control select2-new" required="required" id="account_id" name="account_id">
                            <option selected="selected" value="">Select Account</option>
                            <option value="27">sss cash</option>
                            <option value="113">Saudi National Bank Mohammed Firdaus0</option>
                        </select>
                    </div>
                    <div class="form-group col-md-6">
                        <label for="customer_id" class="form-label">Customer</label><span class="text-danger">*</span>
                        <select class="form-control select2-new" required="required" id="customer_id" name="customer_id">
                            <option selected="selected" value="">Select Customer</option>
                            <option value="30">Customer 1</option>
                            <option value="31">Customer 2</option>
                            <option value="32">Customer 3</option>
                            <option value="33">Customer 4</option>
                            <option value="34">Customer 5</option>
                            <option value="35">Customer 6</option>
                            <option value="36">Customer 7</option>
                            <option value="37">Customer 8</option>
                            <option value="38">Customer 9</option>
                            <option value="87">Fouzan</option>
                        </select>
                    </div>
                    <div class="form-group col-md-6">
                        <label for="category_id" class="form-label">Category</label><span class="text-danger">*</span>
                        <select class="form-control select2-new" required="required" id="category_id" name="category_id">
                            <option selected="selected" value="">Select Category</option>
                            <option value="18">Test cat</option>
                        </select>
                    </div>
                    <div class="form-group col-md-6">
                        <label for="reference" class="form-label">Reference</label><span class="text-danger">*</span>
                        <div class="form-icon-user">
                            <input class="form-control" placeholder="Enter Reference" required="required" name="reference" type="text" id="reference" />
                        </div>
                    </div>
                    <div class="form-group col-md-12">
                        <label for="description" class="form-label">Description</label><span class="text-danger">*</span>
                        <textarea class="form-control" rows="3" required="required" name="description" cols="50" id="description"></textarea>
                    </div>
                    <div class="form-group col-md-12">
                        <label for="add_receipt" class="form-label">Payment Receipt</label>
                        <input type="file" id="imageInput" multiple accept="image/*" class="d-none">
                        <label class="btn btn-default bg-new-primary" for="imageInput">
                            <i class="las la-upload fs-16"></i> Upload Files </label>
                        <div id="imagePreview" class="preview-container file-upload-new"></div>
                    </div>
                </div>

            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary">Save changes</button>
            </div>
        </div>
    </div>
</div>




<!-- Modal Bank account Details -->
<div class="modal fade" id="revenue-details" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel">Bank Account Details</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="userDatatable projectDatatable project-table bg-white w-100 border-0">
                    <div class="table-responsive">
                        <table class="table mb-0 radius-0 th-borderless">
                            <tbody class="sort-table ui-sortable">
                                <tr>
                                    <th>
                                        Date
                                    </th>
                                    <td>
                                        <div class="d-flex userDatatable-content mb-0 align-items-center">
                                            <span>Bank Of Baroda</span>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <th>
                                        Amount
                                    </th>
                                    <td>
                                        <div class="d-flex userDatatable-content mb-0 align-items-center">
                                            <span>Bank Of Baroda</span>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <th>
                                        Account
                                    </th>
                                    <td>
                                        <div class="d-flex userDatatable-content mb-0 align-items-center">
                                            <span>Bank Of Baroda</span>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <th>
                                        Customer
                                    </th>
                                    <td>
                                        <div class="d-flex userDatatable-content mb-0 align-items-center">
                                            <span>Cutomer 1</span>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <th>
                                        Category
                                    </th>
                                    <td>
                                        <div class="d-flex userDatatable-content mb-0 align-items-center">
                                            <span>Bank Of Baroda</span>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <th>
                                        Reference
                                    </th>
                                    <td>
                                        <div class="d-flex userDatatable-content mb-0 align-items-center">
                                            <span>Bank Of Baroda</span>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <th>
                                        Description
                                    </th>
                                    <td>
                                        <div class="d-flex userDatatable-content mb-0 align-items-center max-w-360">
                                            <span>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam</span>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <th>
                                        Payment Receipt
                                    </th>
                                    <td>
                                        <div class="d-flex align-items-center p-2 gap-10">
                                            <div class="view-img wo-img-div rounded" style="background: url('https://images.pexels.com/photos/11181151/pexels-photo-11181151.jpeg'); background-size: cover; background-position: center;">
                                                <img onclick="chatImageClick(this)" src="https://images.pexels.com/photos/11181151/pexels-photo-11181151.jpeg" alt="Maintenance Request Image" class="uploaded-image" width="100%">
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

</div>
</div>







@endsection

@section('scripts')
<script type="text/javascript">
    $(".datepicker").datepicker();
    $("#selectUsers,#statusList").select2();
</script>
<script>
    $(document).ready(function() {
        let currentFiles = [];

        const maxFiles = 1;
        const maxSizeMB = 2;
        const allowedTypes = ['image/jpeg', 'image/png', 'image/webp'];

        $('#imageInput').on('change', function() {
            const newFiles = Array.from(this.files);
            let totalFiles = currentFiles.length + newFiles.length;

            if (totalFiles > maxFiles) {
                alert(`Only ${maxFiles} images allowed.`);
                this.value = ''; // reset input
                return;
            }

            newFiles.forEach((file, index) => {
                if (!allowedTypes.includes(file.type)) {
                    alert(`Invalid file type: ${file.name}`);
                    return;
                }

                if (file.size > maxSizeMB * 1024 * 1024) {
                    alert(`File too large: ${file.name}`);
                    return;
                }

                currentFiles.push(file); // track only valid files

                const reader = new FileReader();
                reader.onload = function(e) {
                    const imgBox = $(`
          <div class="image-box d-flex justify-content-between align-items-center border radius-xl" data-name="${file.name}" data-size="${file.size}">
            <img src="${e.target.result}" alt="Image Preview">
            <button class="remove-btn d-center"><i class="iconsax" icon-name="x"></i></button>
          </div>
        `);
                    $('#imagePreview').append(imgBox);
                };
                reader.readAsDataURL(file);
            });

            this.value = ''; // Clear the file input to allow re-upload of same files
        });

        $('#imagePreview').on('click', '.remove-btn', function() {
            const box = $(this).closest('.image-box');
            const name = box.data('name');
            const size = box.data('size');

            // Remove file from tracking array
            currentFiles = currentFiles.filter(file => !(file.name === name && file.size === size));

            box.remove();
        });
    });
</script>
@endsection
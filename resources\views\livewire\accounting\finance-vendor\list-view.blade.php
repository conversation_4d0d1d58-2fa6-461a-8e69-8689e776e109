<div class="userDatatable projectDatatable project-table bg-white w-100 border-0">
    @if($error)
        <div class="alert alert-danger mx-3 mt-3" role="alert">
            <div class="d-flex align-items-center">
                <i class="iconsax icon fs-22 mr-2" icon-name="warning-2"></i>
                <div>
                    <strong>{{ __('vendors.status.error') }}</strong> {{ $error }}
                    <button class="btn btn-sm btn-outline-danger ml-2" wire:click="fetchVendors">
                        <i class="iconsax icon fs-16" icon-name="refresh-2"></i> {{ __('vendors.buttons.retry') }}
                    </button>
                </div>
            </div>
        </div>
    @endif

    <div class="table-responsive">
        <table class="table mb-0 radius-0 th-osool">
            <thead>
                <tr class="userDatatable-header">
                    <th>
                        #
                    </th>
                    <th wire:click="sortBy('name')" style="cursor: pointer;" title="{{ __('vendors.table.sort_by_name') }}">
                        <i class="iconsax icon fs-22" icon-name="{{ $sortField === 'name' ? ($sortDirection === 'asc' ? 'arrow-up-2' : 'arrow-down-2') : 'swap-vertical-circle' }}"></i> {{ __('vendors.common.name') }}
                    </th>
                    <th wire:click="sortBy('contact')" style="cursor: pointer;" title="{{ __('vendors.table.sort_by_contact') }}">
                        <i class="iconsax icon fs-22" icon-name="{{ $sortField === 'contact' ? ($sortDirection === 'asc' ? 'arrow-up-2' : 'arrow-down-2') : 'swap-vertical-circle' }}"></i> {{ __('vendors.common.contact') }}
                    </th>
                    <th wire:click="sortBy('email')" style="cursor: pointer;" title="{{ __('vendors.table.sort_by_email') }}">
                        <i class="iconsax icon fs-22" icon-name="{{ $sortField === 'email' ? ($sortDirection === 'asc' ? 'arrow-up-2' : 'arrow-down-2') : 'swap-vertical-circle' }}"></i> {{ __('vendors.common.email') }}
                    </th>
                    <th wire:click="sortBy('balance')" style="cursor: pointer;" title="{{ __('vendors.table.sort_by_balance') }}">
                        <i class="iconsax icon fs-22" icon-name="{{ $sortField === 'balance' ? ($sortDirection === 'asc' ? 'arrow-up-2' : 'arrow-down-2') : 'swap-vertical-circle' }}"></i> {{ __('vendors.common.balance') }}
                    </th>
                    <th>
                        {{ __('vendors.common.action') }}
                    </th>
                </tr>
            </thead>
            <tbody class="sort-table ui-sortable">
                @if($loading)
                    <tr>
                        <td colspan="6" class="text-center py-5">
                            <div class="d-flex flex-column align-items-center">
                                <div class="spinner-border text-primary mb-3" role="status">
                                    <span class="sr-only">{{ __('vendors.status.loading') }}</span>
                                </div>
                                <h6 class="text-muted">{{ __('vendors.common.loading_vendors') }}</h6>
                            </div>
                        </td>
                    </tr>
                @else
                    @forelse($vendors as $index => $vendor)
                    <tr class="ui-sortable-handle">
                        <!-- Index -->
                        <td>
                            <div class="d-flex userDatatable-content mb-0 align-items-center">
                                <span>{{ ($pagination->current_page - 1) * $pagination->per_page + $index + 1 }}</span>
                            </div>
                        </td>
                        <!-- Name -->
                        <td>
                            <div class="d-flex userDatatable-content mb-0 align-items-center">
                                <a href="javascript:void(0);" wire:click="viewVendor({{ $vendor['id'] }})" class="text-decoration-none">
                                    <span class="text-primary fw-500">{{ $vendor['name'] ?? '-' }}</span>
                                </a>
                            </div>
                        </td>
                        <!-- Contact -->
                        <td>
                            <div class="d-flex align-items-center gap-10">
                                <span class="">{{ $vendor['phone'] ?? $vendor['contact'] ?? '-' }}</span>
                            </div>
                        </td>
                        <!-- Email -->
                        <td>
                            <div class="d-flex userDatatable-content mb-0 align-items-center">
                                <span>{{ $vendor['email'] ?? '-' }}</span>
                            </div>
                        </td>
                        <!-- Balance -->
                        <td>
                            <div class="d-flex align-items-center gap-10">
                                <img src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSx34eO8RbZlsetN1iAQtxuOLnLOlyJbJmU7Q&s" width="15">
                                <span class="text-new-primary fw-500">{{ $vendor['display_balance'] ?? '0.00' }}</span>
                            </div>
                        </td>
                        <!-- Actions -->
                        <td>
                            <div class="d-inline-block">
                                <ul class="mb-0 d-flex gap-10">
                                    <li>
                                        <a href="javascript:void(0);" wire:click="viewVendor({{ $vendor['id'] }})" title="{{ __('vendors.common.view') }}">
                                            <i class="iconsax icon text-osool fs-18" icon-name="eye"></i>
                                        </a>
                                    </li>
                                    <li>
                                        <a href="javascript:void(0);" wire:click="openEditModal({{ $vendor['id'] }})" title="{{ __('vendors.common.edit') }}">
                                            <i class="iconsax icon text-new-primary fs-18" icon-name="edit-1"></i>
                                        </a>
                                    </li>
                                    <li>
                                        <a href="javascript:void(0);" wire:click="openDeleteModal({{ $vendor['id'] }}, '{{ $vendor['name'] }}')" title="{{ __('vendors.common.delete') }}">
                                            <i class="iconsax icon text-delete fs-18" icon-name="trash"></i>
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </td>
                    </tr>
                    @empty
                        <tr>
                            <td colspan="6" class="text-center py-4">
                                <div class="d-flex flex-column align-items-center">
                                    <i class="iconsax icon fs-48 text-muted mb-3" icon-name="profile-2user"></i>
                                    <h6 class="text-muted">{{ __('vendors.common.no_vendors_found') }}</h6>
                                    @if($search)
                                        <p class="text-muted">{{ __('vendors.messages.try_adjusting_search') }}</p>
                                    @endif
                                </div>
                            </td>
                        </tr>
                    @endforelse
                @endif
            </tbody>
        </table>
    </div>
</div>

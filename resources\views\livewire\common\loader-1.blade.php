<style>
    #overlayer1 {
        width: 100%;
        height: 100%;
        position: fixed;
        z-index: 99998;
        background: white;
        top: 0;
        left: 0;
        max-height: 100vh;
    }

    #overlayer1 {
        background: #ffffffa6 !important;
    }
</style>
<div id="overlayer1" wire:loading.delay wire:target="{{ $target }}">
    <span class="loader-overlay" wire:loading.delay wire:target="{{ $target }}">
        <span class="atbd-spin-dots spin-lg">
            <span class="spin-dot badge-dot dot-primary"></span>
            <span class="spin-dot badge-dot dot-primary"></span>
            <span class="spin-dot badge-dot dot-primary"></span>
            <span class="spin-dot badge-dot dot-primary"></span>
        </span>
    </span>
</div>

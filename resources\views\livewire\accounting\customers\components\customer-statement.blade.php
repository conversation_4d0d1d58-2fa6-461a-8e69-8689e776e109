<div>
    @if($showFullView)
    <div class="contents crm">
        <div class="container-fluid">
            <div class="col-lg-12">
                <div class="row justify-content-sm-between align-items-center justify-content-center my-3 flex-sm-row flex-column">
                    <div class="page-title-wrap p-0">
                        <div class="page-title d-flex justify-content-between">
                            <div class="page-title__left justify-content-sm-between align-items-center justify-content-center">
                                <div class="user-member__title mr-sm-25 ml-0">
                                    <h4 class="text-capitalize fw-500 breadcrumb-title fs-16">
                                        {{ __('accounting.customers.tabs.statement') }}
                                    </h4>
                                </div>
                            </div>
                        </div>
                        <div>
                            <ul class="atbd-breadcrumb nav">
                                <li class="atbd-breadcrumb__item">
                                    <a>{{ __('accounting.navigation.dashboard') }}</a>
                                    <span class="breadcrumb__seperator">
                                        <span class="la la-angle-right"></span>
                                    </span>
                                </li>
                                <li class="atbd-breadcrumb__item">
                                    <a>{{ __('accounting.navigation.customers') }}</a>
                                    <span class="breadcrumb__seperator">
                                        <span class="la la-angle-right"></span>
                                    </span>
                                </li>
                                <li class="atbd-breadcrumb__item">
                                    <a>{{ __('accounting.customers.tabs.statement') }}</a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endif

    <div class="tab-pane fade show active" id="customer-statement" role="tabpanel" aria-labelledby="contact-tab">
        <div class="checkout pt-2 pb-20 mb-30 w-100">
            <div class="row">
                <div class="col-lg-3 pr-md-0 mb-3 mb-md-0">
                    <div class="card p-3">
                        <form class="fs-14" wire:submit.prevent="submitFilters">
                            <div class="form-group">
                                <label for="">{{ __('accounting.customers.forms.from_date') }}</label>
                                <div class="position-relative">
                                    <input type="date" class="form-control" 
                                           wire:model.live.debounce.500ms="fromDate"
                                           placeholder="{{ __('accounting.customers.forms.enter_start_date') }}">
                                    <i class="iconsax field-icon text-light" icon-name="calendar-1"></i>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="">{{ __('accounting.customers.forms.to_date') }}</label>
                                <div class="position-relative">
                                    <input type="date" class="form-control" 
                                           wire:model.live.debounce.500ms="toDate"
                                           placeholder="{{ __('accounting.customers.forms.enter_end_date') }}">
                                    <i class="iconsax field-icon text-light" icon-name="calendar-1"></i>
                                </div>
                            </div>
                            <div class="d-flex gap-10 mt-4 justify-content-end">
                                <button type="button" wire:click="resetFilters" 
                                        class="btn bg-warning btn-sm text-white radius-xl">
                                    {{ __('accounting.customers.forms.reset') }}
                                </button>
                                <button type="submit" class="btn bg-new-primary btn-sm text-white radius-xl">
                                    {{ __('accounting.customers.forms.submit') }}
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
                
                <div class="col-lg-9 fs-14">
                    @if($loading)
                        <div class="d-flex justify-content-center align-items-center py-5">
                            <div class="spinner-border text-primary" role="status">
                                <span class="sr-only">{{ __('accounting.customers.messages.loading') }}</span>
                            </div>
                        </div>
                    @elseif($error)
                        <div class="alert alert-danger" role="alert">
                            {{ $error }}
                        </div>
                    @else
                        <div class="card" id="overview-section">
                            <div class="card-header py-3">
                                <div class="">
                                    <img src="https://workdo-dev.osool.cloud/uploads/logo/logo_dark.png" 
                                         alt="Work Do" class="logo logo-lg" style="max-width: 250px">
                                </div>
                                <div class="">
                                    <strong class="mb-2">{{ $customerDetails['company_name'] ?? 'My Company' }}</strong><br>
                                    <span class="invoice-number fs-14">{{ $customerDetails['email'] ?? '' }}</span>
                                </div>
                            </div>
                            
                            <div class="card-header">
                                <h5 class="mb-sm-0 mb-3">{{ __('accounting.customers.tabs.statement') }}</h5>
                                <div class="d-flex flex-wrap align-items-center">
                                    <span class="mr-3">
                                        {{ $fromDateDisplay }} {{ __('accounting.customers.labels.to') }} {{ $toDateDisplay }}
                                    </span>
                                    <button class="btn btn-xs bg-loss text-white" wire:click="downloadStatement">
                                        <i class="iconsax" icon-name="download-1"></i> 
                                        {{ __('accounting.customers.buttons.download') }}
                                    </button>
                                </div>
                            </div>
                            
                            <div class="p-4">
                                <div class="row">
                                    <!-- Billed To Section -->
                                    <div class="col-md-4 mb-sm-0 mb-3 pr-md-0">
                                        <div class="p-3 address-box radius-xl h-100">
                                            <h6 class="text-dark mb-3 fs-14">{{ __('accounting.customers.sections.billed_to') }}</h6>
                                            <table class="table mb-0 table-borderless new-header td-pl-0 table-address">
                                                <tbody>
                                                    <tr>
                                                        <td><span class="fs-12 fw-50 text-dark">{{ __('accounting.billing_address') }}</span></td>
                                                        <td>{{ $customerDetails['billing_address'] ?? '' }}</td>
                                                    </tr>
                                                    <tr>
                                                        <td><span class="fs-12 fw-50 text-dark">{{ __('accounting.billing_city') }}</span></td>
                                                        <td>{{ $customerDetails['billing_city'] ?? '' }}</td>
                                                    </tr>
                                                    <tr>
                                                        <td><span class="fs-12 fw-50 text-dark">{{ __('accounting.billing_zip') }}</span></td>
                                                        <td>{{ $customerDetails['billing_zip'] ?? '' }}</td>
                                                    </tr>
                                                    <tr>
                                                        <td><span class="fs-12 fw-50 text-dark">{{ __('accounting.billing_country') }}</span></td>
                                                        <td>{{ $customerDetails['billing_country'] ?? '' }}</td>
                                                    </tr>
                                                    <tr>
                                                        <td><span class="fs-12 fw-50 text-dark">{{ __('accounting.billing_contact') }}</span></td>
                                                        <td>{{ $customerDetails['contact'] ?? '' }}</td>
                                                    </tr>
                                                    <tr>
                                                        <td><strong>{{ __('accounting.customers.fields.tax_number') }} :</strong></td>
                                                        <td>{{ $customerDetails['tax_number'] ?? '' }}</td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                    
                                    <!-- Shipped To Section -->
                                    <div class="col-md-4 mb-sm-0 mb-3 pr-md-0">
                                        <div class="p-3 address-box radius-xl h-100">
                                            <h6 class="text-dark mb-3 fs-14">{{ __('accounting.customers.sections.shipped_to') }}</h6>
                                            <table class="table mb-0 table-borderless new-header td-pl-0 table-address">
                                                <tbody>
                                                    <tr>
                                                        <td><span class="fs-12 fw-50 text-dark">{{ __('accounting.shipping_address') }}</span></td>
                                                        <td>{{ $customerDetails['shipping_address'] ?? '' }}</td>
                                                    </tr>
                                                    <tr>
                                                        <td><span class="fs-12 fw-50 text-dark">{{ __('accounting.shipping_city') }}</span></td>
                                                        <td>{{ $customerDetails['shipping_city'] ?? '' }}</td>
                                                    </tr>
                                                    <tr>
                                                        <td><span class="fs-12 fw-50 text-dark">{{ __('accounting.shipping_zip') }}</span></td>
                                                        <td>{{ $customerDetails['shipping_zip'] ?? '' }}</td>
                                                    </tr>
                                                    <tr>
                                                        <td><span class="fs-12 fw-50 text-dark">{{ __('accounting.shipping_country') }}</span></td>
                                                        <td>{{ $customerDetails['shipping_country'] ?? '' }}</td>
                                                    </tr>
                                                    <tr>
                                                        <td><span class="fs-12 fw-50 text-dark">{{ __('accounting.shipping_contact') }}</span></td>
                                                        <td>{{ $customerDetails['shipping_contact'] ?? '' }}</td>
                                                    </tr>
                                                    <tr>
                                                        <td><strong>{{ __('accounting.customers.fields.tax_number') }} :</strong></td>
                                                        <td>{{ $customerDetails['tax_number'] ?? '' }}</td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                    
                                    <!-- Account Summary Section -->
                                    <div class="col-md-4">
                                        <div class="p-3 address-box radius-xl h-100">
                                            <h6 class="text-dark mb-3 fs-14">{{ __('accounting.customers.sections.account_summary') }}</h6>
                                            <table class="table mb-0 table-borderless new-header no-wrap tp-0" id="content_table">
                                                <tbody>
                                                    <tr>
                                                        <td><span class="fs-12 fw-50 text-dark">{{ __('accounting.customers.fields.balance') }} :</span></td>
                                                        <td><span>{{ number_format($balance, 2) }} {{ __('accounting.currency.sar') }}</span></td>
                                                    </tr>
                                                    <tr>
                                                        <td><span class="fs-12 fw-50 text-dark">{{ __('accounting.customers.fields.invoiced_amount') }} :</span></td>
                                                        <td><span>{{ number_format($invoicedAmount, 2) }} {{ __('accounting.currency.sar') }}</span></td>
                                                    </tr>
                                                    <tr>
                                                        <td><span class="fs-12 fw-50 text-dark">{{ __('accounting.customers.fields.amount_paid') }} :</span></td>
                                                        <td><span>{{ number_format($amountPaid, 2) }} {{ __('accounting.currency.sar') }}</span></td>
                                                    </tr>
                                                    <tr>
                                                        <td><span class="fs-12 fw-50 text-dark">{{ __('accounting.customers.fields.balance_due') }} :</span></td>
                                                        <td><span>{{ number_format($dueBalance, 2) }} {{ __('accounting.currency.sar') }}</span></td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Invoice Payment Items List -->
                            <div class="card-body px-0 mx-4 address-box radius-xl pb-0 mb-5 overflow-hidden">
                                <h6 class="text-dark ml-4 mb-4">{{ __('accounting.customers.sections.item_list') }}</h6>
                                <div class="userDatatable projectDatatable project-table bg-white w-100 border-0">
                                    <div class="table-responsive">
                                        <table class="table mb-0 table-borderless new-header" id="content_table">
                                            <thead>
                                                <tr class="userDatatable-header">
                                                    <th>
                                                        <span class="projectDatatable-title">{{ __('accounting.customers.table.headers.date') }}</span>
                                                    </th>
                                                    <th>
                                                        <span class="projectDatatable-title">{{ __('accounting.customers.table.headers.invoice') }}</span>
                                                    </th>
                                                    <th>
                                                        <span class="projectDatatable-title">{{ __('accounting.customers.table.headers.payment') }}</span>
                                                    </th>
                                                    <th>
                                                        <div class="">
                                                            <span class="projectDatatable-title">{{ __('accounting.customers.table.headers.amount') }}</span>
                                                        </div>
                                                    </th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @forelse($invoicePayments as $payment)
                                                    <tr>
                                                        <td>{{ isset($payment['date']) ? \Carbon\Carbon::parse($payment['date'])->format('Y-m-d') : '-' }}</td>
                                                        <td>
                                                            @php
                                                                // Check for various possible field names for invoice/document number
                                                                $invoiceNumber = $payment['document_number'] ??
                                                                               $payment['invoice_number'] ??
                                                                               $payment['bill_number'] ??
                                                                               $payment['reference'] ??
                                                                               null;
                                                                $invoiceId = $payment['document_id'] ??
                                                                           $payment['invoice_id'] ??
                                                                           $payment['bill_id'] ??
                                                                           $payment['id'] ??
                                                                           null;
                                                            @endphp
                                                            @if($invoiceNumber)
                                                                @if($invoiceId)
                                                                    @php
                                                                        try {
                                                                            $invoiceUrl = route('finance.invoice.view', $invoiceId);
                                                                        } catch (\Exception $e) {
                                                                            $invoiceUrl = '#';
                                                                        }
                                                                    @endphp
                                                                    <a href="{{ $invoiceUrl }}">
                                                                        <span class="fw-400 badge badge-round badge-primary badge-lg badge-outlined">
                                                                            {{ $invoiceNumber }}
                                                                        </span>
                                                                    </a>
                                                                @else
                                                                    <span class="fw-400 badge badge-round badge-primary badge-lg badge-outlined">
                                                                        {{ $invoiceNumber }}
                                                                    </span>
                                                                @endif
                                                            @else
                                                                <span>-</span>
                                                            @endif
                                                        </td>
                                                        <td>{{ $payment['payment_type'] ?? $payment['type'] ?? 'Cash' }}</td>
                                                        <td>
                                                            <div class="d-flex align-items-center gap-10">
                                                                <img src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSx34eO8RbZlsetN1iAQtxuOLnLOlyJbJmU7Q&s" width="15">
                                                                <span class="text-new-primary">{{ number_format($payment['amount'] ?? 0, 2) }}</span>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                @empty
                                                    <tr>
                                                        <td colspan="4" class="text-center py-4">
                                                            {{ __('accounting.customers.messages.no_payment_records_found') }}
                                                        </td>
                                                    </tr>
                                                @endforelse
                                            </tbody>
                                            @if(!empty($invoicePayments))
                                                <tfooter>
                                                    <tr>
                                                        <td colspan="2"></td>
                                                        <td><h6>{{ __('accounting.customers.table.headers.total') }}</h6></td>
                                                        <td>
                                                            <div class="d-flex align-items-center gap-10">
                                                                <img src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSx34eO8RbZlsetN1iAQtxuOLnLOlyJbJmU7Q&s" width="15">
                                                                <span class="text-new-primary">{{ number_format($amountPaid, 2) }}</span>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                </tfooter>
                                            @endif
                                        </table>
                                    </div>
                                </div>

                                <!-- Pagination -->
                                @if(!$loading && !$error && $total > 0)
                                    <div class="d-flex justify-content-md-between flex-column flex-md-row justify-content-center align-items-center mt-4 gap-10">
                                        <div class="">
                                            <ul class="atbd-pagination d-flex justify-content-between">
                                                <li>
                                                    <div class="paging-option">
                                                        <div class="dataTables_length d-flex">
                                                            <label class="d-flex align-items-center mb-0">
                                                                <select aria-controls="statement_table" wire:model.live.debounce.250ms="perPage"
                                                                        class="custom-select custom-select-sm form-control form-control-sm mx-2"
                                                                        style="min-height: 35px;">
                                                                    <option value="5">5</option>
                                                                    <option value="10">10</option>
                                                                    <option value="25">25</option>
                                                                    <option value="50">50</option>
                                                                    <option value="100">100</option>
                                                                </select>
                                                                <span class="no-wrap">{{ __('accounting.customers.pagination.entries_per_page') }}</span>
                                                            </label>
                                                        </div>
                                                    </div>
                                                </li>
                                            </ul>
                                        </div>
                                        <div class="">
                                            <div class="user-pagination">
                                                <div class="user-pagination new-pagination">
                                                    <div class="d-flex justify-content-sm-end justify-content-end">
                                                        <nav>
                                                            <span class="relative z-0 inline-flex rounded-md d-flex new-pagination-section flex-wrap">
                                                                <span class="">
                                                                    <span aria-disabled="{{ $currentPage <= 1 ? 'true' : 'false' }}" aria-label="&laquo; Previous">
                                                                        <button class="border-0 {{ $currentPage <= 1 ? 'disabled' : '' }}"
                                                                                {{ $currentPage <= 1 ? 'disabled' : '' }}
                                                                                wire:click="previousPage">
                                                                            <i class="iconsax icon-bold fs-16 mr-0 rotate-ar-y d-inline-block" icon-name="chevron-left"></i>
                                                                        </button>
                                                                    </span>
                                                                </span>

                                                                @for($page = 1; $page <= $lastPage; $page++)
                                                                    <span wire:key="paginator-page-{{ $page }}">
                                                                        <button type="button"
                                                                                class="border-0 {{ $currentPage == $page ? 'current-page' : '' }}"
                                                                                {{ $currentPage == $page ? 'disabled' : '' }}
                                                                                wire:click="gotoPage({{ $page }})">
                                                                            {{ $page }}
                                                                        </button>
                                                                    </span>
                                                                @endfor

                                                                <span>
                                                                    <button type="button"
                                                                            class="border-0 {{ $currentPage >= $lastPage ? 'disabled' : '' }}"
                                                                            {{ $currentPage >= $lastPage ? 'disabled' : '' }}
                                                                            wire:click="nextPage">
                                                                        <i class="iconsax icon-bold fs-16 mr-0 rotate-ar-y d-inline-block" icon-name="chevron-right"></i>
                                                                    </button>
                                                                </span>
                                                            </span>
                                                        </nav>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div>
                                            <p class="text-sm text-gray-700 leading-5 mb-0">
                                                <span>{{ __('accounting.customers.pagination.showing') }}</span>
                                                <span class="font-medium">{{ ($currentPage - 1) * $perPage + 1 }}</span>
                                                <span>{{ __('accounting.customers.pagination.to') }}</span>
                                                <span class="font-medium">{{ min($currentPage * $perPage, $total) }}</span>
                                                <span>{{ __('accounting.customers.pagination.of') }}</span>
                                                <span class="font-medium">{{ $total }}</span>
                                                <span>{{ __('accounting.customers.pagination.results') }}</span>
                                            </p>
                                        </div>
                                    </div>
                                @endif
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

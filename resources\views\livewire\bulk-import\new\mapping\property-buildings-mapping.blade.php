<div>
    <div class = "userDatatable projectDatatable project-table bg-white w-100 border-0">
        <div class = "d-flex gap-10 pb-3 border-bottom mb-3">
            <div class = "d-flex gap-5 align-items-center">
                <i class = "iconsax text-black fs-16" icon-name = "task-list-square"></i> 
                <span class = "text-black">{{ $list->total() }} @lang('import.total')</span>
            </div>
            
            @php
                $listData = $this->manageListStatusData();
            @endphp

            <div class = "d-flex gap-5 align-items-center">
                <i class = "iconsax text-success fs-16" icon-name = "tick-square"></i> 
                <span class = "text-success">{{ isset($listData) && count($listData) > 0 ? $listData['acceptedNumber'] : 0 }} @lang('import.accepted')</span>
            </div>
            <div class = "d-flex gap-5 align-items-center">
                <i class = "iconsax text-danger fs-16" icon-name = "info-circle"></i> 
                <span class = "text-danger">{{ isset($listData) && count($listData) > 0 ? $listData['refusedNumber'] : 0 }} @lang('import.data_issues')</span>
            </div>
        </div>
        <div class = "table-responsive">
            <table class = "table mb-0 accordion">
                <thead>
                    <tr>
                        <th scope = "col">@lang("import.property_name")</th>
                        <th scope = "col">@lang("import.building_name")</th>
                        <th scope = "col">@lang("import.zone")</th>
                        <th scope = "col">@lang("import.unit")</th>
                        <th scope = "col">@lang("import.unit_type")</th>
                        <th scope = "col">@lang("import.status")</th>
                    </tr>
                </thead>
                <tbody>
                    @if(isset($list) && $list->count())
                        @foreach($list as $key => $data)
                            <tr wire:key = "item-buildings-{{ $key }}"> 
                                <td>{{ $data['property_name'] ?? '-' }}</td>
                                <td>{{ $data['building_name'] ?? '-' }}</td>
                                <td>{{ $data['zone'] ?? '-' }}</td>
                                <td>{{ $data['unit'] ?? '-' }}</td>
                               <td>{{ $data['unit_type'] ?? '-' }}</td>
                               <td>
                                    @if(!is_null($this->fullBuildingsValidation($data)) && $this->fullBuildingsValidation($data)[0]['status'] == 'success')
                                        <p class = "text-success">
                                            @lang('import.ready_insert')
                                        </p>
                                    @else
                                        <p>
                                            <a href = "javascript:void(0)" class = "bold-700 accordion-toggle text-danger" style = "text-decoration: underline" data-toggle = "collapse" data-target = "#error-{{ $key }}">
                                                @lang('import.view_errors')
                                            </a>
                                        </p>
                                    @endif
                               </td>
                            </tr>
                            <tr>
                                <td colspan = "6" class = "hiddenRow">
                                    <div class = "accordian-body collapse" id = "error-{{ $key }}">
                                        <table class = "table mb-0">
                                            <tbody>
                                                @if(!is_null($this->fullBuildingsValidation($data)) && $this->fullBuildingsValidation($data)[0]['status'] <> 'success')
                                                    <ul class = "list-group list-group-flush border">
                                                        @foreach($this->fullBuildingsValidation($data)[0]['errors'] as $row)
                                                            <li class = "list-group-item fs-12">{!! $row ?? '-' !!}</li>
                                                        @endforeach
                                                    </ul>
                                                @endif
                                            </tbody>
                                        </table>
                                    </div>
                                </td>
                            </tr>
                        @endforeach

                        @if ($list->hasMorePages())
                            <tr>
                                <td colspan = "6">
                                    <div class = "d-flex justify-content-center gap-2">
                                        <div class = "p-2">
                                            <div wire:loading wire:target = "managePerPage">
                                                <button type = "button" class = "btn btn-primary" wire:loading.attr = "disabled">
                                                    <span class = "spinner-border spinner-border-sm" role = "status" aria-hidden = "true"></span>
                                                    @lang('import.loading3')
                                                </button>
                                            </div>
                                            <button type = "button" class = "btn btn-primary text-center" wire:target = "managePerPage" wire:click = "managePerPage" wire:loading.class = "hide">
                                                @lang('import.load_more')
                                            </button>
                                        </div>
                                        <div class = "p-2">
                                            <div wire:loading wire:target = "manageLoadAll">
                                                <button type = "button" class = "btn btn-info" wire:loading.attr = "disabled">
                                                    <span class = "spinner-border spinner-border-sm" role = "status" aria-hidden = "true"></span>
                                                    @lang('import.loading3')
                                                </button>
                                            </div>
                                            <button type = "button" class = "btn btn-info" wire:target = "manageLoadAll" wire:click = "manageLoadAll" wire:loading.class = "hide">
                                                @lang('import.load_all')
                                            </button>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        @endif
                    @else
                        <tr class = "text-center">
                            <td colspan = "6">@lang("import.empty_property_building")</td>
                        </tr>
                    @endif
                </tbody>
            </table>
        </div>
    </div>
</div>

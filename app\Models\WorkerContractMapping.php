<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class WorkerContractMapping extends Model
{
    use HasFactory;

    protected $table = 'worker_contracts_mapping'; // Explicitly define the table name
    protected $fillable = ['worker_id', 'contract_id', 'start_date'];


    public function worker()
    {
        return $this->belongsTo(User::class, 'worker_id'); 
    }


    public function getWorkerAttendance($start_date,$end_date,$worker_id,$properties)
    {
        $properties = explode(',', $properties);
        $workerAttendances =WorkerAttendances::where('worker_id', $worker_id)
                ->whereIn('clock_in_property_id', $properties)
                ->whereBetween('clock_in_datetime', [$start_date.' 00:00:00', $end_date.' 23:59:59'])
                ->orderBy('clock_in_datetime')
                ->select('id','worker_id','clock_in_property_id','clock_in_datetime','total_hrs')
                ->get();
       
        $grouped = $workerAttendances->groupBy(function ($item) {
                        return \Carbon\Carbon::parse($item->clock_in_datetime)->format('Y-m-d');
                    })->map(function ($items) {
                        $totalMinutes = 0;

                        // foreach ($items as $item) {
                        //     [$hrs, $mins] = explode(':', $item->total_hrs);
                        //     $totalMinutes += ($hrs * 60) + $mins;
                        // }

                        foreach ($items as $item) {
                            $timeParts = explode(':', $item->total_hrs);
                            $hrs = isset($timeParts[0]) ? (int)$timeParts[0] : 0;
                            $mins = isset($timeParts[1]) ? (int)$timeParts[1] : 0;
                            $totalMinutes += ($hrs * 60) + $mins;
                        }

                        // Convert total minutes back to hh:mm format
                        $hours = floor($totalMinutes / 60);
                        $minutes = $totalMinutes % 60;

                        return sprintf('%02d:%02d', $hours, $minutes);
                    });
        return $grouped->toArray();
    }
    
}

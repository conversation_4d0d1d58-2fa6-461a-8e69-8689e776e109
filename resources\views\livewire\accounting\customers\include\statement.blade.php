 <div class="tab-pane fade show active" id="customer-statement" role="tabpanel" aria-labelledby="contact-tab">
    <div class="checkout pt-2 pb-20 mb-30 w-100">
        <div class="row">
            <div class="col-lg-3 pr-md-0 mb-3 mb-md-0">
                <div class="card p-3">
                    
            <form class="fs-14">
              <div class="form-group">
                <label for="">{{ __('accounting.customers.forms.from_date') }}</label>
                <div class="position-relative">
                <input type="text" class="form-control datepicker" id="" aria-describedby="emailHelp" placeholder="{{ __('accounting.customers.forms.enter_start_date') }}">
                <i class="iconsax field-icon text-light" icon-name="calendar-1"></i>
                </div>
                <!-- <small id="emailHelp" class="form-text text-muted">We'll never share your email with anyone else.</small> -->
              </div>
              <div class="form-group">
                <label for="">{{ __('accounting.customers.forms.to_date') }}</label>
                <div class="position-relative">
                <input type="text" class="form-control datepicker" id="" placeholder="{{ __('accounting.customers.forms.enter_end_date') }}">
                <i class="iconsax field-icon text-light" icon-name="calendar-1"></i>
                </div>
              </div>
              <div class="d-flex gap-10 mt-4 justify-content-end">
                <button type="submit" class="btn bg-warning btn-sm text-white radius-xl">{{ __('accounting.customers.forms.reset') }}</button>
                <button type="submit" class="btn bg-new-primary btn-sm text-white radius-xl">{{ __('accounting.customers.forms.submit') }}</button>
              </div>
            </form>
            </div>
        </div>
            <div class="col-lg-9 fs-14">
                <div class="card" id="overview-section">
                        <div class="card-header py-3">
                                <div class="">
                                    <img src="https://workdo-dev.osool.cloud/uploads/logo/logo_dark.png" alt="Work Do" class="logo logo-lg" style="max-width: 250px">
                                </div>
                                <div class="">
                                    <strong class="mb-2">My Company</strong><br>
                                    <span class="invoice-number fs-14"><EMAIL></span>
                                </div>
                        </div>
                    <div class="card-header">
                        <h5 class="mb-sm-0 mb-3">{{ __('accounting.customers.tabs.statement') }}</h5>

                        <div class="d-flex flex-wrap align-items-center">
                            <span class="mr-3">2025-06-1 to 2025-06-30</span>
                            <button class="btn btn-xs bg-loss text-white"><i class="iconsax" icon-name="download-1"></i> {{ __('accounting.customers.buttons.download') }}</button>
                        </div>
                    </div>
                    <div class="p-4">

                            <div class="row">
                                <div class="col-md-4 mb-sm-0 mb-3 pr-md-0">
                                    <div class="p-3 address-box radius-xl h-100">
                                        <h6 class="text-dark mb-3 fs-14">{{ __('accounting.customers.sections.billed_to') }}</h6>
                                        <table class="table mb-0 table-borderless new-header td-pl-0 table-address">
                                            <tbody><tr>
                                                <td><span class="fs-12 fw-50 text-dark">{{ __('accounting.billing_address') }}</span>
                                                </td>
                                                <td>Riyad
                                                </td>
                                            </tr>
                                            <tr>
                                                <td><span class="fs-12 fw-50 text-dark">{{ __('accounting.billing_city') }}</span>
                                                </td>
                                                <td>riyad
                                                </td>
                                            </tr>
                                            <tr>
                                                <td><span class="fs-12 fw-50 text-dark">{{ __('accounting.billing_zip') }}</span></td>
                                                <td>101</td>
                                            </tr>
                                            <tr>
                                                <td><span class="fs-12 fw-50 text-dark">{{ __('accounting.billing_country') }}</span>
                                                </td>
                                                <td>Saudi Arabia
                                                </td>
                                            </tr>
                                            <tr>
                                                <td><span class="fs-12 fw-50 text-dark">{{ __('accounting.billing_contact') }}</span>
                                                </td>
                                                <td>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    <strong>{{ __('accounting.customers.fields.tax_number') }}  :
                                                   </strong>
                                                </td>
                                                <td>
                                                    TAX1234
                                                </td>
                                            </tr>
                                        </tbody></table>
                                    </div>
                                </div>
                                <div class="col-md-4 mb-sm-0 mb-3 pr-md-0">
                                    <div class="p-3 address-box radius-xl h-100">
                                        <h6 class="text-dark mb-3 fs-14">{{ __('accounting.customers.sections.shipped_to') }}</h6>
                                        <table class="table mb-0 table-borderless new-header td-pl-0 table-address">
                                            <tbody><tr>
                                                <td><span class="fs-12 fw-50 text-dark">{{ __('accounting.shipping_address') }}</span>
                                                </td>
                                                <td>Riyad
                                                </td>
                                            </tr>
                                            <tr>
                                                <td><span class="fs-12 fw-50 text-dark">{{ __('accounting.shipping_city') }}</span>
                                                </td>
                                                <td>riyad
                                                </td>
                                            </tr>
                                            <tr>
                                                <td><span class="fs-12 fw-50 text-dark">{{ __('accounting.shipping_zip') }}</span></td>
                                                <td>101</td>
                                            </tr>
                                            <tr>
                                                <td><span class="fs-12 fw-50 text-dark">{{ __('accounting.shipping_country') }}</span>
                                                </td>
                                                <td>Saudi Arabia
                                                </td>
                                            </tr>
                                            <tr>
                                                <td><span class="fs-12 fw-50 text-dark">{{ __('accounting.shipping_contact') }}</span>
                                                </td>
                                                <td>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    <strong>{{ __('accounting.customers.fields.tax_number') }}  :
                                                   </strong>
                                                </td>
                                                <td>
                                                    TAX1234
                                                </td>
                                            </tr>
                                        </tbody></table>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="p-3 address-box radius-xl h-100">
                                        <h6 class="text-dark mb-3 fs-14">{{ __('accounting.customers.sections.account_summary') }}</h6>
                                        <table class="table mb-0 table-borderless new-header no-wrap tp-0" id="content_table">
                <tbody>
                        <tr>
                            <td><span class="fs-12 fw-50 text-dark"> {{ __('accounting.customers.fields.balance') }} : </span>  </td>
                            <td><span> 0.00 ﷼</span></td>
                        </tr>
                         <tr>
                            <td><span class="fs-12 fw-50 text-dark"> {{ __('accounting.customers.fields.invoiced_amount') }} :</span>  </td>
                            <td><span> 0.00 ﷼</span></td>
                        </tr>
                         <tr>
                            <td><span class="fs-12 fw-50 text-dark"> {{ __('accounting.customers.fields.amount_paid') }} :</span> </td>
                            <td><span> 0.00 ﷼</span></td>
                        </tr>
                         <tr>
                            <td> <span class="fs-12 fw-50 text-dark"> {{ __('accounting.customers.fields.balance_due') }} :</span>   </td>
                            <td><span> 0.00 ﷼</span></td>
                        </tr>
                </tbody>
            </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <div class="card-body px-0 mx-4 address-box radius-xl pb-0 mb-5 overflow-hidden">
                        <h6 class="text-dark ml-4 mb-4">{{ __('accounting.customers.sections.item_list') }}</h6>
                        <div>
    <div class="userDatatable projectDatatable project-table bg-white w-100 border-0">
        <div class="table-responsive">
            <table class="table mb-0 table-borderless new-header" id="content_table">
                <thead>
                    <tr class="userDatatable-header">
                        <th>
                            <span class="projectDatatable-title">{{ __('accounting.customers.table.headers.date') }}</span>
                        </th>
                        <th>
                            <span class="projectDatatable-title">{{ __('accounting.customers.table.headers.invoice') }}</span>
                        </th>
                        <th>
                            <span class="projectDatatable-title">{{ __('accounting.customers.table.headers.payment') }}</span>
                        </th>
                        <th>
                            <div class="">
                                <span class="projectDatatable-title">{{ __('accounting.customers.table.headers.amount') }}</span>
                            </div>
                        </th>
                    </tr>
                </thead>

                <tbody>
                        <tr>
                            <td>2025-06-1</td>
                            <td><a href=""><span class="fw-400 badge badge-round badge-primary badge-lg badge-outlined">INV-**********</span></a></td>
                            <td>Cash</td>
                            <td>
                                <div class="d-flex align-items-center gap-10">
                                    <img src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSx34eO8RbZlsetN1iAQtxuOLnLOlyJbJmU7Q&amp;s" width="15"><span class="text-new-primary">25000</span>
                                </div>
                            </td>
                        </tr>
                </tbody>
                <tfooter>
                    <tr>
                        <td colspan="2"></td>
                            <td><h6>{{ __('accounting.customers.table.headers.total') }}</h6></td>
                            <td>
                                <div class="d-flex align-items-center gap-10">
                                    <img src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSx34eO8RbZlsetN1iAQtxuOLnLOlyJbJmU7Q&amp;s" width="15"><span class="text-new-primary">25000</span>
                                </div>
                            </td>
                        </tr>
                </tfooter>
            </table>
            
            
</div>
        <div></div>
    </div>
</div>

<!-- Livewire Component wire-end:vxqzdvCEd0GgkweiPbMf -->                    </div>
                </div>
            </div>
        </div>
    </div>
  </div>
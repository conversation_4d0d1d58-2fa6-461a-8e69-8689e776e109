<div class="table-responsive">
    <div class="card">
        <div class="">
            <div class="card-header py-4 px-3 border-0 d-flex justify-content-between align-items-center">
                <h6 class="text-capitalize fw-500 mb-3 mb-sm-0">@lang('CRMProjects.common.tangibles_tasks_list')</h6>

                <div class="d-flex gap-10 table-search">
                    {{--  <div class="position-relative">
                        <input type="text" class="form-control" placeholder="@lang('CRMProjects.common.search')" >
                        <i class="iconsax field-icon fs-18 mr-0" icon-name="search-normal-2"></i>
                    </div> --}}
                    {{--  <button class="btn bg-grey text-dark"><i class="iconsax icon fs-22 mr-0" icon-name="upload-1"></i>
                        @lang('CRMProjects.common.export')</button> --}}
                </div>
            </div>
        </div>
        <div class="card-body px-0 pt-0">
            <div class="userDatatable projectDatatable project-table bg-white w-100 border-0">
                <div class="table-responsive">
                    <table class="table mb-0 radius-0">
                        <thead>
                            <tr class="userDatatable-header">

                                <th>
                                    @lang('Title')

                                </th>


                                <th>
                                    @lang('CRMProjects.common.stage')
                                </th>
                                <th>
                                    @lang('CRMProjects.work_order_date')
                                </th>
                                <th>
                                    @lang('CRMProjects.common.supervisor')
                                </th>
                                <th>
                                    @lang('work_order.forms.label.worker')
                                </th>
                                <th>
                                    @lang('CRMProjects.work_order_type')
                                </th>

                                <th>
                                    @lang('CRMProjects.common.action')
                                </th>
                            </tr>
                        </thead>

                        <tbody class="sort-table ui-sortable">
                            @if (count($paginatedWorkOrders) > 0)
                                @foreach ($paginatedWorkOrders as $work_Order)
                                    <tr class="ui-sortable-handle" style="opacity: 1;">
                                        <td>
                                            <div class="d-flex userDatatable-content mb-0 align-items-center">
                                                <span>{{ $work_Order->work_order_id ?? 'No Title' }}</span>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex userDatatable-content mb-0 align-items-center">
                                                @if ($work_Order->workorder_journey == 'job_execution')
                                                    <small class="py-1 px-2 bg-draft rounded text-dark">
                                                        {{ __(ucwords(str_replace('_', ' ', $work_Order->workorder_journey))) }}
                                                    </small>
                                                @elseif ($work_Order->workorder_journey == 'job_evaluation')
                                                    <small class="py-1 px-2 bg-warning rounded text-white">
                                                        {{ __(ucwords(str_replace('_', ' ', $work_Order->workorder_journey))) }}
                                                    </small>
                                                @elseif($work_Order->workorder_journey == 'job_approval')
                                                    <small class="py-1 px-2 bg-win  rounded text-white">
                                                        {{ __(ucwords(str_replace('_', ' ', $work_Order->workorder_journey))) }}
                                                    </small>
                                                @elseif($work_Order->workorder_journey == 'finished')
                                                    <small class="py-1 px-2 bg-sucess  rounded text-white">
                                                        {{ __(ucwords(str_replace('_', ' ', $work_Order->workorder_journey))) }}
                                                    </small>
                                                @else
                                                    <small class="py-1 px-2 bg-hold rounded text-white">
                                                        {{ __(ucwords(str_replace('_', ' ', $work_Order->workorder_journey))) }}
                                                    </small>
                                                @endif
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex userDatatable-content mb-0 align-items-center">

                                                <span>{{ date('d M Y', strtotime($work_Order->start_date ?? $work_Order->created_at)) }}</span>
                                            </div>
                                        </td>
                                        <td>
                                                @if (!$work_Order?->AssignedSupervisor)
                                                @lang('CRMProjects.common.not_assigned')
                                            @elseif(!empty($work_Order->AssignedSupervisor->name))
                                                {{ $work_Order->AssignedSupervisor->name }}
                                            @else
                                                ---
                                            @endif
                                        </td>
                                        <td>
                                              @if (!$work_Order->is_collaborative)
                                      <div class="d-flex justify-content-between align-items-center mt-1">
                                     
                                        <span class="fw-600">
                                            @if (!$work_Order?->worker)
                                                @lang('CRMProjects.common.not_assigned')
                                            @elseif(!empty($work_Order->worker->name))
                                                {{ $work_Order->worker->name }}
                                            @else
                                                ---
                                            @endif
                                        </span>
                                    </div>
                                    @else
                                   <div class="d-flex justify-content-between align-items-center mt-1">
                                        <span class="fw-600 text-danger"> @lang('CRMProjects.common.collaborative') </span>
                                       
                                    </div>
                                    @endif
                                        </td>
                                        <td>
                                            @if ($work_Order->work_order_type == 'preventive')
                                                <small class="bg-draft rounded text-dark status-badge">

                                                    @lang('CRMProjects.common.preventive')
                                                </small>
                                            @else
                                                <small class="bg-draft rounded text-dark status-badge">
                                                    @lang('CRMProjects.common.reactive')

                                                </small>
                                            @endif


                                        </td>


                                        <td>
                                            <div class="d-inline-block">

                                                <ul class="mb-0 d-flex flex-wrap gap-10">
                                                    <li>
                                                        <a href="javascript:void(0);" wire:click="showTaskDetails({{ $work_Order->workdo_task_id }})">
                                                            <i class="iconsax icon text-osool fs-18"
                                                                icon-name="eye"></i>
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <a href="javascript:void(0);" wire:click="editTangibleTask({{ $work_Order->workdo_task_id }})">
                                                            <i class="iconsax icon text-new-primary fs-18"
                                                                icon-name="edit-1"></i>
                                                        </a>
                                                    </li>
                                                        <li>
                                                            <a href="javascript:void(0);"
                                                               wire:click="openDeleteModalTangibleTask({{ $work_Order->workdo_task_id }},'{{ $work_Order->work_order_id }}')">
                                                                <i class="iconsax icon text-delete fs-18"
                                                                    icon-name="trash"></i>
                                                            </a>
                                                        </li>

                                                </ul>

                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                            @else
                                <tr>

                                    <td colspan="7">
                                        <div class="form-group col-12">
                                            <div class="item-inner">
                                                <div class="item-content">
                                                    <div class="image-upload border-0 radius-xl">
                                                        <label class="mb-2 mt-4">
                                                            <div class="h-100">
                                                                <div class="dplay-tbl">
                                                                    <div class="dplay-tbl-cell">
                                                                        <div
                                                                            class="upload-bg wh-50 d-flex-center rounded-circle mx-auto position-relative">
                                                                            <i class="iconsax icon fs-22 fs-22 mr-0 text-dark position-relative"
                                                                                icon-name="emoji-sad"></i>
                                                                        </div>
                                                                        <p class="drag_drop_txt mt-3">
                                                                            @lang('CRMProjects.common.no_projects_yet')</p>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            @endif

                        </tbody>

                    </table>
                    
                    <div class="row justtify-content-center">
                        <div class="col-auto mx-auto">
                      
                            {{ $paginatedWorkOrders->links('livewire.c-r-m-projects.work-order.list.custom-pagination-links-view') }}
                        </div>
                    </div>


                </div>
             
            </div>
        </div>
    </div>


</div>

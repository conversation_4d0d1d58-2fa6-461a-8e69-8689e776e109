@extends('layouts.app')
@section('styles')
@endsection
@section('content')
        <div class="contents crm">
           <div class="container-fluid">
            <div class="col-lg-12">
    <div class="row justify-content-sm-between align-items-center justify-content-center my-3 flex-sm-row flex-column">
        <div class="page-title-wrap p-0">
            <div class="page-title d-flex justify-content-between">
                <div class="page-title__left justify-content-sm-between align-items-center justify-content-center">
                    <div class="user-member__title mr-sm-25 ml-0">
                        <h4 class="text-capitalize fw-500 breadcrumb-title fs-16">
                            Manage Customers
                        </h4>
                    </div>
                </div>
            </div>
            <div>
                <ul class="atbd-breadcrumb nav">
                    <li class="atbd-breadcrumb__item">
                        <a>Dashboard</a>
                        <span class="breadcrumb__seperator">
                            <span class="la la-angle-right"></span>
                        </span>
                    </li>
                    <li class="atbd-breadcrumb__item">
                        <a>Manage Customers</a>
                    </li>
                </ul>
            </div>
        </div>

        <div class="d-flex gap-10 breadcrumb_right_icons">
            <div class="d-flex gap-10 breadcrumb_right_icons">
                    <button class="btn btn-white btn-default text-center svg-20 wh-45" wire:click="switchView('cards')">
                        <i class="iconsax icon text-new-primary fs-22 colorRed mr-0" icon-name="task-list"></i>
                    </button>

                    <button class="btn btn-default btn-primary w-100 no-wrap" type="button" aria-expanded="false" data-toggle="modal" data-target="#create-customer"><i class="las la-plus fs-16"></i>Create</button>

            </div>
        </div>
        <!--====End Design for Export PDF===-->
    </div>
</div>


<div class="table-responsive">
    <div class="card">
        <div class="">
            <div class="card-header py-4 px-3 border-0 d-flex justify-content-between align-items-center">
                <h6 class="text-capitalize text-osool fw-500 mb-3 mb-sm-0">Customers</h6>

                <div class="d-flex gap-10 table-search">
                    <div class="position-relative">
                    <input type="text" class="form-control" placeholder="Search">
                    <i class="iconsax field-icon fs-18 mr-0" icon-name="search-normal-2"></i>
                </div>
                     <button class="btn btn-export text-dark"><i class="iconsax icon fs-22 mr-0" icon-name="upload-1"></i> Export</button>
                </div>
            </div>
        </div>
        <div class="card-body px-0 pt-0">
            <div class="row">
                <div class="col-md-4 mb-3">
                    <div class="radius-xl new-shadow">
                        <div class="d-flex p-3 align-items-center">
                            <div class="mr-3 wh-60 fs-16 d-center bg-warning radius-xl text-white">
                                SS
                            </div>
                            <div>
                                <h2 class="fw-500 mb-1 fs-14 text-new-primary">Saeed Saleh</h2>
                                <strong class="">Vend1234</strong>
                                <p class="mb-0">
                                    <EMAIL>
                                </p>
                            </div>
                        </div>
                        <p class="mb-1 px-2 border-top py-3 d-flex">
                            <button class="btn p-0 btn-default h-20 d-center gap-5 flex-fill">
                                <i class="iconsax icon fs-14 mr-0  text-sool" icon-name="edit-1"></i> <span class="fs-14 text-dark fw-600">Edit</span> 
                            </button>
                            <button class="btn p-0 btn-default h-20 d-center gap-5 flex-fill border-right border-left">
                                <i class="iconsax icon fs-14 mr-0  text-sool" icon-name="eye"></i>  <span class="fs-14 text-dark fw-600">View </span> 
                            </button>
                            <button class="btn p-0 btn-default h-20 d-center gap-5 flex-fill">
                                <i class="iconsax icon fs-14 mr-0  text-sool" icon-name="trash"></i>  <span class="fs-14 text-dark fw-600">Delete </span> 
                            </button>
                        </p>
                    </div>
                </div>

                <div class="col-md-4 mb-3">
                    <div class="radius-xl new-shadow">
                        <div class="d-flex p-3 align-items-center">
                            <div class="mr-3 wh-60 fs-16 d-center bg-new-primary radius-xl text-white">
                                SS
                            </div>
                            <div>
                                <h2 class="fw-500 mb-1 fs-14 text-new-primary">Saeed Saleh</h2>
                                <strong class="">Vend1234</strong>
                                <p class="mb-0">
                                    <EMAIL>
                                </p>
                            </div>
                        </div>
                        <p class="mb-1 px-2 border-top py-3 d-flex">
                            <button class="btn p-0 btn-default h-20 d-center gap-5 flex-fill">
                                <i class="iconsax icon fs-14 mr-0  text-sool" icon-name="edit-1"></i> <span class="fs-14 text-dark fw-600">Edit</span> 
                            </button>
                            <button class="btn p-0 btn-default h-20 d-center gap-5 flex-fill border-right border-left">
                                <i class="iconsax icon fs-14 mr-0  text-sool" icon-name="eye"></i>  <span class="fs-14 text-dark fw-600">View </span> 
                            </button>
                            <button class="btn p-0 btn-default h-20 d-center gap-5 flex-fill">
                                <i class="iconsax icon fs-14 mr-0  text-sool" icon-name="trash"></i>  <span class="fs-14 text-dark fw-600">Delete </span> 
                            </button>
                        </p>
                    </div>
                </div>

                <div class="col-md-4 mb-3">
                    <div class="radius-xl new-shadow">
                        <div class="d-flex p-3 align-items-center">
                            <div class="mr-3 wh-60 fs-16 d-center bg-loss radius-xl text-white">
                                SS
                            </div>
                            <div>
                                <h2 class="fw-500 mb-1 fs-14 text-new-primary">Saeed Saleh</h2>
                                <strong class="">Vend1234</strong>
                                <p class="mb-0">
                                    <EMAIL>
                                </p>
                            </div>
                        </div>
                        <p class="mb-1 px-2 border-top py-3 d-flex">
                            <button class="btn p-0 btn-default h-20 d-center gap-5 flex-fill">
                                <i class="iconsax icon fs-14 mr-0  text-sool" icon-name="edit-1"></i> <span class="fs-14 text-dark fw-600">Edit</span> 
                            </button>
                            <button class="btn p-0 btn-default h-20 d-center gap-5 flex-fill border-right border-left">
                                <i class="iconsax icon fs-14 mr-0  text-sool" icon-name="eye"></i>  <span class="fs-14 text-dark fw-600">View </span> 
                            </button>
                            <button class="btn p-0 btn-default h-20 d-center gap-5 flex-fill">
                                <i class="iconsax icon fs-14 mr-0  text-sool" icon-name="trash"></i>  <span class="fs-14 text-dark fw-600">Delete </span> 
                            </button>
                        </p>
                        </p>
                    </div>
                </div>

                <div class="col-md-4 mb-3">
                    <div class="radius-xl new-shadow">
                        <div class="d-flex p-3 align-items-center">
                            <div class="mr-3 wh-60 fs-16 d-center bg-hold radius-xl text-white">
                                SS
                            </div>
                            <div>
                                <h2 class="fw-500 mb-1 fs-14 text-new-primary">Saeed Saleh</h2>
                                <strong class="">Vend1234</strong>
                                <p class="mb-0">
                                    <EMAIL>
                                </p>
                            </div>
                        </div>
                        <p class="mb-1 px-2 border-top py-3 d-flex">
                            <span class="d-center gap-10 flex-fill">
                                <i class="iconsax icon fs-22 mr-0  text-sool" icon-name="emoji-happy"></i> <span class="fs-15 text-dark fw-600">50</span> 
                            </span>
                            <span class="d-center gap-10 flex-fill border-right border-left">
                                <i class="iconsax icon fs-22 mr-0  text-sool" icon-name="wallet-open-tick"></i>  <span class="fs-15 text-dark fw-600">50 </span> 
                            </span>
                            <span class="d-center gap-10 flex-fill">
                                <i class="iconsax icon fs-22 mr-0  text-sool" icon-name="dollar-circle"></i>  <span class="fs-15 text-dark fw-600">50 </span> 
                            </span>
                        </p>
                    </div>
                </div>

                <div class="col-md-4 mb-3">
                    <div class="radius-xl new-shadow">
                        <div class="d-flex p-3 align-items-center">
                            <div class="mr-3 wh-60 fs-16 d-center bg-primary radius-xl text-white">
                                SS
                            </div>
                            <div>
                                <h2 class="fw-500 mb-1 fs-14 text-new-primary">Saeed Saleh</h2>
                                <strong class="">Vend1234</strong>
                                <p class="mb-0">
                                    <EMAIL>
                                </p>
                            </div>
                        </div>
                        <p class="mb-1 px-2 border-top py-3 d-flex">
                            <button class="btn p-0 btn-default h-20 d-center gap-5 flex-fill">
                                <i class="iconsax icon fs-14 mr-0  text-sool" icon-name="edit-1"></i> <span class="fs-14 text-dark fw-600">Edit</span> 
                            </button>
                            <button class="btn p-0 btn-default h-20 d-center gap-5 flex-fill border-right border-left">
                                <i class="iconsax icon fs-14 mr-0  text-sool" icon-name="eye"></i>  <span class="fs-14 text-dark fw-600">View </span> 
                            </button>
                            <button class="btn p-0 btn-default h-20 d-center gap-5 flex-fill">
                                <i class="iconsax icon fs-14 mr-0  text-sool" icon-name="trash"></i>  <span class="fs-14 text-dark fw-600">Delete </span> 
                            </button>
                        </p>
                    </div>
                </div>

                <div class="col-md-4 mb-3">
                    <div class="radius-xl new-shadow">
                        <div class="d-flex p-3 align-items-center">
                            <div class="mr-3 wh-60 fs-16 d-center bg-win radius-xl text-white">
                                SS
                            </div>
                            <div>
                                <h2 class="fw-500 mb-1 fs-14 text-new-primary">Saeed Saleh</h2>
                                <strong class="">Vend1234</strong>
                                <p class="mb-0">
                                    <EMAIL>
                                </p>
                            </div>
                        </div>
                        <p class="mb-1 px-2 border-top py-3 d-flex">
                            <button class="btn p-0 btn-default h-20 d-center gap-5 flex-fill">
                                <i class="iconsax icon fs-14 mr-0  text-sool" icon-name="edit-1"></i> <span class="fs-14 text-dark fw-600">Edit</span> 
                            </button>
                            <button class="btn p-0 btn-default h-20 d-center gap-5 flex-fill border-right border-left">
                                <i class="iconsax icon fs-14 mr-0  text-sool" icon-name="eye"></i>  <span class="fs-14 text-dark fw-600">View </span> 
                            </button>
                            <button class="btn p-0 btn-default h-20 d-center gap-5 flex-fill">
                                <i class="iconsax icon fs-14 mr-0  text-sool" icon-name="trash"></i>  <span class="fs-14 text-dark fw-600">Delete </span> 
                            </button>
                        </p>
                    </div>
                </div>

            </div>
        </div>

    </div>

    
</div>



</div>

           </div>
        </div>


<div class="modal fade" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true" id="create-customer">
<div class="modal-dialog radius-xl modal-lg" role="document">
    <div class="modal-content radius-xl">
        <div class="modal-header">
            <h6 class="modal-title" id="exampleModalLabel">Create Sales Account</h6>
            <button wire:ignore="" type="button" class="close" data-dismiss="modal" aria-label="Close">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-x">
                    <line x1="18" y1="6" x2="6" y2="18"></line>
                    <line x1="6" y1="6" x2="18" y2="18"></line>
                </svg>
            </button>
        </div>
        <form wire:submit.prevent="create">
            <div class="modal-body">
                <div class="row">
                    <div class="col-6">
                        <div class="form-group">
                            <label for="name" class="form-label">Name</label> <span class="text-danger">*</span>
                            <input class="form-control" placeholder="Enter Name" wire:model.defer="name" type="text" id="name" />
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="form-group">
                            <label for="email" class="form-label">Email</label> <span class="text-danger">*</span>
                            <input class="form-control" placeholder="Enter Email" wire:model.defer="email" type="email" id="email" />
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="form-group">
                            <label for="phone" class="form-label">Phone</label> <span class="text-danger">*</span>
                            <input class="form-control" placeholder="Enter Phone" wire:model.defer="phone" type="text" />
                            <div class="text-xs text-danger mt-1">
                                Please use with country code. (ex. +966)
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 col-6">
                        <div class="form-group">
                            <label for="website" class="form-label">Electronic Address</label>
                            <input class="form-control" placeholder="Enter Electronic Address type="text" id="Electronic Address" />
                        </div>
                    </div>
                    <div class="col-md-4 col-6">
                        <div class="form-group">
                            <label for="website" class="form-label">Electronic Address Scheme</label>
                            <input class="form-control" placeholder="Enter Electronic Address Scheme" type="text" id="Electronic Address Scheme" />
                        </div>
                    </div>
                    <div class="col-md-4 col-6">
                        <div class="form-group">
                            <label for="website" class="form-label">Password</label>
                            <input class="form-control" type="Password" placeholder="Enter Electronic Address Scheme" id="Electronic Address Scheme" />
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="form-group">
                            <label for="website" class="form-label">Website</label>
                            <input class="form-control" placeholder="Enter Website" wire:model.defer="website" type="text" id="website" />
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="form-group">
                            <label for="billingaddress" class="form-label">Billing Address</label> <span class="text-danger">*</span>

                            <input class="form-control" placeholder="Billing Address" wire:model.defer="billing_address" type="text" />
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="form-group">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <label for="shippingaddress" class="form-label mb-0">Shipping Address <span class="text-danger">*</span></label>

                                <a class="btn btn-sm align-items-center border p-1" id="copy_billing_to_shipping" data-toggle="tooltip" title="Same As Billing Address" data-bs-placement="top" aria-label="Same As Billing Address">
                                    <i class="fas fa-copy mr-0"></i>
                                </a>
                            </div>
                            <input class="form-control" placeholder="Shipping Address" wire:model.defer="shipping_address" type="text" />
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="form-group">
                            <input class="form-control" placeholder="Billing City" wire:model.defer="billing_city" type="text" />
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="form-group">
                            <input class="form-control" placeholder="Billing State" wire:model.defer="billing_state" type="text" />
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="form-group">
                            <input class="form-control" placeholder="Shipping City" wire:model.defer="shipping_city" type="text" />
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="form-group">
                            <input class="form-control" placeholder="Shipping State" wire:model.defer="shipping_state" type="text" />
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="form-group">
                            <input class="form-control" placeholder="Billing Country" wire:model.defer="billing_country" type="text" />
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="form-group">
                            <input class="form-control" placeholder="Billing Postal Code" wire:model.defer="billing_postalcode" type="number" />
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="form-group">
                            <input class="form-control" placeholder="Shipping Country" wire:model.defer="shipping_country" type="text" />
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="form-group">
                            <input class="form-control" placeholder="Shipping Postal Code" wire:model.defer="shipping_postalcode" type="number" />
                        </div>
                    </div>
                    <div class="col-12">
                        <h4 class="mb-3 fw-500 fs-18">Detail</h4>
                    </div>
                    <div class="col-6">
                        <div class="form-group">
                            <label for="user" class="form-label">Assign User</label>
                            <select class="form-control select2-new select2-hidden-accessible" id="user" wire:model.defer="user" tabindex="-1" aria-hidden="true" data-select2-id="user">
                                <option value="" selected="" data-select2-id="91">--</option>
                                <option value="214">Rashed WhatsApp</option>
                                <option value="216">Abd</option>
                                <option value="251">Moayad</option>
                                <option value="340">Khansaa crm client</option>
                                <option value="373">QA Staff</option>
                                <option value="458">BMA Project</option>
                                <option value="479">BMA-BMA</option>
                                <option value="480">aaaaa</option>
                            </select>
                            <span class="select2 select2-container select2-container--default" dir="ltr" data-select2-id="90" style="width: auto;">
                                <span class="selection">
                                    <span class="select2-selection select2-selection--single" role="combobox" aria-haspopup="true" aria-expanded="false" tabindex="0" aria-disabled="false" aria-labelledby="select2-user-container">
                                        <span class="select2-selection__rendered" id="select2-user-container" role="textbox" aria-readonly="true"><span class="select2-selection__placeholder">Please Select</span></span>
                                        <span class="select2-selection__arrow" role="presentation"><b role="presentation"></b></span>
                                    </span>
                                </span>
                                <span class="dropdown-wrapper" aria-hidden="true"></span>
                            </span>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="form-group">
                            <label for="type" class="form-label">Type</label> <span class="text-danger">*</span>
                            <select class="form-control select2-hidden-accessible" id="type" wire:model.defer="type" tabindex="-1" aria-hidden="true" data-select2-id="type">
                                <option selected="" value="" data-select2-id="95">Select Type</option>
                                <option value="43">Business</option>
                                <option value="45">Management</option>
                            </select>
                            <span class="select2 select2-container select2-container--default" dir="ltr" data-select2-id="94" style="width: auto;">
                                <span class="selection">
                                    <span class="select2-selection select2-selection--single" role="combobox" aria-haspopup="true" aria-expanded="false" tabindex="0" aria-disabled="false" aria-labelledby="select2-type-container">
                                        <span class="select2-selection__rendered" id="select2-type-container" role="textbox" aria-readonly="true"><span class="select2-selection__placeholder">Please Select</span></span>
                                        <span class="select2-selection__arrow" role="presentation"><b role="presentation"></b></span>
                                    </span>
                                </span>
                                <span class="dropdown-wrapper" aria-hidden="true"></span>
                            </span>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="form-group">
                            <label for="industry" class="form-label">Industry</label> <span class="text-danger">*</span>
                            <select class="form-control select2-hidden-accessible" id="industry" wire:model.defer="industry" tabindex="-1" aria-hidden="true" data-select2-id="industry">
                                <option selected="" value="" data-select2-id="100">Select Industry</option>
                                <option value="30">Tobacco</option>
                                <option value="31">Real Estates</option>
                                <option value="33">Health</option>
                            </select>
                            <span class="select2 select2-container select2-container--default" dir="ltr" data-select2-id="99" style="width: auto;">
                                <span class="selection">
                                    <span class="select2-selection select2-selection--single" role="combobox" aria-haspopup="true" aria-expanded="false" tabindex="0" aria-disabled="false" aria-labelledby="select2-industry-container">
                                        <span class="select2-selection__rendered" id="select2-industry-container" role="textbox" aria-readonly="true"><span class="select2-selection__placeholder">Please Select</span></span>
                                        <span class="select2-selection__arrow" role="presentation"><b role="presentation"></b></span>
                                    </span>
                                </span>
                                <span class="dropdown-wrapper" aria-hidden="true"></span>
                            </span>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="form-group">
                            <label for="document_id" class="form-label">Document</label>
                            <select class="form-control select2-hidden-accessible" id="document_id" wire:model.defer="document_id" tabindex="-1" aria-hidden="true" data-select2-id="document_id">
                                <option value="" selected="" data-select2-id="110">--</option>
                                <option value="39">Rental Insurance</option>
                                <option value="95">Compound Insurance</option>
                                <option value="96">Insurance for Life</option>
                                <option value="97">Selling Papers</option>
                                <option value="113">Play Contract</option>
                                <option value="116">LAND SELLING</option>
                                <option value="119">Plays Contracts</option>
                                <option value="122">Jerry Rojas</option>
                            </select>
                            <span class="select2 select2-container select2-container--default" dir="ltr" data-select2-id="109" style="width: auto;">
                                <span class="selection">
                                    <span class="select2-selection select2-selection--single" role="combobox" aria-haspopup="true" aria-expanded="false" tabindex="0" aria-disabled="false" aria-labelledby="select2-document_id-container">
                                        <span class="select2-selection__rendered" id="select2-document_id-container" role="textbox" aria-readonly="true"><span class="select2-selection__placeholder">Please Select</span></span>
                                        <span class="select2-selection__arrow" role="presentation"><b role="presentation"></b></span>
                                    </span>
                                </span>
                                <span class="dropdown-wrapper" aria-hidden="true"></span>
                            </span>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="form-group">
                            <label for="Description" class="form-label">Description</label>
                            <textarea class="form-control" rows="3" placeholder="Enter Description" wire:model.defer="description" cols="50"></textarea>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn bg-hold-light text-white radius-xl" data-dismiss="modal">
                    Cancel
                </button>
                <button type="submit" class="btn bg-new-primary radius-xl">
                    Create
                </button>
            </div>
        </form>
    </div>
</div>
</div>


@endsection

@section('scripts')
<script type="text/javascript">
    $(".datepicker").datepicker();
    $("#selectUsers,#statusList").select2();
</script>
@endsection
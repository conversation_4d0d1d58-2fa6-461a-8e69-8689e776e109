@if($error)
    <div class="alert alert-danger mx-3 mt-3" role="alert">
        <div class="d-flex align-items-center">
            <i class="iconsax icon fs-22 mr-2" icon-name="warning-2"></i>
            <div>
                <strong>{{ __('vendors.status.error') }}</strong> {{ $error }}
                <button class="btn btn-sm btn-outline-danger ml-2" wire:click="fetchVendors">
                    <i class="iconsax icon fs-16" icon-name="refresh-2"></i> {{ __('vendors.buttons.retry') }}
                </button>
            </div>
        </div>
    </div>
@endif

<div class="row">
    @if($loading)
        <div class="col-12">
            <div class="text-center py-5">
                <div class="spinner-border text-primary mb-3" role="status">
                    <span class="sr-only">{{ __('vendors.status.loading') }}</span>
                </div>
                <h6 class="text-muted">{{ __('vendors.common.loading_vendors') }}</h6>
            </div>
        </div>
    @else
        @forelse($vendors as $vendor)
        <div class="col-md-4 mb-3">
            <div class="radius-xl new-shadow">
                <div class="d-flex p-3 align-items-center">
                    <div class="mr-3 wh-60 fs-16 d-center {{ $vendor['avatar_color'] }} radius-xl text-white">
                        {{ $vendor['initials'] }}
                    </div>
                    <div>
                        <h2 class="fw-500 mb-1 fs-14 text-new-primary">{{ $vendor['name'] ?? '-' }}</h2>
                        <strong class="">{{ $vendor['vendor_number'] ?? $vendor['id'] ?? '-' }}</strong>
                        <p class="mb-0">
                            {{ $vendor['email'] ?? '-' }}
                        </p>
                        @if(isset($vendor['phone']) || isset($vendor['contact']))
                        <p class="mb-0 text-muted fs-12">
                            {{ $vendor['phone'] ?? $vendor['contact'] ?? '-' }}
                        </p>
                        @endif
                    </div>
                </div>
                <p class="mb-1 px-2 border-top py-3 d-flex">
                    <button class="btn p-0 btn-default h-20 d-center gap-5 flex-fill" wire:click="openEditModal({{ $vendor['id'] }})">
                        <i class="iconsax icon fs-14 mr-0 text-sool" icon-name="edit-1"></i>
                        <span class="fs-14 text-dark fw-600">{{ __('vendors.common.edit') }}</span>
                    </button>
                    <button class="btn p-0 btn-default h-20 d-center gap-5 flex-fill border-right border-left" wire:click="viewVendor({{ $vendor['id'] }})">
                        <i class="iconsax icon fs-14 mr-0 text-sool" icon-name="eye"></i>
                        <span class="fs-14 text-dark fw-600">{{ __('vendors.common.view') }}</span>
                    </button>
                    <button class="btn p-0 btn-default h-20 d-center gap-5 flex-fill" wire:click="openDeleteModal({{ $vendor['id'] }}, '{{ $vendor['name'] }}')">
                        <i class="iconsax icon fs-14 mr-0 text-sool" icon-name="trash"></i>
                        <span class="fs-14 text-dark fw-600">{{ __('vendors.common.delete') }}</span>
                    </button>
                </p>
            </div>
        </div>
        @empty
            <div class="col-12">
                <div class="text-center py-5">
                    <i class="iconsax icon fs-48 text-muted mb-3" icon-name="profile-2user"></i>
                    <h6 class="text-muted">{{ __('vendors.common.no_vendors_found') }}</h6>
                    @if($search)
                        <p class="text-muted">{{ __('vendors.messages.try_adjusting_search') }}</p>
                        <button class="btn btn-sm btn-outline-primary" wire:click="$set('search', '')">
                            {{ __('vendors.buttons.clear_search') }}
                        </button>
                    @else
                        <p class="text-muted">{{ __('vendors.messages.start_by_creating') }}</p>
                        <button class="btn btn-primary" wire:click="openCreateModal">
                            <i class="las la-plus fs-16"></i> {{ __('vendors.buttons.create_vendor') }}
                        </button>
                    @endif
                </div>
            </div>
        @endforelse
    @endif
</div>

<?php
    namespace App\Jobs\BulkImport;
    use Illuminate\Bus\Queueable;
    use Illuminate\Bus\Batchable;
    use Illuminate\Contracts\Queue\ShouldBeUnique;
    use Illuminate\Contracts\Queue\ShouldQueue;
    use Illuminate\Foundation\Bus\Dispatchable;
    use Illuminate\Queue\InteractsWithQueue;
    use Illuminate\Queue\SerializesModels;
    use Illuminate\Support\Facades\Log;
    use App\Http\Traits\ProjectDetailTrait;
    use App\Http\Traits\BulkImportErrorTrait;
    use App\Http\Traits\BulkImportListTrait;
    use App\Http\Traits\BulkImportTrait;
    use App\Http\Traits\PropertyTrait;
    use App\Http\Traits\RegionTrait;
    use App\Http\Traits\CityTrait;
    use App\Enums\ResultType;
    use App\Enums\ModelAction;
    use App\Enums\Status;
    use App\Enums\ValidationBukImport;

    class PropertiesImportJob implements ShouldQueue{
        use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, Batchable, ProjectDetailTrait, BulkImportErrorTrait, BulkImportListTrait, BulkImportTrait, PropertyTrait, RegionTrait, CityTrait;
        public $list;
        public $projectId;
        public $bulkImportDetailsId;
        public $projectUserId;
        public $userId;

        /**
         * Create a new job instance.
         *
         * @return void
         */
        public function __construct($list, $projectId, $bulkImportDetailsId, $projectUserId, $userId){
            $this->list = $list;
            $this->projectId = $projectId;
            $this->bulkImportDetailsId = $bulkImportDetailsId;
            $this->projectUserId = $projectUserId;
            $this->userId = $userId;
        }

        /**
         * Execute the job.
         *
         * @return void
         */
        public function handle(){
            try {
                $bulkArray = [];
                $project = $this->getProjectDetailInformationByProjectId($this->projectId);

                if(is_null($project)){
                    Log::info("PropertiesImportJob error: No project found with this projectId : ".$this->projectId); 
                }

                elseif(!isset($this->list)){
                    Log::info("PropertiesImportJob error: The properties list is empty"); 
                }

                else{
                    foreach($this->list as $data){
                        $mapChecking = $this->fullPropertiesValidation($data);

                        if(!is_null($mapChecking) && $mapChecking[0]['status'] <> 'success'){
                            $bulkArrayErrors = ['map_status' => true, 'backend_status' => false, 'project_id' => $this->projectId, 'user_id' => $this->userId, 'bulk_import_id' => $this->bulkImportDetailsId, 'sheet' => ResultType::Properties->value, 'identifier' => $data['property_name'], 'row_status' => ModelAction::NotInserted->value, 'created_by' => $this->userId];
                            $bulkImportErrorId = $this->saveBulkImportError($bulkArrayErrors);

                            if(!$bulkImportErrorId){
                                Log::info("PropertiesImportJob error: Cannot save the bulk import error row for Properties sheet : Project ID: ".$this->projectId.", Property Name: ".$data['property_name']); 
                            }
                        }

                        else{
                            if($data['property_type'] == 'building' && $data['building_count'] <> 1){
                                $bulkArrayErrors = ['map_status' => false, 'backend_status' => true, 'project_id' => $this->projectId, 'user_id' => $this->userId, 'bulk_import_id' => $this->bulkImportDetailsId, 'sheet' => ResultType::Properties->value, 'identifier' => $data['property_name'], 'errors' => ValidationBukImport::BuildingCountIssue->value, 'value' => $data['property_name'], 'row_status' => ModelAction::NotInserted->value, 'created_by' => $this->userId];
                                $bulkImportErrorId = $this->saveBulkImportError($bulkArrayErrors);

                                if(!$bulkImportErrorId){
                                    Log::info("PropertiesImportJob error: Cannot save the bulk import error row for Properties sheet : Project ID: ".$this->projectId.", Property Name: ".$data['property_name']); 
                                }
                            }

                            else{
                                $complexName = ($data['property_type'] == 'complex') ? $data['property_name'] : null;
                                $buildingCount = ($data['property_type'] == 'complex') ? $data['building_count'] : 1;
                                $regionRow = $this->getRegionInformationsByValues('name', $data['region']);
                                $cityRow = $this->getCityInformationsByValues('name_en', $data['city']);

                                $array = [
                                    'user_id' => $this->projectUserId ?? null,
                                    'project' => $project->project_name ?? null,
                                    'region_id' => isset($regionRow) ? $regionRow->id : null,
                                    'city_id' => isset($cityRow) ? $cityRow->id : null,
                                    'property_type' => $data ['property_type'] ?? null,
                                    'buildings_count' => $buildingCount ?? null,
                                    'latitude' => $data['gps_location_latitude'] ?? null,
                                    'longitude' => $data['gps_location_longitude'] ?? null,
                                    'location' => 'riyadh' ?? null,
                                    'property_tag' => $data['property_name'] ?? null,
                                    'complex_name' => $complexName ?? null,
                                    'status' => Status::Active->value ?? null,
                                    'last_ip' => $this->getCurrentIpAddress(),
                                    'location_type' => 'single_location' ?? null                                
                                ];

                                $newPropertyId = $this->saveProperty($array);

                                if($newPropertyId){
                                    array_push($bulkArray, $newPropertyId);
                                }

                                else{
                                    $bulkArrayErrors = ['map_status' => false, 'backend_status' => true, 'project_id' => $this->projectId, 'user_id' => $this->userId, 'bulk_import_id' => $this->bulkImportDetailsId, 'sheet' => ResultType::Properties->value, 'identifier' => $data['property_name'], 'row_status' => ModelAction::NotInserted->value, 'errors' => ValidationBukImport::PropertyNotSaved->value, 'value' => $data['property_name'], 'created_by' => $this->userId];
                                    $bulkImportErrorId = $this->saveBulkImportError($bulkArrayErrors);

                                    if(!$bulkImportErrorId){
                                        Log::info("PropertiesImportJob error: Cannot save the bulk import error row for Properties sheet : Project ID: ".$this->projectId.", Property Name: ".$data['property_name']); 
                                    }
                                }
                            }
                        }
                    }
                }

                $implodedValue = isset($bulkArray) && count($bulkArray) > 0 ? $this->implodeDataFromField($bulkArray) : null;
                $bulkImportList = $this->getBulkImportListByValues('bulk_import_id', $this->bulkImportDetailsId);
                $result = null;

                $array = [
                    'properties' => $implodedValue,
                    'project_id' => $this->projectId,
                    'bulk_import_id' => $this->bulkImportDetailsId,
                    'created_by' => !isset($bulkImportList) ? $this->userId : $bulkImportList->created_by,
                    'updated_by' => isset($bulkImportList) ? $this->userId : null,
                ];
                
                if(isset($bulkImportList)){
                    $result = $this->updateBulkImportListByValues('bulk_import_id', $this->bulkImportDetailsId, $array);
                }

                else{
                    $result = $this->saveBulkImportList($array);
                }

                if(!$result){
                    Log::info("PropertiesImportJob error: We cannot do any action on bulk import table for properties column (Same data)"); 
                }
            } 
            
            catch (\Throwable $th) {
                Log::error("PropertiesImportJob error: ".$th);
            }
        }
    }
?>
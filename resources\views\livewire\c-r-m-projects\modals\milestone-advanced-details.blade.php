<div class="modal-dialog modal-lg" id="milestoneAdvancedDetails" tabindex="-1" role="dialog"
    aria-labelledby="createModalLabel" aria-modal="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content radius-xl">
            <div class="modal-header">


                <h6 class="modal-title" id="createModalLabel"> {{ $milestone['title'] ?: '---' }} </h6>
                <button type="button" class="close border-0" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form>
                <div class="modal-body">
                    <div class="row">
                        <div class="form-group col-md-4">
                            <label class="col-form-label text-dark">@lang('CRMProjects.common.created_at')</label>
                            <p>{{ !empty($milestone['created_at']) ? \Helper::formatDateForLocale($milestone['created_at']) : '---' }}
                            </p>

                        </div>

                        <div class="form-group col-md-4">
                            <label class="col-form-label">@lang('CRMProjects.common.start_date')</label>
                            <p>{{ $milestone['start_date'] ?: '---' }}</p>
                        </div>

                        <div class="form-group col-md-4">
                            <label class="col-form-label">@lang('CRMProjects.common.end_date')</label>
                            <p>{{ $milestone['end_date'] ?: '---' }}</p>
                        </div>

                        <div class="form-group col-md-4">
                            <label class="col-form-label">@lang('CRMProjects.common.milestone_cost')</label>
                            <p>{{ $milestone['cost'] ?: '---' }}</p>
                        </div>
                        <div class="form-group col-md-4">
                            <label class="col-form-label">@lang('CRMProjects.common.progress')</label>
                           <p>{{ $milestone['progress'] ? $milestone['progress'] . '%' : '0%' }}</p>
                        </div>
                        <div class="form-group col-md-4">
                            <label class="col-form-label">@lang('CRMProjects.common.total_task')</label>
                            <p>{{ $milestone['total_task'] ?: 0 }}</p>
                        </div>

                        <div class="form-group col-md-12">
                            <label class="col-form-label">@lang('CRMProjects.common.summary')</label>
                            <p>{{ $milestone['summary'] ?: '---' }}</p>
                        </div>
                    </div>

                    @if (!empty($milestone['tasks']) && count($milestone['tasks']) > 0)
                        <div class="row mt-3">

                            <div class="col-12">

                                <div class="table-responsive" style="max-height: 250px; overflow-y: auto;">

                                    <table class="table mb-0 radius-0">
                                        <thead class="bg-white" style="position: sticky; top: 0; z-index: 1000;">
                                            <tr class="">

                                                <th>
                                                    @lang('CRMProjects.common.title') </th>
                                                <th>
                                                    @lang('CRMProjects.common.start_date') </th>
                                                <th>
                                                    @lang('CRMProjects.common.end_date') </th>


                                            </tr>
                                        </thead>
                                        <tbody class="">
                                            @foreach ($milestone['tasks'] ?? [] as $item)
                                                <tr>
                                                    <td>
                                                        {{ $item['title'] }}
                                                    </td>
                                                    <td>
                                                        <span
                                                            class="event-start">{{ \Helper::formatDateForLocale($item['start_date']) }}
                                                    </td>
                                                    <td>
                                                        {{ \Helper::formatDateForLocale($item['due_date']) }}
                                                    </td>
                                                </tr>
                                            @endforeach



                                        </tbody>
                                    </table>


                                </div>
                            </div>

                        </div>
                    @else
                        <div class="row">


                            <div class="col-12">
                                <div class="PropertyListEmpty">
                                    <img src="{{ asset('empty-icon/To_do_list_rafiki.svg') }}" style="width: 20%">
                                    <h6 class="first_title">@lang('CRMProjects.common.no_tasks_found')</h6>
                                    <h6 class="second_title">@lang('CRMProjects.common.tasks_milestone_or_task_group_appear_here')</h6>
                                </div>
                            </div>
                        </div>
                    @endif
                </div>





                <div class="modal-footer">
                    <button type="button" class="btn bg-new-primary radius-xl"
                        data-dismiss="modal">@lang('CRMProjects.common.close')</button>

                </div>
            </form>
        </div>
    </div>
</div>
@section('scripts')
    <script src="/js/livewire/manage-loader.js"></script>
    <script>
        $(function() {
            window.addEventListener('open-modal', () => {
                setTimeout(() => {
                    initDatePicker()
                }, 200);
            });

        });
    </script>
@endsection

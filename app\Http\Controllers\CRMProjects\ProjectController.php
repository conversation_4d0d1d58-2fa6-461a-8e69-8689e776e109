<?php

namespace App\Http\Controllers\CRMProjects;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Contracts\Encryption\DecryptException;

class ProjectController extends Controller
{
   
    public function dashboard(){
        return view('CRMProjects.dashboard');
    }
    public function list(){
        return view('CRMProjects.projects-list');
    }
    public function details($encryptedId)
    {
        try {
            $id = Crypt::decrypt($encryptedId);
    
            if (!$id || !is_numeric($id)) {
                return redirect()->route('CRMProjects.list');
            }
    
            return view('CRMProjects.project-details', ['projectID' => $id]);
        } catch (DecryptException $e) {
            return redirect()->route('CRMProjects.list');
        }
    }
    public function publicDetails($encryptedId)
    {
        try {
            $id = Crypt::decrypt($encryptedId);
            
            return view('CRMProjects.public-project-details', ['projectID' => $id]);
        } catch (DecryptException $e) {
            return abort(404);
        }
    }
    public function calendar()
    {
        return view('CRMProjects.calendar');
    }
}

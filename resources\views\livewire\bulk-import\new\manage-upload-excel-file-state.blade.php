<div wire:poll.750ms>
    @if(!$jobStatus)
        <div class = "border rounded p-3 m-1 pb-0 mt-2">
            <p class = "fs-13">
                <span class = "spinner-border spinner-border-sm mt-1 mx-1" role = "status" aria-hidden = "true"></span>
                @lang('import.import_data_in_progress')
            </p>
        </div>
    @else
        <form id = "select-sheets-form" name = "select-sheets-form" method = "post" action = "{{ route('bulk-import.updateSelectSheetBulkImport', ['token' => $token]) }}">
            @csrf
            <div class = "table-responsive mt-4">
                <table class = "table table-bordered">
                    <thead class = "userDatatable-header">
                        <tr class = "thead-default">
                            <th class = "text-dark text-capitalize">@lang('import.sheet')</th>
                            <th class = "text-dark text-capitalize">@lang('import.count')</th>
                            <th class = "text-dark text-capitalize">@lang('import.action')</th>
                            <th class = "text-dark text-capitalize w-50">@lang('import.description')</th>
                        </tr>
                    </thead>                       
                    <tbody> 
                        <tr class = "{{ $countUsers == 0 ? 'table-light' : '' }}">
                            <td>
                                <div class = "form-inline">
                                    <input type = "checkbox" value = "1" name = "checked_users" id = "checked_users" {{ $countUsers == 0 ? 'disabled' : '' }}>
                                    <label class = "mx-2 text-capitalize fs-12">@lang('import.users_label')</label>
                                </div>
                            </td>
                            <td class = "text-center fs-12">{{ $countUsers ?? 0 }}</td>
                            <td>
                                <button type = "button" class = "btn btn-primary btn-default btn-squared text-capitalize radius-md shadow2 btn-sm mx-auto" id = "users_affectation_btn" {{ $countUsers == 0 ? 'disabled' : '' }} data-toggle = "modal" data-target = "#affect_users_modal">
                                    @lang('import.affectation')
                                </button>
                            </td>
                            <td>
                                <p class = "fs-12">@lang('import.select_users')</p>
                            </td>
                        </tr>
                        <tr class = "{{ $countPriorities == 0 ? 'table-light' : '' }}">
                            <td>
                                <div class = "form-inline">
                                    <input type = "checkbox" value = "1" name = "checked_priorities" id = "checked_priorities" {{ $countPriorities == 0 ? 'disabled' : '' }}>
                                    <label class = "mx-2 text-capitalize fs-12">@lang('import.priorities_label')</label>
                                </div>
                            </td>
                            <td class = "text-center fs-12">{{ $countPriorities ?? 0 }}</td>
                            <td class = "text-center fs-12">-</td>
                            <td>
                                <p class = "fs-12">@lang('import.select_priorities_level')</p>
                            </td>
                        </tr>
                        <tr class = "{{ $countServices == 0 ? 'table-light' : '' }}">
                            <td>
                                <div class = "form-inline">
                                    <input type = "checkbox" value = "1" name = "checked_services" id = "checked_services" {{ $countServices == 0 ? 'disabled' : '' }}>
                                    <label class = "mx-2 text-capitalize fs-12">@lang('import.services_label')</label>
                                </div>
                            </td>
                            <td class = "text-center fs-12">{{ $countServices ?? 0 }}</td>
                            <td class = "text-center fs-12">-</td>
                            <td>
                                <p class = "fs-12">@lang('import.select_services')</p>
                            </td>
                        </tr>
                        <tr class = "{{ $countProperties == 0 ? 'table-light' : '' }}">
                            <td>
                                <div class = "form-inline">
                                    <input type = "checkbox" value = "1" name = "checked_properties" id = "checked_properties" {{ $countProperties == 0 ? 'disabled' : '' }}>
                                    <label class = "mx-2 text-capitalize fs-12">@lang('import.properties_label')</label>
                                </div>
                            </td>
                            <td class = "text-center fs-12">{{ $countProperties ?? 0 }}</td>
                            <td class = "text-center fs-12">-</td>
                            <td>
                                <p class = "fs-12">@lang('import.select_properties')</p>
                            </td>
                        </tr>
                        <tr class = "{{ $countPropertyBuildings == 0 ? 'table-light' : '' }}">
                            <td>
                                <div class = "form-inline">
                                    <input type = "checkbox" value = "1" name = "checked_buildings" id = "checked_buildings" {{ $countPropertyBuildings == 0 ? 'disabled' : '' }}>
                                    <label class = "mx-2 text-capitalize fs-12">@lang('import.property_buildings_label')</label>
                                </div>
                            </td>
                            <td class = "text-center fs-12">{{ $countPropertyBuildings ?? 0 }}</td>
                            <td class = "text-center fs-12">-</td>
                            <td>
                                <p class = "fs-12">@lang('import.select_properties_building')</p>
                            </td>
                        </tr>
                        <tr class = "{{ $countAssets == 0 ? 'table-light' : '' }}">
                            <td>
                                <div class = "form-inline">
                                    <input type = "checkbox" value = "1" name = "checked_assets" id = "checked_assets" {{ $countAssets == 0 ? 'disabled' : '' }}>
                                    <label class = "mx-2 text-capitalize fs-12">@lang('import.assets_label')</label>
                                </div>
                            </td>
                            <td class = "text-center fs-12">{{ $countAssets ?? 0 }}</td>
                            <td class = "text-center fs-12">-</td>
                            <td>
                                <p class = "fs-12">@lang('import.select_assets')</p>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class = "button-group d-flex pt-10 justify-content-end mb-40">
                <a href = "{{ route('bulk-import.openUploadFile') }}" class = "btn btn-light btn-default btn-squared text-capitalize radius-md shadow2 btn-sm">
                    @lang('import.previous')
                </a>
                <button type = "submit" class = "btn btn-primary btn-default btn-squared text-capitalize radius-md shadow2 btn-sm" id = "btn-next" {{ !$jobStatus ? 'disabled' : '' }}>
                    @lang('import.next')
                </button>
            </div>
        </form>
    @endif
    @if($countUsers > 0)
        @livewire('bulk-import.new.affect-users', ['usersList' => $usersList, 'projectId' => $projectId, 'bulkImportDetails' => $bulkImportDetails])
    @endif
</div>

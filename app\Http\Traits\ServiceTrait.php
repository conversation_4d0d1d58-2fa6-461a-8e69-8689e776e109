<?php
    namespace App\Http\Traits;
    use Illuminate\Support\Facades\Log;
    use App\Http\Helpers\ReportQueryHelper;
    use App\Http\Traits\TempBulkImportTrait;
    use App\Http\Traits\FunctionsTrait;
    use App\Http\Traits\PriorityTrait;
    use App\Models\AssetCategory;
    use App\Enums\ModelAction;

    trait ServiceTrait{
        use TempBulkImportTrait, FunctionsTrait, PriorityTrait;

        public function manageEntredService($service, $projectUserId) {
            try {
                $serviceRow = $this->getSpeceficServiceInformationsByValues('asset_category', $service, $projectUserId);

                if(isset($serviceRow)){
                    return ModelAction::Update->value;
                }

                else{
                    return ModelAction::Insert->value;
                }
            }

            catch (\Throwable $th) {
                Log::error("manageEntredService error: ".$th);
            }
        }

        public function getSpeceficServiceInformationsByValues($key, $value, $projectUserId) {
            try {
                return AssetCategory::select('id', 'asset_category')
                ->where('user_id', $projectUserId)
                ->where('is_deleted', 'no')
                ->where($key, $value)
                ->first();
            }

            catch (\Throwable $th) {
                Log::error("getSpeceficServiceInformationsByValues error: ".$th);
            }
        }

        public function saveService($array) {
            try {
                return AssetCategory::insertGetId($array);
            }

            catch (\Throwable $th) {
                Log::error("saveAssetCategory error: ".$th);
            }
        }

        public function updateServiceById($projectUserId, $serviceId, $array) {
            try {
                return AssetCategory::where('id', $serviceId)
                ->where('user_id', $projectUserId)
                ->where('is_deleted', 'no')
                ->update($array);
            }

            catch (\Throwable $th) {
                Log::error("updateAssetCategoryById error: ".$th);
            }
        }

        public function getServiceInformationsById($serviceId) {
            try {
                return AssetCategory::leftJoin('priorities', 'priorities.id', 'asset_categories.priority_id')
                ->where('asset_categories.id', $serviceId)
                ->where('asset_categories.is_deleted', 'no')
                ->select('asset_category', 'service_type', 'priority_id', 'priority_level')
                ->first();
            }

            catch (\Throwable $th) {
                Log::error("getServiceInformationsById Error: ".$th);
            }
        }

        public function getServiceInformationsByValues($key, $value) {
            try {
                return AssetCategory::where($key, $value)
                ->where('is_deleted', 'no')
                ->first();
            }

            catch (\Throwable $th) {
                Log::error("getServiceInformationsByValues Error: ".$th);
            }
        }

        public function deleteServiceByValues($key, $value) {
            try {
                return AssetCategory::whereIn($key, $value)
                ->delete();
            }

            catch (\Throwable $th) {
                Log::error("deleteServiceByValues error: ".$th);
            }
        }

        public function getServicesListByArray($key, $value) {
            try {
                return AssetCategory::whereIn($key, $value)
                ->select('id', 'asset_category', 'service_type')
                ->get();
            } 
            
            catch (\Throwable $th) {
                Log::error("getServicesListByArray Error: ".$th);
            }
        }

        public function getPaginatedServicesListVByValues($key, $value, $perPage) {
            try {
                return AssetCategory::select('asset_category', 'service_type', 'priority_id', 'priorities.priority_level', 'tenant_form_enabled')
                ->leftJoin('priorities', 'priorities.id','asset_categories.priority_id')
                ->whereIn($key, $value)
                ->paginate($perPage, ['*'], 'page'); 
            }

            catch (\Throwable $th) {
                Log::error("getPaginatedServicesListVByValues error: ".$th);
            }
        }

        public function getAssetCategoriesListForBuildingManager($query, $assetCategories) {
            try {
                $explodedAssetCategories = $this->explodeDataFromField($assetCategories);
                return $query->whereIn('asset_categories.id', $explodedAssetCategories);
            }

            catch (\Throwable $th) {
                Log::error("getAssetCategoriesListForBuildingManager error: ".$th);
            }
        }

        public function getAssetCategoriesListForSpAdmin($query, $user) {
            try {
                $assetCategoryIds = $user->assetCategoryIds()->pluck('asset_category_id')->unique()->toArray();
                return $query->whereIn('asset_categories.id', $assetCategoryIds)->where('asset_categories.user_id', $user->project_user_id);
            }

            catch (\Throwable $th) {
                Log::error("getAssetCategoriesListForSpAdmin error: ".$th);
            }
        }

        public function getAssetCategoriesListForOtherUsers($query, $projectUserId) {
            try {
                return $query->where('asset_categories.user_id', $projectUserId);
            }

            catch (\Throwable $th) {
                Log::error("getAssetCategoriesListForOtherUsers error: ".$th);
            }
        }

        public function getAssetCategoriesListByUser($user) {
            try {
                $query = AssetCategory::where('is_deleted', 'no')
                ->where('status', '=', 1);

                if($user->isBuildingManager() || $user->isBuildingManagerEmployee() || $user->isSupervisor()) {
                    $query = $this->getAssetCategoriesListForBuildingManager($query, $user->asset_categories);
                }

                elseif($user->isServiceProviderAdmin()) {
                    $query = $this->getAssetCategoriesListForSpAdmin($query, $user);
                }

                else{
                    $query = $this->getAssetCategoriesListForOtherUsers($query, $user->project_user_id);
                }

                return $query->get()->toArray();
            }

            catch (\Throwable $th) {
                Log::error("getAssetCategoriesListByUser error: ".$th);
            }
        }
    }
?>

<div>
  <style type="text/css">
   .note-toolbar.card-header {
        flex-wrap: wrap !important;
        justify-content: flex-start !important;
      }
      /* Reduce button padding and font-size */
  .note-toolbar .note-btn {
    padding: 4px 6px !important;
    font-size: 12px !important;
    line-height: 1 !important;
    height: 40px;
    width: 40px;
  }

  /* Optional: Reduce dropdown menu font */
  .note-dropdown-menu,
  .note-popover .popover-content {
    font-size: 12px !important;
  }

  /* Reduce the toolbar height and spacing */
  .note-editor .note-toolbar {
    padding: 4px 5px !important;
  }

  /* Optional: Reduce icon sizes */
  .note-btn i {
    font-size: 12px !important;
    margin: 0 !important;
  }
</style>
<style>
.ck-editor__editable_inline {
    min-height: 200px; /* You can increase this value as needed */
}
.ck-powered-by{
    display: none !important;

}
</style>
 <div class="contents crm">
           <div class="container-fluid">
            <div class="col-lg-12">
     <div class="row justify-content-between my-3 flex-sm-row flex-column">
                <div class="page-title-wrap p-0">
                    <div class="page-title d-flex justify-content-between">
                        <div class="user-member__title">
                            <h4 class="text-capitalize fw-500 breadcrumb-title fs-16">
                                @if(isset($rfxId))
                                @lang('rfx.edit_rfx')
                                @else
                                @lang('rfx.create_rfx')
                                @endif
                            </h4>
                        </div>
                    </div>
                    <ul class="atbd-breadcrumb nav">
                        <li class="atbd-breadcrumb__item">
                            <a>@lang('rfx.dashboard')</a>
                            <span class="breadcrumb__seperator">
                                <span class="la la-angle-right"></span>
                            </span>
                        </li>
                        <li class="atbd-breadcrumb__item">
                            <a href="#">@lang('rfx.manage_rfx')</a>
                            <span class="breadcrumb__seperator">
                                <span class="la la-angle-right"></span>
                            </span>
                        </li>
                        <li class="atbd-breadcrumb__item">
                            <a href="#">@if(isset($rfxId))
                                @lang('rfx.edit_rfx')
                                @else
                                @lang('rfx.create_rfx')
                                @endif</a>
                        </li>
                    </ul>
                </div>
                <!-- <div class="d-flex gap-10 breadcrumb_right_icons">
                    <button class="btn btn-default btn-primary w-100 no-wrap" type="button">
                        <i class="iconsax" icon-name="emoji-happy"></i> @lang('rfx.generate_with_ai')
                    </button>
                </div> -->
            </div>
</div>







<div class="">
   <div class="row">
      <div class="col-lg-3 prperty_steps_container">
         <div class="border-0 p-0 card">
            <div class="checkout-progress3 property_progress card-body">
                <div class="step cursor-pointer {{ $currentStep === 1 ? 'current' : '' }}" id="1"   wire:click="goToPreviousStep">
                    <span class="d-inline-block">1</span>
                    <span class="property_steps">@lang('rfx.details')</span>
                </div>

                <div class="step cursor-pointer {{ $currentStep === 2 ? 'current' : '' }}" id="2"   wire:click="goToNextStep">
                    <span class="d-inline-block">2</span>
                    <span class="property_steps">@lang('rfx.purchase')</span>
                </div>

            </div>
        </div>

         <!-- checkout -->
      </div>
      <div class="col-md-9">
         <div class="tab-content" id="myTabContent">
            
@include('livewire.rfx.include.step1')  

@include('livewire.rfx.include.step2')  
    

         </div>
      </div>
   </div>
</div>












           </div>
        </div>


  
</div>
@push('scripts')


<script src="https://cdn.ckeditor.com/ckeditor5/39.0.1/classic/ckeditor.js"></script>
<script>
document.addEventListener('livewire:load', function () {
    const component = @this;
    const editorInstances = {};

    function initCKEditor(id, model) {
        if (editorInstances[id]) return; // Prevent reinitialization

        const el = document.getElementById(id);
        if (!el) return;

        ClassicEditor
            .create(el)
            .then(editor => {
                editor.model.document.on('change:data', () => {
                    component.set(model, editor.getData());
                });
                editorInstances[id] = editor;
            })
            .catch(error => {
                console.error(`CKEditor error on ${id}:`, error);
            });
    }

    // Initial load
    initCKEditor('rfxDescription', 'description');
    initCKEditor('rfxRequirement', 'requirement');
    initCKEditor('termsConditions', 'terms_and_conditions');

    // After Livewire updates
    Livewire.hook('message.processed', () => {
        initCKEditor('rfxDescription', 'description');
        initCKEditor('rfxRequirement', 'requirement');
        initCKEditor('termsConditions', 'terms_and_conditions');
    });
});
</script>


<script type="text/javascript">
    $(".datepicker").datepicker();
    $("#selectUsers,#statusList").select2();
</script>

<script>
document.addEventListener('livewire:load', function () {
    initializeSelect2(); // Initial load

    Livewire.hook('message.processed', (message, component) => {
        initializeSelect2(); // Re-init after Livewire updates
    });

    function initializeSelect2() {
        const $skills = $('#skills');

        if ($skills.hasClass("select2-hidden-accessible")) {
            $skills.select2('destroy'); // Destroy previous if exists
        }

        $skills.select2({
            tags: true,
            width: '100%',
            tokenSeparators: [],
            createTag: function (params) {
                const term = $.trim(params.term);
                if (term === '') return null;
                return {
                    id: term,
                    text: term
                };
            }
        });

        // Re-bind custom Enter and Paste logic
        $skills.on('select2:open', function () {
            setTimeout(() => {
                const $searchField = $('.select2-search__field');

                $searchField.off('keydown.pasteHandler'); // Unbind to avoid duplicates

                $searchField.on('keydown.pasteHandler', function (e) {
                    if (e.key === 'Enter' || e.keyCode === 13) {
                        addTag($(this));
                        e.preventDefault();
                    }
                });

                $searchField.on('paste.pasteHandler', function (e) {
                    const clipboardData = e.originalEvent.clipboardData || window.clipboardData;
                    const pastedData = clipboardData.getData('text').trim();
                    if (pastedData) {
                        addTag($(this), pastedData);
                        e.preventDefault();
                    }
                });
            }, 0);
        });

        // Sync selected skills to Livewire
        $skills.on('change', function () {
            const selected = $(this).val();
            Livewire.emit('skillsUpdated', selected);
        });
    }

    function addTag($input, overrideValue = null) {
        const term = overrideValue || $input.val().trim();
        if (!term) return;

        const $skills = $('#skills');
        const exists = $skills.find('option').filter(function () {
            return $(this).text().toLowerCase() === term.toLowerCase();
        }).length > 0;

        if (!exists) {
            const newOption = new Option(term, term, true, true);
            $skills.append(newOption).trigger('change');
        }

        $input.val('').focus();
    }
});

Livewire.hook('message.processed', (message, component) => {
    initializeSelect2();
});

</script>

<script src="/js/livewire/manage-loader.js"></script>
<script>
function setdate(key,id) {
const selectedDate = document.getElementById(id).value;
Livewire.find('{{ $this->id }}').set(key, selectedDate);
}

window.addEventListener('redirect', event => {
window.location.href = event.detail.url;
});
</script>
<script>
    window.addEventListener('reload-page', () => {
        location.reload();
    });

    document.addEventListener('click', function(e) {
       // console.log('>>>>>>>',e.target.classList.contains('export'));
        if (e.target.classList.contains('export')) {
            showLoader();
        }
    });

    function showLoader() {
        document.getElementById("overlayer").style.display = "block";
        let loaderElements = document.getElementsByClassName("loader-overlay");
        if (loaderElements.length > 0) {
            loaderElements[0].style.display = "block";
        }
    }

    function hideLoader() {
        document.getElementById("overlayer").style.display = "none";

        let loaderElements = document.getElementsByClassName("loader-overlay");
        if (loaderElements.length > 0) {
            loaderElements[0].style.display = "none";
        }
    }

    window.addEventListener('show-loader', event => {
        showLoader();
    });


    window.addEventListener('hide-loader', event => {
        setTimeout(() => {
            hideLoader();
        }, 1000);
    });
</script>  
@endpush
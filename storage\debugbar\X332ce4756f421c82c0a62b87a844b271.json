{"__meta": {"id": "X332ce4756f421c82c0a62b87a844b271", "datetime": "2025-07-31 15:40:34", "utime": **********.851712, "method": "GET", "uri": "/CRMProjects/List?page=1", "ip": "127.0.0.1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 13, "messages": [{"message": "[15:40:32] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\laragon\\www\\Osool-B2G\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1753965632.699365, "xdebug_link": null, "collector": "log"}, {"message": "[15:40:34] LOG.info: Project List ", "message_html": null, "is_string": false, "label": "info", "time": **********.484773, "xdebug_link": null, "collector": "log"}, {"message": "[15:40:34] LOG.info: khansaa-test34444", "message_html": null, "is_string": false, "label": "info", "time": **********.485075, "xdebug_link": null, "collector": "log"}, {"message": "[15:40:34] LOG.warning: Optional parameter $privilege_name declared before required parameter $privilege_section_name is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\Helper.php on line 2418", "message_html": null, "is_string": false, "label": "warning", "time": **********.692936, "xdebug_link": null, "collector": "log"}, {"message": "[15:40:34] LOG.warning: Optional parameter $privilege_name declared before required parameter $privilege_section_name is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\Helper.php on line 2478", "message_html": null, "is_string": false, "label": "warning", "time": **********.693115, "xdebug_link": null, "collector": "log"}, {"message": "[15:40:34] LOG.warning: Optional parameter $search declared before required parameter $asset_id is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\Helper.php on line 3619", "message_html": null, "is_string": false, "label": "warning", "time": **********.693961, "xdebug_link": null, "collector": "log"}, {"message": "[15:40:34] LOG.warning: Optional parameter $privilegeName declared before required parameter $user is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 646", "message_html": null, "is_string": false, "label": "warning", "time": **********.714176, "xdebug_link": null, "collector": "log"}, {"message": "[15:40:34] LOG.warning: Optional parameter $privilegeSectionName declared before required parameter $user is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 646", "message_html": null, "is_string": false, "label": "warning", "time": **********.714303, "xdebug_link": null, "collector": "log"}, {"message": "[15:40:34] LOG.warning: Optional parameter $shouldBeTrue declared before required parameter $user is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 646", "message_html": null, "is_string": false, "label": "warning", "time": **********.714352, "xdebug_link": null, "collector": "log"}, {"message": "[15:40:34] LOG.warning: Optional parameter $filters declared before required parameter $userServiceProvider is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\WorkOrdersTrait.php on line 3418", "message_html": null, "is_string": false, "label": "warning", "time": **********.724857, "xdebug_link": null, "collector": "log"}, {"message": "[15:40:34] LOG.warning: Optional parameter $serviceProviderId declared before required parameter $search is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\UserTrait.php on line 160", "message_html": null, "is_string": false, "label": "warning", "time": **********.72939, "xdebug_link": null, "collector": "log"}, {"message": "[15:40:34] LOG.warning: json_decode(): Passing null to parameter #1 ($json) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 638", "message_html": null, "is_string": false, "label": "warning", "time": **********.762828, "xdebug_link": null, "collector": "log"}, {"message": "[15:40:34] LOG.warning: json_decode(): Passing null to parameter #1 ($json) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\storage\\framework\\views\\f8d18d6f63e9bf36e98d3bb1b82c9e7d86b4c71c.php on line 3", "message_html": null, "is_string": false, "label": "warning", "time": **********.794286, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753965632.236662, "end": **********.851759, "duration": 2.6150970458984375, "duration_str": "2.62s", "measures": [{"label": "Booting", "start": 1753965632.236662, "relative_start": 0, "end": 1753965632.67897, "relative_end": 1753965632.67897, "duration": 0.4423081874847412, "duration_str": "442ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1753965632.678983, "relative_start": 0.4423210620880127, "end": **********.851761, "relative_end": 2.1457672119140625e-06, "duration": 2.1727781295776367, "duration_str": "2.17s", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 47516736, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 22, "templates": [{"name": "CRMProjects.projects-list (\\resources\\views\\CRMProjects\\projects-list.blade.php)", "param_count": 13, "params": ["workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount"], "type": "blade"}, {"name": "livewire.c-r-m-projects.projects-list (\\resources\\views\\livewire\\c-r-m-projects\\projects-list.blade.php)", "param_count": 66, "params": ["errors", "_instance", "viewMode", "projects", "start_date", "end_date", "filter_start_date", "filter_end_date", "search", "budget", "project_type", "nextPageUrl", "prevPageUrl", "status", "projectID", "projecTid", "UsersforCreateProject", "users", "projectType", "priorityLevel", "priority_level", "clients", "vendors", "vendor", "selectedUsers", "selectedUsersForInvite", "usersAlreadyInvited", "selectedvendorsForShare", "vendorsAlreadyInProject", "selectedclientsForShare", "clientssAlreadyInProject", "name", "description", "ProjectTemplateName", "project", "statuss", "bug", "task", "activity", "user", "client", "milestone", "project_file", "building_manager", "selectedProjectID", "workspaceSlug", "page", "totalPages", "perPage", "totalItems", "isRefreshButton", "sortDirection", "sortField", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount"], "type": "blade"}, {"name": "livewire.c-r-m-projects.partials.cards-view (\\resources\\views\\livewire\\c-r-m-projects\\partials\\cards-view.blade.php)", "param_count": 68, "params": ["__env", "app", "errors", "_instance", "viewMode", "projects", "start_date", "end_date", "filter_start_date", "filter_end_date", "search", "budget", "project_type", "nextPageUrl", "prevPageUrl", "status", "projectID", "projecTid", "UsersforCreateProject", "users", "projectType", "priorityLevel", "priority_level", "clients", "vendors", "vendor", "selectedUsers", "selectedUsersForInvite", "usersAlreadyInvited", "selectedvendorsForShare", "vendorsAlreadyInProject", "selectedclientsForShare", "clientssAlreadyInProject", "name", "description", "ProjectTemplateName", "project", "statuss", "bug", "task", "activity", "user", "client", "milestone", "project_file", "building_manager", "selectedProjectID", "workspaceSlug", "page", "totalPages", "perPage", "totalItems", "isRefreshButton", "sortDirection", "sortField", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount"], "type": "blade"}, {"name": "livewire.c-r-m-projects.partials.paginator (\\resources\\views\\livewire\\c-r-m-projects\\partials\\paginator.blade.php)", "param_count": 74, "params": ["__env", "app", "errors", "_instance", "viewMode", "projects", "start_date", "end_date", "filter_start_date", "filter_end_date", "search", "budget", "project_type", "nextPageUrl", "prevPageUrl", "status", "projectID", "projecTid", "UsersforCreateProject", "users", "projectType", "priorityLevel", "priority_level", "clients", "vendors", "vendor", "selectedUsers", "selectedUsersForInvite", "usersAlreadyInvited", "selectedvendorsForShare", "vendorsAlreadyInProject", "selectedclientsForShare", "clientssAlreadyInProject", "name", "description", "ProjectTemplateName", "project", "statuss", "bug", "task", "activity", "user", "client", "milestone", "project_file", "building_manager", "selectedProjectID", "workspaceSlug", "page", "totalPages", "perPage", "totalItems", "isRefreshButton", "sortDirection", "sortField", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount", "__currentLoopData", "loop", "usersToShow", "userCount", "index", "perPageOptions"], "type": "blade"}, {"name": "livewire.c-r-m-projects.modals.delete-confirm (\\resources\\views\\livewire\\c-r-m-projects\\modals\\delete-confirm.blade.php)", "param_count": 68, "params": ["__env", "app", "errors", "_instance", "viewMode", "projects", "start_date", "end_date", "filter_start_date", "filter_end_date", "search", "budget", "project_type", "nextPageUrl", "prevPageUrl", "status", "projectID", "projecTid", "UsersforCreateProject", "users", "projectType", "priorityLevel", "priority_level", "clients", "vendors", "vendor", "selectedUsers", "selectedUsersForInvite", "usersAlreadyInvited", "selectedvendorsForShare", "vendorsAlreadyInProject", "selectedclientsForShare", "clientssAlreadyInProject", "name", "description", "ProjectTemplateName", "project", "statuss", "bug", "task", "activity", "user", "client", "milestone", "project_file", "building_manager", "selectedProjectID", "workspaceSlug", "page", "totalPages", "perPage", "totalItems", "isRefreshButton", "sortDirection", "sortField", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount"], "type": "blade"}, {"name": "livewire.c-r-m-projects.modals.create (\\resources\\views\\livewire\\c-r-m-projects\\modals\\create.blade.php)", "param_count": 68, "params": ["__env", "app", "errors", "_instance", "viewMode", "projects", "start_date", "end_date", "filter_start_date", "filter_end_date", "search", "budget", "project_type", "nextPageUrl", "prevPageUrl", "status", "projectID", "projecTid", "UsersforCreateProject", "users", "projectType", "priorityLevel", "priority_level", "clients", "vendors", "vendor", "selectedUsers", "selectedUsersForInvite", "usersAlreadyInvited", "selectedvendorsForShare", "vendorsAlreadyInProject", "selectedclientsForShare", "clientssAlreadyInProject", "name", "description", "ProjectTemplateName", "project", "statuss", "bug", "task", "activity", "user", "client", "milestone", "project_file", "building_manager", "selectedProjectID", "workspaceSlug", "page", "totalPages", "perPage", "totalItems", "isRefreshButton", "sortDirection", "sortField", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount"], "type": "blade"}, {"name": "livewire.c-r-m-projects.modals.edit (\\resources\\views\\livewire\\c-r-m-projects\\modals\\edit.blade.php)", "param_count": 68, "params": ["__env", "app", "errors", "_instance", "viewMode", "projects", "start_date", "end_date", "filter_start_date", "filter_end_date", "search", "budget", "project_type", "nextPageUrl", "prevPageUrl", "status", "projectID", "projecTid", "UsersforCreateProject", "users", "projectType", "priorityLevel", "priority_level", "clients", "vendors", "vendor", "selectedUsers", "selectedUsersForInvite", "usersAlreadyInvited", "selectedvendorsForShare", "vendorsAlreadyInProject", "selectedclientsForShare", "clientssAlreadyInProject", "name", "description", "ProjectTemplateName", "project", "statuss", "bug", "task", "activity", "user", "client", "milestone", "project_file", "building_manager", "selectedProjectID", "workspaceSlug", "page", "totalPages", "perPage", "totalItems", "isRefreshButton", "sortDirection", "sortField", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount"], "type": "blade"}, {"name": "livewire.c-r-m-projects.modals.inviteUser (\\resources\\views\\livewire\\c-r-m-projects\\modals\\inviteUser.blade.php)", "param_count": 68, "params": ["__env", "app", "errors", "_instance", "viewMode", "projects", "start_date", "end_date", "filter_start_date", "filter_end_date", "search", "budget", "project_type", "nextPageUrl", "prevPageUrl", "status", "projectID", "projecTid", "UsersforCreateProject", "users", "projectType", "priorityLevel", "priority_level", "clients", "vendors", "vendor", "selectedUsers", "selectedUsersForInvite", "usersAlreadyInvited", "selectedvendorsForShare", "vendorsAlreadyInProject", "selectedclientsForShare", "clientssAlreadyInProject", "name", "description", "ProjectTemplateName", "project", "statuss", "bug", "task", "activity", "user", "client", "milestone", "project_file", "building_manager", "selectedProjectID", "workspaceSlug", "page", "totalPages", "perPage", "totalItems", "isRefreshButton", "sortDirection", "sortField", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount"], "type": "blade"}, {"name": "livewire.c-r-m-projects.modals.shareClient (\\resources\\views\\livewire\\c-r-m-projects\\modals\\shareClient.blade.php)", "param_count": 68, "params": ["__env", "app", "errors", "_instance", "viewMode", "projects", "start_date", "end_date", "filter_start_date", "filter_end_date", "search", "budget", "project_type", "nextPageUrl", "prevPageUrl", "status", "projectID", "projecTid", "UsersforCreateProject", "users", "projectType", "priorityLevel", "priority_level", "clients", "vendors", "vendor", "selectedUsers", "selectedUsersForInvite", "usersAlreadyInvited", "selectedvendorsForShare", "vendorsAlreadyInProject", "selectedclientsForShare", "clientssAlreadyInProject", "name", "description", "ProjectTemplateName", "project", "statuss", "bug", "task", "activity", "user", "client", "milestone", "project_file", "building_manager", "selectedProjectID", "workspaceSlug", "page", "totalPages", "perPage", "totalItems", "isRefreshButton", "sortDirection", "sortField", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount"], "type": "blade"}, {"name": "livewire.c-r-m-projects.modals.shareVendors (\\resources\\views\\livewire\\c-r-m-projects\\modals\\shareVendors.blade.php)", "param_count": 68, "params": ["__env", "app", "errors", "_instance", "viewMode", "projects", "start_date", "end_date", "filter_start_date", "filter_end_date", "search", "budget", "project_type", "nextPageUrl", "prevPageUrl", "status", "projectID", "projecTid", "UsersforCreateProject", "users", "projectType", "priorityLevel", "priority_level", "clients", "vendors", "vendor", "selectedUsers", "selectedUsersForInvite", "usersAlreadyInvited", "selectedvendorsForShare", "vendorsAlreadyInProject", "selectedclientsForShare", "clientssAlreadyInProject", "name", "description", "ProjectTemplateName", "project", "statuss", "bug", "task", "activity", "user", "client", "milestone", "project_file", "building_manager", "selectedProjectID", "workspaceSlug", "page", "totalPages", "perPage", "totalItems", "isRefreshButton", "sortDirection", "sortField", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount"], "type": "blade"}, {"name": "livewire.c-r-m-projects.modals.duplication (\\resources\\views\\livewire\\c-r-m-projects\\modals\\duplication.blade.php)", "param_count": 68, "params": ["__env", "app", "errors", "_instance", "viewMode", "projects", "start_date", "end_date", "filter_start_date", "filter_end_date", "search", "budget", "project_type", "nextPageUrl", "prevPageUrl", "status", "projectID", "projecTid", "UsersforCreateProject", "users", "projectType", "priorityLevel", "priority_level", "clients", "vendors", "vendor", "selectedUsers", "selectedUsersForInvite", "usersAlreadyInvited", "selectedvendorsForShare", "vendorsAlreadyInProject", "selectedclientsForShare", "clientssAlreadyInProject", "name", "description", "ProjectTemplateName", "project", "statuss", "bug", "task", "activity", "user", "client", "milestone", "project_file", "building_manager", "selectedProjectID", "workspaceSlug", "page", "totalPages", "perPage", "totalItems", "isRefreshButton", "sortDirection", "sortField", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount"], "type": "blade"}, {"name": "livewire.c-r-m-projects.modals.saveProjectAsTemplate (\\resources\\views\\livewire\\c-r-m-projects\\modals\\saveProjectAsTemplate.blade.php)", "param_count": 68, "params": ["__env", "app", "errors", "_instance", "viewMode", "projects", "start_date", "end_date", "filter_start_date", "filter_end_date", "search", "budget", "project_type", "nextPageUrl", "prevPageUrl", "status", "projectID", "projecTid", "UsersforCreateProject", "users", "projectType", "priorityLevel", "priority_level", "clients", "vendors", "vendor", "selectedUsers", "selectedUsersForInvite", "usersAlreadyInvited", "selectedvendorsForShare", "vendorsAlreadyInProject", "selectedclientsForShare", "clientssAlreadyInProject", "name", "description", "ProjectTemplateName", "project", "statuss", "bug", "task", "activity", "user", "client", "milestone", "project_file", "building_manager", "selectedProjectID", "workspaceSlug", "page", "totalPages", "perPage", "totalItems", "isRefreshButton", "sortDirection", "sortField", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount"], "type": "blade"}, {"name": "layouts.app (\\resources\\views\\layouts\\app.blade.php)", "param_count": 17, "params": ["__env", "app", "errors", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount", "html"], "type": "blade"}, {"name": "layouts.partials._styles (\\resources\\views\\layouts\\partials\\_styles.blade.php)", "param_count": 18, "params": ["__env", "app", "errors", "_instance", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount", "html"], "type": "blade"}, {"name": "layouts.partials._header (\\resources\\views\\layouts\\partials\\_header.blade.php)", "param_count": 18, "params": ["__env", "app", "errors", "_instance", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount", "html"], "type": "blade"}, {"name": "layouts.partials._top_menu (\\resources\\views\\layouts\\partials\\_top_menu.blade.php)", "param_count": 18, "params": ["__env", "app", "errors", "_instance", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount", "html"], "type": "blade"}, {"name": "livewire.notifications.messages-notifications-list (\\resources\\views\\livewire\\notifications\\messages-notifications-list.blade.php)", "param_count": 23, "params": ["chatList", "errors", "_instance", "workspaceSlug", "totalUnreadNotifications", "previousUnreadCount", "newList", "list", "slugs", "userId", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount"], "type": "blade"}, {"name": "livewire.notifications.new-notifications-list-top-nav (\\resources\\views\\livewire\\notifications\\new-notifications-list-top-nav.blade.php)", "param_count": 28, "params": ["list", "totalUnreadNotifications", "errors", "_instance", "user", "perPage", "assignedAsset", "contractsIds", "accessBuildingsIds", "currentDate", "currentDateTime", "readyToLoad", "configOciLink", "ociLink", "selectedLanguage", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount"], "type": "blade"}, {"name": "livewire.menu.aside-nav-list (\\resources\\views\\livewire\\menu\\aside-nav-list.blade.php)", "param_count": 27, "params": ["userPrivilegesAside", "user", "hasViewPrivilege", "errors", "_instance", "has<PERSON>dmin", "projectId", "project", "workOrderMenuItemColor", "flagWorkorderSidebarMenu", "userPrivileges", "closedWorkOrderCount", "maintenanceRequestCount", "vendorRegistrationApplicationRequests", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount"], "type": "blade"}, {"name": "layouts.partials._footer (\\resources\\views\\layouts\\partials\\_footer.blade.php)", "param_count": 20, "params": ["__env", "app", "errors", "_instance", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount", "html", "url", "segments"], "type": "blade"}, {"name": "layouts.partials.check_crm_session (\\resources\\views\\layouts\\partials\\check_crm_session.blade.php)", "param_count": 20, "params": ["__env", "app", "errors", "_instance", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount", "html", "url", "segments"], "type": "blade"}, {"name": "layouts.partials._scripts (\\resources\\views\\layouts\\partials\\_scripts.blade.php)", "param_count": 20, "params": ["__env", "app", "errors", "_instance", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount", "html", "url", "segments"], "type": "blade"}]}, "route": {"uri": "GET CRMProjects/List", "middleware": "web, auth", "controller": "App\\Http\\Controllers\\CRMProjects\\ProjectController@list", "as": "CRMProjects.list", "namespace": "App\\Http\\Controllers\\Admin", "prefix": "/CRMProjects", "where": [], "file": "<a href=\"phpstorm://open?file=C:\\laragon\\www\\Osool-B2G\\app\\Http\\Controllers\\CRMProjects\\ProjectController.php&line=16\">\\app\\Http\\Controllers\\CRMProjects\\ProjectController.php:16-18</a>"}, "queries": {"nb_statements": 14, "nb_failed_statements": 0, "accumulated_duration": 0.027109999999999995, "accumulated_duration_str": "27.11ms", "statements": [{"sql": "select * from `users` where `id` = 7368 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["7368"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00429, "duration_str": "4.29ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "osool_dev_db2", "start_percent": 0, "width_percent": 15.824}, {"sql": "select * from `projects_details` where `projects_details`.`id` = 201 and `projects_details`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["201"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Providers\\AsideViewComposerServiceProvider.php", "line": 59}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 120}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 91}], "duration": 0.00116, "duration_str": "1.16ms", "stmt_id": "\\app\\Providers\\AsideViewComposerServiceProvider.php:59", "connection": "osool_dev_db2", "start_percent": 15.824, "width_percent": 4.279}, {"sql": "select * from `release_notes` where `store_status` = 1 and `release_notes`.`deleted_at` is null group by `version`", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 3048}, {"index": 15, "namespace": "view", "name": "8798481a8e56dccb941dae0e544cebc8cb0bca4d", "line": 51}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 69}], "duration": 0.00362, "duration_str": "3.62ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:3048", "connection": "osool_dev_db2", "start_percent": 20.103, "width_percent": 13.353}, {"sql": "select * from `crm_user` where `crm_user`.`user_id` = 7368 and `crm_user`.`user_id` is not null limit 1", "type": "query", "params": [], "bindings": ["7368"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "9740534860d5c11a9cf9e73e8939e4083f82ba1d", "line": 122}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 23, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 139}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 122}], "duration": 0.0012, "duration_str": "1.2ms", "stmt_id": "view::9740534860d5c11a9cf9e73e8939e4083f82ba1d:122", "connection": "osool_dev_db2", "start_percent": 33.456, "width_percent": 4.426}, {"sql": "select `name`, `name_ar` from `user_type` where `slug` = 'admin' limit 1", "type": "query", "params": [], "bindings": ["admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1929}, {"index": 14, "namespace": "view", "name": "8798481a8e56dccb941dae0e544cebc8cb0bca4d", "line": 161}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 69}], "duration": 0.00121, "duration_str": "1.21ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1929", "connection": "osool_dev_db2", "start_percent": 37.883, "width_percent": 4.463}, {"sql": "select `id`, `project_image`, `use_beneficiary_module`, `use_tenant_module`, `benificiary_status`, `tenant_status`, `project_name`, `project_name_ar`, `use_crm_module` from `projects_details` where `id` = 201 and `projects_details`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["201"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Traits\\ProjectDetailTrait.php", "line": 11}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\Menu\\AsideNavList.php", "line": 128}, {"index": 17, "namespace": null, "name": "\\app\\Http\\Livewire\\Menu\\AsideNavList.php", "line": 71}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 40}], "duration": 0.00114, "duration_str": "1.14ms", "stmt_id": "\\app\\Http\\Traits\\ProjectDetailTrait.php:11", "connection": "osool_dev_db2", "start_percent": 42.346, "width_percent": 4.205}, {"sql": "select * from `work_orders` where `project_user_id` = 7368", "type": "query", "params": [], "bindings": ["7368"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Services\\RequestedItemService.php", "line": 21}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Menu\\AsideNavList.php", "line": 148}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\Menu\\AsideNavList.php", "line": 73}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 40}], "duration": 0.00299, "duration_str": "2.99ms", "stmt_id": "\\app\\Services\\RequestedItemService.php:21", "connection": "osool_dev_db2", "start_percent": 46.551, "width_percent": 11.029}, {"sql": "select count(*) as aggregate from `service_provider_missing_items_requests` where `status` = 'requested' and 0 = 1", "type": "query", "params": [], "bindings": ["requested"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Services\\RequestedItemService.php", "line": 26}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\Menu\\AsideNavList.php", "line": 148}, {"index": 17, "namespace": null, "name": "\\app\\Http\\Livewire\\Menu\\AsideNavList.php", "line": 73}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 40}], "duration": 0.00121, "duration_str": "1.21ms", "stmt_id": "\\app\\Services\\RequestedItemService.php:26", "connection": "osool_dev_db2", "start_percent": 57.58, "width_percent": 4.463}, {"sql": "select `id`, `created_at` from `users` where `project_id` = 201 and `user_type` = 'admin' and `status` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["201", "admin", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Traits\\UserTrait.php", "line": 88}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Traits\\UserTrait.php", "line": 99}, {"index": 17, "namespace": null, "name": "\\app\\Http\\Livewire\\Menu\\AsideNavList.php", "line": 158}, {"index": 18, "namespace": null, "name": "\\app\\Http\\Livewire\\Menu\\AsideNavList.php", "line": 74}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "duration": 0.00411, "duration_str": "4.11ms", "stmt_id": "\\app\\Http\\Traits\\UserTrait.php:88", "connection": "osool_dev_db2", "start_percent": 62.044, "width_percent": 15.16}, {"sql": "select exists(select * from `projects_details` where `id` = 201 and `use_crm_module` = 1 and `projects_details`.`deleted_at` is null) as `exists`", "type": "query", "params": [], "bindings": ["201", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "\\app\\Models\\User.php", "line": 758}, {"index": 16, "namespace": "view", "name": "77a1a8487800179e2f9f8576eb7a3805ef086207", "line": 897}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 19, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 139}], "duration": 0.0010400000000000001, "duration_str": "1.04ms", "stmt_id": "\\app\\Models\\User.php:758", "connection": "osool_dev_db2", "start_percent": 77.204, "width_percent": 3.836}, {"sql": "select exists(select * from `crm_user` where `user_id` = 7368) as `exists`", "type": "query", "params": [], "bindings": ["7368"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "\\app\\Models\\User.php", "line": 758}, {"index": 16, "namespace": "view", "name": "77a1a8487800179e2f9f8576eb7a3805ef086207", "line": 897}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 19, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 139}], "duration": 0.00075, "duration_str": "750μs", "stmt_id": "\\app\\Models\\User.php:758", "connection": "osool_dev_db2", "start_percent": 81.04, "width_percent": 2.767}, {"sql": "select `id` from `users` where `project_id` = 201 and `user_type` = 'admin' and `status` = 1 limit 1", "type": "query", "params": [], "bindings": ["201", "admin", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 928}, {"index": 14, "namespace": "view", "name": "f8d18d6f63e9bf36e98d3bb1b82c9e7d86b4c71c", "line": 13}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 69}], "duration": 0.00352, "duration_str": "3.52ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:928", "connection": "osool_dev_db2", "start_percent": 83.807, "width_percent": 12.984}, {"sql": "select exists(select * from `projects_details` where `id` = 201 and `use_crm_module` = 1 and `projects_details`.`deleted_at` is null) as `exists`", "type": "query", "params": [], "bindings": ["201", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "\\app\\Models\\User.php", "line": 758}, {"index": 16, "namespace": "view", "name": "2c36a85657ec19d9aee4fefa5309928007e1bdb1", "line": 182}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 69}], "duration": 0.00045, "duration_str": "450μs", "stmt_id": "\\app\\Models\\User.php:758", "connection": "osool_dev_db2", "start_percent": 96.791, "width_percent": 1.66}, {"sql": "select exists(select * from `crm_user` where `user_id` = 7368) as `exists`", "type": "query", "params": [], "bindings": ["7368"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "\\app\\Models\\User.php", "line": 758}, {"index": 16, "namespace": "view", "name": "2c36a85657ec19d9aee4fefa5309928007e1bdb1", "line": 182}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 69}], "duration": 0.00041999999999999996, "duration_str": "420μs", "stmt_id": "\\app\\Models\\User.php:758", "connection": "osool_dev_db2", "start_percent": 98.451, "width_percent": 1.549}]}, "models": {"data": {"App\\Models\\CrmUser": 1, "App\\Models\\ReleaseNotes": 1, "App\\Models\\ProjectsDetails": 2, "App\\Models\\User": 2}, "count": 6}, "livewire": {"data": {"c-r-m-projects.projects-list #oU2OuVhkd19we80BpHbr": "array:5 [\n  \"data\" => array:51 [\n    \"viewMode\" => \"cards\"\n    \"projects\" => array:8 [\n      0 => array:16 [\n        \"id\" => 635\n        \"name\" => \"dup 1\"\n        \"status\" => \"Draft\"\n        \"project_type\" => null\n        \"priority_level\" => null\n        \"description\" => \"mohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammad mohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammad\"\n        \"start_date\" => \"2025-05-28\"\n        \"end_date\" => \"2025-05-30\"\n        \"budget\" => \" 2.56K ﷼\"\n        \"users\" => array:1 [\n          0 => array:3 [\n            \"name\" => \"<PERSON><PERSON><PERSON> Hasan\"\n            \"email\" => \"<EMAIL>\"\n            \"avatar\" => \"https://workdo-dev.osool.cloud/uploads/users-avatar/avatar.png\"\n          ]\n        ]\n        \"total_tasks\" => 8\n        \"total_comments\" => 0\n        \"created_by\" => \"<EMAIL>\"\n        \"clients\" => []\n        \"vendors\" => []\n        \"milestones\" => array:5 [\n          0 => array:6 [\n            \"title\" => \"TEST\"\n            \"status\" => \"incomplete\"\n            \"start_date\" => \"2025-07-02\"\n            \"end_date\" => \"2026-07-02\"\n            \"progress\" => \"0\"\n            \"cost\" => \" 1.99K ﷼\"\n          ]\n          1 => array:6 [\n            \"title\" => \"MILESTONE2\"\n            \"status\" => \"incomplete\"\n            \"start_date\" => \"2025-07-02\"\n            \"end_date\" => \"2025-07-03\"\n            \"progress\" => \"0\"\n            \"cost\" => \" 1.2K ﷼\"\n          ]\n          2 => array:6 [\n            \"title\" => \"MILESTONE3\"\n            \"status\" => \"complete\"\n            \"start_date\" => \"2025-07-01\"\n            \"end_date\" => \"2025-07-31\"\n            \"progress\" => \"100\"\n            \"cost\" => \" 100.00 ﷼\"\n          ]\n          3 => array:6 [\n            \"title\" => \"MS4\"\n            \"status\" => \"complete\"\n            \"start_date\" => \"2025-07-01\"\n            \"end_date\" => \"2025-07-31\"\n            \"progress\" => \"100\"\n            \"cost\" => \" 100.00 ﷼\"\n          ]\n          4 => array:6 [\n            \"title\" => \"MS5\"\n            \"status\" => \"complete\"\n            \"start_date\" => \"2025-07-01\"\n            \"end_date\" => \"2025-07-31\"\n            \"progress\" => \"100\"\n            \"cost\" => \" 100.00 ﷼\"\n          ]\n        ]\n      ]\n      1 => array:16 [\n        \"id\" => 634\n        \"name\" => \"dup 0\"\n        \"status\" => \"Draft\"\n        \"project_type\" => null\n        \"priority_level\" => null\n        \"description\" => \"mohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammad mohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammad\"\n        \"start_date\" => \"2025-05-28\"\n        \"end_date\" => \"2025-05-30\"\n        \"budget\" => \" 2.56K ﷼\"\n        \"users\" => array:1 [\n          0 => array:3 [\n            \"name\" => \"Khansaa Hasan\"\n            \"email\" => \"<EMAIL>\"\n            \"avatar\" => \"https://workdo-dev.osool.cloud/uploads/users-avatar/avatar.png\"\n          ]\n        ]\n        \"total_tasks\" => 0\n        \"total_comments\" => 0\n        \"created_by\" => \"<EMAIL>\"\n        \"clients\" => []\n        \"vendors\" => []\n        \"milestones\" => array:1 [\n          0 => array:6 [\n            \"title\" => \"Test\"\n            \"status\" => \"incomplete\"\n            \"start_date\" => \"2025-07-08\"\n            \"end_date\" => \"2025-07-15\"\n            \"progress\" => null\n            \"cost\" => \" 20.00 ﷼\"\n          ]\n        ]\n      ]\n      2 => array:16 [\n        \"id\" => 633\n        \"name\" => \"mohammad Copy 1\"\n        \"status\" => \"Draft\"\n        \"project_type\" => null\n        \"priority_level\" => null\n        \"description\" => \"mohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammad mohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammad\"\n        \"start_date\" => \"2025-05-28\"\n        \"end_date\" => \"2025-05-30\"\n        \"budget\" => \" 2.56K ﷼\"\n        \"users\" => array:1 [\n          0 => array:3 [\n            \"name\" => \"Khansaa Hasan\"\n            \"email\" => \"<EMAIL>\"\n            \"avatar\" => \"https://workdo-dev.osool.cloud/uploads/users-avatar/avatar.png\"\n          ]\n        ]\n        \"total_tasks\" => 1\n        \"total_comments\" => 0\n        \"created_by\" => \"<EMAIL>\"\n        \"clients\" => []\n        \"vendors\" => []\n        \"milestones\" => array:1 [\n          0 => array:6 [\n            \"title\" => \"dgdfg\"\n            \"status\" => \"incomplete\"\n            \"start_date\" => \"2025-07-28\"\n            \"end_date\" => \"2025-07-29\"\n            \"progress\" => null\n            \"cost\" => \" 55.00 ﷼\"\n          ]\n        ]\n      ]\n      3 => array:16 [\n        \"id\" => 632\n        \"name\" => \"tet te Copy\"\n        \"status\" => \"Draft\"\n        \"project_type\" => null\n        \"priority_level\" => null\n        \"description\" => \"Test project\"\n        \"start_date\" => \"2025-03-24\"\n        \"end_date\" => \"2025-03-24\"\n        \"budget\" => \" 0.00 ﷼\"\n        \"users\" => array:1 [\n          0 => array:3 [\n            \"name\" => \"Khansaa Hasan\"\n            \"email\" => \"<EMAIL>\"\n            \"avatar\" => \"https://workdo-dev.osool.cloud/uploads/users-avatar/avatar.png\"\n          ]\n        ]\n        \"total_tasks\" => 0\n        \"total_comments\" => 0\n        \"created_by\" => \"<EMAIL>\"\n        \"clients\" => []\n        \"vendors\" => []\n        \"milestones\" => array:1 [\n          0 => array:6 [\n            \"title\" => \"sfd\"\n            \"status\" => \"incomplete\"\n            \"start_date\" => \"2025-07-16\"\n            \"end_date\" => \"2025-07-17\"\n            \"progress\" => null\n            \"cost\" => \" 444.00 ﷼\"\n          ]\n        ]\n      ]\n      4 => array:16 [\n        \"id\" => 499\n        \"name\" => \"mohammad\"\n        \"status\" => \"Ongoing\"\n        \"project_type\" => null\n        \"priority_level\" => null\n        \"description\" => \"mohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammad mohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammad\"\n        \"start_date\" => \"2025-05-28\"\n        \"end_date\" => \"2025-05-30\"\n        \"budget\" => \" 2.56K ﷼\"\n        \"users\" => array:2 [\n          0 => array:3 [\n            \"name\" => \"Khansaa Hasan\"\n            \"email\" => \"<EMAIL>\"\n            \"avatar\" => \"https://workdo-dev.osool.cloud/uploads/users-avatar/avatar.png\"\n          ]\n          1 => array:3 [\n            \"name\" => \"Kirsten Mclaughlin\"\n            \"email\" => \"<EMAIL>\"\n            \"avatar\" => \"https://workdo-dev.osool.cloud/uploads/users-avatar/avatar.png\"\n          ]\n        ]\n        \"total_tasks\" => 0\n        \"total_comments\" => 0\n        \"created_by\" => \"<EMAIL>\"\n        \"clients\" => []\n        \"vendors\" => []\n        \"milestones\" => []\n      ]\n      5 => array:16 [\n        \"id\" => 469\n        \"name\" => \"tet te\"\n        \"status\" => \"Ongoing\"\n        \"project_type\" => null\n        \"priority_level\" => null\n        \"description\" => \"Test project\"\n        \"start_date\" => \"2025-03-24\"\n        \"end_date\" => \"2025-03-24\"\n        \"budget\" => \" 0.00 ﷼\"\n        \"users\" => array:1 [\n          0 => array:3 [\n            \"name\" => \"Khansaa Hasan\"\n            \"email\" => \"<EMAIL>\"\n            \"avatar\" => \"https://workdo-dev.osool.cloud/uploads/users-avatar/avatar.png\"\n          ]\n        ]\n        \"total_tasks\" => 0\n        \"total_comments\" => 0\n        \"created_by\" => \"<EMAIL>\"\n        \"clients\" => []\n        \"vendors\" => []\n        \"milestones\" => array:1 [\n          0 => array:6 [\n            \"title\" => \"milestone\"\n            \"status\" => \"complete\"\n            \"start_date\" => null\n            \"end_date\" => null\n            \"progress\" => null\n            \"cost\" => \" 40.00 ﷼\"\n          ]\n        ]\n      ]\n      6 => array:16 [\n        \"id\" => 412\n        \"name\" => \"Test ikbel Copy\"\n        \"status\" => \"Ongoing\"\n        \"project_type\" => null\n        \"priority_level\" => null\n        \"description\" => \"Test fff\"\n        \"start_date\" => \"2025-04-23\"\n        \"end_date\" => \"2025-04-29\"\n        \"budget\" => \" 93.00 ﷼\"\n        \"users\" => array:1 [\n          0 => array:3 [\n            \"name\" => \"Khansaa Hasan\"\n            \"email\" => \"<EMAIL>\"\n            \"avatar\" => \"https://workdo-dev.osool.cloud/uploads/users-avatar/avatar.png\"\n          ]\n        ]\n        \"total_tasks\" => 0\n        \"total_comments\" => 0\n        \"created_by\" => \"<EMAIL>\"\n        \"clients\" => []\n        \"vendors\" => []\n        \"milestones\" => []\n      ]\n      7 => array:16 [\n        \"id\" => 409\n        \"name\" => \"Converted Copy\"\n        \"status\" => \"Ongoing\"\n        \"project_type\" => null\n        \"priority_level\" => null\n        \"description\" => \"Test project\"\n        \"start_date\" => \"2025-04-08\"\n        \"end_date\" => \"2025-04-08\"\n        \"budget\" => \" 0.00 ﷼\"\n        \"users\" => array:1 [\n          0 => array:3 [\n            \"name\" => \"Khansaa Hasan\"\n            \"email\" => \"<EMAIL>\"\n            \"avatar\" => \"https://workdo-dev.osool.cloud/uploads/users-avatar/avatar.png\"\n          ]\n        ]\n        \"total_tasks\" => 0\n        \"total_comments\" => 0\n        \"created_by\" => \"<EMAIL>\"\n        \"clients\" => []\n        \"vendors\" => []\n        \"milestones\" => []\n      ]\n    ]\n    \"start_date\" => null\n    \"end_date\" => null\n    \"filter_start_date\" => null\n    \"filter_end_date\" => null\n    \"search\" => null\n    \"budget\" => null\n    \"project_type\" => \"\"\n    \"nextPageUrl\" => \"https://workdo-dev.osool.cloud/api/khansaa-test34444/projects?page=2\"\n    \"prevPageUrl\" => null\n    \"status\" => null\n    \"projectID\" => null\n    \"projecTid\" => null\n    \"UsersforCreateProject\" => []\n    \"users\" => array:7 [\n      0 => array:3 [\n        \"id\" => 65\n        \"name\" => \"Khansaa Hasan\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      1 => array:3 [\n        \"id\" => 209\n        \"name\" => \"Kirsten Mclaughlin\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      2 => array:3 [\n        \"id\" => 212\n        \"name\" => \"Staff Test\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      3 => array:3 [\n        \"id\" => 218\n        \"name\" => \"Khansaa Hasan22\"\n        \"email\" => \"khansaaha995@33gail.2com\"\n      ]\n      4 => array:3 [\n        \"id\" => 383\n        \"name\" => \"NAEL2\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      5 => array:3 [\n        \"id\" => 403\n        \"name\" => \"BMA\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      6 => array:3 [\n        \"id\" => 407\n        \"name\" => \"bma\"\n        \"email\" => \"<EMAIL>\"\n      ]\n    ]\n    \"projectType\" => array:8 [\n      0 => array:2 [\n        \"value\" => \"new_construction\"\n        \"label\" => \"New Construction\"\n      ]\n      1 => array:2 [\n        \"value\" => \"renovation\"\n        \"label\" => \"Renovation\"\n      ]\n      2 => array:2 [\n        \"value\" => \"maintenance\"\n        \"label\" => \"Maintenance\"\n      ]\n      3 => array:2 [\n        \"value\" => \"expansion\"\n        \"label\" => \"Expansion\"\n      ]\n      4 => array:2 [\n        \"value\" => \"demolition\"\n        \"label\" => \"Demolition\"\n      ]\n      5 => array:2 [\n        \"value\" => \"infrastructure\"\n        \"label\" => \"Infrastructure\"\n      ]\n      6 => array:2 [\n        \"value\" => \"design_projects\"\n        \"label\" => \"Design Projects\"\n      ]\n      7 => array:2 [\n        \"value\" => \"compliance_safety\"\n        \"label\" => \"Compliance & Safety\"\n      ]\n    ]\n    \"priorityLevel\" => array:4 [\n      0 => array:2 [\n        \"value\" => \"critical\"\n        \"label\" => \"Critical\"\n      ]\n      1 => array:2 [\n        \"value\" => \"high\"\n        \"label\" => \"High\"\n      ]\n      2 => array:2 [\n        \"value\" => \"medium\"\n        \"label\" => \"Medium\"\n      ]\n      3 => array:2 [\n        \"value\" => \"low\"\n        \"label\" => \"Low\"\n      ]\n    ]\n    \"priority_level\" => null\n    \"clients\" => array:57 [\n      0 => array:3 [\n        \"id\" => 219\n        \"name\" => \"Test deal\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      1 => array:3 [\n        \"id\" => 318\n        \"name\" => \"Test Client\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      2 => array:3 [\n        \"id\" => 323\n        \"name\" => \"Alhayajneh Mohammad\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      3 => array:3 [\n        \"id\" => 492\n        \"name\" => \"Customer 1\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      4 => array:3 [\n        \"id\" => 493\n        \"name\" => \"Customer 2\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      5 => array:3 [\n        \"id\" => 494\n        \"name\" => \"Customer 3\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      6 => array:3 [\n        \"id\" => 495\n        \"name\" => \"Customer 4\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      7 => array:3 [\n        \"id\" => 496\n        \"name\" => \"Customer 5\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      8 => array:3 [\n        \"id\" => 497\n        \"name\" => \"Customer 6\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      9 => array:3 [\n        \"id\" => 498\n        \"name\" => \"Customer 7\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      10 => array:3 [\n        \"id\" => 499\n        \"name\" => \"Customer 8\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      11 => array:3 [\n        \"id\" => 500\n        \"name\" => \"Customer 9\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      12 => array:3 [\n        \"id\" => 501\n        \"name\" => \"Customer 10\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      13 => array:3 [\n        \"id\" => 502\n        \"name\" => \"Customer 11\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      14 => array:3 [\n        \"id\" => 503\n        \"name\" => \"Customer 12\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      15 => array:3 [\n        \"id\" => 504\n        \"name\" => \"Customer 13\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      16 => array:3 [\n        \"id\" => 505\n        \"name\" => \"Customer 14\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      17 => array:3 [\n        \"id\" => 506\n        \"name\" => \"Customer 15\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      18 => array:3 [\n        \"id\" => 507\n        \"name\" => \"Customer 16\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      19 => array:3 [\n        \"id\" => 508\n        \"name\" => \"Customer 17\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      20 => array:3 [\n        \"id\" => 509\n        \"name\" => \"Customer 18\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      21 => array:3 [\n        \"id\" => 510\n        \"name\" => \"Customer 19\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      22 => array:3 [\n        \"id\" => 511\n        \"name\" => \"Customer 20\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      23 => array:3 [\n        \"id\" => 512\n        \"name\" => \"Customer 21\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      24 => array:3 [\n        \"id\" => 513\n        \"name\" => \"Customer 22\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      25 => array:3 [\n        \"id\" => 514\n        \"name\" => \"Customer 23\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      26 => array:3 [\n        \"id\" => 515\n        \"name\" => \"Customer 24\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      27 => array:3 [\n        \"id\" => 516\n        \"name\" => \"Customer 25\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      28 => array:3 [\n        \"id\" => 517\n        \"name\" => \"Customer 26\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      29 => array:3 [\n        \"id\" => 518\n        \"name\" => \"Customer 27\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      30 => array:3 [\n        \"id\" => 519\n        \"name\" => \"Customer 28\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      31 => array:3 [\n        \"id\" => 520\n        \"name\" => \"Customer 29\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      32 => array:3 [\n        \"id\" => 521\n        \"name\" => \"Customer 30\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      33 => array:3 [\n        \"id\" => 522\n        \"name\" => \"Customer 31\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      34 => array:3 [\n        \"id\" => 523\n        \"name\" => \"Customer 32\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      35 => array:3 [\n        \"id\" => 524\n        \"name\" => \"Customer 33\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      36 => array:3 [\n        \"id\" => 525\n        \"name\" => \"Customer 34\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      37 => array:3 [\n        \"id\" => 526\n        \"name\" => \"Customer 35\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      38 => array:3 [\n        \"id\" => 527\n        \"name\" => \"Customer 36\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      39 => array:3 [\n        \"id\" => 528\n        \"name\" => \"Customer 37\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      40 => array:3 [\n        \"id\" => 529\n        \"name\" => \"Customer 38\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      41 => array:3 [\n        \"id\" => 530\n        \"name\" => \"Customer 39\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      42 => array:3 [\n        \"id\" => 531\n        \"name\" => \"Customer 40\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      43 => array:3 [\n        \"id\" => 532\n        \"name\" => \"Customer 41\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      44 => array:3 [\n        \"id\" => 533\n        \"name\" => \"Customer 42\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      45 => array:3 [\n        \"id\" => 534\n        \"name\" => \"Customer 43\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      46 => array:3 [\n        \"id\" => 535\n        \"name\" => \"Customer 44\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      47 => array:3 [\n        \"id\" => 536\n        \"name\" => \"Customer 45\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      48 => array:3 [\n        \"id\" => 537\n        \"name\" => \"Customer 46\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      49 => array:3 [\n        \"id\" => 538\n        \"name\" => \"Customer 47\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      50 => array:3 [\n        \"id\" => 539\n        \"name\" => \"Customer 48\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      51 => array:3 [\n        \"id\" => 540\n        \"name\" => \"Customer 49\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      52 => array:3 [\n        \"id\" => 541\n        \"name\" => \"Customer 50\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      53 => array:3 [\n        \"id\" => 563\n        \"name\" => \"Khansaa 18.06\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      54 => array:3 [\n        \"id\" => 564\n        \"name\" => \"Fouzan\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      55 => array:3 [\n        \"id\" => 631\n        \"name\" => \"Testing\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      56 => array:3 [\n        \"id\" => 652\n        \"name\" => \"New Tenant Dev Server\"\n        \"email\" => \"<EMAIL>\"\n      ]\n    ]\n    \"vendors\" => array:59 [\n      0 => array:3 [\n        \"id\" => 491\n        \"name\" => \"Fouzan\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      1 => array:3 [\n        \"id\" => 568\n        \"name\" => \"Test Vendor 1\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      2 => array:3 [\n        \"id\" => 569\n        \"name\" => \"Test Vendor 2\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      3 => array:3 [\n        \"id\" => 570\n        \"name\" => \"Test Vendor 3\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      4 => array:3 [\n        \"id\" => 571\n        \"name\" => \"Test Vendor 4\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      5 => array:3 [\n        \"id\" => 572\n        \"name\" => \"Test Vendor 5\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      6 => array:3 [\n        \"id\" => 573\n        \"name\" => \"Test Vendor 6\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      7 => array:3 [\n        \"id\" => 574\n        \"name\" => \"Test Vendor 7\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      8 => array:3 [\n        \"id\" => 575\n        \"name\" => \"Test Vendor 8\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      9 => array:3 [\n        \"id\" => 576\n        \"name\" => \"Test Vendor 9\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      10 => array:3 [\n        \"id\" => 577\n        \"name\" => \"Test Vendor 10\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      11 => array:3 [\n        \"id\" => 578\n        \"name\" => \"Test Vendor 11\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      12 => array:3 [\n        \"id\" => 579\n        \"name\" => \"Test Vendor 12\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      13 => array:3 [\n        \"id\" => 580\n        \"name\" => \"Test Vendor 13\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      14 => array:3 [\n        \"id\" => 581\n        \"name\" => \"Test Vendor 14\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      15 => array:3 [\n        \"id\" => 582\n        \"name\" => \"Test Vendor 15\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      16 => array:3 [\n        \"id\" => 583\n        \"name\" => \"Test Vendor 16\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      17 => array:3 [\n        \"id\" => 584\n        \"name\" => \"Test Vendor 17\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      18 => array:3 [\n        \"id\" => 585\n        \"name\" => \"Test Vendor 18\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      19 => array:3 [\n        \"id\" => 586\n        \"name\" => \"Test Vendor 19\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      20 => array:3 [\n        \"id\" => 587\n        \"name\" => \"Test Vendor 20\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      21 => array:3 [\n        \"id\" => 588\n        \"name\" => \"Test Vendor 21\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      22 => array:3 [\n        \"id\" => 589\n        \"name\" => \"Test Vendor 22\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      23 => array:3 [\n        \"id\" => 590\n        \"name\" => \"Test Vendor 23\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      24 => array:3 [\n        \"id\" => 591\n        \"name\" => \"Test Vendor 24\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      25 => array:3 [\n        \"id\" => 592\n        \"name\" => \"Test Vendor 25\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      26 => array:3 [\n        \"id\" => 593\n        \"name\" => \"Test Vendor 26\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      27 => array:3 [\n        \"id\" => 594\n        \"name\" => \"Test Vendor 27\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      28 => array:3 [\n        \"id\" => 595\n        \"name\" => \"Test Vendor 28\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      29 => array:3 [\n        \"id\" => 596\n        \"name\" => \"Test Vendor 29\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      30 => array:3 [\n        \"id\" => 597\n        \"name\" => \"Test Vendor 30\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      31 => array:3 [\n        \"id\" => 598\n        \"name\" => \"Test Vendor 31\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      32 => array:3 [\n        \"id\" => 599\n        \"name\" => \"Test Vendor 32\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      33 => array:3 [\n        \"id\" => 600\n        \"name\" => \"Test Vendor 33\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      34 => array:3 [\n        \"id\" => 601\n        \"name\" => \"Test Vendor 34\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      35 => array:3 [\n        \"id\" => 602\n        \"name\" => \"Test Vendor 35\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      36 => array:3 [\n        \"id\" => 603\n        \"name\" => \"Test Vendor 36\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      37 => array:3 [\n        \"id\" => 604\n        \"name\" => \"Test Vendor 37\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      38 => array:3 [\n        \"id\" => 605\n        \"name\" => \"Test Vendor 38\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      39 => array:3 [\n        \"id\" => 606\n        \"name\" => \"Test Vendor 39\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      40 => array:3 [\n        \"id\" => 607\n        \"name\" => \"Test Vendor 40\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      41 => array:3 [\n        \"id\" => 608\n        \"name\" => \"Test Vendor 41\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      42 => array:3 [\n        \"id\" => 609\n        \"name\" => \"Test Vendor 42\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      43 => array:3 [\n        \"id\" => 610\n        \"name\" => \"Test Vendor 43\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      44 => array:3 [\n        \"id\" => 611\n        \"name\" => \"Test Vendor 44\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      45 => array:3 [\n        \"id\" => 612\n        \"name\" => \"Test Vendor 45\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      46 => array:3 [\n        \"id\" => 613\n        \"name\" => \"Test Vendor 46\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      47 => array:3 [\n        \"id\" => 614\n        \"name\" => \"Test Vendor 47\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      48 => array:3 [\n        \"id\" => 615\n        \"name\" => \"Test Vendor 48\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      49 => array:3 [\n        \"id\" => 616\n        \"name\" => \"Test Vendor 49\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      50 => array:3 [\n        \"id\" => 617\n        \"name\" => \"Test Vendor 50\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      51 => array:3 [\n        \"id\" => 618\n        \"name\" => \"Test Vendor 51\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      52 => array:3 [\n        \"id\" => 619\n        \"name\" => \"Test Vendor 52\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      53 => array:3 [\n        \"id\" => 620\n        \"name\" => \"Test Vendor 53\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      54 => array:3 [\n        \"id\" => 621\n        \"name\" => \"Test Vendor 54\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      55 => array:3 [\n        \"id\" => 622\n        \"name\" => \"Test Vendor\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      56 => array:3 [\n        \"id\" => 628\n        \"name\" => \"Test Vendor\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      57 => array:3 [\n        \"id\" => 629\n        \"name\" => \"Test Vendor\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      58 => array:3 [\n        \"id\" => 653\n        \"name\" => \"SP Admin Test Dev\"\n        \"email\" => \"<EMAIL>\"\n      ]\n    ]\n    \"vendor\" => []\n    \"selectedUsers\" => []\n    \"selectedUsersForInvite\" => []\n    \"usersAlreadyInvited\" => []\n    \"selectedvendorsForShare\" => []\n    \"vendorsAlreadyInProject\" => []\n    \"selectedclientsForShare\" => []\n    \"clientssAlreadyInProject\" => []\n    \"name\" => null\n    \"description\" => null\n    \"ProjectTemplateName\" => null\n    \"project\" => \"\"\n    \"statuss\" => null\n    \"bug\" => []\n    \"task\" => []\n    \"activity\" => []\n    \"user\" => []\n    \"client\" => []\n    \"milestone\" => []\n    \"project_file\" => []\n    \"building_manager\" => []\n    \"selectedProjectID\" => null\n    \"workspaceSlug\" => \"khansaa-test34444\"\n    \"page\" => 1\n    \"totalPages\" => 4.0\n    \"perPage\" => 8\n    \"totalItems\" => 26\n    \"isRefreshButton\" => true\n    \"sortDirection\" => \"desc\"\n    \"sortField\" => \"\"\n  ]\n  \"name\" => \"c-r-m-projects.projects-list\"\n  \"view\" => \"livewire.c-r-m-projects.projects-list\"\n  \"component\" => \"App\\Http\\Livewire\\CRMProjects\\ProjectsList\"\n  \"id\" => \"oU2OuVhkd19we80BpHbr\"\n]", "notifications.messages-notifications-list #LlaOz9BdxJVE4ZmGF9GP": "array:5 [\n  \"data\" => array:7 [\n    \"workspaceSlug\" => \"khansaa-test34444\"\n    \"totalUnreadNotifications\" => 0\n    \"previousUnreadCount\" => 0\n    \"newList\" => null\n    \"list\" => []\n    \"slugs\" => array:3 [\n      0 => \"facebook\"\n      1 => \"whatsapp\"\n      2 => \"instagram\"\n    ]\n    \"userId\" => null\n  ]\n  \"name\" => \"notifications.messages-notifications-list\"\n  \"view\" => \"livewire.notifications.messages-notifications-list\"\n  \"component\" => \"App\\Http\\Livewire\\Notifications\\MessagesNotificationsList\"\n  \"id\" => \"LlaOz9BdxJVE4ZmGF9GP\"\n]", "notifications.new-notifications-list-top-nav #WhR7BQve6fzZlKFJyQwN": "array:5 [\n  \"data\" => array:11 [\n    \"user\" => null\n    \"perPage\" => null\n    \"assignedAsset\" => null\n    \"contractsIds\" => null\n    \"accessBuildingsIds\" => null\n    \"currentDate\" => null\n    \"currentDateTime\" => null\n    \"readyToLoad\" => null\n    \"configOciLink\" => null\n    \"ociLink\" => null\n    \"selectedLanguage\" => null\n  ]\n  \"name\" => \"notifications.new-notifications-list-top-nav\"\n  \"view\" => \"livewire.notifications.new-notifications-list-top-nav\"\n  \"component\" => \"App\\Http\\Livewire\\Notifications\\NewNotificationsListTopNav\"\n  \"id\" => \"WhR7BQve6fzZlKFJyQwN\"\n]", "menu.aside-nav-list #": "array:7 [\n  \"data\" => array:10 [\n    \"user\" => App\\Models\\User {#4015\n      #connection: \"mysql\"\n      #table: \"users\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:69 [\n        \"id\" => 7368\n        \"email\" => \"<EMAIL>\"\n        \"password\" => \"$2y$10$Nrgb2fubI18xCSRqaKjUjOzVJbptwn80b0U.ID9srqgW/v2G.7cpy\"\n        \"name\" => \"Crm Test 02\"\n        \"first_name\" => null\n        \"last_name\" => null\n        \"phone\" => null\n        \"profile_img\" => null\n        \"emp_id\" => \"99090\"\n        \"profession_id\" => null\n        \"emp_dept\" => null\n        \"building_ids\" => null\n        \"contract_ids\" => null\n        \"supervisor_id\" => null\n        \"sp_admin_id\" => null\n        \"address\" => null\n        \"country_id\" => 1\n        \"city_id\" => 1\n        \"role_regions\" => null\n        \"role_cities\" => null\n        \"asset_categories\" => null\n        \"keeper_warehouses\" => null\n        \"properties\" => null\n        \"contracts\" => null\n        \"beneficiary\" => null\n        \"service_provider\" => \"1\"\n        \"user_type\" => \"admin\"\n        \"user_privileges\" => null\n        \"approved_max_amount\" => null\n        \"created_by\" => 21\n        \"project_id\" => 201\n        \"project_user_id\" => 7368\n        \"device_token\" => null\n        \"device_type\" => \"android\"\n        \"api_token\" => null\n        \"otp\" => null\n        \"apartment\" => null\n        \"unit_receival_date\" => null\n        \"unit_receival_later_clicked_at\" => null\n        \"langForSms\" => \"Arabic\"\n        \"otp_verified\" => 0\n        \"email_verified\" => 0\n        \"email_attempts\" => 0\n        \"last_email_attempt_at\" => null\n        \"allow_akaunting\" => 1\n        \"status\" => 1\n        \"is_deleted\" => \"no\"\n        \"created_at\" => \"2025-02-26 20:35:15\"\n        \"modified_at\" => \"2025-07-31 15:40:31\"\n        \"save_later_date\" => null\n        \"favorite_language\" => \"-\"\n        \"last_ip\" => null\n        \"deleted_at\" => null\n        \"last_login_datetime\" => null\n        \"temp_password\" => null\n        \"otp_for_password\" => null\n        \"otp_for_password_verified\" => 0\n        \"selected_app_langugage\" => \"en\"\n        \"temp_phone_number\" => null\n        \"is_subcontractors_worker\" => 0\n        \"first_login\" => 1\n        \"is_unit_link\" => 0\n        \"later_booking_alert\" => null\n        \"akaunting_vendor_id\" => null\n        \"akaunting_customer_id\" => null\n        \"associated_workdo_id\" => null\n        \"is_bma_area_manager\" => 0\n        \"workspace_slug\" => \"khansaa-test34444\"\n        \"crm_api_token\" => \"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczovL3dvcmtkby1kZXYub3Nvb2wuY2xvdWQvYXBpL2xvZ2luIiwiaWF0IjoxNzUzOTY1NjMxLCJleHAiOjE3NTM5NjkyMzEsIm5iZiI6MTc1Mzk2NTYzMSwianRpIjoic1pjZ2NWalpZN3BpTmk0bSIsInN1YiI6IjY1IiwicHJ2IjoiMjNiZDVjODk0OWY2MDBhZGIzOWU3MDFjNDAwODcyZGI3YTU5NzZmNyJ9.TLJmpSuhbDbDDxd_ao3QzRLE3UmXsTObOCnY5kJvftU\"\n      ]\n      #original: array:69 [\n        \"id\" => 7368\n        \"email\" => \"<EMAIL>\"\n        \"password\" => \"$2y$10$Nrgb2fubI18xCSRqaKjUjOzVJbptwn80b0U.ID9srqgW/v2G.7cpy\"\n        \"name\" => \"Crm Test 02\"\n        \"first_name\" => null\n        \"last_name\" => null\n        \"phone\" => null\n        \"profile_img\" => null\n        \"emp_id\" => \"99090\"\n        \"profession_id\" => null\n        \"emp_dept\" => null\n        \"building_ids\" => null\n        \"contract_ids\" => null\n        \"supervisor_id\" => null\n        \"sp_admin_id\" => null\n        \"address\" => null\n        \"country_id\" => 1\n        \"city_id\" => 1\n        \"role_regions\" => null\n        \"role_cities\" => null\n        \"asset_categories\" => null\n        \"keeper_warehouses\" => null\n        \"properties\" => null\n        \"contracts\" => null\n        \"beneficiary\" => null\n        \"service_provider\" => \"1\"\n        \"user_type\" => \"admin\"\n        \"user_privileges\" => null\n        \"approved_max_amount\" => null\n        \"created_by\" => 21\n        \"project_id\" => 201\n        \"project_user_id\" => 7368\n        \"device_token\" => null\n        \"device_type\" => \"android\"\n        \"api_token\" => null\n        \"otp\" => null\n        \"apartment\" => null\n        \"unit_receival_date\" => null\n        \"unit_receival_later_clicked_at\" => null\n        \"langForSms\" => \"Arabic\"\n        \"otp_verified\" => 0\n        \"email_verified\" => 0\n        \"email_attempts\" => 0\n        \"last_email_attempt_at\" => null\n        \"allow_akaunting\" => 1\n        \"status\" => 1\n        \"is_deleted\" => \"no\"\n        \"created_at\" => \"2025-02-26 20:35:15\"\n        \"modified_at\" => \"2025-07-31 15:40:31\"\n        \"save_later_date\" => null\n        \"favorite_language\" => \"-\"\n        \"last_ip\" => null\n        \"deleted_at\" => null\n        \"last_login_datetime\" => null\n        \"temp_password\" => null\n        \"otp_for_password\" => null\n        \"otp_for_password_verified\" => 0\n        \"selected_app_langugage\" => \"en\"\n        \"temp_phone_number\" => null\n        \"is_subcontractors_worker\" => 0\n        \"first_login\" => 1\n        \"is_unit_link\" => 0\n        \"later_booking_alert\" => null\n        \"akaunting_vendor_id\" => null\n        \"akaunting_customer_id\" => null\n        \"associated_workdo_id\" => null\n        \"is_bma_area_manager\" => 0\n        \"workspace_slug\" => \"khansaa-test34444\"\n        \"crm_api_token\" => \"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczovL3dvcmtkby1kZXYub3Nvb2wuY2xvdWQvYXBpL2xvZ2luIiwiaWF0IjoxNzUzOTY1NjMxLCJleHAiOjE3NTM5NjkyMzEsIm5iZiI6MTc1Mzk2NTYzMSwianRpIjoic1pjZ2NWalpZN3BpTmk0bSIsInN1YiI6IjY1IiwicHJ2IjoiMjNiZDVjODk0OWY2MDBhZGIzOWU3MDFjNDAwODcyZGI3YTU5NzZmNyJ9.TLJmpSuhbDbDDxd_ao3QzRLE3UmXsTObOCnY5kJvftU\"\n      ]\n      #changes: []\n      #casts: array:2 [\n        \"email_verified_at\" => \"datetime\"\n        \"deleted_at\" => \"datetime\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dates: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: array:2 [\n        \"projectDetails\" => App\\Models\\ProjectsDetails {#4033\n          #connection: \"mysql\"\n          #table: \"projects_details\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:22 [\n            \"id\" => 201\n            \"user_id\" => 0\n            \"project_name\" => \"Khadeer CRM Test 02\"\n            \"project_name_ar\" => \"Khadeer CRM Test 02\"\n            \"industry_type\" => \"1\"\n            \"project_image\" => null\n            \"created_by\" => 0\n            \"is_deleted\" => 0\n            \"created_at\" => \"2025-02-26 17:34:01\"\n            \"updated_at\" => \"2025-04-23 13:56:58\"\n            \"use_erp_module\" => 1\n            \"use_crm_module\" => 1\n            \"use_tenant_module\" => 1\n            \"tenant_status\" => 1\n            \"use_beneficiary_module\" => 1\n            \"benificiary_status\" => 1\n            \"community_status\" => 1\n            \"contract_status\" => 1\n            \"contract_start_date\" => null\n            \"contract_end_date\" => null\n            \"share_post\" => 1\n            \"deleted_at\" => null\n          ]\n          #original: array:22 [\n            \"id\" => 201\n            \"user_id\" => 0\n            \"project_name\" => \"Khadeer CRM Test 02\"\n            \"project_name_ar\" => \"Khadeer CRM Test 02\"\n            \"industry_type\" => \"1\"\n            \"project_image\" => null\n            \"created_by\" => 0\n            \"is_deleted\" => 0\n            \"created_at\" => \"2025-02-26 17:34:01\"\n            \"updated_at\" => \"2025-04-23 13:56:58\"\n            \"use_erp_module\" => 1\n            \"use_crm_module\" => 1\n            \"use_tenant_module\" => 1\n            \"tenant_status\" => 1\n            \"use_beneficiary_module\" => 1\n            \"benificiary_status\" => 1\n            \"community_status\" => 1\n            \"contract_status\" => 1\n            \"contract_start_date\" => null\n            \"contract_end_date\" => null\n            \"share_post\" => 1\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:16 [\n            0 => \"user_id\"\n            1 => \"project_name\"\n            2 => \"project_name_ar\"\n            3 => \"project_image\"\n            4 => \"industry_type\"\n            5 => \"created_by\"\n            6 => \"is_deleted\"\n            7 => \"use_erp_module\"\n            8 => \"use_tenant_module\"\n            9 => \"tenant_status\"\n            10 => \"use_beneficiary_module\"\n            11 => \"benificiary_status\"\n            12 => \"community_status\"\n            13 => \"contract_status\"\n            14 => \"share_post\"\n            15 => \"use_crm_module\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n          #excludedAttributes: []\n          +auditEvent: null\n          +auditCustomOld: null\n          +auditCustomNew: null\n          +isCustomEvent: false\n          +preloadedResolverData: []\n        }\n        \"crmUser\" => App\\Models\\CrmUser {#4003\n          #connection: \"mysql\"\n          #table: \"crm_user\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:8 [\n            \"id\" => 2\n            \"user_id\" => 7368\n            \"crm_user_id\" => 65\n            \"created_at\" => \"2025-02-26 20:35:15\"\n            \"updated_at\" => \"2025-02-26 20:35:15\"\n            \"instagram_connect\" => 1\n            \"facebook_connect\" => 0\n            \"whatsapp_connect\" => 0\n          ]\n          #original: array:8 [\n            \"id\" => 2\n            \"user_id\" => 7368\n            \"crm_user_id\" => 65\n            \"created_at\" => \"2025-02-26 20:35:15\"\n            \"updated_at\" => \"2025-02-26 20:35:15\"\n            \"instagram_connect\" => 1\n            \"facebook_connect\" => 0\n            \"whatsapp_connect\" => 0\n          ]\n          #changes: []\n          #casts: []\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:2 [\n            0 => \"user_id\"\n            1 => \"crm_user_id\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n        }\n      ]\n      #touches: []\n      +timestamps: true\n      #hidden: array:2 [\n        0 => \"password\"\n        1 => \"remember_token\"\n      ]\n      #visible: []\n      #fillable: array:62 [\n        0 => \"allow_akaunting\"\n        1 => \"email\"\n        2 => \"password\"\n        3 => \"name\"\n        4 => \"first_name\"\n        5 => \"last_name\"\n        6 => \"apartment\"\n        7 => \"unit_receival_date\"\n        8 => \"later_booking_alert\"\n        9 => \"phone\"\n        10 => \"profile_img\"\n        11 => \"address\"\n        12 => \"country_id\"\n        13 => \"city_id\"\n        14 => \"role_regions\"\n        15 => \"role_cities\"\n        16 => \"asset_categories\"\n        17 => \"properties\"\n        18 => \"contracts\"\n        19 => \"beneficiary\"\n        20 => \"service_provider\"\n        21 => \"user_type\"\n        22 => \"project_id\"\n        23 => \"project_user_id\"\n        24 => \"created_by\"\n        25 => \"status\"\n        26 => \"user_privileges\"\n        27 => \"approved_max_amount\"\n        28 => \"emp_id\"\n        29 => \"profession_id\"\n        30 => \"emp_dept\"\n        31 => \"building_ids\"\n        32 => \"contract_ids\"\n        33 => \"supervisor_id\"\n        34 => \"sp_admin_id\"\n        35 => \"langForSms\"\n        36 => \"deleted_at\"\n        37 => \"otp\"\n        38 => \"temp_password\"\n        39 => \"otp_for_password\"\n        40 => \"otp_for_password_verified\"\n        41 => \"temp_phone_number\"\n        42 => \"favorite_language\"\n        43 => \"is_subcontractors_worker\"\n        44 => \"keeper_warehouses\"\n        45 => \"save_later_date\"\n        46 => \"first_login\"\n        47 => \"is_unit_link\"\n        48 => \"akaunting_vendor_id\"\n        49 => \"akaunting_customer_id\"\n        50 => \"crm_api_token\"\n        51 => \"workspace_slug\"\n        52 => \"is_bma_area_manager\"\n        53 => \"assigned_workers\"\n        54 => \"sleep_mode\"\n        55 => \"offline_mode\"\n        56 => \"attendance_mandatory\"\n        57 => \"admin_level\"\n        58 => \"role\"\n        59 => \"attendance_target\"\n        60 => \"salary\"\n        61 => \"show_extra_info\"\n      ]\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n      #rememberTokenName: \"remember_token\"\n      #accessToken: null\n      #forceDeleting: false\n      #excludedAttributes: []\n      +auditEvent: null\n      +auditCustomOld: null\n      +auditCustomNew: null\n      +isCustomEvent: false\n      +preloadedResolverData: []\n      -roleClass: null\n      -permissionClass: null\n      -wildcardClass: null\n    }\n    \"hasAdmin\" => 7368\n    \"projectId\" => null\n    \"project\" => App\\Models\\ProjectsDetails {#4108\n      #connection: \"mysql\"\n      #table: \"projects_details\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:9 [\n        \"id\" => 201\n        \"project_image\" => null\n        \"use_beneficiary_module\" => 1\n        \"use_tenant_module\" => 1\n        \"benificiary_status\" => 1\n        \"tenant_status\" => 1\n        \"project_name\" => \"Khadeer CRM Test 02\"\n        \"project_name_ar\" => \"Khadeer CRM Test 02\"\n        \"use_crm_module\" => 1\n      ]\n      #original: array:9 [\n        \"id\" => 201\n        \"project_image\" => null\n        \"use_beneficiary_module\" => 1\n        \"use_tenant_module\" => 1\n        \"benificiary_status\" => 1\n        \"tenant_status\" => 1\n        \"project_name\" => \"Khadeer CRM Test 02\"\n        \"project_name_ar\" => \"Khadeer CRM Test 02\"\n        \"use_crm_module\" => 1\n      ]\n      #changes: []\n      #casts: array:1 [\n        \"deleted_at\" => \"datetime\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dates: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      +timestamps: true\n      #hidden: []\n      #visible: []\n      #fillable: array:16 [\n        0 => \"user_id\"\n        1 => \"project_name\"\n        2 => \"project_name_ar\"\n        3 => \"project_image\"\n        4 => \"industry_type\"\n        5 => \"created_by\"\n        6 => \"is_deleted\"\n        7 => \"use_erp_module\"\n        8 => \"use_tenant_module\"\n        9 => \"tenant_status\"\n        10 => \"use_beneficiary_module\"\n        11 => \"benificiary_status\"\n        12 => \"community_status\"\n        13 => \"contract_status\"\n        14 => \"share_post\"\n        15 => \"use_crm_module\"\n      ]\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n      #forceDeleting: false\n      #excludedAttributes: []\n      +auditEvent: null\n      +auditCustomOld: null\n      +auditCustomNew: null\n      +isCustomEvent: false\n      +preloadedResolverData: []\n    }\n    \"workOrderMenuItemColor\" => \"#000\"\n    \"flagWorkorderSidebarMenu\" => false\n    \"userPrivileges\" => null\n    \"closedWorkOrderCount\" => null\n    \"maintenanceRequestCount\" => 0\n    \"vendorRegistrationApplicationRequests\" => null\n  ]\n  \"oldData\" => null\n  \"actionQueue\" => null\n  \"name\" => \"menu.aside-nav-list\"\n  \"view\" => \"livewire.menu.aside-nav-list\"\n  \"component\" => \"App\\Http\\Livewire\\Menu\\AsideNavList\"\n  \"id\" => null\n]"}, "count": 4}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "82Y3S2Ii8XAEUfJqJwoJcxv4amqMyZ5Eyd3DAmvL", "captcha_answer": "32", "_previous": "array:1 [\n  \"url\" => \"http://osool-b2g.test/CRMProjects/List?page=1\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "7368", "plain_user_password": "123456", "locale": "ar", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/CRMProjects/List", "status_code": "<pre class=sf-dump id=sf-dump-2085740313 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2085740313\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1521980250 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>page</span>\" => \"<span class=sf-dump-str>1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1521980250\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-627301524 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>page</span>\" => \"<span class=sf-dump-str>1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-627301524\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1916564802 data-indent-pad=\"  \"><span class=sf-dump-note>array:11</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"45 characters\">http://osool-b2g.test/CRMProjects/List?page=1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"95 characters\">en-US,en;q=0.9,ar;q=0.8,bg;q=0.7,es;q=0.6,fr;q=0.5,he;q=0.4,ms;q=0.3,pt;q=0.2,it;q=0.1,pl;q=0.1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"409 characters\">XSRF-TOKEN=eyJpdiI6ImNBQnJLQktpSTIxMmExWStDWmVzMWc9PSIsInZhbHVlIjoiY2NXSzAwbmpyTWFrRUtvMGRFb0hyUlJHdGtxQjF2OTh1Y05EWkFwa1YvSzEvOEJjQlRtR2RZWEgyZkc5cm51bmNMSkdleWVTd0k5RWYzSGxsYzBYZHBGMWNTZ1Zuek8vSHdDalV3TW1HbXQvZFJReUVJVWc1NnROdmovY01PeFMiLCJtYWMiOiIzMDUzMGE1ZmM2OGRiYjEyNTRkODUzNGQ5NTZhZTU0OWZhNGEwM2JjY2UzYjE1MTMzM2Y3NGZiOGQ0NzlhODRmIiwidGFnIjoiIn0%3D; osool_session=Au4reHHigzkkmVveWlxvlehOkYGVCq0wIt4yceot</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1916564802\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-681557947 data-indent-pad=\"  \"><span class=sf-dump-note>array:41</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>REDIRECT_STATUS</span>\" => \"<span class=sf-dump-str title=\"3 characters\">200</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_CACHE_CONTROL</span>\" => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_DNT</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"45 characters\">http://osool-b2g.test/CRMProjects/List?page=1</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"95 characters\">en-US,en;q=0.9,ar;q=0.8,bg;q=0.7,es;q=0.6,fr;q=0.5,he;q=0.4,ms;q=0.3,pt;q=0.2,it;q=0.1,pl;q=0.1</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"409 characters\">XSRF-TOKEN=eyJpdiI6ImNBQnJLQktpSTIxMmExWStDWmVzMWc9PSIsInZhbHVlIjoiY2NXSzAwbmpyTWFrRUtvMGRFb0hyUlJHdGtxQjF2OTh1Y05EWkFwa1YvSzEvOEJjQlRtR2RZWEgyZkc5cm51bmNMSkdleWVTd0k5RWYzSGxsYzBYZHBGMWNTZ1Zuek8vSHdDalV3TW1HbXQvZFJReUVJVWc1NnROdmovY01PeFMiLCJtYWMiOiIzMDUzMGE1ZmM2OGRiYjEyNTRkODUzNGQ5NTZhZTU0OWZhNGEwM2JjY2UzYjE1MTMzM2Y3NGZiOGQ0NzlhODRmIiwidGFnIjoiIn0%3D; osool_session=Au4reHHigzkkmVveWlxvlehOkYGVCq0wIt4yceot</span>\"\n  \"<span class=sf-dump-key>PATH</span>\" => \"<span class=sf-dump-str title=\"1017 characters\">C:\\Program Files\\Parallels\\Parallels Tools\\Applications;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\Git\\cmd;C:\\laragon\\bin\\php\\php-8.3.16-Win32-vs16-x64;C:\\ProgramData\\ComposerSetup\\bin;C:\\laragon\\bin\\composer;C:\\laragon\\bin\\git\\bin;C:\\laragon\\bin\\git\\cmd;C:\\laragon\\bin\\git\\mingw64\\bin;C:\\laragon\\bin\\git\\usr\\bin;C:\\laragon\\bin\\mysql\\mysql-8.4.3-winx64\\bin;C:\\laragon\\bin\\ngrok;C:\\laragon\\bin\\nodejs\\node-v22;C:\\laragon\\bin\\php\\php-8.3.16-Win32-vs16-x64;C:\\laragon\\bin\\python\\python-3.13;C:\\laragon\\bin\\python\\python-3.13\\Scripts;C:\\laragon\\usr\\bin;C:\\Users\\<USER>\\AppData\\Local\\Yarn\\config\\global\\node_modules\\.bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin</span>\"\n  \"<span class=sf-dump-key>SystemRoot</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\WINDOWS</span>\"\n  \"<span class=sf-dump-key>COMSPEC</span>\" => \"<span class=sf-dump-str title=\"27 characters\">C:\\WINDOWS\\system32\\cmd.exe</span>\"\n  \"<span class=sf-dump-key>PATHEXT</span>\" => \"<span class=sf-dump-str title=\"53 characters\">.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC</span>\"\n  \"<span class=sf-dump-key>WINDIR</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\WINDOWS</span>\"\n  \"<span class=sf-dump-key>SERVER_SIGNATURE</span>\" => \"\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">Apache/2.4.62 (Win64) OpenSSL/3.0.15 PHP/8.3.16</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  \"<span class=sf-dump-key>SERVER_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"2 characters\">80</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"31 characters\">C:/laragon/www/Osool-B2G/public</span>\"\n  \"<span class=sf-dump-key>REQUEST_SCHEME</span>\" => \"<span class=sf-dump-str title=\"4 characters\">http</span>\"\n  \"<span class=sf-dump-key>CONTEXT_PREFIX</span>\" => \"\"\n  \"<span class=sf-dump-key>CONTEXT_DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"31 characters\">C:/laragon/www/Osool-B2G/public</span>\"\n  \"<span class=sf-dump-key>SERVER_ADMIN</span>\" => \"<span class=sf-dump-str title=\"17 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"41 characters\">C:/laragon/www/Osool-B2G/public/index.php</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">50908</span>\"\n  \"<span class=sf-dump-key>REDIRECT_URL</span>\" => \"<span class=sf-dump-str title=\"17 characters\">/CRMProjects/List</span>\"\n  \"<span class=sf-dump-key>REDIRECT_QUERY_STRING</span>\" => \"<span class=sf-dump-str title=\"6 characters\">page=1</span>\"\n  \"<span class=sf-dump-key>GATEWAY_INTERFACE</span>\" => \"<span class=sf-dump-str title=\"7 characters\">CGI/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>QUERY_STRING</span>\" => \"<span class=sf-dump-str title=\"6 characters\">page=1</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"24 characters\">/CRMProjects/List?page=1</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1753965632.2367</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1753965632</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-681557947\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-617891006 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">82Y3S2Ii8XAEUfJqJwoJcxv4amqMyZ5Eyd3DAmvL</span>\"\n  \"<span class=sf-dump-key>osool_session</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-617891006\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2009927330 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 12:40:32 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IkRiSWdOYkd1Ym5rb0l0TlBNdFpQa3c9PSIsInZhbHVlIjoiQTdYNThramUxdk56UE1Iam1DdVJoMnZndVBMSnh1OHZXOHNCc0pKWEtrSFMrR3g2VTFlamdkMm4va1AvdlVWdzViTkFEWGFhNzdQcWdIN3g2aEtJendVemhOak1oblU2dFJmSnMxcVplUFlqaWs5WlZwU2UzZ3cwY0dXMTgyUGoiLCJtYWMiOiI4MmM4ODBlMGQ2ZGIyMTk5MmY1NzA0ZDBhYmFiYjI4NWQzZDVhNmU0M2MxNGU0YjExMGRjM2UzY2JkNTQ3YjY5IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 14:40:34 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"441 characters\">osool_session=eyJpdiI6ImhZNTdZbzVNc0VGeGloTVNQQ1hGS1E9PSIsInZhbHVlIjoiczU1ZzllZkcxeE5xM1ZrS0REc1J1WU1MUW90TElUejl2TlF1Umt6L0p3cHF3eEw2cVpmUUdPd1FkOUU2OHliWkxWVU5tQkZPNzd3NDE0cWhRWEcwWDEvUTJoN0lEM3lORGdaUi9LV2F0SG1SSGd5VDZsUDN4UEFFME5xMjN2NjkiLCJtYWMiOiIwYmZjMzIxYTg0NDYwNzIxNTQ3M2Y2M2JlNDg3ZmRhNDA1ZDk5OGY3ZDgyNzk1MWYzMDVkMmZkYTRmNGU0MWRiIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 14:40:34 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IkRiSWdOYkd1Ym5rb0l0TlBNdFpQa3c9PSIsInZhbHVlIjoiQTdYNThramUxdk56UE1Iam1DdVJoMnZndVBMSnh1OHZXOHNCc0pKWEtrSFMrR3g2VTFlamdkMm4va1AvdlVWdzViTkFEWGFhNzdQcWdIN3g2aEtJendVemhOak1oblU2dFJmSnMxcVplUFlqaWs5WlZwU2UzZ3cwY0dXMTgyUGoiLCJtYWMiOiI4MmM4ODBlMGQ2ZGIyMTk5MmY1NzA0ZDBhYmFiYjI4NWQzZDVhNmU0M2MxNGU0YjExMGRjM2UzY2JkNTQ3YjY5IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 14:40:34 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"413 characters\">osool_session=eyJpdiI6ImhZNTdZbzVNc0VGeGloTVNQQ1hGS1E9PSIsInZhbHVlIjoiczU1ZzllZkcxeE5xM1ZrS0REc1J1WU1MUW90TElUejl2TlF1Umt6L0p3cHF3eEw2cVpmUUdPd1FkOUU2OHliWkxWVU5tQkZPNzd3NDE0cWhRWEcwWDEvUTJoN0lEM3lORGdaUi9LV2F0SG1SSGd5VDZsUDN4UEFFME5xMjN2NjkiLCJtYWMiOiIwYmZjMzIxYTg0NDYwNzIxNTQ3M2Y2M2JlNDg3ZmRhNDA1ZDk5OGY3ZDgyNzk1MWYzMDVkMmZkYTRmNGU0MWRiIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 14:40:34 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2009927330\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1459208707 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">82Y3S2Ii8XAEUfJqJwoJcxv4amqMyZ5Eyd3DAmvL</span>\"\n  \"<span class=sf-dump-key>captcha_answer</span>\" => <span class=sf-dump-num>32</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"45 characters\">http://osool-b2g.test/CRMProjects/List?page=1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>7368</span>\n  \"<span class=sf-dump-key>plain_user_password</span>\" => \"<span class=sf-dump-str title=\"6 characters\">123456</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1459208707\", {\"maxDepth\":0})</script>\n"}}
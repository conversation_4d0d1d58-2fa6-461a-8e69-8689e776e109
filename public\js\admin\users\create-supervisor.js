$("#user_create_supervisor_form").validate({
    ignore: "input[type=hidden]",
    rules: {
        worker_name: {
            required: true,
            minlength: 2,
            maxlength: 50,
            //lettersandspace: true,
        },
        worker_id: {
            required: true,
            minlength: 2,
            maxlength: 10,
            remote: {
                url: $("#ajax_check_employee_id_unique").val(),
                type: "post",
                data: {
                    emp_id: function () {
                        return $("#worker_id").val();
                    },
                    project_id :$("#project_id").val(), 
                    user_type :function () {
                        return $("#user_type").val();
                    },
                    locale: "en",
                    _token: $('meta[name="csrf-token"]').attr("content"),
                },
            },
        },
        profession: {
            required: function () {
                return !$('.profession').is(':hidden');
            }
        },
        phone: {
            // @flip1@ remove from
            // required: true,
            number: true,
            minlength: 9,
            maxlength: 9,

            remote: {
                url: $("#ajax_check_userphone_unique").val(),
                type: "post",
                data: {
                    phone: function () {
                        return $("#phone").val();
                    },
                    locale: "en",
                    _token: $('meta[name="csrf-token"]').attr("content"),
                },
            },
        },
        salary: {
            number: {
                depends: function() {
                    return $("#user_type").val() === "sp_worker";
                }
            },
            min: {
                depends: function() {
                    return $("#user_type").val() === "sp_worker";
                },
                param: 0
            }
        },
        attendance_target: {
            number: {
                depends: function() {
                    return $("#user_type").val() === "sp_worker";
                }
            },
            min: {
                depends: function() {
                    return $("#user_type").val() === "sp_worker";
                },
                param: 0
            },
            max: {
                depends: function() {
                    return $("#user_type").val() === "sp_worker";
                },
                param: 24
            }
        },
    },
    messages: {
        worker_name: {
            required: translations.general_sentence.validation.This_field_is_required,
            minlength: translations.general_sentence.validation.Please_enter_at_least_2_characters,
            maxlength: translations.general_sentence.validation.Please_enter_no_more_than_50_characters,
        },
        worker_id: {
            required: translations.general_sentence.validation.This_field_is_required,
            minlength: translations.general_sentence.validation.Please_enter_at_least_2_characters,
            maxlength: translations.general_sentence.validation.Please_enter_no_more_than_10_characters,
            remote: translations.general_sentence.validation.Worker_ID_alredy_exist_Enter_different_one,
        },
        nationality_id: {
            required: translations.general_sentence.validation.This_field_is_required,
        },
        profession_id: {
            required: translations.general_sentence.validation.This_field_is_required
        },
        salary: {
            required: translations.general_sentence.validation.This_field_is_required,
            number: translations.general_sentence.validation.Please_enter_a_valid_number,
            min: 'Salary should be  equal or greater than 0',
        },
        attendance_target: {
            required: translations.general_sentence.validation.This_field_is_required,
            number: translations.general_sentence.validation.Please_enter_a_valid_number,
            min: translations.general_sentence.validation.Working_hours_per_day_between_0_and_24,
            max: translations.general_sentence.validation.Working_hours_per_day_between_0_and_24,
        },
        // role: {
        //     required: translations.general_sentence.validation.This_field_is_required,
        // },
        // admin_level: {
        //     required: translations.general_sentence.validation.This_field_is_required,
        // },
        phone: {
            required: translations.general_sentence.validation.This_field_is_required,
            number: translations.general_sentence.validation.Please_enter_a_valid_number,
            minlength: translations.general_sentence.validation.Please_enter_at_least_9_numbers,
            maxlength:  translations.general_sentence.validation.Please_enter_no_more_than_9_characters,
            remote:  translations.general_sentence.validation.Phone_no_alredy_exist_Enter_different_number,
        },
    },
    errorPlacement: function (error, element) {
        error.addClass("invalid-feedback");
        if (element.attr("name") == "worker_id") 
        {
            error.appendTo($("#msg_error_workID"));

        } else if (element.attr("id") == "profession_id") {
            error.appendTo($("#profession-id-error"));
        } else if (element.attr("name") == "nationality_id") {
            error.appendTo($("#nationality-id-error"));
        }else if (element.attr("id") == "role") {
            error.appendTo($("#role-level-error"));
        } else if (element.attr("id") == "admin_level") {
            error.appendTo($("#admin-level-error"));
        } else {
            error.insertAfter(element);
        }
    },
    highlight: function (element, errorClass, validClass) {
        $(element).addClass("is-invalid");
    },
    unhighlight: function (element, errorClass, validClass) {
        $(element).removeClass("is-invalid");
        var elem = $(element);
        if (elem.hasClass("select2-offscreen")) {
            $("#s2id_" + elem.attr("id") + " ul").removeClass(errorClass);
        } else {
            elem.removeClass(errorClass);
        }
    },
    submitHandler: function (form) {
        //$.LoadingOverlay("show");
        form.submit();
    },
});


$("#user_type").on("change", function () {
    var user_type = $(this).val();

    var spWorkerSection = $("#sp_worker_section");
    var extraInfoFields = $("#extra_info_fields");
    var showExtraInfoCheckbox = $("#show_extra_info");

    // New logic: show/hide extra info for sp_worker
    if (user_type === 'sp_worker') {
        spWorkerSection.show();
    } else {
        spWorkerSection.hide();
        extraInfoFields.hide();
        showExtraInfoCheckbox.prop('checked', false);
    }
}).trigger("change");

// Checkbox toggle for showing extra info fields
$("#show_extra_info").on("change", function () {
    if ($(this).is(":checked")) {
        $("#extra_info_fields").show();
    } else {
        $("#extra_info_fields").hide();
    }
});

$(document).ready(function () {
    $("#role, #admin_level").select2({
            // placeholder: translations.user_management_module.user_forms.label.choose_a_favorite_language,
            dropdownCssClass: "tag",
            language: {
                noResults: () => translations.general_sentence.validation.No_results_found,
            }
    });
});



// SALARY
$('#salary').on('keyup', function () {
    sessionStorage.setItem('createuser_salary', JSON.stringify($(this).val()));
});
let salaryStored = sessionStorage.getItem('createuser_salary');
if (salaryStored) {
    $('#salary').val(JSON.parse(salaryStored));
}

// ATTENDANCE TARGET
$('#attendance_target').on('keyup', function () {
    sessionStorage.setItem('createuser_attendance_target', JSON.stringify($(this).val()));
});
let attendanceStored = sessionStorage.getItem('createuser_attendance_target');
if (attendanceStored) {
    $('#attendance_target').val(JSON.parse(attendanceStored));
}

// ROLE
$('#role').on('change', function () {
    sessionStorage.setItem('createuser_role', JSON.stringify($(this).val()));
});
let roleStored = sessionStorage.getItem('createuser_role');
if (roleStored) {
    $('#role').val(JSON.parse(roleStored)).change();
}

// ADMIN LEVEL
$('#admin_level').on('change', function () {
    sessionStorage.setItem('createuser_admin_level', JSON.stringify($(this).val()));
});
let adminLevelStored = sessionStorage.getItem('createuser_admin_level');
if (adminLevelStored) {
    $('#admin_level').val(JSON.parse(adminLevelStored)).change();
}

// ATTENDANCE MANDATORY
$('input[name="attendance_mandatory"]').on('change', function () {
    sessionStorage.setItem('createuser_attendance_mandatory', JSON.stringify($(this).val()));
});
let attendanceMandatoryStored = sessionStorage.getItem('createuser_attendance_mandatory');
if (attendanceMandatoryStored !== null) {
    $('input[name="attendance_mandatory"][value="' + JSON.parse(attendanceMandatoryStored) + '"]').prop('checked', true);
}

// OPTIONAL: Also persist the toggle state of "Show extra information"
$('#show_extra_info').on('change', function () {
    sessionStorage.setItem('createuser_show_extra_info', $(this).is(':checked'));
    toggleExtraInfoFields(); // call UI toggle
});

let showExtraInfo = sessionStorage.getItem('createuser_show_extra_info');
if (showExtraInfo === 'true') {
    $('#show_extra_info').prop('checked', true);
    toggleExtraInfoFields(); // trigger UI change on load
}

function toggleExtraInfoFields() {
    if ($('#show_extra_info').is(':checked')) {
        $('#extra_info_fields').show();
    } else {
        $('#extra_info_fields').hide();
    }
}


$("#file_upload").on("change", function (event) {
    var files = document.getElementById("file_upload").files;
    if (files.length) 
    {
        var file_size_error = false;
        var file_type_error = false;
    
        var file_size_in_kb = files[0].size / 2048;
        var file_type = files[0].type;
    
        if (file_size_in_kb > 2048) {
            file_size_error = true;
        }
    
        var supported_types = ["image/jpeg", "image/png", "image/jpg"];
    
        if (!supported_types.includes(file_type)) {
            file_type_error = true;
        }
    
        if (file_size_error == true || file_type_error == true) {
                 document.getElementById('file_upload').value = '';;
    
            var error_message = "";
    
            if (file_size_error == true && file_type_error == true) {
                error_message= translations.general_sentence.validation.Please_upload_only_jpg_jpeg_png_image_of_max_size_1mb;
            } else if (file_size_error == true && file_type_error == false) {
                error_message= translations.general_sentence.validation.File_size_should_not_be_more_than_1_mb;
            } else {
                error_message= translations.general_sentence.validation.Please_upload_only_jpg_jpeg_png_image;
            }
    
            swal({
                title: error_message,
                // text: error_message,
                icon: "warning",
                buttons: true,
                dangerMode: true,
                
            })    } else {
            var reader = new FileReader();
            reader.onload = function () {
                var output = document.getElementById("output_pic");
                output.src = "";
                output.src = reader.result;
                console.log(reader.result);
            };
            reader.readAsDataURL(event.target.files[0]);
            $(".remove-img").removeClass('hide');
        }
    }
    else
    {
        document.getElementById('file_upload').value = '';
        let app_url = $('#app_url').val();
        //$("#file_upload").attr('value','');
        
        $("#output_pic").attr('src',app_url + '/img/upload.png');
    
        $(".remove-img").addClass('hide');
    }
    
});

$('.confirm_remove_photo').click( function () {
    document.getElementById('file_upload').value = '';
    let app_url = $('#app_url').val();
    //$("#file_upload").attr('value','');
    
    $("#output_pic").attr('src',app_url + '/img/upload.png');

    $(".remove-img").addClass('hide');
});

$("#countryOption").on("change", function () {
    let value_cnt = $(this).val();
    $.ajax({
        url: $(this).data("url"),
        method: "GET",
        data: {
            _token: $('meta[name="csrf-token"]').attr("content"),
            id: value_cnt,
        },
        dataType: "json",
        beforeSend: function () {},
        success: function (data) {
            $("#cityOption").empty();
            var language = "{{App::getLocale()}}";
            $.each(data, function (key, value) {

                if(language=='en')
                {
                    $("#cityOption").append(
                    $("<option></option>")
                        .attr("value", value.id)
                        .text(value.name_en)
                    );
                }
                else
                {
                    $("#cityOption").append(
                    $("<option></option>")
                        .attr("value", value.id)
                        .text(value.name_ar)
                    );
                }
            });
        },
        error: function (data) {
            var errors = $.parseJSON(data.responseText);
            toastr.error(data, translations.general_sentence.validation.Error, {
                timeOut: 5000,
                positionClass: "toast-top-center",
                progressBar: true,
            });
        },
    });
});

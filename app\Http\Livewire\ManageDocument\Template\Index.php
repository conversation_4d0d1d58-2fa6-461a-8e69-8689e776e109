<?php

namespace App\Http\Livewire\ManageDocument\Template;

use App\Services\ManageDocument\DocumentService;
use App\Services\ManageDocument\TemplateService;
use Illuminate\Support\Facades\Http;
use Livewire\Component;


class Index extends Component
{
    public $items = [];
    public $total = 0, $search, $sort_by, $sort_dir = 'desc';
    public $page = 1;
    public $perPage = 10;

    protected $listeners = ['newDocumentAdded', 'deleteItem' => 'delete', 'documentUpdated', 'fetchData', 'perPageUpdated_fetchData'];

    protected $queryString = [
        'search' => ['except' => ''],
        'sort_by'   => ['except' => ''],
        'sort_dir'   => ['except' => 'desc'],
        'page'   => ['except' => 1],
    ];

    public function mount()
    {
        $this->fetchData();
    }

    public function perPageUpdated_fetchData($val)
    {
        $this->perPage = $val;
        $this->fetchData();
    }

    public function updatedSearch()
    {
        $this->fetchData();
    }

    public function newDocumentAdded($data)
    {
        array_unshift($this->items, $data);
        $this->total += 1;
        $this->emit('refreshPagination:fetchData', $this->page, 'fetchData', $this->total, $this->perPage);
    }

    public function documentUpdated($data)
    {
        foreach ($this->items as $index => $item) {
            if ($item['id'] == $data['id']) {
                unset($this->items[$index]);
                array_unshift($this->items, $data);
                break;
            }
        }
    }

    public function resetFilters()
    {
        $this->search = null;
        $this->fetchData();
    }

    public function fetchData($page = 1)
    {
        $page = request()->query('page', $page);;
        $service =  app(TemplateService::class);
        $data = $service->list(['page' => $page, 'per_page' => $this->perPage, 'search' => $this->search, 'sort_by' => $this->sort_by, 'sort_dir' => $this->sort_dir]);

        if (@$data['status'] == "success") {
            $this->items = $data['data']['items'];
            $this->total = $data['data']['total'];
            $this->page = $data['data']['current_page'];
            $this->emit('refreshPagination:fetchData', $page, 'fetchData', $this->total, $this->perPage);
        }
    }

    public function sort($sort_by)
    {
        $this->sort_by = $sort_by;
        $this->sort_dir = $this->sort_dir == 'desc' ? 'asc' : 'desc';
        $this->fetchData();
    }

    public function download($type)
    {
        $workdoBaseUrl = env('CRM_API_BASE_URL');
        $workspaceSlug = auth()->user()->workspace;
        $apiUrl = "$workdoBaseUrl/api/{$workspaceSlug}/manage-documents/document-templates-export?type=$type";

        $response = Http::withToken(auth()->user()->crm_api_token) // if authentication is needed
            ->get($apiUrl);

        if ($response->successful()) {
            $filename = 'document_templates_' . now()->format('Ymd_His') . '.' . ($type === 'excel' ? 'xlsx' : 'csv');

            return response()->streamDownload(function () use ($response) {
                echo $response->body();
            }, $filename, [
                'Content-Type' => $type === 'excel'
                    ? 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                    : 'text/csv',
            ]);
        }
    }

    public function printPage()
    {
        if (!$this->items) {
            $this->dispatchBrowserEvent('show-toastr', [
                'type' => 'error',
                'message' =>  __('No Data Available')
            ]);
            return;
        }
        $htmlContent = view('livewire.manage-document.template.print', [
            'items' => $this->items
        ])->render();

        $this->emit('openPrintWindow', $htmlContent);
    }

    public function delete($id, $delete)
    {
        if ($id && $delete) {
            $service =  app(TemplateService::class);
            $response = $service->delete($id);

            if ($response['status'] === 'success') {
                foreach ($this->items as $index => $item) {
                    if ($item['id'] == $id) {
                        unset($this->items[$index]);
                        $this->total -= 1;
                        $this->emit('refreshPagination:fetchData', $this->page, 'fetchData', $this->total, $this->perPage);
                        break;
                    }
                }
                $this->dispatchBrowserEvent('close-confirm-modal');

                $this->dispatchBrowserEvent('show-toastr', [
                    'type' => 'success',
                    'message' => __("document_module.doc_temp_deleted_success")
                ]);
            } else {
                $this->dispatchBrowserEvent('show-toastr', [
                    'type' => 'error',
                    'message' => $response['errors']
                ]);
            }
        }
    }

    public function render()
    {
        return view('livewire.manage-document.template.index');
    }
}

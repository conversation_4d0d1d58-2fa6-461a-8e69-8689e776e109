<div>
   <div class="contents crm">
      <div class="container-fluid">
         <div class="col-lg-12">
            <div class="row justify-content-sm-between align-items-center justify-content-center my-3 flex-sm-row flex-column">
                <div class="page-title-wrap p-0">
                <div class="page-title d-flex justify-content-between">
                <div class="page-title__left justify-content-sm-between align-items-center justify-content-center">
                <div class="user-member__title mr-sm-25 ml-0">
                <h4 class="text-capitalize fw-500 breadcrumb-title fs-16">
                @lang('rfx.page_title')
                </h4>
                </div>
                </div>
                </div>
                <div>
                <ul class="atbd-breadcrumb nav">
                <li class="atbd-breadcrumb__item">
                <a>@lang('rfx.breadcrumb.dashboard')</a>
                <span class="breadcrumb__seperator">
                <span class="la la-angle-right"></span>
                </span>
                </li>
                <li class="atbd-breadcrumb__item">
                <a>@lang('rfx.breadcrumb.manage')</a>
                </li>
                </ul>
                </div>
                </div>
               <div class="d-flex gap-10 breadcrumb_right_icons">
                  <div class="d-flex gap-10 breadcrumb_right_icons">
                    <button class="btn btn-white btn-default text-center svg-20 wh-45"
                    wire:click="toggleView"
                    title="{{ $viewMode === 'list' ? __('rfx.view_modes.cards') : __('rfx.view_modes.list') }}">
                    @if($viewMode === 'list')
                    <i class="iconsax icon text-new-primary fs-22 colorRed mr-0" icon-name="layout-3"></i>
                    @else
                    <i class="iconsax icon text-new-primary fs-22 colorRed mr-0" icon-name="task-list"></i>
                    @endif
                    </button>

                     <button class="btn btn-default btn-primary w-100 no-wrap" wire:click="goToCreatePage" type="button"><i class="las la-plus fs-16"></i> {{ __('rfx.create') }}</button>

                  </div>
               </div>
               <!--====End Design for Export PDF===-->
            </div>
         </div>
         <div class="">
            <div class="row">
               <div class="col-lg-4 col-md-6">
                    <div class="card">
                        <div class="card-body">
                            <div class="row align-items-center justify-content-between">
                                <div class="col-auto mb-3 mb-sm-0">
                                    <div class="d-flex align-items-center gap-10">
                                        <div class="radius-xl theme-avtar bg-opacity-primary wh-45 d-center">
                                            <i class="iconsax icon fs-22 mr-0 text-primary" icon-name="cast"></i>
                                        </div>
                                        <div class="ms-3">
                                            <small class="text-muted">@lang('rfx.summary.total')</small>
                                            <h6 class="m-0">@lang('rfx.summary.rfxs')</h6>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-auto text-end">
                                    <h4 class="m-0">{{$totalRfx}}</h4>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6 mt-md-0 mt-3">
                    <div class="card">
                        <div class="card-body">
                            <div class="row align-items-center justify-content-between">
                                <div class="col-auto mb-3 mb-sm-0">
                                    <div class="d-flex align-items-center gap-10">
                                        <div class="radius-xl theme-avtar bg-opacity-success wh-45 d-center">
                                            <i class="iconsax icon fs-22 mr-0 text-success" icon-name="cast"></i>
                                        </div>
                                        <div class="ms-3">
                                            <small class="text-muted">@lang('rfx.summary.active')</small>
                                            <h6 class="m-0">@lang('rfx.summary.rfxs')</h6>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-auto text-end">
                                    <h4 class="m-0">{{$activeRfx}}</h4>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6 mt-md-0 mt-3">
                    <div class="card">
                        <div class="card-body">
                            <div class="row align-items-center justify-content-between">
                                <div class="col-auto mb-3 mb-sm-0">
                                    <div class="d-flex align-items-center gap-10">
                                        <div class="radius-xl theme-avtar bg-opacity-danger wh-45 d-center">
                                            <i class="iconsax icon fs-22 mr-0 text-danger" icon-name="cast"></i>
                                        </div>
                                        <div class="ms-3">
                                            <small class="text-muted">@lang('rfx.summary.inactive')</small>
                                            <h6 class="m-0">@lang('rfx.summary.rfxs')</h6>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-auto text-end">
                                    <h4 class="m-0">{{$inactiveRfx}}</h4>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
           <div class="card mt-3" data-select2-id="108">
                <div class="card-body" data-select2-id="107">
                    <div class="d-flex flex-wrap gap-10">
                        <div class="flex-fill max-w-150">
                            <label class="text-osool">@lang('rfx.filter.location')</label>
                            <select class="form-control select2-new" wire:model="location" id="location">
                                <option value="">@lang('rfx.filter.select_location')</option>
                                @foreach($locations as $key=>$this_data)
                                    <option value="{{ $key }}">{{ $this_data }}</option>
                                @endforeach
                            </select>
                        </div>

                        <div class="flex-fill max-w-150">
                            <label class="text-osool">@lang('rfx.filter.status')</label>
                            <select class="form-control select2-new" wire:model="status" id="status">
                                <option value="">@lang('rfx.filter.select_status')</option>
                                @foreach($statuses as $key=>$this_data)
                                    <option value="{{ $key }}">{{ $this_data }}</option>
                                @endforeach
                            </select>
                        </div>

                        <div class="flex-fill max-w-180">
                            <label class="text-osool">@lang('rfx.filter.start_date')</label>
                            <div class="position-relative">
                                <input type="text" class="form-control datepicker" id="start_date" wire:model="start_date" placeholder="@lang('rfx.filter.enter_start_date')" onchange="setdate('start_date','start_date')" />
                                <i class="iconsax field-icon" icon-name="calendar-search"></i>
                            </div>
                        </div>

                        <div class="flex-fill max-w-180">
                            <label class="text-osool">@lang('rfx.filter.end_date')</label>
                            <div class="position-relative">
                                <input type="text" class="form-control datepicker" id="end_date" wire:model="end_date" placeholder="@lang('rfx.filter.enter_end_date')" onchange="setdate('end_date','end_date')" />
                                <i class="iconsax field-icon" icon-name="calendar-search"></i>
                            </div>
                        </div>

                        <div class="flex-fill">
                            <label class="d-md-block d-none">&nbsp;</label>
                            <div class="d-flex gap-10">
                                <button type="button" class="btn bg-opacity-new-primary btn-sm text-new-primary radius-md px-5" wire:click="apply">
                                    @lang('rfx.filter.apply')
                                </button>
                                <button type="button" class="btn bg-opacity-loss btn-sm text-loss wh-45 radius-md" wire:click="resetFilters">
                                    <i class="iconsax mr-0 fs-18" icon-name="rotate-left" title="@lang('rfx.filter.reset')"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>


            <div class="card mt-3">
               <div class="">
                  <div class="card-header py-4 px-3 border-0 d-flex justify-content-between align-items-center">
                        <h6 class="text-capitalize text-osool fw-500 mb-3 mb-sm-0">
                            @lang('rfx.header.title')
                        </h6>

                        <div class="d-flex gap-10 table-search flex-wrap">
                            <div class="position-relative">
                                <input type="text" class="form-control" placeholder="{{ __('rfx.header.search_placeholder') }}" wire:model.debounce.500ms="search">
                                <i class="iconsax field-icon fs-18 mr-0" icon-name="search-normal-2"></i>
                            </div>

                            <button class="btn btn-export text-dark"  wire:click="export"><i class="iconsax icon fs-22 mr-0" icon-name="upload-1"></i> {{ __('rfx.export') }}</button>


                            <button class="btn bg-new-primary wh-45" wire:click="resetFilters">
                                <i class="iconsax icon fs-18 mr-0" icon-name="download-1" title="@lang('rfx.header.reset')"></i>
                            </button>
                        </div>
                    </div>
               </div>

@include('livewire.rfx.include.'.$viewMode.'-view')      
     
@include('livewire.rfx.include.pagination')    

@livewire('common.delete-confirm')
               
            </div>
         </div>
      </div>
   </div>
</div>
@push('scripts')
<script type="text/javascript">
    $(".datepicker").datepicker();
    $("#bank_type, #account_type, #wallet_type").select2();
    $(document).ready(function() {
      // Toggle active class and manage dropdown on button click
      $('.filter-button').on('click', function (e) {
        e.stopPropagation(); // Prevent the click from bubbling up to the document
        $(this).toggleClass('active'); // Toggle the 'active' class
        $(this).closest('.dropdown').find(".dropdown-menu").toggleClass('show'); // Toggle the Bootstrap dropdown
      });

      // Remove active class and close dropdown when clicking outside
      $(document).on('click', function (e) {
        if (!$(e.target).closest('.dropdown').length) {
          // If the click is outside the dropdown, remove the 'active' class and close the dropdown
          $('.filter-button').removeClass('active');
          $('.dropdown-menu').removeClass('show');
          $('.filter-button').attr('aria-expanded', 'false');
        }
      });

      // Prevent dropdown from closing when clicking inside the dropdown menu
      $('.dropdown-menu').on('click', function (e) {
        e.stopPropagation();
      });
    });




    document.addEventListener('livewire:load', function () {

        $('#start_date').datepicker({
            format: 'yyyy-mm-dd',
            autoclose: true
        });

        $('#end_date').datepicker({
            format: 'yyyy-mm-dd',
            autoclose: true
        });

        $('#location').select2().on('change', function () {
            @this.set('location', $(this).val());
        });

        $('#status').select2().on('change', function () {
            @this.set('status', $(this).val());
        });

        // Reinitialize select2 after Livewire DOM update
        Livewire.hook('message.processed', () => {
            $('#location, #status').select2();
        });
    });



function setdate(key,id) {
const selectedDate = document.getElementById(id).value;
Livewire.find('{{ $this->id }}').set(key, selectedDate);
//console.log('>>>>>>>sdfgh>>>>>>>>>>',key, selectedDate);
}
</script>


<script>
    window.addEventListener('reload-page', () => {
        location.reload();
    });

    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('btn-export')) {
            showLoader();
        }
    });

    function showLoader() {
        document.getElementById("overlayer").style.display = "block";
        let loaderElements = document.getElementsByClassName("loader-overlay");
        if (loaderElements.length > 0) {
            loaderElements[0].style.display = "block";
        }
    }

    function hideLoader() {
        document.getElementById("overlayer").style.display = "none";

        let loaderElements = document.getElementsByClassName("loader-overlay");
        if (loaderElements.length > 0) {
            loaderElements[0].style.display = "none";
        }
    }

    window.addEventListener('show-loader', event => {
        showLoader();
    });


    window.addEventListener('hide-loader', event => {
        setTimeout(() => {
            hideLoader();
        }, 1000);
    });
</script>  
@endpush
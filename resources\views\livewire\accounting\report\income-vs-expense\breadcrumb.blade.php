<div class="col-lg-12">
    <div class="row justify-content-sm-between align-items-center justify-content-center my-3 flex-sm-row flex-column">
        @include('applications.admin.common.breadcrumb', [
            'links' => [
                [
                    'title' => __('accounting.report'),
                ],
                [
                    'title' => __('accounting.income_vs_expense_sum'),
                ],
            ],
        ])

        <div class="d-flex gap-10 breadcrumb_right_icons">
            <div class="dropdown">

                <button class="btn btn-default btn-primary no-wrap wh-45 px-0 dropdown-toggle" type="button"
                    id="dropdownMenuButton" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false"><i
                        class="iconsax mr-0 fs-18" icon-name="download-1"></i></button>
                <div class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                    <a onclick="sendChartAndDownload()" class="dropdown-item" href="javascript:void(0)"><i
                            class="las la-file-pdf"></i> @lang('accounting.pdf')</a>
                    <a onclick="showLoader()" wire:click='downloadCsv' class="dropdown-item" href="javascript:void(0)"><i class="las la-file-excel"></i>
                        @lang('accounting.csv')</a>
                </div>
            </div>

        </div>
        <!--====End Design for Export PDF===-->
    </div>
</div>
<script>
    function getChartImageBase64() {
        const canvas = document.getElementById('monthlyChart');
        if (canvas) {
            return canvas.toDataURL('image/png');
        }
        return null;
    }

    function sendChartAndDownload() {
        showLoader(); // your existing loader function

        const base64Image = getChartImageBase64();
        if (base64Image) {
            @this.set('chartImage', base64Image); // set Livewire property
        }
        @this.call('downloadPdf'); // call the Livewire method
    }
</script>

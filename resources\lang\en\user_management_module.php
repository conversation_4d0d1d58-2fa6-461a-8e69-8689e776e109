<?php


     /*
    |--------------------------------------------------------------------------
    | User management module
    |--------------------------------------------------------------------------
    |
    | language translate for create, edit , list and delte
    | english verion file
    | update@sobhon
    |
     */

return [

    'email'=> [
        'verify_your_email' => 'Verify Your Email',
        'verify'=>'Verify',
        'desc'=>'Hello :username, By verifying you email you’ll be benefiting from Osool’s services (Printing reports, Receiving emails related to work orders and KPIs)',
        'thank_you_for_verify_your_email' => 'Thank You for verifying your email',
        'you_have_been_successfully_registered_to_osool'=>'You have been successfully registered to Osool',
        'go_to_login'=>'Go to login page',
        'resend_email_verification_subject' => 'Resend Email Verification',
        'success_message_resend_varification_mail' => 'Email has been sent to the user successfully',
        'email_attempts_times' => 'Resend Attempts reached limit, try again in',
        'Email_not_verfied_yet'=>'Email not verfied yet',
        'email_sent'=>'Email Sent',
        'verify_successful' => 'Verify Successful',
        'verify_successful_desc' => 'Hello :username,Thank you verifying your email on Osool. Your proactive step in confirming your email not only helps us ensure the security of your account but also facilitates smoother engagement within our platform.',
        'login'=>'Login',
        'verify_your_email_successful'=>'Email verify successful',
        'email_already_verified'=>'Email already verified',
        'login_to_osool'=>'You can now login into Osool',
    ],

    'common'=> [
        'search_by_property_name'=> 'Search by property name',
        'search_by_property_name1'=> 'Search by name or number',
        'create_new_project'=>'Create New Project',
        'project_information'=>'Project Information',
        'assigned_admins'=>'Assigned Admins',
        'select_admins'=>'Select Admins',
        'please_add_project_information'=>'Please add project Information',
        'enter_name_english'=>'Enter Name [English]',
        'enter_name_arabic'=>'Enter Name [Arabic]',
        'client_industry'=>'Client Industry',
        'start_date' => 'Start date of contract',
        'end_date' => 'End date of contract',
        'use_erp_module' => 'Use Osool Plus (Plus Features/Modules)',
        'use_tenant_app_and_module'=>'Use tenant app and Module',
        'use_community_module_in_tenant_app'=>'Use community module in tenant app',
        'allow_tenants_to_view_contracts'=>'Allow tenants to view contracts',
        'allow_tenants_to_share_posts_in_community'=>'Allow tenants to share posts in community',
        'show'=>'Show',
        'hide'=>'Hide',
        'initiate'=>"Initiate purchase requests",
        'send'=>"Send purchase requests to vendors",
        'receive'=>"Reject quotations",
        'app_reject'=>"Require approval to issue quotations",
        'approval'=> "Require approval for amounts above than:",
        'createpo'=>"Request BAFO",
        'manage_payment_process'=>"Manage payment processing based on invoices",
        'auto_purchase'=>"Enable Auto-Purchase for Items",
        'allow'=>'Allow',
        'prevent'=>'Prevent',
        'use_benificiary_module'=>'Use Beneficiary Module',
        'save_and_submit'=>'Save & Submit',
        'edit_project_information'=>'Edit Project Information',
        'delete_project'=>'Delete Project',
        'update'=>'Update',
        'project'=>'Project',
        'has_been_created'=>'has been Created',
        'project_owner' =>'Project Owner',
        'user_info'=>'User Information',
        'user_role'=>'User Role',
        'user_previleges'=>'User Privileges',
        'user_sub_previleges'=>'User Sub-Privileges',
        'confirm'=>'Confirmation',
        'users_list'=>'Users List',
        'search_by_name'=>'Search by Name',
        'password'=>'Password',
        'new_password'=>'New Password',
        'confirm_password'=>'Confirm Password',
        'filter_user'=>'Filter User',
        'pick_user_details'=>'Pick User Details',
        'Reset_Password'=>'Reset Password',
        'Dont_have_an_account'=>"Don't have an account?",
        'Sign_up'=>'Sign up',
        'Admin'=>'Admin',
        'Osool_B2G_Administrator'=>'Osool B2G Administrator',
        'projects_list' => 'Projects List',
        'worker_id_description'=>'ID is used to log in to the mobile app',
        'Can_be_Edit_Delete_from_Service_Provider_Admin'=>'Can be Edit/Delete from Service Provider Admin',
        'No_Projects_created_yet' => 'No Projects created yet',
        'The_Projects_list_will_appear_here' => 'The Projects list will appear here',
        'Create_New_Project' => 'Create New Project',
        'verified' => 'Verified?',
        'yes' => 'Yes',
        'no' => 'No',
        'manage_all' => 'Manage All',
        'all_properties' => 'All Properties',
        'selected_properties' => 'Selected Properties',
        'clear_selection' => 'Clear Selection',
        'select_all' => 'Select All',
        'workers_list'=>'Workers List',
        'workers'=>'Workers',
        'user_sub_privileges'=>'User Sub-Privileges',
        'wo_approving'=>'Work Order Approving',
        'privileges_type'=>'Privileges Type',
        'user_role_list' => 'Role List',
        'user_role_listing' => 'Roles Listing',
        'add_new_user_role' => 'Add New User Role',
        'user_permission_list' => 'Permission List',
        'add_new_user_permission' => 'Add New User Permission',
        'update_user_permission' => 'Update User Permission',
        'roles' => 'Roles',
        'permissions' => 'Permissions',
        'create_role' => 'Create Role',
        'search_placeholder' => 'Search',
        'role_id' => 'ID',
        'role_name' => 'Role',
        'role_display_name' => 'Display Name',
        'role_display_name_ar' => 'Display Name Arabic',
        'role_description' => 'Description',
        'actions' => 'Actions',
        'role_new' => 'New Role',
        'role_edit' => 'Edit Role',
        'close_btn'=>'Close',
        'manage_role_permissions' => 'Manage Role Permissions',
        'manage_user_permissions' => 'Manage User Permissions',
        'manage' => 'Manage',
        'manage_permission_per_role' => 'Manage User Permissions of',
        'user_permissions_listing' => 'Permissions Listing',
        'label_permission_name' => 'Permission name',
        'label_permission_id' => 'ID',
        'label_name' => 'Name',
        'label_close' => 'Close',
        'label_update_permission' => 'Update Permission',
        'label_update_role' => 'Update Role',
        'label_add_permission' => 'Create Permission',
        'select_all_permissions' => 'Select All',
        'enter_pass_leave_blank'=>'Enter new password (leave blank to keep current)',


    ],

    'user_button'=>[
        'add_new_user'=>'Add New User',
        'send_sms_for_all_tenants'=>'Send SMS For All Tenants',
        'send_sms_for_tenants'=>'Send SMS For Tenants',
        'edit_user'=>'Edit User',
        'delete_user'=>'Delete User',
        'previous'=>'Previous',
        'next'=>'Next',
        'back'=>'Back',
        'cancel'=>'Cancel',
        'save_next'=>'Save & Next',
        'go_to_user'=>'Go To Users List',
        'filter_user_type'=>'Filter by User Type',
        'skip_this_step'=>'Skip This Step',
        'edit_project' => 'Edit Project',
        'enter_project' => 'Enter Project',
        'go_to_projects_list'=>'Go to Projects List',

        'tenant_add_new_shared_documents'=>'Add new shared document',
        'tenant_go_to_shared_documents_list'=>'Go to shared documents list',

        'update_button' => 'Update',
        'resend_email_varification' => 'Resend Email Verification',
    ],

    'delete_user'=>[
        'confirm'=>'Confirm',
        'cancel'=>'Cancel',
        'are_you_sure'=>'Are you sure?',
        'confirm_msg'=>'Once deleted, you will not be able to recover this',
        'delete_admin'=>'Delete Service Provider Admin',
    ],

    'bread_crumbs'=>[
        'user_management'=>'User Management',
        'add_user'=>'Add User',
        'edit_user'=>'Edit User',
        'dashboard'=>'Dashboard',
        'user'=>'User',
        'User'=>'User',
        'project' => 'Project',
        'user-list-admin' => 'Admin Management',
        'projects'=>'Projects',
        'Go_to_Admins'=>'Go To Admins',

    ],


    'user_previleges'=> [
        'use_qr'=> 'Use QR code to start/end work',

        'create'=> 'Create',
        'edit'=>'Edit',
        'view'=>'View',
        'no_view'=>'No View',
        'property'=>'Property',
        'contracts'=>'Contracts',
        'beneficiary'=>'Beneficiary',
        'service_provider'=>'Service Provider',
        'user_create_msg'=>'User has been created',
        'user_update_msg'=>'User has been updated',
        'user_success'=>'Email has been sent to the new user with the login information',
        'work_order'=>'Work Order',
        'report_maintenance'=>'Report Maintenance',
        'asset'=>'Assets',
        'has_been_Updated'=>'has been updated',
        'No_users_found'=>'No users found',
        'No_projects_found' => 'No Projects Found',
        'project_updated' => 'has been updated',
        'worker_mail_send_message' => 'Now worker can login to Worker App using the following ID and Password',
        'team_leader_mail_send_message' => 'Now team leader can login to Worker App using the following ID and Password',
        'team_leader_mail_send_message_without_pass' => 'Now team leader can login to Worker App using the following ID, with pre-assigned password',
        'worker_mail_send_message_without_pass' => 'Now worker can login to Worker App using the following ID, with pre-assigned password',
        'worker_id' => 'Worker ID',
        'password' => 'Password',
        'purchases' => 'Purchases',
        'inventory' => 'Inventory',
        'access_inventory' => 'Access to Inventory',
        'warehouses' => 'Warehouses',
        'vendors' => 'Vendors',
        'workorders' => 'Work orders',

        'public_service_providers' => 'Public Service Providers',
        'edit_project' => 'Edit Project',
        'client_requests' => 'Client Requests',
        'show' => 'Show',
        'hide' => 'Hide',
        'assign_bma_area_manager' => 'Assign as Area Building Manager',
    ],



    'create_user'=> [

        'new_user'=>'Add new'
    ],

    'edit_user'=> [
          'phone_number_msg'=>'Phone number can’t be updated in this page to update phone number',
          'click_here'=>'Click here',
    ],

    /**-------------------------forms fields--------------- */

    'user_forms' => [

        'label' => [
            'booking_management' => 'Booking Management',
            'user_activate_success'=> 'User has been activated',
            'user_deactivate_success'=> 'User has been deactivated',
            'no_property_found' => 'No properties found.',
            'no_ducument_found' => 'No Documents Uploaded',
            'english'=>'English',
            'arabic'=>'Arabic',
            'urdu'=>'Urdu',
            'deactivated'=>'Deactivated',
            'No_status_requests_from_worker'=>"No status requests from worker",
            'choose_a_favorite_language'=>'Choose A Favorite Language',
            'registration_status' => 'Registration Status',
            'registered' => 'Registered',
            'not_registered' => 'Not Registered',
            'ongoing' => 'Ongoing',
            'expired' => 'Expired',
            'pending'=> 'Pending',
            'registration_status_help_text' => 'Once the user signs up in the tenant app, he/she will be marked as registered',

            'project_name'=>'Project Name',

            'admin_name'=>'Admin Name',
            'admin_email'=>'Admin Email',
            'tenant_email'=>'Email Address',
            'projects'=>'Projects',

            'photo'=>'User Photo',
            'emp_name'=>'Employee Name',
            'name'=>'Employee Name',
            'emp_id'=>'Employee ID',
            'emp_email'=>'Employee Email',
            'emp_dept' =>'Employee Department',
            'emp_phone' => 'Phone Number',
            'mobile_no' => 'Phone Number',
            'role' => 'Role',
            'password' => 'Password',
            'email'=>'Employee Email',
            'dept' =>'Employee Department',
            'phone' => 'Phone Number',
            'emp_country'=>'Country',
            'emp_country_lives_in'=>'Office Location (Country)',
            'emp_city'=>'City',
            'emp_work_city'=>'Place of Contract (City)',
            'emp_work_city_bm'=>'Place of Property (City)',
            'emp_city_lives_in'=>'Office Location (City)',
            'emp_region'=>'Place of Contract (Region)',
            'emp_region_bm'=>'Place of Property (Region)',
            // 'emp_city'=>'Choose City',
            'emp_asset'=>'Asset Category',
            'emp_status'=>'User Status',
            'active'=>'Active',
            'inactive'=>'Inactive',
            'inactive_keep_tab'=>'Inactive (Keep tab)',
            'inactive_hide_tab'=>'Inactive (Hide tab)',
            'eid'=>'EID',
            'city'=>'Place of Contract (City)',
            'access_to'=>'Access to',
            'user_type'=>'User Type',
            'company_name'=>'Company Name',
            /*'worker_admin'=>'Worker Admin',*/
            'worker_admin'=>'Worker’s Supervisor',
            'employee_admin'=>'Employee Admin',
            'worker_name'=>'Worker Name',
            'worker_id'=>'Worker ID',
            'profession'=>'Profession',
            'select_profession_heading'=>'Profession',
            'activate_crm'=> 'Activate CRM',
            'choose_a_profession' => 'Choose a Profession',
            'nationality'=>'Nationality',
            'choose_a_nationality'=>'Choose a Nationality',
            'contract'=>'Contract',
            'building'=>'Building',
            'choose_supervisor'=>'Choose Supervisor',
            'choose_admin'=>'Choose Admin',

            'sp_supervisor_name'=>'Supervisor Name',
            'sp_supervisor_admin'=>'Supervisor\'s Admin',
            'sp_supervisor_id'=>'Supervisor ID',
            'sp_supervisor_email'=>'Supervisor Email',

            'tenants_first_name'=>'First Name',
            'tenants_last_name'=>'Last Name',
            'tenants_appartment'=>'Apartment',
            'tenants_appartment_and_villa'=>'Apartment/Villa',
            'unit_receival_date' => 'Unit receival date',
            'no_description_added'=>'No description added',
            'notAdded'=>'User didn’t provide',
            'tenants_phone_number'=>'Phone Number',
            'tenants_status'=>'Status',
            'warranty_status'=>'Warranty Status',
            'tenants_residenti_nformation'=>'Tenant Information',
            'tenants_building'=>'Building',
            'tenants_building_beside_number'=>'Buildings',
            'tenant_confirmation_building_name'=>'Building',
            'tenants_private_documents'=>'Private Documents',
            'tenants_contact_information'=>'Contact Information',
            'tenants_list'=>'Tenants List',
            'tenants_shared_files'=>'Shared Files',
            'add_new_tenant'=>'Add New Tenant',
            'tenants'=>'Tenants',
            'add_tenant'=>'Add Tenant',
            'sms_management'=>'SMS Management',
            'language_sms_received'=>'Language For SMS Received',
            'summery'=>'Summary',
            'tenant_upload_documents'=>'Upload Documents',
            'sms_has_been_sent'=>'SMS has been sent to the new tenant to download the APP',
            'has_been_added_successfully'=>'has been added successfully!',
            'go_to_tenant_list'=>'Go To Tenant List',
            'edit_tenant'=>'Edit Tenant',
            'add_new_document'=>'Add New Document',
            'documents'=>'Documents',
            'upload_shared_document'=>'Upload shared document',
            'go_to_shared_file'=>'Go To Shared File',
            'shared_file_uploaded_successfully'=>'Shared Document has been updated successfully',
            'document_uploaded_successfully'=>'Document has been updated successfully',
            'tenant_menu_label'=>'Tenant Management',
            'tenant_list_title'=>'Tenants List',
            'rename'=>'Rename',
            'delete_file'=>'Delete File',
            'edit_shared_document'=>'Edit shared document',
            'drag_and_drop'=>'Drag and Drop',
            'no_documents_yet'=>'No Documents yet',
            'no_private_documents_yet' => 'No private documents uploaded yet!',
            'no_private_documents_yet_desc' => 'You can upload the files here and they will be shared with the tenant only',
            'has_been_updated_successfully'=>'has been updated successfully!',
            'upload_files_shared_with_tenants'=>'You can upload files here which will be shared with all tenants',
            'no_files_yet'=>'No Files Yet',
            'tenant_edit_resend_sms'=>'Resend SMS',

            'shared_document_added_successfully'=>'Shared documents have been added successfully!',
            'it_will_appear_all_tenant_in_the_building'=>'It will appear to all tenants in the building',

            'supervisor_name'=>'Supervisor Name',
            'supervisor_id'=>'Supervisor ID',
            'supervisor_email'=>'Supervisor Email',
            'supervisor_department'=>'Supervisor Department',

            'tenants_property_list'=>'Property List',
            'all'=>'All',
            'Tenants'=>'Tenants',
            'Properties'=>'Properties',
            'Complex'=>'Complex',

            'tenants_no_property_yet'=>'No properties yet',
            'tenants_property_list_appear_here'=>'The properties list will appear here',

            'no_tenant_yet'=>'No tenants yet!',
            'you_can_add_tenant_from_here'=>'You can add tenant from here',

            'reports'=>'Reports',
            'generate_report'=>'Generate Report',
            'report_long_description'=>'You can generate your desired report as PDF or Excel here based on your selected variables',
            'serviceprovider'=>'Service provider',
            'workorder_type'=>'Work Order Type',
            'reports_format'=>'Reports Format',
            'generate'=>'Generate',
            'select_date'=>'Select date',

            'create_user_as_sp_admin'=>'Service Provider’s Admin Name',
            'tenant_module_is_inactive'=>'Tenant Module is Inactive',
            'tenant_module_is_inactive_message'=>'All functionalities of tenant module are disabled for your project. Please contact Osool support for help',
            'favorite_language'=>'Favorite Language',
            'presence'=>'Presence',
            'online'=>'Online',
            'injured'=>'Injured',
            'not_available'=>'Not Available',
            'offline'=>'Offline',
            'vocation'=>'Vacation',
            'worker_request'=>'Worker Request',
            'change_status_to'=>'Change status to',
            'description'=>'Description',
            'date_time_from'=>'Date & Time (From)',
            'date_time_to'=>'Date & Time (To)',
            'reassign_workers_work_to'=>"Reassign worker's work order to",
            'status_reason'=>'Status Reason',
            'enter_reason'=>'Enter Reason',
            'choose_status'=>'Choose status',
            'note'=>'Note:',
            'have'=>'have',
            'you_will_need_to_choose_another_worker'=>"you'll need to choose another worker to transfer this work orders or keep assigned but it may affect performance.",
            'reassign_manually_later'=>'Reassign manually later',
            'the_worker_status_changed_to'=>'The worker status is changed to',
            'is_offline_until'=>'is offline until ',
            'do_you_want_to_change'=>'do you want to change to active?',
            'reject'=>'Reject',
            'rejected'=>'Rejected',
            'reject_worker_request'=>'Reject Worker Request',
            'reject_reason'=>'Reject Reason',
            'approve'=>'Approve',
            'approved'=>'Approved',
            'the_worker_request_was'=>'The worker request was',
            'the_leave_request_has_been'=>'The Leave Request has been',
            'confirm_worker_change_leave_status'=>'Worker is on leave. Do you want to change leave status?',
            'worker_is_on_vacation_change_status'=>'The worker is on Vacation. Do you want to change to available status?',
            'worker_is_injured_change_status'=>'The worker is on Work Injury. Do you want to change to available status?',
            'worker_is_not_availalble_change_status'=>'The worker is Not available. Do you want to change to available status?',
            'worker_status_was_changed_to'=>'Worker status changed to',
            'attachments'=>'Attachments',
            'close'=>'Close',
            'submit'=>'Submit',
            'worker_status'=>'Worker Status',
            'ok'=>'OK',
            'store_keeper_name'=>'Store Keeper Name',
            'store_keeper_id'=>'Store Keeper ID',
            'store_keeper_email'=>'Store Keeper Email',
            'store_keeper_department'=>'Store Keeper Department',
            "store_keeper_service_provider"=>"Service Provider",
            'store_keeper_service_provider_help'=>'If this user account is not the service providers employee, ignore this field',
            'store_keeper_service_provider_deselect'=>'Deselect service provider',
            "warehouse" => "Warehouse",
            'leave_requests'=>'Leave Requests',
            'leave_mode'=>'Leave Mode',
            'user_accounts'=>'User Accounts',
            'worker_list'=>'Worker List',
            'is_active'=>'is Active',
            'deactive'=>'Deactivate',
            'are_you_sure_deactivate'=>'Are you sure you want to deactivate',
            'caution'=>'Caution',
            'deactivating_the_leave_mode'=>'Deactivating the leave mode will enable workers to modify Work Orders, create new maintenance requests, or submit new leave requests. Please note that once you deactivate it, it cannot be reactivated again.',
            'leave_request_list'=>'Leave Request List',
            'request'=>'Request',
            'search_by_name_or_leave_id'=>'Search by name, Leave request ID',
            'status'=>'Status',
            'worker'=>'Worker',
            'leave_request_id'=>'Leave Request ID',
            'worker_name'=>'Worker Name',
            'leave_reason'=>'Leave Reason',
            'request_status'=>'Request Status',
            'request_date'=>'Request Date',
            'expired_date'=>'Expired Date',
            'actions'=>'Actions',
            'leave_request'=>'Leave request',
            'start_date_time'=>'Start Date & Time',
            'end_date_time'=>'End Date & Time',
            'expire_date_time'=>'Expire Date & Time',
            'details'=>'Details',
            'attached_files'=>'Attached Files',
            'download'=>'Download',
            'successful'=>'Successful',
            'the_leave_request'=>'The Leave Request has been',
            'why_would_you_reject'=>'Why would you reject this leave request? Please provide a valid reason to inform the worker.',
            'reason'=>'Reason',
            'you_have_exceeded'=>'You have exceeded the maximum number of characters',
            'the_leave_request_rejected'=>'The Leave Request has been Rejected',
            'rejection_reason'=>'Rejection Reason',
            'new_leave_requests'=>'New leave request has been received',
            'requests'=>'Requests',
            'advanced_reports'=>'Advanced Reports',
            'generate_adv_reports'=>'Generate Advanced Report',
            'select_contract'=>'Select Contract',
            'select_report_type'=>'Select Report Type',
            'select_period'=>'Select Period',



            'report_period' => 'Report Period',
            'from' => 'From',
            'to' => 'To',
            'report_extract_date' => 'Report Extract Date',
            'contract_name' => 'Contract Name',
            'service_provider' => 'Service Provider',
            'number_of_services' => 'Number of Services Included',
            'serial' => 'Serial',
            'service_name' => 'Service Name',
            'total_wo_count' => 'Total WO Count',
            'pm_work_orders' => 'PM Work Orders',
            'rm_work_orders' => 'RM Work Orders',
            'price_of_service' => 'Price of Service (SAR)',
            'total_costs' => 'Total Costs (SAR)',

            'number_of_materials_included' => 'Number of Materials included',
            'material_name' => 'Material Name',
            'unit' => 'Unit',
            'open_stock' => 'Open Stock',
            'cost_per_unit' => 'Cost/Unit (SAR)',
            'quantity_used'          => 'Quantity Used',
            'total_consumed_cost'    => 'Total Consumed Cost (SAR)',
            'remaining_stock'        => 'Remaining Stock',
            'remaining_stock_cost'   => 'Remaining Stock Cost (SAR)',
            'reorder_level'          => 'Reorder Level',
            'reorder_warning'        => 'Reorder Warning',

            'number_of_workforce_members' => 'Number of workforce members',
            'worker_name' => 'Worker Name',
            'job_title' => 'Job Title',
            'nationality' => 'Nationality',
            'admin_level' => 'Admin Level',
            'phone_number' => 'Phone Number',
            'working_hours_per_day' => 'Working Hours / Day',
            'required_monthly_attendance' => 'Required Monthly Attendance / Hours',
            'required_whole_monthly_attendance' => 'Expected whole Attendance / Hours',
            'mandatory_attendance' => 'Mandatory Attendance',
            'salary' => 'Salary (SAR)',
            'expected_salary' => 'Expected Salary (SAR)',
            'hour_salary' => 'Hour Salary',
            'total_attendance' => 'Total Attendance / Hour',
            'attendance_percent' => 'Attendance Percent %',
            'net_payable' => 'Net Payable (SAR)',
            'total_deduction' => 'Total Deduction (SAR)',

        ],

        'place_holder' => [
            'emp_name'=>"Employee Name",
            //'emp_name'=>"Worker Name",
            'emp_id'=>'Employee ID',
            //'emp_id'=>'Worker ID',
            'emp_email'=>'Employee Email',
            'emp_country'=>'Please Select',
            'emp_city'=>'Please Select',
            'emp_dept'=>'Employee Department',
            //'emp_dept'=>'Worker Department',
            'tenants_shared_file_search'=>'Search by Document Name',
            'loading' => 'loading...',
            'Only_cities_that_has_contracts' => 'Only cities that has contracts will be shown here, you can create a contract from the tab: Data - Contracts.'
        ],
    ],

        /**-----------------validation------------------------------- */

        'user_validation' => [
            'email_exist'=>'Email already exist, please enter unique email',
            'phone_exist' =>'Phone number already exist, please use unique one',
            'unique_area_manager'=>'There is already another area manager, and more than one area manager cannot be assigned to buildings in the same project.'
        ],

        'attendance' => [
            'attendance'=>'Attendance',
            'search_by_name_id' =>'Search by name or ID',
            'attendance_history'=>'Attendance History',
            'clock_in_time_date'=>'Clock In Time and Date',
            'property_clock_in'=>'Property of Clock in',
            'clock_out_on_behalf'=>'Clock Out on behalf of Worker',
            'select_date_time'=>'Select Time and Date',
            'clock_out_time_date'=>'Clock out Time and Date',
            'No_workers_are_currently_clocked_in'=>'No workers are currently clocked in',
            'Select_clock_out_time_and_date'=>'Select clock out time and date',
            'property_clock_out'=>'Property of clock out',
            'clockout_by'=>'Clocked Out by',
            'total_hours'=>'Overall Hours',
            'select_date'=>'Select Date',
            'from_date'=>'From Date',
            'to_date'=>'To Date',
            'select_workers'=>'Select Workers',
            'choose_format'=>'Choose Format',
            'choose_lang'=>'Choose language',
            'choose_file_format'=>'Choose File Format',
            'select_clock_out_property'=>'Select clock out property',
            'id'=>'ID',
            'total_entries'=>'Total Entries',
            'select_property'=>'Select Property',
            'clockout_popup_text'=>'You want to clock out on behalf of the worker? Once the worker is clocked out, it cannot be undone',
            'clockout_popup_title'=>'Are you sure?',
            'You_successfully_clocked_out_on_behalf_of_the_worker'=>'You successfully clocked out on behalf of the worker',
            'no_result_found'=>'No Result Found',
            'project'=>'Project',
            'total_workers'=>'Total Workers',
            'entries'=>'Entries',
            'worker_details'=>'Worker Details',
            'worker_name'=>'Worker Name',
            'worker_id'=>'Worker ID',
            'history'=>'History',
            'total_hrs'=>'Total Hours',
            'hours'=>'Hours',
        ],



        'team_leader_module' => [
            'team_leader_supervisor' => 'Team Leader Supervisor',
            'team_leader' => 'Team Leader',
            'assigned_workers' => 'Assigned Workers',
            'team_leader_name' => 'Team Leader Name',
            'team_leader_nationality' => 'Team Leader Nationality',
            'team_leader_id' => 'Team Leader ID',
            'team_leader_mobile_number' => 'Team Leader Mobile number',
            'allow_to_take_action_on_behalf_of_workers' => 'Allow to take action on behalf of workers',
            'allow_to_report_maintenance_issues' => 'Allow to report maintenance issues',
            'allow_to_scan_qr_codes_to_start_any_wo_on_behalf_of_workers' => 'Allow to scan QR codes to start any WO on behalf of workers',
            'team_leader_success_account_create' => 'Now team leader can login to Worker App using the following ID, with pre-assigned password',
        ],


    ];







?>

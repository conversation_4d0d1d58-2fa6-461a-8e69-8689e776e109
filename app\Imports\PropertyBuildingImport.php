<?php
    namespace App\Imports;
    use Illuminate\Support\Collection;
    use Maatwebsite\Excel\Concerns\ToCollection;
    use Maatwebsite\Excel\Concerns\WithCalculatedFormulas;
    use Maatwebsite\Excel\Concerns\WithHeadingRow;
    use Maatwebsite\Excel\Concerns\SkipsOnError;
    use Maatwebsite\Excel\Concerns\WithBatchInserts;
    use Maatwebsite\Excel\Concerns\WithChunkReading;
    use Maatwebsite\Excel\Concerns\Importable;
    use Maatwebsite\Excel\Concerns\SkipsErrors;
    use Illuminate\Support\Facades\Log;
    use App\Http\Traits\FunctionsTrait;
    use App\Http\Traits\BulkImportTempTrait;

    class PropertyBuildingImport implements ToCollection, WithCalculatedFormulas, WithHeadingRow, SkipsOnError, WithBatchInserts, WithChunkReading{
        use Importable, SkipsErrors, FunctionsTrait, BulkImportTempTrait;

        protected $bulkImportTempId;

        public function __construct($bulkImportTempId){
            $this->bulkImportTempId = $bulkImportTempId;
        }

        /**
        * @param Collection $collection
        */
        public function collection(Collection $dataRows){
            try {
                $collection = collect();

                if($this->valueIsRequired($dataRows)){
                    Log::info("PropertyBuildingImport error: No properties bulding sheet found in this file!");
                }

                else{
                    $dataRows->chunk(500)->each(function ($chunk) use ($collection, $dataRows) {
                        $chunk->each(function ($row) use ($collection, $dataRows) {
                            $row['property_name'] = isset($row['property_name']) ? trim($row['property_name']) : null;
                            $row['bulding_name'] = isset($row['bulding_name']) ? trim($row['bulding_name']) : null;
                            $row['zone'] = isset($row['zone']) ? trim($row['zone']) : null;
                            $row['unit'] = isset($row['unit']) ? trim($row['unit']) : null;
                            $row['unit_type'] = isset($row['unit_type']) ? trim($row['unit_type']) : null;
                            $collection->push($row);
                        });
                    });

                    $filteredCollection = $collection->filter(function ($item) {
                        return collect($item)->filter()->isNotEmpty();
                    });

                    $jsonData = $filteredCollection->toJson();
                    $updatedBulkImportTemp = $this->updateBulkImportTempByValues('id', $this->bulkImportTempId, ['buildings_data' => $jsonData]);

                    if($updatedBulkImportTemp){
                        Log::info("PropertyBuildingImport: Properties buildings data processed successfully: ".$filteredCollection->count()." records found.");
                    }

                    else{
                        Log::info("PropertyBuildingImport: Unable to import and save the properties buildings sheet.");
                    }
                }
            } 
            
            catch (\Throwable $th) {
                Log::error("PropertyBuildingImport error: ".$th);
            }
        }

        /**
         * headingRow
         *
         * @return int
         */
        public function headingRow(): int{
            return 1;
        } 

         /**
         * onError
         *
         * @param  mixed $e
         * @return void
         */
        public function onError(\Throwable $e){
            Log::error("PropertyBuildingImport (onError) error: ".$e->getMessage());
        }

        /**
         * batchSize
         *
         * @return int
         */
        public function batchSize(): int{
            return 500;
        }

         /**
         * chunkSize
         *
         * @return int
         */
        public function chunkSize(): int{
            return 500;
        }
    }
?>
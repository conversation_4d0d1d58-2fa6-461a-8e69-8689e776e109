<div class="tab-pane fade show active" id="details-tab" role="tabpanel" 
     aria-labelledby="home-tab" 
     @if($currentStep !== 1) style="display: none;" @endif>
               <div class="card">
                <div class="card-header"><h6> 
@if(isset($rfxId))
@lang('rfx.edit_rfx')
@else
@lang('rfx.create_rfx')
@endif

                </h6></div>
                  <div class="card-body">
                     <div class="row">
                        <div class="form-group col-md-4">
                           <label for="title">@lang('rfx.title')<span class="text-danger">*</span></label>
                            <input wire:model.defer="title" id="title" class="form-control" type="text"
                                placeholder="@lang('rfx.enter_title')" required>
                                @error('title') <span class="text-danger small">{{ $message }}</span> @enderror
                        </div>
                        <div class="form-group col-md-4">
                          <label for="category">@lang('rfx.category')<span class="text-danger">*</span></label>
                          <select wire:model.defer="category" id="category" class="form-control" required>
                            <option value="">@lang('rfx.select_category')</option>
                            @foreach($categories as $key => $label)
                              <option value="{{ $key }}">{{ $label }}</option>
                            @endforeach
                          </select>
                          @error('category') <span class="text-danger small">{{ $message }}</span> @enderror
                        </div>
                        <div class="form-group col-md-4">
                              <label for="rfx_type">@lang('rfx.type')<span class="text-danger">*</span></label>
                              <select wire:model.defer="rfx_type" id="rfx_type" class="form-control">
                                <option value="">@lang('rfx.select_type')</option>
                                @foreach($types as $key => $label)
                                  <option value="{{ $key }}">{{ $label }}</option>
                                @endforeach
                              </select>
                              @error('type') <span class="text-danger small">{{ $message }}</span> @enderror
                            </div>

                            {{-- Status --}}
                            <div class="form-group col-md-4">
                              <label for="status">@lang('rfx.status')<span class="text-danger">*</span></label>
                              <select wire:model.defer="status" id="status" class="form-control" required>
                                <option value="">@lang('rfx.select_status')</option>
                                @foreach($statuses as $key => $label)
                                  <option value="{{ $key }}">{{ $label }}</option>
                                @endforeach
                              </select>
                              @error('status') <span class="text-danger small">{{ $message }}</span> @enderror
                            </div>

                            <div class="form-group col-md-4">
                              <label for="location">@lang('rfx.location')<span class="text-danger">*</span></label>
                              <input wire:model.defer="location" id="location" class="form-control" type="text"
                                     placeholder="@lang('rfx.enter_location')" required>
                                      @error('status') <span class="text-danger small">{{ $message }}</span> @enderror
                            </div>

                            <div class="form-group col-md-4">
                              <label for="position">@lang('rfx.no_positions')<span class="text-danger">*</span></label>
                              <input wire:model.defer="position" id="position" class="form-control" type="number" min="1"
                                     placeholder="@lang('rfx.enter_positions')" required>
                                      @error('position') <span class="text-danger small">{{ $message }}</span> @enderror
                            </div>

                            <div class="form-group col-md-4">
                              <label for="budget_from">@lang('rfx.budget_from')<span class="text-danger">*</span></label>
                              <input wire:model.defer="budget_from" id="budget_from" class="form-control" type="number" min="0"
                                     placeholder="@lang('rfx.enter_amount')" required>
                                      @error('budget_from') <span class="text-danger small">{{ $message }}</span> @enderror
                            </div>

                            <div class="form-group col-md-4">
                              <label for="budget_to">@lang('rfx.budget_to')<span class="text-danger">*</span></label>
                              <input wire:model.defer="budget_to" id="budget_to" class="form-control" type="number" min="0"
                                     placeholder="@lang('rfx.enter_amount')" required>
                                      @error('budget_to') <span class="text-danger small">{{ $message }}</span> @enderror
                            </div>

                            <div class="form-group col-md-4">
                              <label for="start_date">@lang('rfx.start_date')<span class="text-danger">*</span></label>
                              <input wire:model.defer="start_date" id="start_date" class="form-control datepicker" autocomplete="off" required onchange="setdate('start_date','start_date')">
                               @error('start_date') <span class="text-danger small">{{ $message }}</span> @enderror
                            </div>

                            <div class="form-group col-md-4">
                              <label for="end_date">@lang('rfx.end_date')<span class="text-danger">*</span></label>
                              <input wire:model.defer="end_date" id="end_date" class="form-control datepicker" autocomplete="off" required onchange="setdate('end_date','end_date')">
                               @error('end_date') <span class="text-danger small">{{ $message }}</span> @enderror
                            </div>

                            <div class="form-group col-md-8">
                              <label for="skills">@lang('rfx.skill_box')<span class="text-danger">*</span></label>
                              <div wire:ignore>
                                  <select id="skills" class="form-control" multiple style="width: 100%">
                                      @foreach ($skills as $skill)
                                          <option value="{{ $skill }}" selected>{{ $skill }}</option>
                                      @endforeach
                                  </select>
                              </div>
                              @error('skills') <span class="text-danger small">{{ $message }}</span> @enderror
                            </div>
                     </div>
                     <div class="row">
                        <div class="col-6 form-group" id="users">
                           <label for="user_id">@lang('rfx.client')<span class="text-danger">*</span></label>
                           <select wire:model.defer="user_id" id="user_id" class="form-control">
                              <option value="">@lang('rfx.select_client')</option>
                              @foreach($clients as $key => $label)
                                  <option value="{{ $key }}">{{ $label }}</option>
                                @endforeach
                           </select>
                            @error('user_id') <span class="text-danger small">{{ $message }}</span> @enderror
                        </div>
                     </div>
                  </div>
               </div>
               <div class="card mt-3">
                  <div class="card-body">
                     <div class="row">
                        <div class="form-group col-md-6">
                           <label for="">@lang('rfx.rfx_description')<span class="text-danger">*</span></label>
                        <div wire:ignore>
                        <textarea id="rfxDescription" class="form-control">{{ $description }}</textarea>
                        </div>
                        @error('description') <span class="text-danger small">{{ $message }}</span> @enderror
                        </div>
                        <div class="form-group col-md-6">
                           <label for="">@lang('rfx.rfx_requirement')<span class="text-danger">*</span></label>
                        <div wire:ignore>
                        <textarea id="rfxRequirement" class="form-control">{{ $requirement }}</textarea>
                        </div>
                         @error('requirement') <span class="text-danger small">{{ $message }}</span> @enderror
                        </div>
                     </div>
                  </div>
               </div>
               <div class="card mt-3">
                  <div class="card-header"><h6>@lang('rfx.rfx_checkboxes')</h6></div>
                  <div class="card-body">
                     <div class="row">
                        <div class="col-md-6">
                           <div class="form-group mb-0">
                              <label>@lang('rfx.need_to_ask')</label>
                            <div class="mt-4">
                            <div class="form-check custom-checkbox">
                                <input type="checkbox" class="form-check-input" id="check-gender"
                                       wire:model="applicant" value="gender">
                                <label class="form-check-label" for="check-gender">@lang('rfx.gender')</label>
                            </div>

                            <div class="form-check custom-checkbox">
                                <input type="checkbox" class="form-check-input" id="check-dob"
                                       wire:model="applicant" value="dob">
                                <label class="form-check-label" for="check-dob">@lang('rfx.dob')</label>
                            </div>

                            <div class="form-check custom-checkbox">
                                <input type="checkbox" class="form-check-input" id="check-country"
                                       wire:model="applicant" value="country">
                                <label class="form-check-label" for="check-country">@lang('rfx.country')</label>
                            </div>
                        </div>

                           </div>
                        </div>
                        <div class="col-md-6">
   <div class="form-group mb-0">
      <h6>@lang('rfx.need_to_show_option')</h6>
      <div class="mt-4">
         <div class="form-check custom-checkbox">
            <input type="checkbox" class="form-check-input" id="check-profile"
                   wire:model="visibility" value="profile">
            <label class="form-check-label" for="check-profile">@lang('rfx.profile_image')</label>
         </div>
         <div class="form-check custom-checkbox">
            <input type="checkbox" class="form-check-input" id="check-proposal"
                   wire:model="visibility" value="proposal">
            <label class="form-check-label" for="check-proposal">@lang('rfx.proposal')</label>
         </div>
         <div class="form-check custom-checkbox">
            <input type="checkbox" class="form-check-input" id="check-letter"
                   wire:model="visibility" value="letter">
            <label class="form-check-label" for="check-letter">@lang('rfx.cover_letter')</label>
         </div>
         <div class="form-check custom-checkbox">
            <input type="checkbox" class="form-check-input" id="check-terms"
                   wire:model="visibility" value="terms">
            <label class="form-check-label" for="check-terms">@lang('rfx.terms_and_conditions')</label>
         </div>
      </div>
   </div>
</div>

                     </div>
                  </div>
                  <div class="card-header">
                     <h5 class="mt-4">@lang('rfx.questions_checkboxes')</h5>
                  </div>
                  <div class="card-body">
                     <div class="row">
                        <div class="col-md-6">
                           <div class="form-group">
                              <h6>@lang('rfx.custom_questions')</h6>
                              <div class="my-4">
                                 <p class="text-danger d-none" id="required_checkbox_validation">
                                    @lang('rfx.select_required_question')
                                 </p>
                              </div>
                           </div>
                        </div>
                        <div class="col-md-12" id="termsandcondition">
                           <div class="form-group">
                              <label for="">@lang('rfx.terms_conditions')<span class="text-danger">*</span></label>
                              <div wire:ignore>
                              <textarea id="termsConditions" class="form-control">{{ $terms_and_conditions }}</textarea>
                              </div>
                            @error('terms_and_conditions') <span class="text-danger small">{{ $message }}</span> @enderror
                           </div>
                        </div>
                        <div class="col-12 d-flex justify-content-end mt-4">
                           <button type="button" wire:click="goToNextStep"
                           class="btn btn-default btn-primary no-wrap">
                           <i class="iconsax" icon-name="arrow-right"></i> @lang('rfx.next')
                           </button>
                        </div>
                     </div>
                  </div>
               </div>
            </div>
<div>
    <div class="card">
        <div class="card-header py-4 px-3 border-0 d-flex justify-content-between align-items-center">
            <h6 class="text-capitalize fw-500 mb-3 mb-sm-0">
                @lang('finace_manage.common.debit_note_summary')
            </h6>
        </div>
        <hr class="mt-1 mb-0"/>
        <div class="card-body px-0 pt-0">
            <div class="userDatatable projectDatatable project-table bg-white w-100 border-0">
                <div class="table-responsive">
                    <table class="table mb-0 radius-0 table-border">
                        <thead>
                            <tr>
                                <th>@lang('finace_manage.common.date')</th>
                                <th>@lang('finace_manage.common.amount')</th>
                                <th>@lang('finace_manage.common.description')</th>
                                <th>@lang('finace_manage.common.action')</th>
                            </tr>
                        </thead>
                        <tbody>
                            @if(isset($invoiceDetails) && $invoiceDetails['status'] == 'success')
                                @foreach($invoiceDetails['data']['debit_notes'] as $key => $value)
                                    <tr style="opacity: 1;">
                                        <td>{{ $value['date'] }}</td>
                                        <td>{{ $value['amount'] }}</td>
                                        <td>{{ $value['description'] }}</td>
                                        <td> 

<a href = "javascript:void(0);" class = "remove" wire:click = "deleteModal({{ $value['id'] }})"  >
<i class = "iconsax icon text-delete fs-18 mr-0" icon-name = "trash"></i>
</a>
<a href = "javascript:void(0);" wire:click="editDebitNote({{ $value['id'] }})" class="edit">
                                            <i class="iconsax icon text-new-primary fs-18" icon-name="edit-1"></i>
</a>



                            </td>  
                                    </tr>
                                @endforeach
                            @else
                                <tr>
                                    <td colspan="4" class="text-center text-muted">
                                        @lang('general.no_data_available')
                                    </td>
                                </tr>
                            @endif
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

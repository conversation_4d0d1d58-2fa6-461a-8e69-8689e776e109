<div>
    <div class="contents crm">
        <div class="container-fluid">
            <div class="col-lg-12">
                <div class="row justify-content-sm-between align-items-center justify-content-center">
                    <div class="page-title-wrap">
                        <div class="page-title d-flex justify-content-between">
                            <div
                                class="page-title__left justify-content-sm-between align-items-center justify-content-center">
                                <div class="user-member__title mr-sm-25 ml-0">
                                    <h4 class="text-capitalize fw-500 breadcrumb-title fs-16">
                                        @lang('CRMProjects.manage_tangible_task')
                                    </h4>
                                </div>
                            </div>
                        </div>
                        <div>
                            <ul class="atbd-breadcrumb nav">
                                <li class="atbd-breadcrumb__item">
                                    <a href="{{ route('admin.dashboard') }}"> @lang('CRMProjects.common.dashboard')</a>
                                    <span class="breadcrumb__seperator">
                                        <span class="la la-angle-right"></span>
                                    </span>
                                </li>
                                <li class="atbd-breadcrumb__item">
                                    <a>@lang('CRMProjects.common.projects')</a>
                                    <span class="breadcrumb__seperator">
                                        <span class="la la-angle-right"></span>
                                    </span>
                                </li>
                                <li class="atbd-breadcrumb__item">
                                    <a>@lang('CRMProjects.task-board')</a>
                                    <span class="breadcrumb__seperator">
                                        <span class="la la-angle-right"></span>
                                    </span>
                                </li>
                                <li class="atbd-breadcrumb__item">
                                    <a>@lang('CRMProjects.manage_tangible_task')</a>

                                </li>
                            </ul>
                        </div>
                    </div>


                    <div class="d-flex gap-10 breadcrumb_right_icons">
                        <div class="d-flex gap-10 breadcrumb_right_icons">
                            <div class="dropdown slide-dropdown ">
                                {{-- <button class="btn btn-white btn-default text-center svg-20 wh-45 dropdown-toggle"
                                    data-toggle="dropdown" aria-expanded="true">
                                    <i class="iconsax icon text-new-primary fs-22 colorRed mr-0" icon-name="sort"></i>
                                </button> --}}



                                {{--                 <div class="dropdown-menu market-dropdown filter-dropdown px-3 text-osool"
                                    aria-labelledby="dropdownMenuButton" data-auto-close="false"
                                    x-placement="bottom-start">
                                    <h5 class="fs-14 fw-500 text-osool">@lang('CRMProjects.common.start_end_date')</h5>
                                    <div class="dropdown-divider my-2"></div>
                                    <form class="fs-14" id="dateFilterForm">
                                        <div class="form-group">
                                            <label for="">@lang('CRMProjects.common.start_date')</label>
                                            <div class="position-relative">
                                                <input type="text" name="filter_start_date" id="filter_start_date"
                                                    inline-datepicker="" type="text" datepicker-autohide
                                                    class="form-control datepicker" aria-describedby="emailHelp"
                                                    placeholder="@lang('CRMProjects.common.enter_start_date')"
                                                    wire:model.defer="filter_start_date">
                                                <i class="iconsax field-icon text-light" icon-name="calendar-1"></i>
                                            </div>
                                        </div>

                                        <div class="form-group">
                                            <label for="">@lang('CRMProjects.common.end_date')</label>
                                            <div class="position-relative">
                                                <input type="text" class="form-control datepicker"
                                                    name="filter_end_date" id="filter_end_date"
                                                    placeholder="@lang('CRMProjects.common.enter_end_date')" wire:model.defer="filter_end_date">
                                                <i class="iconsax field-icon text-light" icon-name="calendar-1"></i>
                                            </div>
                                        </div>

                                        <div class="d-flex gap-10">
                                            <button type="button" class="btn bg-loss text-white radius-xl "
                                                wire:click="resetDateFilter">@lang('CRMProjects.common.reset')</button>
                                            <button type="submit"
                                                class="btn bg-new-primary text-white radius-xl flex-fill"
                                                wire:click="submitDateFilter">@lang('CRMProjects.common.submit')</button>
                                        </div>
                                    </form>


                                </div> --}}

                            </div>



                            {{--  <button class="btn btn-white btn-default text-center svg-20 wh-45" data-toggle="tooltip"
                                title="{{ $isRefreshButton ? __('CRMProjects.common.clear_filter ') : __('CRMProjects.common.refrech') }}"
                                wire:click="handleButtonClickResetOrRefresh">
                                <i class="iconsax icon text-new-primary fs-22 colorRed mr-0"
                                    icon-name="{{ $isRefreshButton ? 'filter-x' : 'refresh' }}">
                                </i>
                            </button> --}}


                            @if ($viewMode == 'cards')
                                <button class="btn btn-white btn-default text-center svg-20 wh-45"
                                    wire:click="switchView('table')">
                                    <i class="iconsax icon text-new-primary fs-22 colorRed mr-0"
                                        icon-name="hamburger-menu"></i>
                                </button>
                            @endif

                            @if ($viewMode == 'table')
                                <button class="btn btn-white btn-default text-center svg-20 wh-45"
                                    wire:click="switchView('cards')">
                                    <i class="iconsax icon text-new-primary fs-22 colorRed mr-0"
                                        icon-name="layout-3"></i>
                                </button>
                            @endif

                            @if (auth()->user()->user_type == 'building_manager')
                                <button wire:click="openChooseWOTypeMoal()"
                                    class="btn btn-default btn-primary w-100 no-wrap" type="button"
                                    aria-expanded="false"><i class="las la-plus fs-16"></i>@lang('CRMProjects.create_tangible_task')</button>
                            @endif
                        </div>
                    </div>

                </div>
            </div>


            @if ($viewMode === 'cards')
                @include('livewire.c-r-m-projects.work-order.list.cards-view')
            @else
                @include('livewire.c-r-m-projects.work-order.list.table-view')
            @endif

        </div>

    </div>



    @include('livewire.c-r-m-projects.work-order.Modals.Task-Initialise-WorkOrder')
    @include('livewire.c-r-m-projects.work-order.Modals.confirm-delete-tabgible-task')
    @include('livewire.project.task-board.modals.edit-tangible-task')
    @include('livewire.project.task-board.modals.show-tangible-task')
    <script>
        function initializeSelect2(id, livewireModel, placeholder = "{{ __('CRMProjects.common.please_select') }}") {
            var element = $('#' + id);

            if (element.length) {
                element.select2({
                    placeholder: placeholder,
                    allowClear: true
                });

                element.on('change', function() {
                    @this.set(livewireModel, $(this).val());
                });

            } else {
                console.warn(`Element with ID '${id}' does not exist.`);
            }
        }

        document.addEventListener("DOMContentLoaded", function() {
if (!window.toastrEventRegistered) {
        window.toastrEventRegistered = true;
        window.addEventListener("show-toastr", event => {
            toastr.options = {
                closeButton: true,
                progressBar: true,
                positionClass: "toast-top-right",
                timeOut: "3000"
            };

            if (event.detail.type === "success") {
                toastr.success(event.detail.message);
            } else if (event.detail.type === "error") {
                toastr.error(event.detail.message);
            }
        });
    }
            /*   if (!$('#usersListforcreateProject').hasClass('select2-hidden-accessible')) {
                  $('#usersListforcreateProject').select2({
                      placeholder: '@lang('CRMProjects.common.choose_user')'
                  });
              }

              // Sync Select2 changes with Livewire model
              $('#usersListforcreateProject').on('change', function() {
                  // Get the selected values
                  let selectedValues = $(this).val();

                  // Update Livewire's model
                  @this.set('UsersforCreateProject', selectedValues);
              }); */
            document.addEventListener("livewire:load", function() {
                  
                Livewire.hook('message.processed', () => {
                    initializeSelect2('userAssigned', 'userAssigned',
                        "{{ __('CRMProjects.common.choose_user') }}");
                    initializeSelect2('editAssignedUsertangible', 'taskEdit.selected_assign_to',
                        "{{ __('CRMProjects.common.choose_user') }}");

                });



            });

        });

        function showLoader() {
            document.getElementById("overlayer").style.display = "block";
            let loaderElements = document.getElementsByClassName("loader-overlay");
            if (loaderElements.length > 0) {
                loaderElements[0].style.display = "block";
            }
        }

        function hideLoader() {
            document.getElementById("overlayer").style.display = "none";

            let loaderElements = document.getElementsByClassName("loader-overlay");
            if (loaderElements.length > 0) {
                loaderElements[0].style.display = "none";
            }
        }

        window.addEventListener('show-loader', event => {
            showLoader();
        });


        window.addEventListener('hide-loader', event => {
            setTimeout(() => {
                hideLoader();
            }, 1000);
        });

        function toggleModal(modalId, action) {
            if (action === 'show') {
                $('#' + modalId).modal('show');
            } else if (action === 'hide') {
                $('#' + modalId).modal('hide');
            }
        }
        window.addEventListener('open-modal', event => {
            toggleModal(event.detail.modalId, 'show');
        });

        window.addEventListener('close-modal', event => {
            toggleModal(event.detail.modalId, 'hide');
        });
        
    </script>


</div>

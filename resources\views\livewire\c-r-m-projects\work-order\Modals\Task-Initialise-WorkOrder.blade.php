<div wire:ignore.self class="modal fade new-popup " id="Task-Initialise-WorkOrder" role="dialog" tabindex="-1"
    aria-labelledby="staticBackdropLabel" aria-hidden="true">



    <div class="modal-dialog radius-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="modal-title fw-500" id="staticBackdropLabel">
                    @lang('CRMProjects.create_tangible_task')</h6>

                <button wire:ignore type="button" class="close border-0" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class=" form-group">
                    <label class="col-form-label">@lang('Priority')</label> <span class="text-danger">*</span>
                    <select class="form-control form-control-light" wire:model.defer="selectedprioritie"
                        id="task-selectedprioritie">
                        <option value="">@lang('Select')</option>
                        @foreach ($prioritiesList as $key => $val)
                            <option value="{{ $key }}">
                                {{ $val }}
                            </option>
                        @endforeach
                    </select>
                    @error('selectedprioritie')
                        <span class="text-danger">{{ $message }}</span>
                    @enderror
                </div>
               {{--  <div class="form-group">
                    <label for="user" class="form-label">@lang('Assign User')</label> <span
                        class="text-danger">*</span>
                    <select class="form-control " id="userAssigned" wire:model.defer="userAssigned" multiple>

                        @foreach ($usersForAssign as $user)
                            <option value="{{ $user['id'] }}">
                                {{ $user['name'] }}
                            </option>
                        @endforeach
                    </select>
                    @error('userAssigned')
                        <span class="text-danger">{{ $message }}</span>
                    @enderror
                </div> --}}

                <div class="form-group">
                    <label for="user" class="form-label">@lang('CRMProjects.milestone')</label> <span
                        class="text-danger">*</span>
                    <select class="form-control" id="milestonesList" wire:model.defer="selectedmilestone">
                        <option value="" selected>@lang('Select')</option>
                        @foreach ($milestonesList as $milestone)
                            <option value="{{ $milestone['id'] }}">
                                {{ $milestone['title'] }}
                            </option>
                        @endforeach
                    </select>
                    @error('selectedmilestone')
                        <span class="text-danger">{{ $message }}</span>
                    @enderror
                </div>
                <div class="form-group">
                    <label for="user" class="form-label">@lang('CRMProjects.work_order_type')</label> <span
                        class="text-danger">*</span>
                    <select class="form-control" id="selectedWOType" wire:model.defer="selectedWOType">
                        <option value="" selected>@lang('Select')</option>

                      
                        <option value="reactive">{{ __('CRMProjects.work_order_reactive') }}</option>
                        <option value="preventive">{{ __('CRMProjects.work_order_preventive') }}</option>

                    </select>
                    @error('selectedmilestone')
                        <span class="text-danger">{{ $message }}</span>
                    @enderror
                </div>






                <div class="d-flex justify-content-between pt-25">
                    <button type="" data-dismiss="modal" aria-label="Close"
                        class="btn btn-light btn-default btn-squared fw-400 text-capitalize b-light color-light ">
                        @lang('CRMProjects.common.cancel')
                    </button>
                    <div class="button-group d-flex ">
                       
                        <button wire:click="initialiseWO()"
                            class="btn btn-primary btn-default btn-squared text-capitalize" value=" Submit">
                            @lang('CRMProjects.common.create') </button>
                    </div>

                </div>

            </div>

        </div>

    </div>

</div>

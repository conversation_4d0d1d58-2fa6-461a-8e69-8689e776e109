<?php

namespace App\Http\Livewire\AdvanceContracts;

use Livewire\Component;
use Livewire\WithFileUploads;
use Illuminate\Support\Facades\Log;
use App\Models\AdvanceContractDraft;
use Intervention\Image\Facades\Image;
use Illuminate\Support\Facades\Storage;
use App\Repositories\AdvanceContractRepository;

class StepFive extends Component
{
    use WithFileUploads;

    public $uuid;
    public $contractImage;
    public $comments;
    public $compressedPath = null;
    public $previewUrl = null;
    public $route_prefix;
    public $isVariation = false;
    public $currentStep = 5;

    protected $rules = [
        'contractImage' => 'nullable|file|mimes:jpeg,jpg,png,webp,gif,heic,heif|max:10240',
        'comments' => 'nullable|string|max:1000',
    ];

    public function mount(AdvanceContractRepository $advanceContractRepository, $uuid = null, $prefix = null)
    {
        $this->uuid = $uuid;
        $this->route_prefix = $prefix;
        $this->isVariation = request()->routeIs('variation.*');
        // If uuid is provided, load existing data
        if ($uuid) {
            $this->uuid = $uuid;

            $draft = $advanceContractRepository->findByUuid($this->uuid);;

            if ($draft) {
                $data = $draft->extra_data;
                if (is_array($data)) {
                    $this->compressedPath = $data['file_path'] ?? null;
                    $this->comments = $data['comments'] ?? null;

                     // Generate preview URL if file already exists
                    if ($this->compressedPath) {
                        $this->previewUrl = Storage::disk('oci')->url($this->compressedPath);
                    }
                }
            }
        }
    }

    /**
     * Handle image upload and compression.
     */
    public function updatedContractImage()
    {
        try {
            $this->validateOnly('contractImage');

            if ($this->contractImage) {
                // Save uploaded file temporarily
                $tempRelativePath = $this->contractImage->store('temporary_images', 'public');
                $fullLocalPath = storage_path('app/public/' . $tempRelativePath);

                Log::debug('Stored temp image path: ' . $fullLocalPath);

                if (!file_exists($fullLocalPath)) {
                    $this->addError('contractImage', 'File not found after saving. Please try again.');
                    return;
                }

                // Compress the image
                $image = Image::make($fullLocalPath);
                $image->resize(1200, 1200, function ($constraint) {
                    $constraint->aspectRatio();
                    $constraint->upsize();
                });

                $fileName = 'contract_' . time() . '.' . $this->contractImage->getClientOriginalExtension();
                $compressedTempPath = storage_path('app/public/temporary_images/' . $fileName);
                $image->save($compressedTempPath);

                $filePathInOci = 'advanced_contracts/' . $fileName;

                // Upload to OCI and make it public
                Storage::disk('oci')->put($filePathInOci, file_get_contents($compressedTempPath));
                Storage::disk('oci')->setVisibility($filePathInOci, 'public');

                $this->previewUrl = Storage::disk('oci')->url($filePathInOci);

                // Cleanup
                unlink($compressedTempPath);
                unlink($fullLocalPath);

                $this->compressedPath = $filePathInOci;
            }
        } catch (\Exception $e) {
            Log::error("Image compression failed: " . $e->getMessage());
            $this->addError('contractImage', 'Failed to upload image. Please try again.');
            $this->contractImage = null;
        }
    }

    /**
     * Remove the uploaded image from storage.
     */
    public function removeImage()
    {
        if ($this->compressedPath && Storage::disk('oci')->exists($this->compressedPath)) {
            Storage::disk('oci')->delete($this->compressedPath);
        }

        $this->contractImage = null;
        $this->compressedPath = null;
        $this->previewUrl = null;
    }

    /**
     * Save the form data.
     */
    public function submit(AdvanceContractRepository $advanceContractRepository)
    {
        $this->validate();

        $contract = $advanceContractRepository->findByUuid($this->uuid);

        $contract->extra_data = [
            'file_path' => $this->compressedPath,
            'comments' => $this->comments,
        ];

        $contract->save();

        // Mark current step as saved in session
        $savedSteps = session()->get("contract_saved_steps_{$this->uuid}", []);
        if (!in_array($this->currentStep, $savedSteps)) {
            $savedSteps[] = $this->currentStep;
        }
        session()->put("contract_saved_steps_{$this->uuid}", $savedSteps);
        
        $this->dispatchBrowserEvent('show-toastr', ['type' => 'success', 'message' => __('advance_contracts.general.extras_saved')]);
        return redirect()->route($this->route_prefix .'confirmation', ['uuid' => $this->uuid]);
    }


    public function render()
    {
        return view('livewire.advance-contracts.step-five');
    }
}

<?php

namespace App\Http\Controllers\Admin\AdvanceContracts;

use Illuminate\Support\Str;
use App\Http\Helpers\Helper;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Http\Traits\FunctionsTrait;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use App\Traits\HandlesAdvanceContractDraft;
use App\Http\Helpers\AdvanceContractHelper;

class AdvanceContractsController extends Controller
{
    use FunctionsTrait, HandlesAdvanceContractDraft;

    private $view_path = 'applications.admin.advanced-contracts';
    private $title;
    private $route_prefix;
    private $isVariationOrder = false;
    public function __construct()
    {
        $this->middleware(function ($request, $next) {
            $this->title = AdvanceContractHelper::getTitleBasedOnRoute();
            $this->route_prefix = AdvanceContractHelper::getRoutePrefix();
            $this->isVariationOrder = request()->routeIs('variation.*');
            return $next($request);
        });
    }
    // From Advanced-Contracts-Module
    public function mainInformation($uuid = null)
    {
        $user = $this->getAuthenticatedUser();
        if (is_null($user)) {
            return redirect('admin/dashboards.404');
        }
        $savedSteps = session()->get("contract_saved_steps_{$uuid}", []);
        return view($this->view_path . '.main_information', [
            'uuid' => $uuid,
            'title' =>  $this->title ,
            'route_prefix' => $this->route_prefix,
            'isVariationOrder'  => $this->isVariationOrder,
            'savedSteps'  => $savedSteps
        ]);
    }

    // From feature/OS3-2350/property-contract
    public function openCreateDataAgreement($uuid = null)
    {
        try {
            $savedSteps = session()->get("contract_saved_steps_{$uuid}", []);
            return view($this->view_path . '.data_agreement_kpi', [
                'uuid' => $uuid,
                'title' =>  $this->title ,
                'route_prefix' => $this->route_prefix,
                'isVariationOrder'  => $this->isVariationOrder,
                'savedSteps'  => $savedSteps
            ]);
        } catch (\Throwable $th) {
            Log::error("openCreateDataAgreement Error: " . $th);
            return redirect('admin/dashboards.404');
        }
    }

    // From feature/OS3-2350/property-contract
    public function AjaxGetPropertyUnitZoneList(Request $request, $id = null)
    {
        if ($request->ajax()) {
            if (!empty($request->id) || !empty($request->_token)) {
                $users = Auth::user();
                $roles_city = json_decode($users->role_cities);

                return response()->json($this->getAjaxPropertyList($request->id, $request->r_id));
            }
        }
    }

    // From Advanced-Contracts-Module
    public function assetPPM($uuid = null)
    {
        $savedSteps = session()->get("contract_saved_steps_{$uuid}", []);
        return view($this->view_path . '.assets_ppm', [
            'uuid' => $uuid,
            'title' =>  $this->title ,
            'route_prefix' => $this->route_prefix,
            'isVariationOrder'  => $this->isVariationOrder,
            'savedSteps'  => $savedSteps
        ]);
    }

    public function workforceTeam($uuid = null)
    {
        $savedSteps = session()->get("contract_saved_steps_{$uuid}", []);
        return view($this->view_path . '.workforce_team', [
            'uuid' => $uuid,
            'title' =>  $this->title ,
            'route_prefix' => $this->route_prefix,
            'isVariationOrder'  => $this->isVariationOrder,
            'savedSteps'  => $savedSteps
        ]);
    }

    public function extras($uuid = null)
    {
        $savedSteps = session()->get("contract_saved_steps_{$uuid}", []);
        return view($this->view_path . '.extras', [
            'uuid' => $uuid,
            'title' =>  $this->title,
            'route_prefix' => $this->route_prefix,
            'isVariationOrder'  => $this->isVariationOrder,
            'savedSteps'  => $savedSteps
        ]);
    }

    public function confirmation($uuid = null)
    {
        $savedSteps = session()->get("contract_saved_steps_{$uuid}", []);
        return view($this->view_path . '.confirmation', [
            'uuid' => $uuid,
            'title' =>  $this->title,
            'route_prefix' =>  $this->route_prefix,
            'isVariationOrder'  => $this->isVariationOrder,
            'savedSteps'  => $savedSteps
        ]);
    }

    public function viewAdvanceContractDetail($id = null)
    {
      
        try {
            $user = $this->getAuthenticatedUser();
            if (is_null($user)) {
                return redirect('admin/dashboards.404');
            }

            $ResPrivileges = Helper::checkLoggedinUserPrivileges('view', 'contracts');

            if (!$ResPrivileges['success']) {
                return $ResPrivileges['redirect_url'];
            }

            return view($this->view_path . '.advance_contract_details',[
            'id' => $id
            ]);
            
        } catch (\Throwable $th) {
            Log::error("viewAdvanceContractDetail Error: " . $th);
            return redirect('admin/dashboards.404');
        }
    }

    public function viewVariationOrder($id = null)
    {
      
        try {
            $user = $this->getAuthenticatedUser();
            if (is_null($user)) {
                return redirect('admin/dashboards.404');
            }

            $ResPrivileges = Helper::checkLoggedinUserPrivileges('view', 'contracts');

            if (!$ResPrivileges['success']) {
                return $ResPrivileges['redirect_url'];
            }

            return view($this->view_path . '.advance_contract_variation_changes',[
            'id' => $id
            ]);
            
        } catch (\Throwable $th) {
            Log::error("viewAdvanceContractDetail Error: " . $th);
            return redirect('admin/dashboards.404');
        }
    }

    
}

<div class="modal-dialog radius-xl modal-lg" role="document">
    <div class="modal-content radius-xl">
        <div class="modal-header">
            <h6 class="modal-title" id="exampleModalLabel">
                {{ $isEditMode ? __('vendors.modals.edit_vendor') : __('vendors.modals.create_vendor') }}
            </h6>
            <button wire:ignore type="button" class="close" data-dismiss="modal" aria-label="Close">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-x">
                    <line x1="18" y1="6" x2="6" y2="18"></line>
                    <line x1="6" y1="6" x2="18" y2="18"></line>
                </svg>
            </button>
        </div>
        <form wire:submit.prevent="save">
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="name" class="form-label">{{ __('vendors.form.name') }}</label> <span class="text-danger">*</span>
                            <input class="form-control @error('name') is-invalid @enderror"
                                   placeholder="{{ __('vendors.form.enter_name') }}"
                                   wire:model.defer="name"
                                   type="text"
                                   id="name" />
                            @error('name') <div class="invalid-feedback">{{ $message }}</div> @enderror
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="contact" class="form-label">{{ __('vendors.form.contact') }}</label> <span class="text-danger">*</span>
                            <input class="form-control @error('contact') is-invalid @enderror"
                                   placeholder="{{ __('vendors.form.enter_contact') }}"
                                   wire:model.defer="contact"
                                   type="text"
                                   id="contact" />
                            <small class="text-xs text-danger mt-1">
                                {{ __('vendors.form.phone_country_code_note') }}
                            </small>
                            @error('contact') <div class="invalid-feedback">{{ $message }}</div> @enderror
                        </div>
                    </div>
                    @if(!$isEditMode)
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="email" class="form-label">{{ __('vendors.form.email') }}</label> <span class="text-danger">*</span>
                            <input class="form-control @error('email') is-invalid @enderror"
                                   placeholder="{{ __('vendors.form.enter_email') }}"
                                   wire:model.defer="email"
                                   type="email"
                                   id="email" />
                            @error('email') <div class="invalid-feedback">{{ $message }}</div> @enderror
                        </div>
                    </div>
                    @endif
                    @if(!$isEditMode)
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="password" class="form-label">{{ __('vendors.form.password') }}</label>
                            <span class="text-danger">*</span>
                            <input class="form-control @error('password') is-invalid @enderror"
                                   placeholder="{{ __('vendors.form.enter_password') }}"
                                   wire:model.defer="password"
                                   dir="ltr"
                                   type="password"
                                   id="password" />
                            @error('password') <div class="invalid-feedback">{{ $message }}</div> @enderror
                        </div>
                    </div>
                    @endif
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="tax_number" class="form-label">{{ __('vendors.form.tax_number') }}</label>
                            <input class="form-control @error('tax_number') is-invalid @enderror"
                                   placeholder="{{ __('vendors.form.enter_tax_number') }}"
                                   wire:model.defer="tax_number"
                                   type="text"
                                   id="tax_number" />
                            @error('tax_number') <div class="invalid-feedback">{{ $message }}</div> @enderror
                        </div>
                    </div>
                    <div class="col-md-12 clearfix"></div>
                    <div class="col-6">
                        <h6 class="mb-3">{{ __('vendors.form.billing_address') }}</h6>
                        <div class="form-group">
                            <label for="billing_name" class="form-label">{{ __('vendors.form.billing_name') }}</label> <span class="text-danger">*</span>
                            <input class="form-control @error('billing_name') is-invalid @enderror"
                                   placeholder="{{ __('vendors.form.enter_billing_name') }}"
                                   wire:model.defer="billing_name"
                                   type="text" />
                            @error('billing_name') <div class="invalid-feedback">{{ $message }}</div> @enderror
                        </div>
                        <div class="form-group">
                            <label for="billing_phone" class="form-label">{{ __('vendors.form.billing_phone') }}</label> <span class="text-danger">*</span>
                            <input class="form-control @error('billing_phone') is-invalid @enderror"
                                   placeholder="{{ __('vendors.form.enter_billing_phone') }}"
                                   wire:model.defer="billing_phone"
                                   type="text" />
                            @error('billing_phone') <div class="invalid-feedback">{{ $message }}</div> @enderror
                        </div>
                        <div class="form-group">
                            <label for="billingaddress" class="form-label">{{ __('vendors.form.billing_address') }}</label> <span class="text-danger">*</span>
                            <input class="form-control @error('billing_address') is-invalid @enderror"
                                   placeholder="{{ __('vendors.form.enter_billing_address') }}"
                                   wire:model.defer="billing_address"
                                   type="text" />
                            @error('billing_address') <div class="invalid-feedback">{{ $message }}</div> @enderror
                        </div>
                        <div class="row">
                            <div class="col-6">
                                <div class="form-group">
                                    <input class="form-control @error('billing_city') is-invalid @enderror"
                                           placeholder="{{ __('vendors.form.billing_city') }}"
                                           wire:model.defer="billing_city"
                                           type="text" />
                                    @error('billing_city') <div class="invalid-feedback">{{ $message }}</div> @enderror
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="form-group">
                                    <input class="form-control @error('billing_state') is-invalid @enderror"
                                           placeholder="{{ __('vendors.form.billing_state') }}"
                                           wire:model.defer="billing_state"
                                           type="text" />
                                    @error('billing_state') <div class="invalid-feedback">{{ $message }}</div> @enderror
                                </div>
                            </div>
                             <div class="col-6">
                                <div class="form-group">
                                    <input class="form-control @error('billing_country') is-invalid @enderror"
                                           placeholder="{{ __('vendors.form.billing_country') }}"
                                           wire:model.defer="billing_country"
                                           type="text" />
                                    @error('billing_country') <div class="invalid-feedback">{{ $message }}</div> @enderror
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="form-group">
                                    <input class="form-control @error('billing_zip') is-invalid @enderror"
                                           placeholder="{{ __('vendors.form.billing_zip') }}"
                                           wire:model.defer="billing_zip"
                                           type="text" />
                                    @error('billing_zip') <div class="invalid-feedback">{{ $message }}</div> @enderror
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-6">
                        <h6 class="mb-3">{{ __('vendors.form.shipping_address') }}</h6>

                        <div class="form-group">
                            <label for="shipping_name" class="form-label">{{ __('vendors.form.shipping_name') }}</label> <span class="text-danger">*</span>
                            <input class="form-control @error('shipping_name') is-invalid @enderror"
                                   placeholder="{{ __('vendors.form.enter_shipping_name') }}"
                                   wire:model.defer="shipping_name"
                                   type="text" />
                            @error('shipping_name') <div class="invalid-feedback">{{ $message }}</div> @enderror
                        </div>
                        <div class="form-group">
                            <label for="shipping_phone" class="form-label">{{ __('vendors.form.shipping_phone') }}</label> <span class="text-danger">*</span>
                            <input class="form-control @error('shipping_phone') is-invalid @enderror"
                                   placeholder="{{ __('vendors.form.enter_shipping_phone') }}"
                                   wire:model.defer="shipping_phone"
                                   type="text" />
                            @error('shipping_phone') <div class="invalid-feedback">{{ $message }}</div> @enderror
                        </div>
                        <div class="form-group">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <label for="shippingaddress" class="form-label mb-0">{{ __('vendors.form.shipping_address') }} <span class="text-danger">*</span></label>

                                <a class="btn btn-sm align-items-center border p-1" id="copy_billing_to_shipping" data-toggle="tooltip" title="{{ __('vendors.form.same_as_billing_address') }}" data-bs-placement="top" aria-label="{{ __('vendors.form.same_as_billing_address') }}">
                                    <i class="iconsax mr-0" icon-name="document-copy"></i>
                                </a>
                            </div>
                            <input class="form-control @error('shipping_address') is-invalid @enderror"
                                   placeholder="{{ __('vendors.form.enter_shipping_address') }}"
                                   wire:model.defer="shipping_address"
                                   type="text" />
                            @error('shipping_address') <div class="invalid-feedback">{{ $message }}</div> @enderror
                        </div>
                        <div class="row">

                    <div class="col-6">
                        <div class="form-group">
                            <input class="form-control @error('shipping_city') is-invalid @enderror"
                                   placeholder="{{ __('vendors.form.shipping_city') }}"
                                   wire:model.defer="shipping_city"
                                   type="text" />
                            @error('shipping_city') <div class="invalid-feedback">{{ $message }}</div> @enderror
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="form-group">
                            <input class="form-control @error('shipping_state') is-invalid @enderror"
                                   placeholder="{{ __('vendors.form.shipping_state') }}"
                                   wire:model.defer="shipping_state"
                                   type="text" />
                            @error('shipping_state') <div class="invalid-feedback">{{ $message }}</div> @enderror
                        </div>
                    </div>

                    <div class="col-6">
                        <div class="form-group">
                            <input class="form-control @error('shipping_country') is-invalid @enderror"
                                   placeholder="{{ __('vendors.form.shipping_country') }}"
                                   wire:model.defer="shipping_country"
                                   type="text" />
                            @error('shipping_country') <div class="invalid-feedback">{{ $message }}</div> @enderror
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="form-group">
                            <input class="form-control @error('shipping_zip') is-invalid @enderror"
                                   placeholder="{{ __('vendors.form.shipping_zip') }}"
                                   wire:model.defer="shipping_zip"
                                   type="text" />
                            @error('shipping_zip') <div class="invalid-feedback">{{ $message }}</div> @enderror
                        </div>
                    </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn bg-hold-light text-white radius-xl" data-dismiss="modal">
                    {{ __('vendors.buttons.cancel') }}
                </button>
                <button type="submit" class="btn bg-new-primary radius-xl" @if($isSaving) disabled @endif>
                    @if(!$isSaving)
                        {{ $isEditMode ? __('vendors.buttons.update') : __('vendors.buttons.create') }}
                    @else
                        <i class="fas fa-spinner fa-spin"></i> {{ __('vendors.buttons.saving') }}
                    @endif
                </button>
            </div>
        </form>
    </div>
</div>

@push('scripts')
<script>
    // Copy billing address to shipping address
    document.addEventListener('click', function(e) {
        if (e.target.closest('#copy_billing_to_shipping')) {
            e.preventDefault();

            // Get billing address values
            const billingName = document.querySelector('input[wire\\:model\\.defer="billing_name"]').value;
            const billingPhone = document.querySelector('input[wire\\:model\\.defer="billing_phone"]').value;
            const billingAddress = document.querySelector('input[wire\\:model\\.defer="billing_address"]').value;
            const billingCity = document.querySelector('input[wire\\:model\\.defer="billing_city"]').value;
            const billingState = document.querySelector('input[wire\\:model\\.defer="billing_state"]').value;
            const billingCountry = document.querySelector('input[wire\\:model\\.defer="billing_country"]').value;
            const billingZip = document.querySelector('input[wire\\:model\\.defer="billing_zip"]').value;

            // Set shipping address values
            document.querySelector('input[wire\\:model\\.defer="shipping_name"]').value = billingName;
            document.querySelector('input[wire\\:model\\.defer="shipping_phone"]').value = billingPhone;
            document.querySelector('input[wire\\:model\\.defer="shipping_address"]').value = billingAddress;
            document.querySelector('input[wire\\:model\\.defer="shipping_city"]').value = billingCity;
            document.querySelector('input[wire\\:model\\.defer="shipping_state"]').value = billingState;
            document.querySelector('input[wire\\:model\\.defer="shipping_country"]').value = billingCountry;
            document.querySelector('input[wire\\:model\\.defer="shipping_zip"]').value = billingZip;

            // Trigger input events to update Livewire
            document.querySelector('input[wire\\:model\\.defer="shipping_name"]').dispatchEvent(new Event('input'));
            document.querySelector('input[wire\\:model\\.defer="shipping_phone"]').dispatchEvent(new Event('input'));
            document.querySelector('input[wire\\:model\\.defer="shipping_address"]').dispatchEvent(new Event('input'));
            document.querySelector('input[wire\\:model\\.defer="shipping_city"]').dispatchEvent(new Event('input'));
            document.querySelector('input[wire\\:model\\.defer="shipping_state"]').dispatchEvent(new Event('input'));
            document.querySelector('input[wire\\:model\\.defer="shipping_country"]').dispatchEvent(new Event('input'));
            document.querySelector('input[wire\\:model\\.defer="shipping_zip"]').dispatchEvent(new Event('input'));
        }
    });


</script>
@endpush

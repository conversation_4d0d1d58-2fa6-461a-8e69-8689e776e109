document.addEventListener("DOMContentLoaded", function() {
    let isClickScrolling = false;

    $(".list-group-item a").on("click", function(e) {
        e.preventDefault();

        $(".list-group-item").removeClass("active");
        $(this)
            .parent()
            .addClass("active");

        const href = $(this).attr("href");
        const $target = $(href);

        if ($target.length) {
            const targetOffset = $target.offset().top;

            isClickScrolling = true;

            $("html, body").animate(
                {
                    scrollTop: targetOffset - 100
                },
                500,
                function() {
                    isClickScrolling = false;
                }
            );
        }
    });

    $(window).on("scroll", function() {
        if (isClickScrolling) return;

        const containerRect = {
            top: 0,
            bottom: window.innerHeight
        };

        $(".card").each(function() {
            const cardRect = this.getBoundingClientRect();
            const cardHeight = cardRect.height;

            const visibleHeight =
                Math.min(cardRect.bottom, containerRect.bottom) -
                Math.max(cardRect.top, containerRect.top);

            if (visibleHeight >= cardHeight / 2) {
                $(".list-group-item").removeClass("active");
                $(`.list-group-item a[href="#${this.id}"]`)
                    .parent()
                    .addClass("active");
            }

            if (!$(".list-group-item").hasClass("active")) {
                $(".list-group-item").removeClass("active");
                $(".list-group-item:first").addClass("active");
            }
        });
    });
    $(window).trigger("scroll");
});

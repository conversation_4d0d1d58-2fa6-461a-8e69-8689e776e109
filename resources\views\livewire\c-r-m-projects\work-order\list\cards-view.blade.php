<div class="table-responsive new-scrollbar crm-leads">
    <div class="d-flex gap-10 pb-4 ">

        @foreach ($paginatedWorkOrders as $status => $data)
            <div class="col-md-3 col-sm-6 mb-md-0 mb-2 p-0">
                <div class="card">
                    <div class="card-header header-left d-flex justify-content-between p-2 min-h-0 border-0 gap-10">
                        <h6 class="fs-14 fw-400">{{ __(ucwords(str_replace('_', ' ', $status))) }}</h6>
                        <span
                            class="p-2 d-inline-block btn-primary btn-sm wh-30 d-flex align-items-center justify-content-center rounded-xl">
                            {{ $data['total'] }}
                        </span>
                    </div>

                    <div class="card-body p-2">
                        <div id="stage-{{ $status }}" class="sortable-leads">

                            @foreach ($data['items'] as $workorder)
                                <div class="card radius-xl p-2 shadow-lg sortable-leads">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div class="d-flex">

                                            <a class="ml-2" href="#"
                                                wire:click="showTaskDetails({{ $workorder->workdo_task_id }})">
                                                {{ $workorder->work_order_id ?? 'No Title' }}

                                                <i class="las la-angle-right rotate-ar-y"></i>
                                            </a>
                                        </div>
                                        <div class="dropdown">
                                            <span
                                                class="d-block rounded border wh-20 text-center dropdown-toggle cursor-pointer"
                                                data-toggle="dropdown">
                                                <i class="las la-ellipsis-v"></i>
                                            </span>
                                            <div class="dropdown-menu">
                                                <a href="#" class="dropdown-item"
                                                    wire:click="showTaskDetails({{ $workorder->workdo_task_id }})">
                                                    <span data-feather="eye"></span> @lang('CRMProjects.common.view_task')
                                                </a>
                                                <a href="#" class="dropdown-item"
                                                    wire:click="editTangibleTask({{ $workorder->workdo_task_id }})">
                                                    <span data-feather="edit"></span> @lang('CRMProjects.common.edit_task')
                                                </a>
                                                <a href="#" class="dropdown-item"
                                                    wire:click="openDeleteModalTangibleTask({{ $workorder->workdo_task_id }},'{{ $workorder->work_order_id }}')">
                                                    <span data-feather="trash"></span> @lang('CRMProjects.common.delete')
                                                </a>

                                            </div>
                                        </div>
                                    </div>

                                    {{--  <div class="text-osool mt-2">
                                        <span>{{ date('d M Y', strtotime($workorder->start_date ?? $workorder->created_at)) }}</span>
                                    </div> --}}
                                    <div class="d-flex justify-content-between align-items-center mt-3">
                                        <span class="fw-600">{{ __('CRMProjects.common.supervisor') }}:</span>
                                        <span class="fw-600">
                                            @if (!$workorder?->AssignedSupervisor)
                                                @lang('CRMProjects.common.not_assigned')
                                            @elseif(!empty($workorder->AssignedSupervisor->name))
                                                {{ $workorder->AssignedSupervisor->name }}
                                            @else
                                                ---
                                            @endif
                                        </span>

                                    </div>
                                    @if (!$workorder->is_collaborative)
                                      <div class="d-flex justify-content-between align-items-center mt-1">
                                        <span class="fw-600"> @lang('work_order.forms.label.worker') :</span>
                                        <span class="fw-600">
                                            @if (!$workorder?->worker)
                                                @lang('CRMProjects.common.not_assigned')
                                            @elseif(!empty($workorder->worker->name))
                                                {{ $workorder->worker->name }}
                                            @else
                                                ---
                                            @endif
                                        </span>
                                    </div>
                                    @else
                                   <div class="d-flex justify-content-between align-items-center mt-1">
                                        <span class="fw-600 text-danger"> @lang('CRMProjects.common.collaborative_wo') </span>
                                       
                                    </div>
                                    @endif
                                  

                                    <div class="d-flex justify-content-between align-items-center mt-3 w-100">
                                        <div>
                                            <span class="mr-2 fs-14 fw-500"><i
                                                    class="iconsax icon text-osool fs-17 mr-0"
                                                    icon-name="calendar-1"></i>
                                                {{ date('d M Y', strtotime($workorder->start_date ?? $workorder->created_at)) }}
                                            </span>


                                        </div>
                                        <div class="text-osool mt-2">
                                            @if ($workorder->work_order_type == 'preventive')
                                                <small class="bg-draft rounded text-dark status-badge">

                                                    @lang('CRMProjects.common.preventive')
                                                </small>
                                            @else
                                                <small class="bg-draft rounded text-dark status-badge">
                                                    @lang('CRMProjects.common.reactive')

                                                </small>
                                            @endif


                                        </div>
                                    </div>

                                </div>
                            @endforeach

                            <div class=" row justify-content-center mt-2 ">
                                @if (count($data['items']) >= ($perPagePerStatus[$status] ?? 10))
                                    <div class="col-auto text-center mt-2 ">
                                        <button wire:click="loadMore('{{ $status }}')"
                                            class="btn btn-sm btn-primary">
                                            @lang('CRMProjects.common.btn_Load_more')

                                        </button>
                                    </div>
                                @endif
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        @endforeach

    </div>
</div>

<?php

namespace App\Http\Livewire\CRMProjects;


use Livewire\Component;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Log;
use App\Services\CRM\Projects\ProjectServices;
use App\Services\CRM\CRMUserService;
use App\Services\CRM\Sales\DocumentTypeService;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Storage;
use App\Services\CRM\CRMUsers;
use Illuminate\Support\Facades\Http;
use Livewire\WithFileUploads;
use Livewire\TemporaryUploadedFile;
use App\Enums\FileType;
use App\Enums\Language;
use App\Http\Traits\ExportTrait;
use App\Http\Traits\FunctionsTrait;
use App\Http\Helpers\Helper;
use Auth;
use App\Jobs\MilestoneJob;
use App\Enums\SwalType;
use App\Models\Exports;
use GuzzleHttp\Client;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Crypt;

class PublicProjectDetails extends Component
{

    use WithFileUploads, ExportTrait, FunctionsTrait;

    public $projectID;
    public $workspaceSlug;
    public $projectData = [];
    public $attachment;
    public $chartData = [];
    public $daysleft = '';
    public $start_date;
    public $filenametoDelete;
    public $progress = 0;
    public $end_date, $budget, $description, $name;
    public $input_name;
    public $users = [];
    public $project_type;
    public $projectType = [];
    public $priorityLevel = [];
    public $priority_level;
    public $isModalOpen = false;

    public $attachments = [];
    public $documentTypes = [];
    public $projects = [];
    public $files = [];
    public $isDeleteModalOpen = false;

    public $filePaths = [];
    public $folderKey = 'uploads';
    public $maxFileSize = 5120;

    public $attachmentFiles;
    public $currentDate;

    public  $user;
    public $invoices = [], $bills = [], $retainers = [], $proposals = [], $rfxs = [], $rfxApplicants = [], $rfxApplications = [], $vendorOnBoards = [], $arrSchedule = [], $stages = [], $bug_stages = [], $documents = [], $currency;


    public $settingsState = [
        'basic_details' => 0,
        'member' => 0,
        'client' => 0,
        'vendor' => 0,
        'milestone' => 0,
        'activity' => 0,
        'attachment' => 0,
        'task' => 0,
        'bug_report' => 0,
        'invoice' => 0,
        'bill' => 0,
        'timesheet' => 0,
        'documents' => 0,
        'progress' => 0,
        'password_protected' => 0,
        'password' => '',
        'retainer' => 0,
        'proposal' => 0,
        'procurement' => 0,
    ];

    public $milestoneData = [];

    public $status = 'incomplete';
    public $statuss;
    public $cost;
    public $summary;

    public $fileData = [];
    public $type, $user_id, $subject, $notes, $project, $document_id;

    public $projectDetails;

    public $selectedUsersForInvite = [];
    public $usersAlreadyInvited = [];
    public $selectedvendorsForShare = [];
    public $vendorsAlreadyInProject = [];
    public $selectedclientsForShare = [];
    public $clientssAlreadyInProject = [];
    public $clients = [];
    public $vendors = [];
    public $fileType = 'pdf';
    public $fileLanguage = 'en';
    public $bmas_list = [];
    public $assignedBMAList = [];
    public $projectAccessUserId = 0;
    public $projectAccessDeleteMessage = '';
    public $projectAccessUserName = '';
    public $projectAccessUserType = '';

    public $selectedBuildingsManager = [];
    public $allBuildingsManager = [];
    public $deleteAssignBMA = [
        'bma_id' => '',
        'bma_name' => '',

    ];


    public $currentPage = [];
    public function currentPage($data)
    {
        $this->currentPage[$data['functionName']] = $data['page'];
    }


    public function mount($projectID = null)
    {
        App::setLocale('en');
        Session::put('locale', 'en');
        $this->currency = Helper::currency();
        $this->workspaceSlug = auth()->user()->workspace ?? 'default-workspace';

        if (!$projectID) {

            return redirect()->route('CRMProjects.list');
        }

        $this->projectID = $projectID;

        $this->fetchData();

        $this->milestoneData = $this->projectData['milestones'] ?? [];
        $project_setting = $this->projectData['project_setting'] ?? [];
        foreach ($this->settingsState as $key => $value) {
            if (isset($project_setting[$key])) {
                if ($project_setting[$key] == 'on') {
                    $this->settingsState[$key] = 1;
                } else {
                    $this->settingsState[$key] = 0;
                }
            } else {
                $this->settingsState[$key] = 0;
            }
        }

        $this->usersAlreadyInvited =  collect($this->projectData['users'] ?? [])->pluck('email')->toArray();
        $this->clientssAlreadyInProject =  collect($this->projectData['clients'] ?? [])->pluck('email')->toArray();
        $this->vendorsAlreadyInProject =  collect($this->projectData['vendors'] ?? [])->pluck('email')->toArray();
    }

    public function fetchData()
    {
        try {

            $client = new Client(['base_uri' => env('CRM_API_BASE_URL'), 'verify' => false]);
            $response = $client->get("/api/public-project-details/$this->projectID", [
                'headers' => [
                    'Accept' => 'application/json',
                ]
            ]);
            $response = json_decode($response->getBody(), true);

            if (isset($response['status']) && $response['status'] === 'success') {
                $this->projectData = $response['data']['project'];
                $this->chartData = $response['data']['chartData'];
                $this->daysleft = $response['data']['daysleft'];
                $project_setting = $response['data']['project']['project_setting'];
                $this->invoices = $response['data']['invoices'];
                $this->bills = $response['data']['bills'];
                $this->retainers = $response['data']['retainers'];
                $this->proposals = $response['data']['proposals'];
                $this->documents = $this->projectData['documents'];
                $this->rfxs = $response['data']['rfxs'];
                $this->rfxApplications = $response['data']['rfxApplications'];
                $this->rfxApplicants = $response['data']['rfxApplicants'];
                $this->vendorOnBoards = $response['data']['vendorOnBoards'];
                $this->arrSchedule = $response['data']['arrSchedule'];
                $this->stages = $response['data']['stages'];
                $this->bug_stages = $response['data']['bug_stages'];


                foreach ($this->settingsState as $key => $value) {
                    if (isset($project_setting[$key])) {
                        if ($project_setting[$key] == 'on') {
                            $this->settingsState[$key] = 1;
                        } else {
                            $this->settingsState[$key] = 0;
                        }
                    } else {
                        $this->settingsState[$key] = 0;
                    }
                }

                $this->milestoneData = $response['data']['project']['milestones'];
            }
        } catch (\Exception $e) {

            // dd($e->getMessage());
        }
    }

    public function render()
    {

        return view(
            'livewire.c-r-m-projects.public-project-details',
            [
                'projectData' => $this->projectData,
                'settingsState' => $this->settingsState,
                'daysleft' => $this->daysleft,
                'milestones_list' => $this->milestoneData,
                'files_list' => $this->fileData,
                'chartData' => $this->chartData

            ]
        );
    }
}

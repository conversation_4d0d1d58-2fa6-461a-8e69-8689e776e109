<div class="userDatatable projectDatatable project-table bg-white w-100 border-0">
    @if($error)
        <div class="alert alert-danger mx-3 mt-3" role="alert">
            <div class="d-flex align-items-center">
                <i class="iconsax icon fs-22 mr-2" icon-name="warning-2"></i>
                <div>
                    <strong>{{ __('customers.status.error') }}</strong> {{ $error }}
                    <button class="btn btn-sm btn-outline-danger ml-2" wire:click="fetchCustomers">
                        <i class="iconsax icon fs-16" icon-name="refresh-2"></i> {{ __('customers.buttons.retry') }}
                    </button>
                </div>
            </div>
        </div>
    @endif

    <div class="table-responsive">
        <table class="table mb-0 radius-0 th-osool">
            <thead>
                <tr class="userDatatable-header">
                    <th wire:click="sortBy('customer_id')" style="cursor: pointer;" title="{{ __('customers.table.sort_by_customer_id') }}">
                        <i class="iconsax icon fs-22" icon-name="{{ $sortField === 'customer_id' ? ($sortDirection === 'asc' ? 'arrow-up-2' : 'arrow-down-2') : 'swap-vertical-circle' }}"></i> {{ __('customers.common.customer_id') }}
                    </th>
                    <th wire:click="sortBy('name')" style="cursor: pointer;" title="{{ __('customers.table.sort_by_name') }}">
                        <i class="iconsax icon fs-22" icon-name="{{ $sortField === 'name' ? ($sortDirection === 'asc' ? 'arrow-up-2' : 'arrow-down-2') : 'swap-vertical-circle' }}"></i> {{ __('customers.common.name') }}
                    </th>
                    <th wire:click="sortBy('phone')" style="cursor: pointer;" title="{{ __('customers.table.sort_by_contact') }}">
                        <i class="iconsax icon fs-22" icon-name="{{ $sortField === 'phone' ? ($sortDirection === 'asc' ? 'arrow-up-2' : 'arrow-down-2') : 'swap-vertical-circle' }}"></i> {{ __('customers.common.contact') }}
                    </th>
                    <th wire:click="sortBy('balance')" style="cursor: pointer;" title="{{ __('customers.table.sort_by_balance') }}">
                        <i class="iconsax icon fs-22" icon-name="{{ $sortField === 'balance' ? ($sortDirection === 'asc' ? 'arrow-up-2' : 'arrow-down-2') : 'swap-vertical-circle' }}"></i> {{ __('customers.common.balance') }}
                    </th>
                    <th wire:click="sortBy('email')" style="cursor: pointer;" title="{{ __('customers.table.sort_by_email') }}">
                        <i class="iconsax icon fs-22" icon-name="{{ $sortField === 'email' ? ($sortDirection === 'asc' ? 'arrow-up-2' : 'arrow-down-2') : 'swap-vertical-circle' }}"></i> {{ __('customers.common.email') }}
                    </th>
                    <th>
                        {{ __('customers.common.action') }}
                    </th>
                </tr>
            </thead>
            <tbody class="sort-table ui-sortable">
                @if($loading)
                    <tr>
                        <td colspan="6" class="text-center py-5">
                            <div class="d-flex flex-column align-items-center">
                                <div class="spinner-border text-primary mb-3" role="status">
                                    <span class="sr-only">{{ __('customers.status.loading') }}</span>
                                </div>
                                <h6 class="text-muted">{{ __('customers.common.loading_customers') }}</h6>
                            </div>
                        </td>
                    </tr>
                @else
                    @forelse($customers as $customer)
                    <tr class="ui-sortable-handle">
                        <td>
                            <div class="d-flex userDatatable-content mb-0 align-items-center">
                                <div>
                                    <a href="{{ route('finance.customers.show', $customer['id']) }}" class="text-primary fw-500">
                                        {{ $customer['display_id'] ?? $customer['customer_id'] ?? $customer['id'] }}
                                    </a>
                                </div>
                            </div>
                        </td>
                        <td>
                            <div class="d-flex userDatatable-content mb-0 align-items-center">
                                <div>
                                    <span>{{ $customer['name'] }}</span>
                                </div>
                            </div>
                        </td>
                        <td>
                            <div class="d-flex align-items-center gap-10">
                                <span class="">{{ $customer['phone'] }}</span>
                            </div>
                        </td>
                        <td>
                            <div class="d-flex userDatatable-content mb-0 align-items-center">
                                <span class="fw-500">{{ number_format($customer['balance'] ?? 0, 2) }}</span>
                            </div>
                        </td>
                        <td>
                            <div class="d-flex userDatatable-content mb-0 align-items-center">
                                <span>{{ $customer['email'] }}</span>
                            </div>
                        </td>
                        <td>
                            <div class="d-inline-block">
                                <ul class="mb-0 d-flex gap-10">
                                    <li>
                                        <a wire:click="goToView('{{ Crypt::encrypt($customer['id']) }}')">
                                            <i class="iconsax icon text-osool fs-18" icon-name="eye"></i>
                                        </a>
                                    </li>
                                    <li>
                                        <a href="javascript:void(0);" wire:click="openEditModal({{ $customer['id'] }})">
                                            <i class="iconsax icon text-new-primary fs-18" icon-name="edit-1"></i>
                                        </a>
                                    </li>
                                    <li>
                                        <a href="javascript:void(0);" wire:click="openDeleteModal({{ $customer['id'] }}, '{{ $customer['name'] }}')">
                                            <i class="iconsax icon text-delete fs-18" icon-name="trash"></i>
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </td>
                    </tr>
                    @empty
                        <tr>
                            <td colspan="6" class="text-center py-4">
                                <div class="d-flex flex-column align-items-center">
                                    <i class="iconsax icon fs-48 text-muted mb-3" icon-name="user-search"></i>
                                    <h6 class="text-muted">{{ __('customers.common.no_customers_found') }}</h6>
                                    @if($search)
                                        <p class="text-muted">{{ __('customers.messages.try_adjusting_search') }}</p>
                                    @endif
                                </div>
                            </td>
                        </tr>
                    @endforelse
                @endif
            </tbody>
        </table>
    </div>
</div>

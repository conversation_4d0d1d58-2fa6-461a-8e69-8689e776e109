@if ($paginator->hasPages())
    <nav class="d-flex justify-content-center">
        <ul class="pagination mb-0">
            {{-- Previous Page Link --}}
            @if ($paginator->onFirstPage())
                <li class="page-item disabled">
                    <span class="page-link">&laquo;</span>
                </li>
            @else
                <li class="page-item">
                    <a href="#" 
                       wire:click.prevent="gotoPage({{ $paginator->currentPage() - 1 }})" 
                       class="page-link" 
                       rel="prev">&laquo;</a>
                </li>
            @endif

            {{-- Page Links (max 3 pages) --}}
            @php
                $current = $paginator->currentPage();
                $last = $paginator->lastPage();
                $start = max($current - 1, 1);
                $end = min($start + 2, $last);

                if ($end - $start < 2) {
                    $start = max($end - 2, 1);
                }
            @endphp

            @for ($i = $start; $i <= $end; $i++)
                @if ($i == $current)
                    <li class="page-item active">
                        <span class="page-link">{{ $i }}</span>
                    </li>
                @else
                    <li class="page-item">
                        <a href="#" wire:click.prevent="gotoPage({{ $i }})" class="page-link">{{ $i }}</a>
                    </li>
                @endif
            @endfor

            {{-- Next Page Link --}}
            @if ($paginator->hasMorePages())
                <li class="page-item">
                    <a href="#" 
                       wire:click.prevent="gotoPage({{ $paginator->currentPage() + 1 }})" 
                       class="page-link" 
                       rel="next">&raquo;</a>
                </li>
            @else
                <li class="page-item disabled">
                    <span class="page-link">&raquo;</span>
                </li>
            @endif
        </ul>
    </nav>
@endif

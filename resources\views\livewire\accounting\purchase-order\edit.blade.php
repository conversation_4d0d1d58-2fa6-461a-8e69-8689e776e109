<div>
    @if($isLoading)
        <div class="text-center py-4">
            <div class="spinner-border text-primary" role="status">
                <span class="sr-only">@lang('purchase.edit.form.loading')</span>
            </div>
        </div>
    @else
        <div class="contents crm">
            <div class="container-fluid">
                <div class="col-lg-12">
                    <div class="row justify-content-sm-between align-items-center justify-content-center my-3 flex-sm-row flex-column">
                        <div class="page-title-wrap p-0">
                            <div class="page-title d-flex justify-content-between">
                                <div class="page-title__left justify-content-sm-between align-items-center justify-content-center">
                                    <div class="user-member__title mr-sm-25 ml-0">
                                        <h4 class="text-capitalize fw-500 breadcrumb-title fs-16">
                                            @lang('purchase.edit.title')
                                        </h4>
                                    </div>
                                </div>
                            </div>
                            <div>
                                <ul class="atbd-breadcrumb nav">
                                    <li class="atbd-breadcrumb__item">
                                        <a>@lang('purchase.edit.breadcrumb.dashboard')</a>
                                        <span class="breadcrumb__seperator">
                                            <span class="la la-angle-right"></span>
                                        </span>
                                    </li>
                                    <li class="atbd-breadcrumb__item">
                                        <a>@lang('purchase.edit.breadcrumb.purchase_order')</a>
                                        <span class="breadcrumb__seperator">
                                            <span class="la la-angle-right"></span>
                                        </span>
                                    </li>
                                    <li class="atbd-breadcrumb__item">
                                        <a>@lang('purchase.edit.breadcrumb.edit')</a>
                                    </li>
                                </ul>
                            </div>
                        </div>

                        <div class="d-flex gap-10 breadcrumb_right_icons">
                            <button class="btn btn-default btn-primary no-wrap"
                                    wire:click="submit"
                                    wire:loading.attr="disabled">
                                    <i class="las la-save fs-16"></i> @lang('purchase.edit.form.save')
                            </button>
                        </div>
                    </div>
                </div>
                @if (session()->has('message'))
                    <div class="alert alert-danger">
                        {{ session('message') }}
                    </div>
                @endif
                @if ($errors->has('form_error'))
                    <div class="alert alert-danger">
                        {{ $errors->first('form_error') }}
                    </div>
                @endif
                <div class="card">
                    <div class="card-header d-flex justify-content-start gap-5 text-osool border-0">
                        <h6>@lang('purchase.edit.title') - {{ $purchaseNumber }}</h6>
                    </div>

                    <div class="px-4">
                        <div class="row">
                            <div class="col-lg-3">
                                <div class="form-group">
                                    <label class="form-label text-dark fw-600">@lang('purchase.edit.form.invoice_number')</label>
                                    <input class="form-control" value="{{ $purchaseNumber }}" disabled />
                                </div>
                            </div>

                            <div class="col-lg-4">
                                <div class="form-group">
                                    <label class="form-label text-dark fw-600">@lang('purchase.edit.form.billing_type')</label>
                                    <select class="form-control" wire:model="formData.billing_type" required>
                                        <option value="">@lang('purchase.edit.form.choose_option')</option>
                                        @foreach($dropdownData['billing_types'] as $key => $value)
                                            <option value="{{ $key }}" @if($key == $formData['billing_type']) selected @endif>
                                                {{ $value }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>

                            <div class="col-lg-4">
                                <div class="form-group">
                                    <label class="form-label text-dark fw-600">@lang('purchase.edit.form.vendor')</label>
                                    <select class="form-control" wire:model="formData.vendor_id" required>
                                        <option value="">@lang('purchase.edit.form.choose_option')</option>
                                        @foreach($dropdownData['vendors'] as $vendor)
                                            <option value="{{ $vendor['id'] }}" @if($vendor['id'] == $formData['vendor_id']) selected @endif>
                                                {{ $vendor['name'] }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>

                            <div class="col-lg-4">
                                <div class="form-group">
                                    <label class="form-label text-dark fw-600">@lang('purchase.edit.form.warehouse')</label>
                                    <select class="form-control" wire:model="formData.warehouse_id" required>
                                        <option value="">@lang('purchase.edit.form.choose_option')</option>
                                        @foreach($dropdownData['warehouses'] as $warehouse)
                                            <option value="{{ $warehouse['id'] }}" @if($warehouse['id'] == $formData['warehouse_id']) selected @endif>
                                                {{ $warehouse['name'] }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>

                            <div class="col-lg-4">
                                <div class="form-group">
                                    <label class="form-label text-dark fw-600">@lang('purchase.edit.form.category')</label>
                                    <select class="form-control" wire:model="formData.category_id" required>
                                        <option value="">@lang('purchase.edit.form.choose_option')</option>
                                        @foreach($dropdownData['categories'] as $category)
                                            <option value="{{ $category['id'] }}" @if($category['id'] == $formData['category_id']) selected @endif>
                                                {{ $category['name'] }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>

                            <div class="col-lg-2">
                                <div class="form-group">
                                    <label class="form-label text-dark fw-600">@lang('purchase.edit.form.purchase_date')</label>
                                    <input type="date" class="form-control" wire:model="formData.purchase_date" required>
                                </div>
                            </div>

                            <div class="col-lg-2">
                                <div class="form-group">
                                    <label class="form-label text-dark fw-600">@lang('purchase.edit.form.due_date')</label>
                                    <input type="date" class="form-control" wire:model="formData.due_date" required>
                                </div>
                            </div>

                            <div class="col-lg-2">
                                <div class="form-group">
                                    <label class="form-label text-dark fw-600">@lang('purchase.edit.form.status')</label>
                                    <select class="form-control" wire:model="formData.status">
                                        @foreach(array_values($dropdownData['statuses']) as $key => $value)
                                            <option value="{{ $key }}" @if($key == $formData['status']) selected @endif>
                                                {{ $value }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card mt-3 px-4 py-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <h6>@lang('purchase.edit.form.item_details')</h6>
                        <button class="btn btn-default btn-primary no-wrap" wire:click="addItem">
                            <i class="iconsax icon fs-22 text-white mr-2" icon-name="add"></i>
                            @lang('purchase.edit.form.add_item')
                        </button>
                    </div>

                    <div class="table-responsive mt-4">
                        <table class="table mb-0 radius-0 th-osool">
                            <thead>
                            <tr class="userDatatable-header">
                                <th>@lang('purchase.edit.form.item')</th>
                                <th>@lang('purchase.edit.form.quantity')</th>
                                <th>@lang('purchase.edit.form.price')</th>
                                <th>@lang('purchase.edit.form.discount') (%)</th>
                                <th>@lang('purchase.edit.form.tax') (%)</th>
                                <th>@lang('purchase.edit.form.description')</th>
                                <th>Amount</th>
                                <th></th>
                            </tr>
                            </thead>
                            <tbody>
                            @foreach($formData['items'] as $index => $item)
                                <tr>
                                    <td>
                                        <select class="form-control" wire:model="formData.items.{{ $index }}.item" required>
                                            <option value="">@lang('purchase.edit.form.choose_option')</option>
                                            @foreach($dropdownData['items'] as $product)
                                                <option value="{{ $product['id'] }}"
                                                        @if($product['id'] == $item['item']) selected @endif>
                                                    {{ $product['name'] }}
                                                </option>
                                            @endforeach
                                        </select>
                                        <select class="form-control mt-2" wire:model="formData.items.{{ $index }}.product_type">
                                            @foreach($dropdownData['item_types'] as $key => $value)
                                                <option value="{{ $key }}"
                                                        @if($key == ($item['product_type'] ?? 'product')) selected @endif>
                                                    {{ $value }}
                                                </option>
                                            @endforeach
                                        </select>
                                    </td>
                                    <td>
                                        <input type="number" class="form-control"
                                               wire:model="formData.items.{{ $index }}.quantity"
                                               min="1" required>
                                    </td>
                                    <td>
                                        <input type="number" step="0.01" class="form-control"
                                               wire:model="formData.items.{{ $index }}.price"
                                               min="0" required>
                                    </td>
                                    <td>
                                        <input type="number" step="0.01" class="form-control"
                                               wire:model="formData.items.{{ $index }}.discount"
                                               min="0">
                                    </td>
                                    <td>
                                        <input type="number" step="0.01" class="form-control"
                                               wire:model="formData.items.{{ $index }}.tax"
                                               min="0">
                                    </td>
                                    <td>
                                            <textarea class="form-control"
                                                      wire:model="formData.items.{{ $index }}.description"></textarea>
                                    </td>
                                    <td>
                                        ${{ number_format(floatval($item['quantity']) * floatval($item['price']), 2) }}
                                    </td>
                                    <td>
                                        <button class="btn btn-sm btn-danger"
                                                wire:click="removeItem({{ $index }})">
                                            @lang('purchase.edit.form.remove_item')
                                        </button>
                                    </td>
                                </tr>
                            @endforeach
                            </tbody>
                        </table>
                    </div>

                    <!-- Totals Section -->
                    <div class="text-right p-3">
                        <div class="my-2">@lang('purchase.edit.form.sub_total'): ${{ number_format($subTotal, 2) }}</div>
                        <div class="mb-4">@lang('purchase.edit.form.total_discount'): ${{ number_format($totalDiscount, 2) }}</div>
                        <div class="fw-bold">@lang('purchase.edit.form.total'): ${{ number_format($grandTotal, 2) }}</div>
                    </div>
                </div>
            </div>
        </div>
    @endif
</div>

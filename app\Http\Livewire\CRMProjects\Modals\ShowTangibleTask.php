<?php

namespace App\Http\Livewire\CRMProjects\Modals;

use Livewire\Component;
use App\Models\ServiceRequest;
use App\Models\ServiceRequestImage;
use App\Http\Helpers\ImagesUploadHelper;
use App\Models\VendorServiceCategory;
use App\Services\CRM\Projects\ProjectTemplateService;
use App\Services\CRM\Sales\ProjectService;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Contracts\Encryption\DecryptException;
class ShowTangibleTask extends Component
{

     public $projectId, $itemId;
     public $tangibledetails = [
        'project' => '',
        'created_at' => '',
        'milestone' => '',
        'title' => '',
        'priority' => '',
        'assign_to' => '',
        'stage' => '',
        'startDate' => '',
        'endDate' => '',
        'description' => '',
        'workorder_id' => '',
        'workorder_type' => 'reactive',
    ];

    
   public function mount()
    {
        try {
            $this->projectId = decrypt(request()->id);
        } catch (DecryptException $e) {
            return abort(404);
        }
    }
    protected $listeners = ['showTangibleTask'];
    public function render()
    {
        return view('livewire.c-r-m-projects.modals.show-tangible-task');
    }
    public function showTangibleTask($id){
          $this->itemId = $id;
           $this->fetchData();
       $this->dispatchBrowserEvent('open-modal', ['modalId' => 'showTangibleTask']);
        $this->dispatchBrowserEvent('hideLoader');
    }

      public function fetchData()
    {
        $service =  app(ProjectService::class);
        $response = $service->taskData($this->projectId, $this->itemId);
     /*    dd($response ); */
        if (@$response['status'] == "success") {
            $data = $response['data'];
            $this->tangibledetails['project']  = $data['project_id'];
            $this->tangibledetails['milestone'] = $data['milestone_id'];
            $this->tangibledetails['title'] = $data['title'];
            $this->tangibledetails['priority'] = $data['priority'];
            $this->tangibledetails['assign_to'] = $data['assign_to'];
            $this->tangibledetails['stage'] = $data['status'];
            $this->tangibledetails['workorder_type'] = $data['workorder_type'];
            $this->tangibledetails['workorder_id'] = $data['workorder_id'];
            $this->tangibledetails['description'] = $data['description'] ?? '';
            $this->tangibledetails['created_at'] = Carbon::parse($data['created_at'])->format('Y-m-d');
            $this->tangibledetails['startDate'] = Carbon::parse($data['start_date'])->format('Y-m-d');
            $this->tangibledetails['endDate'] = Carbon::parse($data['due_date'])->format('Y-m-d h:i A');
        }
    }
}

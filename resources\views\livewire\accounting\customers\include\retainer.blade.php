 <div class="tab-pane fade show active" id="customer-retainer" role="tabpanel" aria-labelledby="contact-tab">
    <div class="card">
    <div class="">
        <div class="card-header py-4 px-3 border-0 d-flex justify-content-between align-items-center">
            <h6 class="text-capitalize text-osool fw-500 mb-3 mb-sm-0">{{ __('accounting.customers.tabs.retainer') }}</h6>
            <div class="d-flex gap-10 table-search">
                <div class="position-relative">
                    <input type="text" class="form-control" placeholder="{{ __('accounting.customers.forms.search_placeholder') }}">
                    <i class="iconsax field-icon fs-18 mr-0" icon-name="search-normal-2"></i>
                </div>
                <!-- <button class="btn btn-export text-dark"><i class="iconsax icon fs-22 mr-0" icon-name="upload-1"></i> Export</button> -->
            </div>
        </div>
    </div>
    <div class="card-body px-0 pt-0">
        <div class="userDatatable projectDatatable project-table bg-white w-100 border-0">
            <div class="table-responsive">
                <table class="table mb-0 radius-0 th-osool">
                    <thead>
                        <tr class="userDatatable-header">
                            <th>
                                {{ __('accounting.customers.table.headers.retainer') }}
                            </th>
                            <th><i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i> {{ __('accounting.customers.table.headers.customer') }}</th>
                            <th><i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i> {{ __('accounting.customers.table.headers.issue_date') }}</th>
                            <th><i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i> {{ __('accounting.customers.table.headers.amount') }}</th>
                            <th><i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i> {{ __('accounting.customers.table.headers.status') }}</th>
                            <th><i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i> {{ __('accounting.customers.table.headers.action') }}</th>
                        </tr>
                    </thead>
                    <tbody class="sort-table ui-sortable">
                        <tr class="ui-sortable-handle" style="opacity: 1;">
                            <td>
                                <div class="userDatatable-inline-title">
                                    <span>Mohammad Khadeer</span>
                                </div>
                            </td>
                            <td>
                                <div class="userDatatable-inline-title">
                                    <span>Mohammad Adil</span>
                                </div>
                            </td>
                            <td>
                                <div class="d-flex userDatatable-content mb-0 align-items-center">
                                    <span>09-09-2025</span>
                                </div>
                            </td>
                            <td>
                                <div class="d-flex align-items-center gap-10">
                                    <img src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSx34eO8RbZlsetN1iAQtxuOLnLOlyJbJmU7Q&amp;s" width="15" /><span class="text-new-primary">25000</span>
                                </div>
                            </td>
                            <td>
                                <span class="badge-new bg-opacity-loss text-loss rounded">{{ __('accounting.customers.status.drafts') }}</span>
                            </td>
                            <td>
                                <div class="d-inline-block">
                                    <ul class="mb-0 d-flex gap-10">
                                        <li>
                                            <a href="" title="{{ __('accounting.customers.actions.convert_to_invoice') }}">
                                                <i class="iconsax icon text-osool fs-18" icon-name="change-shape-1"></i>
                                            </a>
                                        </li>
                                        <li>
                                            <a href="">
                                                <i class="iconsax icon text-osool fs-18" icon-name="eye"></i>
                                            </a>
                                        </li>
                                        <li>
                                            <a href="javascript:void(0);" wire:click="openEditModal(588)">
                                                <i class="iconsax icon text-new-primary fs-18" icon-name="edit-1"></i>
                                            </a>
                                        </li>
                                        <li>
                                            <a href="javascript:void(0);" wire:click="openDeleteModal(588, 'Beyond Tech1 Copy test task')">
                                                <i class="iconsax icon text-delete fs-18" icon-name="trash"></i>
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                            </td>
                        </tr>
                    
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <div class="card-body pt-0">
        <div class="d-flex justify-content-md-between flex-column flex-md-row justify-content-center align-items-center mt-4 gap-10">
            <div class="">
                <ul class="atbd-pagination d-flex justify-content-between">
                    <li>
                        <div class="paging-option">
                            <div class="dataTables_length d-flex">
                                <label class="d-flex align-items-center mb-0">
                                    <select aria-controls="workorder_table" wire:model.live.debounce.250ms="perPage" class="custom-select custom-select-sm form-control form-control-sm mx-2" style="min-height: 35px;">
                                        <option value="5">5</option>
                                        <option value="10">10</option>
                                        <option value="25">25</option>
                                        <option value="50">50</option>
                                        <option value="100">100</option>
                                    </select>
                                    <span class="no-wrap"> {{ __('accounting.customers.pagination.entries_per_page') }} </span>
                                </label>
                            </div>
                        </div>
                    </li>
                </ul>
            </div>
            <div class="">
                <div class="user-pagination">
                    <div class="user-pagination new-pagination">
                        <div class="d-flex justify-content-sm-end justify-content-end">
                            <nav>
                                <span class="relative z-0 inline-flex rounded-md d-flex new-pagination-section flex-wrap">
                                    <span class="">
                                        <span aria-disabled="true" aria-label="&amp;laquo; Previous">
                                            <button class="border-0 disabled" aria-hidden="true" disabled="">
                                                <i class="iconsax icon-bold fs-16 mr-0 rotate-ar-y d-inline-block" icon-name="chevron-left"></i>
                                            </button>
                                        </span>
                                    </span>

                                    <span wire:key="paginator-page-1-page1">
                                        <button class="border-0 current-page" disabled="">1</button>
                                    </span>
                                    <span wire:key="paginator-page-1-page2">
                                        <button type="button" class="border-0">2</button>
                                    </span>
                                    <span wire:key="paginator-page-1-page3">
                                        <button type="button" class="border-0">3</button>
                                    </span>
                                    <span wire:key="paginator-page-1-page4">
                                        <button type="button" class="border-0">4</button>
                                    </span>
                                    <span wire:key="paginator-page-1-page5">
                                        <button type="button" class="border-0">5</button>
                                    </span>
                                    <span wire:key="paginator-page-1-page6">
                                        <button type="button" class="border-0">6</button>
                                    </span>
                                    <span wire:key="paginator-page-1-page7">
                                        <button type="button" class="border-0">7</button>
                                    </span>
                                    <span wire:key="paginator-page-1-page8">
                                        <button type="button" class="border-0">8</button>
                                    </span>
                                    <span wire:key="paginator-page-1-page9">
                                        <button type="button" class="border-0">9</button>
                                    </span>

                                    <span>
                                        <button type="button" class="border-0">
                                            <i class="iconsax icon-bold fs-16 mr-0 rotate-ar-y d-inline-block" icon-name="chevron-right"></i>
                                        </button>
                                    </span>
                                </span>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
            <div>
                <p class="text-sm text-gray-700 leading-5 mb-0">
                    <span>{{ __('accounting.customers.pagination.showing') }}</span>
                    <span class="font-medium">1</span>
                    <span>{{ __('accounting.customers.pagination.to') }}</span>
                    <span class="font-medium">6</span>
                    <span>{{ __('accounting.customers.pagination.of') }}</span>
                    <span class="font-medium">52</span>
                    <span>{{ __('accounting.customers.pagination.results') }}</span>
                </p>
            </div>
        </div>
    </div>
</div>
  </div>
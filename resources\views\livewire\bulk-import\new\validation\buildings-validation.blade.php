<div>
    <div class = "userDatatable projectDatatable project-table w-100">
        <div class = "d-flex gap-10 pb-3 mb-3 justify-content-between">
            <div class = "d-flex gap-5 align-items-center">
                <div class = "d-flex gap-5 align-items-center mx-2">
                    <i class = "iconsax text-success fs-16" icon-name = "clipboard-tick"></i> 
                    <span class = "text-success">{{ $buildingsList->total() }} @lang('import.inserted_rows')</span>
                </div>
                <div class = "d-flex gap-5 align-items-center">
                    <i class = "iconsax text-warning fs-16" icon-name = "info-circle"></i> 
                    <span class = "text-warning">{{ $dataErrorsCount }} @lang('import.data_issues')</span>
                </div>
                <div class = "d-flex gap-5 align-items-center">
                    <i class = "iconsax text-danger fs-16" icon-name = "info-circle"></i> 
                    <span class = "text-danger">{{ $systemErrorsCount }} @lang('import.system_errors')</span>
                </div>
            </div>
            <div>
                <button type = "button" class = "btn btn-danger mx-2" data-toggle = "modal" data-target = "#confirm-delete-buildings">
                    @lang('import.delete_inserted_buildings')
                </button>
            </div>
        </div>
    </div>
    <div class = "row">
        <div class = "col-sm-6 mb-3 mb-sm-0">
            <div class = "card">
                <div class = "card-body">
                    <h5 class = "card-title">@lang('import.inserted_rows')</h5>
                    <p class = "card-text">@lang('import.buildings_inserted_text')</p>
                    <div class = "table-responsive" id = "buildings-table">
                        <table class = "table mb-0">
                            <thead>
                                <tr>
                                    <th scope = "col">
                                        <span style = "font-size:11px">@lang("import.property_text")</span>
                                    </th>
                                    <th scope = "col">
                                        <span style = "font-size:11px">@lang("import.building_name")</span>
                                    </th>
                                    <th scope = "col">
                                        <span style = "font-size:11px">@lang("import.zone")</span>
                                    </th>
                                    <th scope = "col" class = "extra-col d-none">
                                        <span style = "font-size:11px">@lang("import.unit")</span>
                                    </th>
                                    <th scope = "col" class = "extra-col d-none">
                                        <span style = "font-size:11px">@lang("import.unit_type")</span>
                                    </th>
                                     <th scope = "col" class = "extra-col d-none">
                                        <span style = "font-size:11px">@lang("import.rooms_count")</span>
                                    </th>
                                     <th>
                                        <span id = "show-action-btn5" class = "mx-1" style = "text-decoration: underline; cursor: pointer;">
                                            @lang('import.more')
                                        </span>
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                @if(isset($buildingsList) && $buildingsList->count())
                                    @foreach($buildingsList as $key => $data)
                                        <tr wire:key = "buildings-{{ $key }}">
                                            <td>
                                                <p style = "font-size:11px">{{ $data->property->property_tag ?? '-' }}</p>
                                            </td>
                                            <td>
                                                <p style = "font-size:11px">{{ $data->building_name ?? '-' }}</p>
                                            </td>
                                            <td>
                                                <p style = "font-size:11px">{{ $data->room->floor ?? '-' }}</p>
                                            </td>
                                            <td class = "extra-row d-none">
                                                <p style = "font-size:11px">{{ $data->room->room ?? '-'  }}</p>
                                            </td>
                                            <td class = "extra-row d-none">
                                                <p style = "font-size:11px">{{ $data->room->roomType->room_type ?? '-'  }}</p>
                                            </td>
                                            <td class = "extra-row d-none">
                                                <p style = "font-size:11px">{{ $data->rooms_count ?? '-'  }}</p>
                                            </td>
                                        </tr>
                                    @endforeach

                                    @if ($buildingsList->hasMorePages())
                                        <tr>
                                            <td colspan = "8">
                                                <div class = "d-flex justify-content-center gap-2">
                                                    <div class = "p-2">
                                                        <div wire:loading wire:target = "managePerPageList">
                                                            <button type = "button" class = "btn btn-primary" wire:loading.attr = "disabled">
                                                                <span class = "spinner-border spinner-border-sm" role = "status" aria-hidden = "true"></span>
                                                                @lang('import.loading3')
                                                            </button>
                                                        </div>
                                                        <button type = "button" class = "btn btn-primary text-center" wire:target = "managePerPageList" wire:click = "managePerPageList" wire:loading.class = "hide">
                                                            @lang('import.load_more')
                                                        </button>
                                                    </div>
                                                    <div class = "p-2">
                                                        <div wire:loading wire:target = "manageLoadAllList">
                                                            <button type = "button" class = "btn btn-info" wire:loading.attr = "disabled">
                                                                <span class = "spinner-border spinner-border-sm" role = "status" aria-hidden = "true"></span>
                                                                @lang('import.loading3')
                                                            </button>
                                                        </div>
                                                        <button type = "button" class = "btn btn-info" wire:target = "manageLoadAllList" wire:click = "manageLoadAllList({{ $buildingsList->total() }})" wire:loading.class = "hide">
                                                            @lang('import.load_all')
                                                        </button>
                                                    </div>
                                                </div>
                                            </td>
                                        </tr>
                                    @endif
                                @else
                                    <tr class = "text-center">
                                        <td colspan = "7">@lang("import.empty_property_building")</td>
                                    </tr>
                                @endif
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <div class = "col-sm-6 mb-3 mb-sm-0">
            <div class = "card">
                <div class = "card-body">
                    <h5 class = "card-title">@lang('import.errors_list')</h5>
                    <p class = "card-text">@lang('import.buildings_errors_text')</p>
                    <div class = "table-responsive">
                        <table class = "table mb-0">
                            <thead>
                                <tr>
                                    <th scope = "col">
                                        <span style = "font-size:11px">@lang("import.identifier")</span>
                                    </th>
                                    <th scope = "col">
                                        <span style = "font-size:11px">@lang("import.value")</span>
                                    </th>
                                    <th scope = "col">
                                        <span style = "font-size:11px">@lang("import.error")</span>
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                @if(isset($errorsList) && $errorsList->count())
                                    @foreach($errorsList as $key => $data)
                                        <tr wire:key = "errors-properties-{{ $key }}"> 
                                            <td>
                                                <p style = "font-size:11px">{{ $data->identifier ?? '-' }}</p>
                                            </td>
                                            <td>
                                                <p style = "font-size:11px">{{ $data->value ?? '-' }}</p>
                                            </td>
                                            <td>
                                                <p style = "font-size:11px">
                                                    @if(isset($data->errors))
                                                        @switch($data->errors->value)
                                                            @case(\App\Enums\ValidationBukImport::PropertyNotExist->value)
                                                                @lang('validation_bulk_import_step.property_not_exist')
                                                            @break

                                                            @case(\App\Enums\ValidationBukImport::RoomTypeFloorNotUpdated->value)
                                                                @lang('validation_bulk_import_step.room_type_floor_not_updated')
                                                            @break

                                                            @case(\App\Enums\ValidationBukImport::RoomTypeFloorNotSaved->value)
                                                                @lang('validation_bulk_import_step.room_type_floor_not_saved')
                                                            @break

                                                            @case(\App\Enums\ValidationBukImport::RoomTypeNotSaved->value)
                                                                @lang('validation_bulk_import_step.room_type_not_saved')
                                                            @break

                                                            @case(\App\Enums\ValidationBukImport::PropertyBuildingNotSaved->value)
                                                                @lang('validation_bulk_import_step.property_building_not_saved')
                                                            @break

                                                            @default
                                                                -
                                                            @break
                                                        @endswitch
                                                    @elseif((isset($data->errors) || !isset($data->errors)) && !$data->backend_status)
                                                        @lang('import.data_issue_displayed')
                                                    @else
                                                        -
                                                    @endif
                                                </p>
                                            </td>
                                        </tr>
                                    @endforeach

                                    @if ($errorsList->hasMorePages())
                                        <tr>
                                            <td colspan = "3">
                                                <div class = "d-flex justify-content-center gap-2">
                                                    <div class = "p-2">
                                                        <div wire:loading wire:target = "managePerPage">
                                                            <button type = "button" class = "btn btn-primary" wire:loading.attr = "disabled">
                                                                <span class = "spinner-border spinner-border-sm" role = "status" aria-hidden = "true"></span>
                                                                @lang('import.loading3')
                                                            </button>
                                                        </div>
                                                        <button type = "button" class = "btn btn-primary text-center" wire:target = "managePerPage" wire:click = "managePerPage" wire:loading.class = "hide">
                                                            @lang('import.load_more')
                                                        </button>
                                                    </div>
                                                    <div class = "p-2">
                                                        <div wire:loading wire:target = "manageLoadAll">
                                                            <button type = "button" class = "btn btn-info" wire:loading.attr = "disabled">
                                                                <span class = "spinner-border spinner-border-sm" role = "status" aria-hidden = "true"></span>
                                                                @lang('import.loading3')
                                                            </button>
                                                        </div>
                                                        <button type = "button" class = "btn btn-info" wire:target = "manageLoadAll" wire:click = "manageLoadAll({{ $errorsList->total() }})" wire:loading.class = "hide">
                                                            @lang('import.load_all')
                                                        </button>
                                                    </div>
                                                </div>
                                            </td>
                                        </tr>
                                    @endif
                                @else
                                    <tr class = "text-center">
                                        <td colspan = "3">@lang("import.empty_errors")</td>
                                    </tr>
                                @endif
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class = "modal fade delete" id = "confirm-delete-buildings" tabindex = "-1" role = "dialog" aria-labelledby = "deleteModalLabel" aria-hidden = "true" wire:ignore.self>
        <div class = "modal-dialog modal-sm modal-dialog-centered" role = "document">
            <div class = "modal-content radius-xl">
                <div class = "modal-body">
                    <div class = "text-center">
                        <h1 class = "text-loss mb-4">
                            <i class = "las la-exclamation-circle fs-60"></i>
                        </h1>
                        <h5 class = "mb-3">@lang('CRMProjects.common.are_you_sure')</h5>
                        <p>
                            @lang('import.question_delete_sheet')
                            @lang('CRMProjects.common.this_action_cannot_be_undone') 
                        </p>
                    </div>
                </div>
                <div class = "modal-footer justify-content-between border-0 gap-10">
                    <button type = "button" class = "btn bg-hold-light text-white flex-fill radius-xl" data-dismiss = "modal">@lang('import.cancel')</button>
                    <div wire:loading class = "text-center" wire:target = "destroyBuildingsList">
                        <button type = "button" class = "btn bg-loss flex-fill radius-xl text-white" wire:loading.attr = "disabled">
                            <span class = "spinner-border spinner-border-sm" role = "status" aria-hidden = "true"></span> 
                            @lang('work_order.common.loading')
                        </button>
                    </div>
                    <button class = "btn bg-loss flex-fill radius-xl text-white" wire:loading.class = "hide" wire:target = "destroyBuildingsList" wire:click = "destroyBuildingsList()">@lang('import.delete')</button>
                </div>
            </div>
        </div>
    </div>
</div>

    <div>
    <style>
        .no-bg-icon {
            background-color: transparent;
            border-left: none;
            padding-left: 0.5rem;
            padding-right: 0.5rem;
            color: #6c757d;
            border-top-left-radius: 0;
            border-bottom-left-radius: 0;
            border-color: #e3e6ef;
        }

        .custom-input-group .form-control {
            border-right: none;
            border-top-right-radius: 0;
            border-bottom-right-radius: 0;

        }
        .currency-text {
            font-size: 14px;
            border-color: #e3e6ef;
            background-color: transparent;  /* Optional background */
            border-top-right-radius: 0;
            border-bottom-right-radius: 0;
            border-right: none;
        }

        .amount-input{
            border-left: none;
            border-top-left-radius: 0;
            border-bottom-left-radius: 0;
        }
    </style>
    @if ($isVariation)
    <div class="col-12 mb-3 px-0">
          <livewire:advance-contracts.alerts.variation-order />
    </div>
    @endif
    <div class="checkout-shipping-form pt-20 card">
        <div class="px-0 pt-0 pb-0 mt-lg-0 border-0">
            <div class="card-header border-bottom-0 align-content-start pb-sm-0 pb-3 px-0 mb-0 pt-0 color-dark fw-500">
                {{ __('advance_contracts.main_information.title') }}
            </div>
            <div class="card-body px-0 pb-0 pt-0">
                <form wire:submit.prevent="submit" enctype="multipart/form-data">
                <div class="form-group mb-15"> {{ __('advance_contracts.main_information.contract_with') }}
                    <label for="contract_with"><span class="required">*</span></label>
                    <div class="atbd-select">
                        <!-- Use the Livewire wire:model directive for reactive binding -->
                        <select name="contract_with" id="contract_with" data-placeholder="{{ __('advance_contracts.general.please_choose') }}" class="form-control" wire:model="contract_with"     {{ $isVariation ? 'disabled' : '' }}  >
                            <option value=""></option>
                            @foreach ($companyList as $company)
                                @if (empty($company['deleted_at']))
                                    <option value="{{ $company['id'] }}">
                                        {{ $company['name'] }} - {{ $company['service_provider_id'] }}
                                        @if ($company['global_sp'] == 1)
                                            [PSP]
                                        @endif
                                    </option>
                                @endif
                            @endforeach

                        </select>
                    </div>
                    @error('contract_with') <span class="text-danger">{{ $message }}</span> @enderror
                </div>

                    <h6 class="mb-3 fw-500">{{ __('advance_contracts.main_information.information') }}</h6>

                    <div class="form-group">
                        <div class="row">
                            <div class="col-sm-6">
                                <label for="contract_name">{{ __('advance_contracts.main_information.contract_name') }} <span class="required">*</span></label>
                                <input type="text" class="form-control" wire:model="contract_name" placeholder="{{ __('advance_contracts.general.enter_name') }}" />
                                @error('contract_name') <span class="text-danger">{{ $message }}</span> @enderror
                            </div>
                            <div class="col-sm-6">
                                <label for="amount">{{ __('advance_contracts.main_information.enter_amount') }} <span class="required">*</span></label>
                                <input type="text" class="form-control" wire:model="amount" placeholder="{{ __('advance_contracts.general.enter_amount') }}" />
                                @error('amount') <span class="text-danger">{{ $message }}</span> @enderror
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="form-group col-sm-6">
                        <label for="interval">{{ __('advance_contracts.main_information.interval_of_payments') }} <span class="required">*</span></label>
                        @php
                            $paymentIntervals = [
                                '' =>  "",
                                'monthly' => __('data_contract.contract_forms.options.payment_interval.monthly'),
                                'quarterly' => __('data_contract.contract_forms.options.payment_interval.quarterly'),
                                'semi_annually' => __('data_contract.contract_forms.options.payment_interval.semi_annually'),
                                'annually' => __('data_contract.contract_forms.options.payment_interval.annually'),
                            ];
                        @endphp

                        <select class="form-control select2-new" data-placeholder="{{ __('advance_contracts.general.please_choose') }}" id="interval_type" wire:model="interval">
                            @foreach ($paymentIntervals as $key => $label)
                                <option value="{{ $key }}">{{ $label }}</option>
                            @endforeach
                        </select>
                        @error('interval') <span class="text-danger">{{ $message }}</span> @enderror
                        </div>
                    </div>
                    <div class="form-group">
                        <h6 class="mb-3 fw-500">{{ __('advance_contracts.main_information.duration') }}</h6>
                        <label for="name1">{{ __('advance_contracts.main_information.start_end_date') }}</label>
                        <div class="row align-items-center">
                            <div class="col-md-4">
                                <div class="position-relative">
                                    <input type="text" autocomplete="off" id="start_date" class="form-control" wire:model="start_date"  placeholder="{{ __('advance_contracts.main_information.start_date') }}" />
                                    <i class="iconsax field-icon" icon-name="calendar-1"></i>
                                </div>
                                @error('start_date') <span class="text-danger">{{ $message }}</span> @enderror
                            </div>

                            <div class="col-md-1 text-center">
                                <span class="fw-bold">-</span>
                            </div>

                            <div class="col-md-4">
                                <div class="position-relative">
                                    <input type="text" autocomplete="off" id="end_date" class="form-control" wire:model="end_date" placeholder="{{ __('advance_contracts.main_information.end_date') }}"  />
                                    <i class="iconsax field-icon" icon-name="calendar-1"></i>
                                </div>
                                @error('end_date') <span class="text-danger">{{ $message }}</span> @enderror
                            </div>
                        </div>
                        </div>

                    <div class="d-flex justify-content-between">
                        <h6 class="mb-3 fw-500 fs-14">{{ __('advance_contracts.main_information.monthly_payment_schedule') }}</h6>
                        <h6 class="mb-3 fw-500 fs-14">{{ __('advance_contracts.main_information.total_amount') }}: <img src="{{ asset('currency.svg') }}" alt="Currency" style="height: 1em;vertical-align: top;" />  {{ $amount ?? 0 }}   </h6>
                    </div>

                    <div class="form-group">
                        <table x-data="rackShelfData" class="table table--default rackshelvestable">
                            <thead class="userDatatable-header">
                                <tr class="warehouse-table-tr">
                                    <th>{{ __('advance_contracts.main_information.month') }}</th>
                                    <th>{{ __('advance_contracts.main_information.amount') }}</th>
                                    <th></th>
                                </tr>
                            
                            </thead>
                            <tbody>
                                @foreach ($months as $index => $month)
                                    <tr>
                                        <td>
                                            <span class="form-control-plaintext ">{{ $month['month'] }}</span>
                                            @error("months.$index.month") <span class="text-danger">{{ $message }}</span> @enderror
                                        </td>
                                        <td width="250px"> 
                                            <div class="input-group ">
                                                <span class="b-b-l-r-5 b-b-r-r-0 b-t-l-r-5 b-t-r-r-0 border-left border-right-0 currency-text input-group-text">    
                                                    <img src="{{ asset('currency.svg') }}" alt="Currency" style="height: 1.2em;" />
                                                </span>
                                                <input type="number" class="amount-input b-b-r-r-5 b-t-r-r-5 border-left-0 border-right form-control" wire:model="months.{{ $index }}.amount" readonly/>
                                            </div>
                                            @error("months.$index.amount") <span class="text-danger">{{ $message }}</span> @enderror
                                        </td>
                                        <td width="55px">
                                            {{-- Keep the logic but hide the icon --}}
                                            @if ($index !== 0)
                                                <a class="remove action_icon d-none" wire:click="removeMonth({{ $index }})" title="Remove" href="javascript:;">
                                                    <i class="la la-trash-alt big"></i>
                                                </a>
                                            @endif
                                        </td>
                                    </tr>
                                @endforeach

                            </tbody>
                        </table>
                    </div>                                  

                    <h6 class="mb-3 fw-500">{{ __('advance_contracts.main_information.type') }}</h6>
                    <div class="form-group">
                        <div class="form-check">
                            <input type="checkbox"  {{ $isVariation ? 'disabled' : '' }}  id="is_subcontract" wire:model="is_subcontract" class="form-check-input" />
                            <label for="is_subcontract" class="form-check-label">{{ __('advance_contracts.main_information.subcontract_note') }}</label>
                        </div>
                    </div>

                    @if($is_subcontract)
                        <div class="form-group mt-3">
                            <label for="selectedSubcontractId">{{ __('advance_contracts.main_information.select_parent_contract') }}</label>
                            <select data-placeholder="{{ __('advance_contracts.general.please_choose') }}" wire:model="selectedSubcontractId" class="form-control select2-new" id="selectedSubcontractId"  {{ $isVariation ? 'disabled' : '' }} >
                                <option value=""></option>
                                @foreach($subcontractOptions as $contract)
                                    <option value="{{ $contract['id'] }}">{{ $contract['name'] }}</option>
                                @endforeach
                            </select>
                            <span class="text-danger error-text selectedSubcontractId_error"></span>
                        </div>
                    @endif
                    <div class="d-flex pt-40 justify-content-md-end button-group d-flex pt-25 justify-content-end">
                        <a href="{{ url('/data/contracts') }}" class="btn btn-outline-lighten fw-400 text-capitalize radius-md">{{ __('advance_contracts.buttons.cancel') }} </a>
                        <button type="submit" id="create" class="btn bg-primary border-0 btn-default text-white btn-squared text-capitalize radius-md shadow2">{{ __('advance_contracts.buttons.save_next') }}</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    @push('scripts')
    <script>
        function applySelect2OnDatepickerDropdowns() {
            setTimeout(() => {
                $(".ui-datepicker-month, .ui-datepicker-year").select2({
                    width: 'auto',
                    minimumResultsForSearch: Infinity // Hide search box
                });
                // Add layout classes to the title container
                $(".ui-datepicker-title").addClass("mx-30 d-flex gap-10").css("min-height", "48px");
            }, 0); // Allow DOM to render first
        }
        $(function () {
            let startDate = null;

            $("#start_date").datepicker({
                dateFormat: "dd-mm-yy",
                changeMonth: true,
                changeYear: true,
                beforeShow: function (input, inst) {
                    applySelect2OnDatepickerDropdowns();
                },
                onChangeMonthYear: function (year, month, inst) {
                    applySelect2OnDatepickerDropdowns();
                },
                onSelect: function (dateText, inst) {
                    startDate = $(this).datepicker("getDate");
                    @this.set('start_date', dateText);
                    const nextValidDate = getNextValidDate(startDate, $("#interval_type").val());
                    $("#end_date").val("").datepicker("option", {
                        minDate: getMinDate(startDate),
                        defaultDate: nextValidDate
                    });
                }
            });

            $("#end_date").datepicker({
                dateFormat: "dd-mm-yy",
                changeMonth: true,
                changeYear: true,
                beforeShow: function (input, inst) {
                    applySelect2OnDatepickerDropdowns();
                },
                onChangeMonthYear: function (year, month, inst) {
                    applySelect2OnDatepickerDropdowns();
                },
                beforeShowDay: function (date) {
                    if (!startDate) return [false];
                    const intervalType = $("#interval_type").val();
                    switch (intervalType) {
                        case "monthly": return [isMonthly(date, startDate)];
                        case "quarterly": return [isQuarterly(date, startDate)];
                        case "semi_annually": return [isSemiAnnually(date, startDate)];
                        case "annually": return [isYearly(date, startDate)];
                        default: return [false];
                    }
                },
                onSelect: function (dateText, inst) {
                    @this.set('end_date', dateText);
                }
            });

            $("#interval_type").change(function () {
                if (startDate) {
                    $("#end_date").val("");
                    const nextValidDate = getNextValidDate(startDate, $(this).val());
                    $("#end_date").datepicker("option", {
                        defaultDate: nextValidDate,
                        minDate: getMinDate(startDate)
                    }).datepicker("refresh");
                }
            });

            function getMinDate(startDate) {
                const min = new Date(startDate);
                min.setDate(min.getDate() + 1);
                return min;
            }

            function getNextValidDate(start, intervalType) {
                const nextDate = new Date(start);
                switch (intervalType) {
                    case "monthly": nextDate.setMonth(nextDate.getMonth() + 1); break;
                    case "quarterly": nextDate.setMonth(nextDate.getMonth() + 3); break;
                    case "semi_annually": nextDate.setMonth(nextDate.getMonth() + 6); break;
                    case "annually": nextDate.setFullYear(nextDate.getFullYear() + 1); break;
                }
                return nextDate;
            }

            function isMonthly(date, start) {
                const diff = monthDiff(start, date);
                return diff >= 1 && date.getDate() === start.getDate();
            }

            function isQuarterly(date, start) {
                const diff = monthDiff(start, date);
                return diff >= 3 && diff % 3 === 0 && date.getDate() === start.getDate();
            }

            function isSemiAnnually(date, start) {
                const diff = monthDiff(start, date);
                return diff >= 6 && diff % 6 === 0 && date.getDate() === start.getDate();
            }

            function isYearly(date, start) {
                const diffYear = date.getFullYear() - start.getFullYear();
                return diffYear >= 1 && date.getMonth() === start.getMonth() && date.getDate() === start.getDate();
            }

            function monthDiff(d1, d2) {
                return (d2.getFullYear() - d1.getFullYear()) * 12 + (d2.getMonth() - d1.getMonth());
            }
        });

        window.addEventListener('show-toastr', event => {
            toastr.options = {
                "closeButton": true,
                "progressBar": true,
                "positionClass": "toast-top-center",
                "timeOut": "3000"
            };
            if (event.detail.type === 'success') {
                toastr.success(event.detail.message);
            } else if (event.detail.type === 'error') {
                toastr.error(event.detail.message);
            }
        });

        document.addEventListener('livewire:load', function () {
        initializeSelect2();
    });

    Livewire.hook('message.processed', (message, component) => {
        initializeSelect2();
    });
    function initializeSelect2() {
       const selectPlaceholder = "{{ __('advance_contracts.general.please_choose') }}";

        $('select').select2({
                allowClear: true, // Enable clear button
                placeholder: function() {
                    return $(this).data('placeholder') || selectPlaceholder; // Default placeholder if none is provided
                }
            });
        $('#contract_with').on('change', function () {
                @this.set('contract_with', $(this).val());
            });
        $('#interval_type').on('change', function () {
            @this.set('interval', $(this).val());
        }); 
        $('#selectedSubcontractId').on('change', function () {
            @this.set('selectedSubcontractId', $(this).val());
        });     
    }
    </script>
    @endpush
</div>

<?php
    namespace App\Imports;
    use Illuminate\Support\Collection;
    use Maatwebsite\Excel\Concerns\ToCollection;
    use Maatwebsite\Excel\Concerns\WithCalculatedFormulas;
    use Maatwebsite\Excel\Concerns\WithHeadingRow;
    use Maatwebsite\Excel\Concerns\SkipsOnError;
    use Maatwebsite\Excel\Concerns\WithBatchInserts;
    use Maatwebsite\Excel\Concerns\WithChunkReading;
    use Maatwebsite\Excel\Concerns\Importable;
    use Maatwebsite\Excel\Concerns\SkipsErrors;
    use Illuminate\Support\Facades\Log;
    use App\Http\Traits\FunctionsTrait;
    use App\Http\Traits\BulkImportTempTrait;

    class PropertiesImport implements ToCollection, WithCalculatedFormulas, WithHeadingRow, SkipsOnError, WithBatchInserts, WithChunkReading{
        use Importable, SkipsErrors, FunctionsTrait, BulkImportTempTrait;

        protected $bulkImportTempId;

        public function __construct($bulkImportTempId){
            $this->bulkImportTempId = $bulkImportTempId;
        }

        /**
        * @param Collection $collection
        */
        public function collection(Collection $dataRows){
            try {
                $collection = collect();

                if($this->valueIsRequired($dataRows)){
                    Log::info("PropertiesImport error: No properties sheet found in this file!");
                }

                else{
                    $dataRows->chunk(500)->each(function ($chunk) use ($collection, $dataRows) {
                        $chunk->each(function ($row) use ($collection, $dataRows) {
                            $row['region'] = isset($row['region']) ? trim($row['region']) : null;
                            $row['city'] = isset($row['city']) ? trim($row['city']) : null;
                            $row['property_type'] = isset($row['property_type']) ? trim(strtolower($row['property_type'])) : null;
                            $row['property_name'] = isset($row['property_name']) ? trim($row['property_name']) : null;
                            $row['building_count'] = isset($row['building_count']) ? trim($row['building_count']) : null;
                            $row['gps_location_latitude'] = isset($row['gps_location_latitude']) ? trim($row['gps_location_latitude']) : null;
                            $row['gps_location_longitude'] = isset($row['gps_location_longitude']) ? trim($row['gps_location_longitude']) : null;
                            $collection->push($row);
                        });
                    });

                    $filteredCollection = $collection->filter(function ($item) {
                        return collect($item)->filter()->isNotEmpty();
                    });

                    $jsonData = $filteredCollection->toJson();
                    $updatedBulkImportTemp = $this->updateBulkImportTempByValues('id', $this->bulkImportTempId, ['properties_data' => $jsonData]);

                    if($updatedBulkImportTemp){
                        Log::info("PropertiesImport: Properties data processed successfully: ".$filteredCollection->count()." records found.");
                    }

                    else{
                        Log::info("PropertiesImport: Unable to import and save the properties sheet.");
                    }
                }
            } 
            
            catch (\Throwable $th) {
                Log::error("PropertiesImport error: ".$th);
            }
        }

         /**
         * uniqueBy
         *
         * @return void
         */
        public function uniqueBy(){
            return 'property_name';
        }

        /**
         * headingRow
         *
         * @return int
         */
        public function headingRow(): int{
            return 1;
        } 

         /**
         * onError
         *
         * @param  mixed $e
         * @return void
         */
        public function onError(\Throwable $e){
            Log::error("PropertiesImport (onError) error: ".$e->getMessage());
        }

        /**
         * batchSize
         *
         * @return int
         */
        public function batchSize(): int{
            return 500;
        }

         /**
         * chunkSize
         *
         * @return int
         */
        public function chunkSize(): int{
            return 500;
        }
    }
?>
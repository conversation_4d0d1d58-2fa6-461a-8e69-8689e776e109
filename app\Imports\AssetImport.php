<?php
    namespace App\Imports;
    use Illuminate\Support\Collection;
    use Maatwebsite\Excel\Concerns\ToCollection;
    use Maatwebsite\Excel\Concerns\WithCalculatedFormulas;
    use Maatwebsite\Excel\Concerns\WithHeadingRow;
    use Maatwebsite\Excel\Concerns\SkipsOnError;
    use Maatwebsite\Excel\Concerns\WithBatchInserts;
    use Maatwebsite\Excel\Concerns\WithChunkReading;
    use Maatwebsite\Excel\Concerns\Importable;
    use Maatwebsite\Excel\Concerns\SkipsErrors;
    use Illuminate\Support\Facades\Log;
    use App\Http\Traits\FunctionsTrait;
    use App\Http\Traits\BulkImportTempTrait;

    class AssetImport implements ToCollection, WithCalculatedFormulas, WithHeadingRow, SkipsOnError, WithBatchInserts, WithChunkReading{
        use Importable, SkipsErrors, FunctionsTrait, BulkImportTempTrait;

        protected $bulkImportTempId;

        public function __construct($bulkImportTempId){
            $this->bulkImportTempId = $bulkImportTempId;
        }

        /**
        * @param Collection $collection
        */
        public function collection(Collection $dataRows){
            try {
                $collection = collect();

                if($this->valueIsRequired($dataRows)){
                    Log::info("AssetImport error: No assets sheet found in this file!");
                }

                else{
                    $dataRows->chunk(500)->each(function ($chunk) use ($collection, $dataRows) {
                        $chunk->each(function ($row) use ($collection, $dataRows) {
                            $row['building_name'] = isset($row['building_name']) ? trim($row['building_name']) : null;
                            $row['property_name'] = isset($row['property_name']) ? trim($row['property_name']) : null;
                            $row['service_type'] = isset($row['service_type']) ? trim($row['service_type']) : null;
                            $row['zone'] = isset($row['zone']) ? trim($row['zone']) : null;
                            $row['unit'] = isset($row['unit']) ? trim($row['unit']) : null;
                            $row['asset_name'] = isset($row['asset_name']) ? trim($row['asset_name']) : null;
                            $row['asset_symbol'] = isset($row['asset_symbol']) ? trim($row['asset_symbol']) : null;
                            $row['asset_number'] = isset($row['asset_number']) ? trim($row['asset_number']) : null;
                            $row['purchase_date'] = isset($row['date_of_purchase']) ? trim($row['date_of_purchase']) : null;
                            $row['manufacturer_name'] = isset($row['manufacturer_name']) ? trim($row['manufacturer_name']) : null;
                            $row['model_number'] = isset($row['model_number']) ? trim($row['model_number']) : null;
                            $row['asset_status'] = isset($row['asset_status']) ? trim(strtolower($row['asset_status'])) : null;
                            $row['status_date'] = isset($row['date_of_status']) ? trim($row['date_of_status']) : null;
                            $collection->push($row);
                        });
                    });

                    $filteredCollection = $collection->filter(function ($item) {
                        return collect($item)->filter()->isNotEmpty();
                    });

                    $jsonData = $filteredCollection->toJson();
                    $updatedBulkImportTemp = $this->updateBulkImportTempByValues('id', $this->bulkImportTempId, ['assets_data' => $jsonData]);

                    if($updatedBulkImportTemp){
                        Log::info("AssetImport: Assets data processed successfully: ".$filteredCollection->count()." records found.");
                    }

                    else{
                        Log::info("AssetImport: Unable to import and save the assets sheet.");
                    }


                }
            } 
            
            catch (\Throwable $th) {
                Log::error("AssetImport error: ".$th);
            }
        }

        /**
         * headingRow
         *
         * @return int
         */
        public function headingRow(): int{
            return 1;
        } 

         /**
         * onError
         *
         * @param  mixed $e
         * @return void
         */
        public function onError(\Throwable $e){
            Log::error("AssetImport (onError) error: ".$e->getMessage());
        }

        /**
         * batchSize
         *
         * @return int
         */
        public function batchSize(): int{
            return 500;
        }

         /**
         * chunkSize
         *
         * @return int
         */
        public function chunkSize(): int{
            return 500;
        }
    }
?>
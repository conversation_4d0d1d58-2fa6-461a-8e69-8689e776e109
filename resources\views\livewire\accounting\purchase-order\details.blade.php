<div>
    @if($isLoading)
        <div class="text-center py-4">
            <div class="spinner-border text-primary" role="status">
                <span class="sr-only">@lang('purchase.details.loading')</span>
            </div>
        </div>
    @else
        <div class="contents crm">
            <div class="container-fluid">
                <div class="col-lg-12">
                    <div class="row justify-content-sm-between align-items-center justify-content-center my-3 flex-sm-row flex-column">
                        <div class="page-title-wrap p-0">
                            <div class="page-title d-flex justify-content-between">
                                <div class="page-title__left justify-content-sm-between align-items-center justify-content-center">
                                    <div class="user-member__title mr-sm-25 ml-0">
                                        <h4 class="text-capitalize fw-500 breadcrumb-title fs-16">
                                            @lang('purchase.details.title')
                                        </h4>
                                    </div>
                                </div>
                            </div>
                            <div>
                                <ul class="atbd-breadcrumb nav">
                                    <li class="atbd-breadcrumb__item">
                                        <a>@lang('purchase.details.breadcrumb.dashboard')</a>
                                        <span class="breadcrumb__seperator">
                                            <span class="la la-angle-right"></span>
                                        </span>
                                    </li>
                                    <li class="atbd-breadcrumb__item">
                                        <a>@lang('purchase.details.breadcrumb.purchase_order')</a>
                                        <span class="breadcrumb__seperator">
                                            <span class="la la-angle-right"></span>
                                        </span>
                                    </li>
                                    <li class="atbd-breadcrumb__item">
                                        <a>@lang('purchase.details.breadcrumb.details')</a>
                                    </li>
                                </ul>
                            </div>
                        </div>

                        <div class="d-flex gap-10 breadcrumb_right_icons">
                            <button class="btn btn-white btn-default text-center svg-20 px-2 wh-45">
                                <i class="iconsax icon fs-22 text-osool mr-0" icon-name="printer"></i>
                            </button>
                            <button class="btn btn-white btn-default text-center svg-20 px-2 wh-45">
                                <i class="iconsax icon fs-22 text-osool mr-0" icon-name="link-1"></i>
                            </button>
                            <button class="btn btn-white btn-default text-center svg-20 px-2 wh-45">
                                <i class="iconsax icon fs-22 text-osool mr-0" icon-name="send-2"></i>
                            </button>
                            <a href="{{ route('purchase.edit', $purchaseOrderId) }}" class="btn btn-white btn-default text-center svg-20 px-2 wh-45">
                                <i class="iconsax icon fs-22 text-new-primary mr-0" icon-name="edit-1"></i>
                            </a>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="mb-3" data-select2-id="108">
                        <div class="card-header d-flex justify-content-start gap-5 text-osool">
                            <h6>@lang('purchase.details.po_information')</h6>
                        </div>
                        <div class="p-4">
                            <h6 class="fs-14">@lang('purchase.details.po_number') #{{ $purchaseOrder['purchase_number'] }}</h6>
                            <h6 class="fs-14 my-4">@lang('purchase.details.invoice_date') {{ $purchaseOrder['purchase_date_formatted'] }}</h6>
                            <div class="d-flex align-items-center">
                                <h6 class="fs-14">@lang('purchase.details.invoice')</h6>
                                <button class="bg-osool-new btn h-40 text-white ml-4">@lang('purchase.details.open')</button>
                            </div>
                        </div>
                        <div class="bg-white border-0 project-table projectDatatable px-4 userDatatable w-100">
                            <div class="table-responsive card">
                                <table class="table mb-0 radius-0 th-osool">
                                    <thead>
                                    <tr class="userDatatable-header">
                                        <th>@lang('purchase.details.item')</th>
                                        <th>@lang('purchase.details.quantity')</th>
                                        <th>@lang('purchase.details.price')</th>
                                        <th>@lang('purchase.details.discount')</th>
                                        <th>@lang('purchase.details.tax')</th>
                                        <th>@lang('purchase.details.description')</th>
                                        <th>@lang('purchase.details.amount')</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    @foreach($purchaseOrder['purchase_items'] as $item)
                                        <tr>
                                            <td>{{ $item['product']['name'] ?? 'N/A' }}</td>
                                            <td>{{ $item['quantity'] }}</td>
                                            <td>${{ number_format($item['price'], 2) }}</td>
                                            <td>{{ $item['discount'] }}%</td>
                                            <td>{{ $item['tax'] }}%</td>
                                            <td>{{ $item['description'] ?? '-' }}</td>
                                            <td>${{ number_format($item['quantity'] * $item['price'], 2) }}</td>
                                        </tr>
                                    @endforeach
                                    <tr>
                                        <td colspan="6"></td>
                                        <td>
                                            <div>@lang('purchase.details.sub_total'): ${{ number_format($purchaseOrder['sub_total'], 2) }}</div>
                                            <div>@lang('purchase.details.discount'): ${{ number_format($purchaseOrder['total_discount'], 2) }}</div>
                                        </td>
                                    </tr>
                                    <tr class="bg-new-primary fw-700 text-white">
                                        <td colspan="6"></td>
                                        <td>
                                            <div>@lang('purchase.details.total'): ${{ number_format($purchaseOrder['grand_total'], 2) }}</div>
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <div class="row mb-3 px-4">
                        <div class="col-md-4 mb-sm-0 mb-3 pr-md-0">
                            <div class="p-3 address-box radius-xl h-100 card">
                                <h6 class="text-dark mb-3 fs-14">@lang('purchase.details.from')</h6>
                                <table class="table mb-0 table-borderless new-header td-pl-0 table-address">
                                    <tbody>
                                    <tr>
                                        <td><span class="fs-12 fw-50 text-dark fw-600">@lang('purchase.details.company_address')</span></td>
                                        <td>{{ $purchaseOrder['company']['address'] ?? 'N/A' }}</td>
                                    </tr>
                                    <tr>
                                        <td><span class="fs-12 fw-50 text-dark fw-600">@lang('purchase.details.company_city')</span></td>
                                        <td>{{ $purchaseOrder['company']['city'] ?? 'N/A' }}</td>
                                    </tr>
                                    <tr>
                                        <td><span class="fs-12 fw-50 text-dark fw-600">@lang('purchase.details.zip_code')</span></td>
                                        <td>{{ $purchaseOrder['company']['zip_code'] ?? 'N/A' }}</td>
                                    </tr>
                                    <tr>
                                        <td><span class="fs-12 fw-50 text-dark fw-600">@lang('purchase.details.company_country')</span></td>
                                        <td>{{ $purchaseOrder['company']['country'] ?? 'N/A' }}</td>
                                    </tr>
                                    <tr>
                                        <td><span class="fs-12 fw-50 text-dark fw-600">@lang('purchase.details.company_contact')</span></td>
                                        <td>{{ $purchaseOrder['company']['phone'] ?? 'N/A' }}</td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="col-md-4 mb-sm-0 mb-3 pr-md-0">
                            <div class="p-3 address-box radius-xl h-100 card">
                                <h6 class="text-dark mb-3 fs-14">@lang('purchase.details.billing_address')</h6>
                                <table class="table mb-0 table-borderless new-header td-pl-0 table-address">
                                    <tbody>
                                    <tr>
                                        <td><span class="fs-12 fw-50 text-dark fw-600">@lang('purchase.details.address')</span></td>
                                        <td>{{ $purchaseOrder['billing_address']['address'] ?? 'N/A' }}</td>
                                    </tr>
                                    <tr>
                                        <td><span class="fs-12 fw-50 text-dark fw-600">@lang('purchase.details.city')</span></td>
                                        <td>{{ $purchaseOrder['billing_address']['city'] ?? 'N/A' }}</td>
                                    </tr>
                                    <tr>
                                        <td><span class="fs-12 fw-50 text-dark fw-600">@lang('purchase.details.zip_code')</span></td>
                                        <td>{{ $purchaseOrder['billing_address']['zip_code'] ?? 'N/A' }}</td>
                                    </tr>
                                    <tr>
                                        <td><span class="fs-12 fw-50 text-dark fw-600">@lang('purchase.details.country')</span></td>
                                        <td>{{ $purchaseOrder['billing_address']['country'] ?? 'N/A' }}</td>
                                    </tr>
                                    <tr>
                                        <td><span class="fs-12 fw-50 text-dark fw-600">@lang('purchase.details.contact')</span></td>
                                        <td>{{ $purchaseOrder['billing_address']['contact_name'] ?? 'N/A' }}</td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="p-3 address-box radius-xl h-100 card">
                                <h6 class="text-dark mb-3 fs-14">@lang('purchase.details.shipping_address')</h6>
                                <table class="table mb-0 table-borderless new-header td-pl-0 table-address">
                                    <tbody>
                                    <tr>
                                        <td><span class="fs-12 fw-50 text-dark fw-600">@lang('purchase.details.address')</span></td>
                                        <td>{{ $purchaseOrder['shipping_address']['address'] ?? 'N/A' }}</td>
                                    </tr>
                                    <tr>
                                        <td><span class="fs-12 fw-50 text-dark fw-600">@lang('purchase.details.city')</span></td>
                                        <td>{{ $purchaseOrder['shipping_address']['city'] ?? 'N/A' }}</td>
                                    </tr>
                                    <tr>
                                        <td><span class="fs-12 fw-50 text-dark fw-600">@lang('purchase.details.zip_code')</span></td>
                                        <td>{{ $purchaseOrder['shipping_address']['zip_code'] ?? 'N/A' }}</td>
                                    </tr>
                                    <tr>
                                        <td><span class="fs-12 fw-50 text-dark fw-600">@lang('purchase.details.country')</span></td>
                                        <td>{{ $purchaseOrder['shipping_address']['country'] ?? 'N/A' }}</td>
                                    </tr>
                                    <tr>
                                        <td><span class="fs-12 fw-50 text-dark fw-600">@lang('purchase.details.contact')</span></td>
                                        <td>{{ $purchaseOrder['shipping_address']['contact_name'] ?? 'N/A' }}</td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    @endif
</div>

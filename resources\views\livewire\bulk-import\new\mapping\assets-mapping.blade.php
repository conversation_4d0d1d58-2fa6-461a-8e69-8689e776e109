<div>
    <div class = "userDatatable projectDatatable project-table bg-white w-100 border-0">
        <div class = "d-flex gap-10 pb-3 border-bottom mb-3">
            <div class = "d-flex gap-5 align-items-center">
                <i class = "iconsax text-black fs-16" icon-name = "task-list-square"></i> 
                <span class = "text-black">{{ $list->total() }} @lang('import.total')</span>
            </div>
            
            @php
                $listData = $this->manageListStatusData();
            @endphp

            <div class = "d-flex gap-5 align-items-center">
                <i class = "iconsax text-success fs-16" icon-name = "tick-square"></i> 
                <span class = "text-success">{{ isset($listData) && count($listData) > 0 ? $listData['acceptedNumber'] : 0 }} @lang('import.accepted')</span>
            </div>
            <div class = "d-flex gap-5 align-items-center">
                <i class = "iconsax text-danger fs-16" icon-name = "info-circle"></i> 
                <span class = "text-danger">{{ isset($listData) && count($listData) > 0 ? $listData['refusedNumber'] : 0 }} @lang('import.data_issues')</span>
            </div>
        </div>
        <div class = "table-responsive">
            <table class = "table mb-0 accordion" id = "assets-table">
                <thead>
                    <th scope = "col">@lang("import.property_name")</th>
                    <th scope = "col">@lang("import.building_name")</th>
                    <th scope = "col">@lang("import.service_type")</th>
                    <th scope = "col">@lang("import.zone")</th>
                    <th scope = "col">@lang("import.unit")</th>
                    <th scope = "col">@lang("import.asset_name")</th>
                    <th scope = "col">@lang("import.asset_symbol")</th>
                    <th scope = "col">
                        <div class = "form-inline d-flex" wire:ignore>
                            <div class = "form-check form-check-inline">
                                <input type = "radio" value = "{{ \App\Enums\Identifier::Asset->value }}"class = "form-check-input mx-1" checked>
                                <label class = "form-check-label mx-2">@lang("import.asset_number")</label>
                                <i class = "fa fa-question-circle color-light" data-toggle = "tooltip" data-placement = "top" title = "@lang('import.set_asset')"></i>
                            </div>                                                            
                        </div>
                    </th>
                    <th scope = "col" class = "extra-col d-none">@lang("import.data_purchase")</th>
                    <th scope = "col" class = "extra-col d-none">@lang("import.manif_name")</th>
                    <th scope = "col" class = "extra-col d-none">@lang("import.model_number")</th>
                    <th scope = "col" class = "extra-col d-none">@lang("import.asset_status")</th>
                    <th scope = "col" class = "extra-col d-none">@lang("import.date_of_status")</th>
                    <th scope = "col" colspan = "2">
                        @lang("import.status")
                        <span id = "show-action-btn" class = "mx-1" style = "text-decoration: underline; cursor: pointer;">
                            @lang('import.more')
                        </span>
                    </th>
                </thead>
                <tbody>
                    @if(isset($list) && $list->count())
                        @foreach($list as $key => $data)
                            <tr wire:key = "item-assets-{{ $key }}"> 
                                <td>{{ $data['property_name'] ?? '-' }}</td>
                                <td>{{ $data['building_name'] ?? '-' }}</td>
                                <td>{{ $data['service_type'] ?? '-' }}</td>
                                <td>{{ $data['zone'] ?? '-' }}</td>
                                <td>{{ $data['unit'] ?? '-' }}</td>
                                <td>{{ $data['asset_name'] ?? '-' }}</td>
                                <td>{{ $data['asset_symbol'] ?? '-' }}</td>
                                <td>{{ $data['asset_number'] ?? '-' }}</td>
                                <td class = "extra-row d-none">{{ $data['purchase_date'] ?? '-' }}</td>
                                <td class = "extra-row d-none">{{ $data['manufactured_name'] ?? '-' }}</td>
                                <td class = "extra-row d-none">{{ $data['model_number'] ?? '-' }}</td>
                                <td class = "text-uppercase extra-row d-none">{{ $data['asset_status'] ?? '-' }}</td>
                                <td class = "extra-row d-none">{{ $data['status_date'] ?? '-' }}</td>
                                <td>
                                    @if(!is_null($this->fullAssetsValidation($data)) && ($this->fullAssetsValidation($data)[0]['status'] <> 'success' || $this->speceficAssetsValidation($data)[0]['status'] <> 'success'))
                                        <p>
                                            <a href = "javascript:void(0)" class = "bold-700 accordion-toggle text-danger" style = "text-decoration: underline" data-toggle = "collapse" data-target = "#error-{{ $key }}">
                                                @lang('import.view_errors')
                                            </a>
                                        </p>
                                    @else
                                        <p class = "text-success">
                                            {{ $this->manageEntredAsset($data['asset_name'], $data['asset_number'], $projectUserId) == \App\Enums\ModelAction::Insert->value ? __('import.ready_insert') : __('import.ready_update') }}
                                        </p>
                                    @endif
                                </td>
                            </tr>
                            <tr>
                                <td colspan = "6" class = "hiddenRow">
                                    <div class = "accordian-body collapse" id = "error-{{ $key }}">
                                        <table class = "table mb-0">
                                            <tbody>
                                                @if(!is_null($this->fullAssetsValidation($data)) && $this->fullAssetsValidation($data)[0]['status'] <> 'success')
                                                    <ul class = "list-group list-group-flush border">
                                                        @foreach($this->fullAssetsValidation($data)[0]['errors'] as $row)
                                                            <li class = "list-group-item fs-12">{!! $row ?? '-' !!}</li>
                                                        @endforeach
                                                    </ul>
                                                @endif

                                                @if(!is_null($this->speceficAssetsValidation($data)) && $this->speceficAssetsValidation($data)[0]['status'] <> 'success')
                                                    <ul class = "list-group list-group-flush border">
                                                        @foreach($this->speceficAssetsValidation($data)[0]['errors'] as $row)
                                                            <li class = "list-group-item fs-12">{!! $row ?? '-' !!}</li>
                                                        @endforeach
                                                    </ul>
                                                @endif
                                            </tbody>
                                        </table>
                                    </div>
                                </td>
                            </tr>
                        @endforeach

                        @if ($list->hasMorePages())
                            <tr>
                                <td colspan = "14">
                                    <div class = "d-flex justify-content-center gap-2">
                                        <div class = "p-2">
                                            <div wire:loading wire:target = "managePerPage">
                                                <button type = "button" class = "btn btn-primary" wire:loading.attr = "disabled">
                                                    <span class = "spinner-border spinner-border-sm" role = "status" aria-hidden = "true"></span>
                                                    @lang('import.loading3')
                                                </button>
                                            </div>
                                            <button type = "button" class = "btn btn-primary text-center" wire:target = "managePerPage" wire:click = "managePerPage" wire:loading.class = "hide">
                                                @lang('import.load_more')
                                            </button>
                                        </div>
                                        <div class = "p-2">
                                            <div wire:loading wire:target = "manageLoadAll">
                                                <button type = "button" class = "btn btn-info" wire:loading.attr = "disabled">
                                                    <span class = "spinner-border spinner-border-sm" role = "status" aria-hidden = "true"></span>
                                                    @lang('import.loading3')
                                                </button>
                                            </div>
                                            <button type = "button" class = "btn btn-info" wire:target = "manageLoadAll" wire:click = "manageLoadAll" wire:loading.class = "hide">
                                                @lang('import.load_all')
                                            </button>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        @endif
                    @else
                        <tr class = "text-center">
                            <td colspan = "14">@lang("import.empty_assets")</td>
                        </tr>
                    @endif
                </tbody>
            </table>
        </div>
    </div>
</div>

<!DOCTYPE html>
<html>
<head>
    <title>{{ __('notifications.crm_projects.email.' . $notificationType . '.subject', [
        'milestone_name' => $milestoneData['milestone']['name'] ?? 'Unknown Milestone',
        'days' => $milestoneData['days_remaining'] ?? 0
    ], $locale) }}</title>
    <meta charset="utf-8">
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .content {
            background-color: #ffffff;
            padding: 20px;
            border: 1px solid #dee2e6;
            border-radius: 5px;
        }
        .footer {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
            font-size: 14px;
            color: #6c757d;
        }
        .milestone-info {
            background-color: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .milestone-info strong {
            color: #495057;
        }
        .rtl {
            direction: rtl;
            text-align: right;
        }
        .ltr {
            direction: ltr;
            text-align: left;
        }
    </style>
</head>
<body>
    <div class="header">
        <h2>{{ __('notifications.crm_projects.email.header', [], $locale) }}</h2>
    </div>

    <div class="content {{ $locale === 'ar' ? 'rtl' : 'ltr' }}">
        <p>{{ __('notifications.crm_projects.email.greeting', [], $locale) }}</p>
        
        <p>{{ __('notifications.crm_projects.email.' . $notificationType . '.body', [
            'milestone_name' => $milestoneData['milestone']['name'] ?? 'Unknown Milestone',
            'project_name' => $milestoneData['project']['name'] ?? 'Unknown Project',
            'due_date' => isset($milestoneData['milestone']['due_date']) ? \Carbon\Carbon::parse($milestoneData['milestone']['due_date'])->format('Y-m-d') : 'Unknown Date',
            'completion_date' => isset($milestoneData['milestone']['completion_date']) ? \Carbon\Carbon::parse($milestoneData['milestone']['completion_date'])->format('Y-m-d') : 'Unknown Date',
            'days' => $milestoneData['days_remaining'] ?? 0
        ], $locale) }}</p>

        <div class="milestone-info">
            <strong>{{ __('notifications.crm_projects.email.milestone_details', [], $locale) }}</strong><br>
            <strong>{{ __('notifications.crm_projects.email.milestone_name', [], $locale) }}:</strong> {{ $milestoneData['milestone']['name'] ?? 'Unknown Milestone' }}<br>
            <strong>{{ __('notifications.crm_projects.email.project_name', [], $locale) }}:</strong> {{ $milestoneData['project']['name'] ?? 'Unknown Project' }}<br>
            @if(isset($milestoneData['milestone']['due_date']))
                <strong>{{ __('notifications.crm_projects.email.due_date', [], $locale) }}:</strong> {{ \Carbon\Carbon::parse($milestoneData['milestone']['due_date'])->format('Y-m-d') }}<br>
            @endif
            @if(isset($milestoneData['milestone']['completion_date']))
                <strong>{{ __('notifications.crm_projects.email.completion_date', [], $locale) }}:</strong> {{ \Carbon\Carbon::parse($milestoneData['milestone']['completion_date'])->format('Y-m-d') }}<br>
            @endif
        </div>

        <p>{{ __('notifications.crm_projects.email.closing', [], $locale) }}</p>
    </div>

    <div class="footer {{ $locale === 'ar' ? 'rtl' : 'ltr' }}">
        <p>{{ __('notifications.crm_projects.email.regards', [], $locale) }}<br>
        {{ __('notifications.crm_projects.email.team', [], $locale) }}</p>
    </div>
</body>
</html>

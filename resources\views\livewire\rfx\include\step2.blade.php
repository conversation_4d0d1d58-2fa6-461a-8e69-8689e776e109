<div class="tab-pane fade show active" id="purchase-tab" role="tabpanel" 
     aria-labelledby="profile-tab" 
     @if($currentStep !== 2) style="display: none;" @endif>
    <div class="card">
        <div class="card-header">
            <h6 class="mb-0">@lang('rfx.billing_type')<span class="text-danger">*</span></h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-6 form-group">
                    <select class="form-control" wire:model="billing_type" >
                        <option value="" >@lang('rfx.select_type')</option>
                        <option value="items">@lang('rfx.item')</option>
                        <option value="rfx">@lang('rfx.rfx')</option>
                    </select>
                    @error('billing_type') <span class="text-danger small">{{ $message }}</span> @enderror
                </div>
            </div>
        </div>
    </div>

    @if($billing_type === 'items')
    {{-- Items Table --}}
    <div class="card mt-3">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h6>@lang('rfx.items')</h6>
        <button class="btn btn-primary btn-sm" type="button" wire:click="addItem">
            <i class="las la-plus"></i> @lang('rfx.add_item')
        </button>
    </div>

    <div class="userDatatable projectDatatable project-table bg-white w-100 border-0">
        <div class="table-responsive">
            <table class="table table-bordered mb-0">
                <thead>
                    <tr>
                        <th>@lang('rfx.items')<span class="text-danger">*</span></th>
                        <th>@lang('rfx.quantity')<span class="text-danger">*</span></th>
                        <th>@lang('rfx.price')<span class="text-danger">*</span></th>
                        <th>@lang('rfx.discount')</th>
                        <th>@lang('rfx.tax')</th>
                        <th>@lang('rfx.amount')<br><small>@lang('rfx.after_discount_tax')</small></th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($items as $index => $item)
                    <tr>
                        <td>
                            <select class="form-control" wire:model="items.{{ $index }}.item">
                                <option value="">--</option>
                                @foreach($item_array as $key => $label)
                                    <option value="{{ $key }}">{{ $label }}</option>
                                @endforeach
                            </select>
                            @error("items.$index.item") <span class="text-danger small">{{ $message }}</span> @enderror
                        </td>
                        <td>
                            <input type="number" class="form-control" min="1" wire:model.lazy="items.{{ $index }}.quantity" />
                            @error("items.$index.quantity") <span class="text-danger small">{{ $message }}</span> @enderror
                        </td>
                        <td>
                            <div class="input-group">
                                <input type="number" class="form-control" min="0" wire:model.lazy="items.{{ $index }}.price" />
                                <div class="input-group-append">
                                    <span class="input-group-text">SAR</span>
                                </div>
                            </div>
                            @error("items.$index.price") <span class="text-danger small">{{ $message }}</span> @enderror
                        </td>
                        <td>
                            <input type="number" class="form-control" min="0" wire:model.lazy="items.{{ $index }}.discount" />
                        </td>
                        <td>
                            @foreach($item['taxes'] ?? [] as $tax)
                            <span class="badge badge-primary">{{$tax['name']}}({{ $tax['rate'] ?? 0 }})</span>
                            @endforeach
                        </td>
                        <td>
                            {{ number_format($item['total'] ?? 0, 2) }} SAR
                            <a href="javascript:void(0);" class="text-danger float-right" wire:click="removeItem({{ $index }})">
                                <i class="iconsax" icon-name="trash"></i>
                            </a>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="3">
                            <textarea class="form-control" placeholder="@lang('rfx.description')" wire:model.lazy="items.{{ $index }}.product_description"></textarea>
                        </td>
                        <td colspan="4"></td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </div>
    </div>

    @elseif($billing_type === 'rfx')
    {{-- RFx Table --}}
    <div class="card mt-3">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h6>@lang('rfx.rfxs')</h6>
        <button class="btn btn-primary btn-sm" type="button" wire:click="addRfx">
            <i class="las la-plus"></i> @lang('rfx.add_item')
        </button>
    </div>

    <div class="userDatatable projectDatatable project-table bg-white w-100 border-0">
        <div class="table-responsive">
            <table class="table table-bordered mb-0">
                <thead>
                    <tr>
                        <th>@lang('rfx.task')<span class="text-danger">*</span></th>
                        <th>@lang('rfx.description')<span class="text-danger">*</span></th>
                        <th>@lang('rfx.action')</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse ($rfx as $index => $item)
                        <tr>
                            <td>
                                <input wire:model.defer="rfx.{{ $index }}.task"
                                       class="form-control"
                                       type="text"
                                       placeholder="@lang('rfx.enter_task')"
                                       required>
                                @error("rfx.$index.task") <span class="text-danger small">{{ $message }}</span> @enderror

                            </td>
                            <td>
                                <textarea wire:model.defer="rfx.{{ $index }}.rfx_description"
                                          class="form-control"
                                          placeholder="@lang('rfx.enter_description')"></textarea>
                                @error("rfx.$index.rfx_description") <span class="text-danger small">{{ $message }}</span> @enderror

                            </td>
                            <td>
                                <a href="javascript:void(0);" class="text-danger"
                                   wire:click="removeRfx({{ $index }})">
                                    <i class="iconsax" icon-name="trash"></i>
                                </a>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="3" class="text-center text-muted">@lang('rfx.no_items')</td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
    </div>
</div>


    @endif

     <div class="col-12 justify-content-end mt-4">
    <button wire:click="$set('currentStep', 1)" class="btn btn-info btn-info no-wrap float-left">@lang('rfx.back')</button>     
    <button wire:click.prevent="save" class="btn btn-default btn-primary no-wrap float-right">
@if(isset($rfxId))
    @lang('rfx.update')
@else
    @lang('rfx.create')
@endif


</button>
                        </div>
</div>
<?php
    namespace App\Imports;
    use Illuminate\Support\Collection;
    use Maatwebsite\Excel\Concerns\ToCollection;
    use Maatwebsite\Excel\Concerns\WithCalculatedFormulas;
    use Maatwebsite\Excel\Concerns\WithHeadingRow;
    use Maatwebsite\Excel\Concerns\SkipsOnError;
    use Maatwebsite\Excel\Concerns\WithBatchInserts;
    use Maatwebsite\Excel\Concerns\WithChunkReading;
    use Maatwebsite\Excel\Concerns\Importable;
    use Maatwebsite\Excel\Concerns\SkipsErrors;
    use Illuminate\Support\Facades\Log;
    use App\Http\Traits\FunctionsTrait;
    use App\Http\Traits\BulkImportTempTrait;

    class ServicesImport implements ToCollection, WithCalculatedFormulas, WithHeadingRow, SkipsOnError, WithBatchInserts, WithChunkReading{
        use Importable, SkipsErrors, FunctionsTrait, BulkImportTempTrait;

        protected $bulkImportTempId;

        public function __construct($bulkImportTempId){
            $this->bulkImportTempId = $bulkImportTempId;
        }

        /**
        * @param Collection $collection
        */
        public function collection(Collection $dataRows){
            try {
                $collection = collect();

                if($this->valueIsRequired($dataRows)){
                    Log::info("ServicesImport error: No services sheet found in this file!");
                }

                else{
                   $dataRows->chunk(500)->each(function ($chunk) use ($collection, $dataRows) {
                        $chunk->each(function ($row) use ($collection, $dataRows) {
                            $row['service_name'] = isset($row['service_name']) ? trim($row['service_name']) : null;
                            $row['priority'] = isset($row['priority']) ? trim($row['priority']) : null;
                            $row['service_type'] = isset($row['service_type']) ? trim(strtolower($row['service_type'])) : null;
                            $collection->push($row);
                        });
                    });

                    $filteredCollection = $collection->filter(function ($item) {
                        return collect($item)->filter()->isNotEmpty();
                    });

                    $jsonData = $filteredCollection->toJson();
                    $updatedBulkImportTemp = $this->updateBulkImportTempByValues('id', $this->bulkImportTempId, ['services_data' => $jsonData]);

                    if($updatedBulkImportTemp){
                        Log::info("ServicesImport: Services data processed successfully: ".$filteredCollection->count()." records found.");
                    }

                    else{
                        Log::info("ServicesImport: Unable to import and save the services sheet.");
                    }
                }
            } 
            
            catch (\Throwable $th) {
                Log::error("ServicesImport error: ".$th);
            }
        }

         /**
         * uniqueBy
         *
         * @return void
         */
        public function uniqueBy(){
            return 'service_name';
        }

        /**
         * headingRow
         *
         * @return int
         */
        public function headingRow(): int{
            return 1;
        } 

        /**
         * onError
         *
         * @param  mixed $e
         * @return void
         */
        public function onError(\Throwable $e){
            Log::error("ServicesImport (onError) error: ".$e->getMessage());
        }

        /**
         * batchSize
         *
         * @return int
         */
        public function batchSize(): int{
            return 500;
        }

         /**
         * chunkSize
         *
         * @return int
         */
        public function chunkSize(): int{
            return 500;
        }
    }
?>
<div>
    <div class = "userDatatable projectDatatable project-table bg-white w-100 border-0">
        <div class = "d-flex gap-10 pb-3 border-bottom mb-3">
            <div class = "d-flex gap-5 align-items-center">
                <i class = "iconsax text-black fs-16" icon-name = "task-list-square"></i> 
                <span class = "text-black">{{ $list->total() }} @lang('import.total')</span>
            </div>
            
            @php
                $listData = $this->manageListStatusData();
            @endphp

            <div class = "d-flex gap-5 align-items-center">
                <i class = "iconsax text-success fs-16" icon-name = "tick-square"></i> 
                <span class = "text-success">{{ isset($listData) && count($listData) > 0 ? $listData['acceptedNumber'] : 0 }} @lang('import.accepted')</span>
            </div>
            <div class = "d-flex gap-5 align-items-center">
                <i class = "iconsax text-danger fs-16" icon-name = "info-circle"></i> 
                <span class = "text-danger">{{ isset($listData) && count($listData) > 0 ? $listData['refusedNumber'] : 0 }} @lang('import.data_issues')</span>
            </div>
        </div>
        <div class = "table-responsive">
            <table class = "table mb-0 accordion">
                <thead>
                    <tr>
                        <th scope = "col">
                            <div class = "form-inline">
                                <input type = "radio" value = "{{ \App\Enums\Identifier::PriorityLevel->value }}"class = "mx-1" checked>
                                <label class = "mx-2">@lang("import.priority_name")</label>
                                <i class = "fa fa-question-circle color-light" data-toggle = "tooltip" data-placement = "top" title = "@lang('import.set_priority_name')"></i>
                            </div>
                        </th>
                        <th scope = "col">@lang("import.service_window")</th>
                        <th scope = "col">@lang("import.service_window_type")</th>
                        <th scope = "col">@lang("import.response_time")</th>
                        <th scope = "col">@lang("import.response_time_type")</th>
                        <th scope = "col">@lang("import.status")</th>
                    </tr>   
                </thead>
                <tbody>
                    @if(isset($list) && $list->count())
                        @foreach($list as $key => $data)
                            <tr wire:key = "item-priorities-{{ $key }}"> 
                                <td>{{ $data['priority_name'] ?? '-' }}</td>
                                <td>{{ $data['service_window'] ?? '-' }}</td>
                                <td class = "text-capitalize">{{ $data['service_window_type'] ?? '-' }}</td>
                                <td>{{ $data['response_time'] ?? '-' }}</td>
                                <td class = "text-capitalize">{{ $data['response_time_type'] ?? '-' }}</td>
                                <td>
                                    @if(!is_null($this->fullPrioritiesValidation($data)) && $this->fullPrioritiesValidation($data)[0]['status'] == 'success')
                                        <p class = "text-success">
                                            {{ $this->manageEntredPriorityLevel($data['priority_name'], $projectUserId) == \App\Enums\ModelAction::Insert->value ? __('import.ready_insert') : __('import.ready_update') }}
                                        </p>
                                    @else
                                        <p>
                                            <a href = "javascript:void(0)" class = "bold-700 accordion-toggle text-danger" style = "text-decoration: underline" data-toggle = "collapse" data-target = "#error-{{ $key }}">
                                                @lang('import.view_errors')
                                            </a>
                                        </p>
                                    @endif
                                </td>
                            </tr>
                            <tr>
                                <td colspan = "6" class = "hiddenRow">
                                    <div class = "accordian-body collapse" id = "error-{{ $key }}">
                                        <table class = "table mb-0">
                                            <tbody>
                                                @if(!is_null($this->fullPrioritiesValidation($data)) && $this->fullPrioritiesValidation($data)[0]['status'] <> 'success')
                                                    <ul class = "list-group list-group-flush border">
                                                        @foreach($this->fullPrioritiesValidation($data)[0]['errors'] as $row)
                                                            <li class = "list-group-item fs-12">{!! $row ?? '-' !!}</li>
                                                        @endforeach
                                                    </ul>
                                                @endif
                                            </tbody>
                                        </table>
                                    </div>
                                </td>
                            </tr>
                        @endforeach

                        @if ($list->hasMorePages())
                            <tr>
                                <td colspan = "6">
                                    <div class = "d-flex justify-content-center gap-2">
                                        <div class = "p-2">
                                            <div wire:loading wire:target = "managePerPage">
                                                <button type = "button" class = "btn btn-primary" wire:loading.attr = "disabled">
                                                    <span class = "spinner-border spinner-border-sm" role = "status" aria-hidden = "true"></span>
                                                    @lang('import.loading3')
                                                </button>
                                            </div>
                                            <button type = "button" class = "btn btn-primary text-center" wire:target = "managePerPage" wire:click = "managePerPage" wire:loading.class = "hide">
                                                @lang('import.load_more')
                                            </button>
                                        </div>
                                        <div class = "p-2">
                                            <div wire:loading wire:target = "manageLoadAll">
                                                <button type = "button" class = "btn btn-info" wire:loading.attr = "disabled">
                                                    <span class = "spinner-border spinner-border-sm" role = "status" aria-hidden = "true"></span>
                                                    @lang('import.loading3')
                                                </button>
                                            </div>
                                            <button type = "button" class = "btn btn-info" wire:target = "manageLoadAll" wire:click = "manageLoadAll" wire:loading.class = "hide">
                                                @lang('import.load_all')
                                            </button>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        @endif
                    @else
                        <tr>
                            <td colspan = "6" class = "text-center">@lang("import.empty_priorities")</td>
                        </tr>
                    @endif
                </tbody>
            </table>
        </div>
    </div>
</div>

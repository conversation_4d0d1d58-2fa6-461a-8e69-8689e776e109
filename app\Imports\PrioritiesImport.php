<?php
    namespace App\Imports;
    use Illuminate\Support\Collection;
    use Maatwebsite\Excel\Concerns\ToCollection;
    use Maatwebsite\Excel\Concerns\WithCalculatedFormulas;
    use Maatwebsite\Excel\Concerns\WithHeadingRow;
    use Maatwebsite\Excel\Concerns\SkipsOnError;
    use Maatwebsite\Excel\Concerns\WithBatchInserts;
    use Maatwebsite\Excel\Concerns\WithChunkReading;
    use Maatwebsite\Excel\Concerns\Importable;
    use Maatwebsite\Excel\Concerns\SkipsErrors;
    use Illuminate\Support\Facades\Log;
    use App\Http\Traits\FunctionsTrait;
    use App\Http\Traits\BulkImportTempTrait;

    class PrioritiesImport implements ToCollection, WithCalculatedFormulas, WithHeadingRow, SkipsOnError, WithBatchInserts, WithChunkReading{
        use Importable, SkipsErrors, FunctionsTrait, BulkImportTempTrait;

        protected $bulkImportTempId;

        public function __construct($bulkImportTempId){
            $this->bulkImportTempId = $bulkImportTempId;
        }

        /**
        * @param Collection $collection
        */
        public function collection(Collection $dataRows){
            try {
                $collection = collect();
                
                if($this->valueIsRequired($dataRows)){
                    Log::info("PrioritiesImport error: No priorities sheet found in this file!");
                }

                else{
                    $dataRows->chunk(500)->each(function ($chunk) use ($collection, $dataRows) {
                        $chunk->each(function ($row) use ($collection, $dataRows) {
                            $row['priority_name'] = isset($row['priority_name']) ? trim($row['priority_name']) : null;
                            $row['service_window'] = isset($row['service_window']) ? trim($row['service_window']) : null;
                            $row['service_window_type'] = isset($row['service_window_type']) ? trim(strtolower($row['service_window_type'])) : null;
                            $row['response_time'] = isset($row['response_time']) ? trim($row['response_time']) : null;
                            $row['response_time_type'] = isset($row['response_time_type']) ? trim(strtolower($row['response_time_type'])) : null;
                            $collection->push($row);
                        });
                    });

                    $filteredCollection = $collection->filter(function ($item) {
                        return collect($item)->filter()->isNotEmpty();
                    });

                    $jsonData = $filteredCollection->toJson();
                    $updatedBulkImportTemp = $this->updateBulkImportTempByValues('id', $this->bulkImportTempId, ['priorities_data' => $jsonData]);

                    if($updatedBulkImportTemp){
                        Log::info("PrioritiesImport: Priorities data processed successfully: ".$filteredCollection->count()." records found.");
                    }

                    else{
                        Log::info("PrioritiesImport: Unable to import and save the priorities sheet.");
                    }
                }
            } 
            
            catch (\Throwable $th) {
                Log::error("PrioritiesImport error: ".$th);
            }
        }

         /**
         * uniqueBy
         *
         * @return void
         */
        public function uniqueBy(){
            return 'priority_name';
        }

        /**
         * headingRow
         *
         * @return int
         */
        public function headingRow(): int{
            return 1;
        } 

         /**
         * onError
         *
         * @param  mixed $e
         * @return void
         */
        public function onError(\Throwable $e){
            Log::error("PrioritiesImport (onError) error: ".$e->getMessage());
        }

        /**
         * batchSize
         *
         * @return int
         */
        public function batchSize(): int{
            return 500;
        }

         /**
         * chunkSize
         *
         * @return int
         */
        public function chunkSize(): int{
            return 500;
        }
    }
?>
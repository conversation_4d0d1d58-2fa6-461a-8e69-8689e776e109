<?php
    namespace App\Jobs\BulkImport;
    use Illuminate\Bus\Queueable;
    use Illuminate\Bus\Batchable;
    use Illuminate\Contracts\Queue\ShouldBeUnique;
    use Illuminate\Contracts\Queue\ShouldQueue;
    use Illuminate\Foundation\Bus\Dispatchable;
    use Illuminate\Queue\InteractsWithQueue;
    use Illuminate\Queue\SerializesModels;
    use Illuminate\Support\Facades\Log;
    use App\Http\Traits\ProjectDetailTrait;
    use App\Http\Traits\BulkImportErrorTrait;
    use App\Http\Traits\BulkImportListTrait;
    use App\Http\Traits\BulkImportTrait; 
    use App\Http\Traits\PropertyTrait; 
    use App\Http\Traits\FunctionsTrait; 
    use App\Http\Traits\RoomsTypeFloorTrait; 
    use App\Http\Traits\AssetNameTrait; 
    use App\Http\Traits\AssetNameAssetCategoryTrait;
    use App\Http\Traits\AssetHistoryTrait;
    use App\Http\Traits\PropertyBuildingTrait;
    use App\Http\Traits\ServiceTrait;
    use App\Http\Traits\AssetTrait;
    use App\Enums\ResultType;
    use App\Enums\ModelAction;
    use App\Enums\ValidationBukImport; 

    class AssetImportJob implements ShouldQueue{
        use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, Batchable, ProjectDetailTrait, BulkImportErrorTrait, BulkImportListTrait, BulkImportTrait, PropertyTrait, FunctionsTrait, RoomsTypeFloorTrait, AssetNameTrait, AssetNameAssetCategoryTrait, AssetHistoryTrait, PropertyBuildingTrait, ServiceTrait, AssetTrait;
        public $list;
        public $projectId;
        public $bulkImportDetailsId;
        public $projectUserId;
        public $userId;

        /**
         * Create a new job instance.
         *
         * @return void
         */
        public function __construct($list, $projectId, $bulkImportDetailsId, $projectUserId, $userId){
            $this->list = $list;
            $this->projectId = $projectId;
            $this->bulkImportDetailsId = $bulkImportDetailsId;
            $this->projectUserId = $projectUserId;
            $this->userId = $userId;
        }

        /**
         * Execute the job.
         *
         * @return void
         */
        public function handle(){
            try {
                $bulkArray = [];
                $project = $this->getProjectDetailInformationByProjectId($this->projectId);

                if(is_null($project)){
                    Log::info("AssetImportJob error: No project found with this projectId : ".$this->projectId); 
                }

                elseif(!isset($this->list)){
                    Log::info("AssetImportJob error: The assets list is empty"); 
                }

                else{
                    foreach($this->list as $data){
                        $mapChecking = $this->fullAssetsValidation($data);

                        if(!is_null($mapChecking) && $mapChecking[0]['status'] <> 'success'){
                            $bulkArrayErrors = ['map_status' => true, 'backend_status' => false, 'project_id' => $this->projectId, 'user_id' => $this->userId, 'bulk_import_id' => $this->bulkImportDetailsId, 'sheet' => ResultType::Assets->value, 'identifier' => $data['asset_number'], 'row_status' => ModelAction::NotInserted->value, 'created_by' => $this->userId];
                            $bulkImportErrorId = $this->saveBulkImportError($bulkArrayErrors);

                            if(!$bulkImportErrorId){
                                Log::info("AssetImportJob error: Cannot save the bulk import error row for Assets sheet : Project ID: ".$this->projectId.", Property Name: ".$data['property_name'].", Building Name: ".$data['building_name'].", Asset Number: ".$data['asset_number']); 
                            }
                        }

                        else{
                            $purchaseDate = isset($data['purchase_date']) && !$this->checkIfValidDate($data['purchase_date']) ? null : $data['purchase_date'];
                            $assetStatus = isset($data['asset_status']) && !in_array($data['asset_status'], array('new', 'used', 'damaged', 'abandoned', 'disposed', 'donated', 'escrowed', 'for sale', 'in repair', 'in service', 'in storage', 'in use', 'leased', 'expired', 'missing', 'n/a', 'out of service', 'owned', 'owned and leased', 'pipeline', 'salvaged', 'sold', 'stolen', 'sub-leased', 'sub left', 'under contract', 'unknown')) ? null : $data['asset_status'];
                            $statusDate = isset($data['status_date']) && !$this->checkIfValidDate($data['status_date']) ? null : $data['status_date'];
                            $propertyRow = $this->getPropertyInformationsByValues("property_tag", $data['property_name'], $this->projectUserId);

                            if(isset($propertyRow)){
                                $buildingName = $propertyRow->property_type == 'building' ? $data['property_name'] : $data['building_name'];
                                $propertyBuildingRow = $this->getPropertyBuildingInformationsByValues($propertyRow->id, $buildingName);

                                if(isset($propertyBuildingRow)){
                                    $serviceRow = $this->getSpeceficServiceInformationsByValues('asset_category', $data['service_type'], $this->projectUserId);

                                    if(isset($serviceRow)){
                                        $generateQrCode = $this->fullGenerateQrCodeFunction(1000000000, 9999999999);

                                        if(isset($generateQrCode) && count($generateQrCode) > 0){
                                            $assetTag = $data['asset_symbol'].$data['asset_number'] ?? null;
                                            $purchaseDate = isset($purchaseDate) ? $this->changeDateFormatByCarbon('Y-m-d', $purchaseDate) : null;
                                            $statusDate = isset($statusDate) ? $this->changeDateFormatByCarbon('Y-m-d', $statusDate) : null;
                                            $randomNumber = $generateQrCode['randomNumber'];
                                            $dataUrl = $generateQrCode['dataUrl'];
                                            $assetRow = $this->getAssetInformationsByBuildingIdAndValues($this->projectUserId, $propertyBuildingRow->id, $data['asset_number'], $data['asset_name']);
                                            $roomTypeFloorRow = $this->getRoomTypeFloorByData($data['zone'], $data['unit'], $propertyRow->id, $propertyBuildingRow->id);
                                            $assetNameRow = $this->getAssetNameInformationsByAllValues($this->projectUserId, $data['asset_name'], $serviceRow->id);
                                            
                                            if(!isset($assetRow)){
                                                if(isset($roomTypeFloorRow)){
                                                    $array = [
                                                        'user_id' => $this->projectUserId ?? null,
                                                        'asset_name' => $data['asset_name'] ?? null,
                                                        'asset_category_id' => $serviceRow->id ?? null,
                                                        'asset_symbol' => $data['asset_symbol'] ?? null,
                                                        'last_ip' => $this->getCurrentIpAddress()
                                                    ];

                                                    $newAssetNameId = isset($assetNameRow) ? $assetNameRow->id : $this->saveAssetname($array);
                                                    
                                                    if($newAssetNameId){
                                                        $array = [
                                                            'asset_name_id' => $newAssetNameId,
                                                            'asset_category_id' => $serviceRow->id
                                                        ];

                                                        $assetNameServiceRow = $this->getAssetNameAssetCategoriesByValues($newAssetNameId, $serviceRow->id);
                                                        $newAssetNameServiceId = isset($assetNameServiceRow) ? $assetNameServiceRow->id : $this->saveAssetNameAssetCategory($array);

                                                        if($newAssetNameServiceId){
                                                            $assetArray = [
                                                                'asset_tag' => $assetTag ?? null,
                                                                'user_id' => $this->projectUserId ?? null,
                                                                'property_id' => $propertyRow->id ?? null,
                                                                'floor' => $data['zone'] ?? null,
                                                                'room' => $data['unit'] ?? null,
                                                                'unit_id' => $roomTypeFloorRow->id ?? null,
                                                                'asset_category_id' => $serviceRow->id ?? null,
                                                                'asset_name_id' => $newAssetNameId ?? null,
                                                                'asset_number' => $data['asset_number'] ?? null,
                                                                'last_ip' => $this->getCurrentIpAddress(),
                                                                'barcode_value' => $randomNumber ?? null,
                                                                'barcode_select' => 0,
                                                                'barcode_img_str' => $dataUrl ?? null,
                                                                'asset_symbol' => $data['asset_symbol'] ?? null,
                                                                'building_id' => $propertyBuildingRow->id ?? null,
                                                                'purchase_date' => $purchaseDate ?? null,
                                                                'model_number' => $data['model_number'] ?? null,
                                                                'manufacturer_name' => $data['manufacturer_name'] ?? null,
                                                                'asset_status' => $assetStatus ?? null,
                                                                'asset_damage_date' => $statusDate ?? null
                                                            ];
                                                            
                                                            $newAssetId = $this->saveAsset($assetArray);
                                                            
                                                            if($newAssetId){
                                                                $newAssetHistoryId = $this->saveAssetHistory('added', $this->userId, $newAssetId, null, $this->getCurrentDateTime());

                                                                if($newAssetHistoryId){
                                                                    array_push($bulkArray, $newAssetId);
                                                                }

                                                                else{
                                                                  $bulkArrayErrors = ['map_status' => false, 'backend_status' => true, 'project_id' => $this->projectId, 'user_id' => $this->userId, 'bulk_import_id' => $this->bulkImportDetailsId, 'sheet' => ResultType::Assets->value, 'identifier' => $data['asset_number'], 'errors' => ValidationBukImport::AssetHistoryNotSaved->value, 'value' => $data['building_name'], 'row_status' => ModelAction::NotInserted->value, 'created_by' => $this->userId];
                                                                    $bulkImportErrorId = $this->saveBulkImportError($bulkArrayErrors);

                                                                    if(!$bulkImportErrorId){
                                                                        Log::info("AssetImportJob error: Cannot save the bulk import error row for Asset sheet : Project ID: ".$this->projectId.", Property Name: ".$data['property_name']); 
                                                                    }    
                                                                }
                                                            }

                                                            else{
                                                                $bulkArrayErrors = ['map_status' => false, 'backend_status' => true, 'project_id' => $this->projectId, 'user_id' => $this->userId, 'bulk_import_id' => $this->bulkImportDetailsId, 'sheet' => ResultType::Assets->value, 'identifier' => $data['asset_number'], 'errors' => ValidationBukImport::AssetNotSaved->value, 'value' => $data['building_name'], 'row_status' => ModelAction::NotInserted->value, 'created_by' => $this->userId];
                                                                $bulkImportErrorId = $this->saveBulkImportError($bulkArrayErrors);

                                                                if(!$bulkImportErrorId){
                                                                    Log::info("AssetImportJob error: Cannot save the bulk import error row for Asset sheet : Project ID: ".$this->projectId.", Property Name: ".$data['property_name']); 
                                                                }  
                                                            }
                                                        }
                                                        
                                                        else{
                                                            $bulkArrayErrors = ['map_status' => false, 'backend_status' => true, 'project_id' => $this->projectId, 'user_id' => $this->userId, 'bulk_import_id' => $this->bulkImportDetailsId, 'sheet' => ResultType::Assets->value, 'identifier' => $data['asset_number'], 'errors' => ValidationBukImport::AssetNameServiceNotSaved->value, 'value' => $data['asset_name'], 'row_status' => ModelAction::NotInserted->value, 'created_by' => $this->userId];
                                                            $bulkImportErrorId = $this->saveBulkImportError($bulkArrayErrors);

                                                            if(!$bulkImportErrorId){
                                                                Log::info("AssetImportJob error: Cannot save the bulk import error row for Asset sheet : Project ID: ".$this->projectId.", Property Name: ".$data['property_name']); 
                                                            }  
                                                        }
                                                    }

                                                    else{
                                                        $bulkArrayErrors = ['map_status' => false, 'backend_status' => true, 'project_id' => $this->projectId, 'user_id' => $this->userId, 'bulk_import_id' => $this->bulkImportDetailsId, 'sheet' => ResultType::Assets->value, 'identifier' => $data['asset_number'], 'errors' => ValidationBukImport::AssetNameNotSaved->value, 'value' => $data['asset_name'], 'row_status' => ModelAction::NotInserted->value, 'created_by' => $this->userId];
                                                        $bulkImportErrorId = $this->saveBulkImportError($bulkArrayErrors);

                                                        if(!$bulkImportErrorId){
                                                            Log::info("AssetImportJob error: Cannot save the bulk import error row for Asset sheet : Project ID: ".$this->projectId.", Property Name: ".$data['property_name']); 
                                                        }  
                                                    }
                                                }
                                                
                                                else{
                                                    $bulkArrayErrors = ['map_status' => false, 'backend_status' => true, 'project_id' => $this->projectId, 'user_id' => $this->userId, 'bulk_import_id' => $this->bulkImportDetailsId, 'sheet' => ResultType::Assets->value, 'identifier' => $data['asset_number'], 'errors' => ValidationBukImport::RoomTypeFloorNotExist->value, 'value' => $data['unit'], 'row_status' => ModelAction::NotInserted->value, 'created_by' => $this->userId];
                                                    $bulkImportErrorId = $this->saveBulkImportError($bulkArrayErrors);

                                                    if(!$bulkImportErrorId){
                                                        Log::info("AssetImportJob error: Cannot save the bulk import error row for Asset sheet : Project ID: ".$this->projectId.", Property Name: ".$data['property_name']); 
                                                    }  
                                                }
                                            }

                                            else{
                                                if(isset($roomTypeFloorRow)){
                                                    if(isset($assetNameRow)){
                                                         $assetArray = [
                                                            'asset_tag' => $assetTag ?? null,
                                                            'user_id' => $this->projectUserId ?? null,
                                                            'property_id' => $propertyRow->id ?? null,
                                                            'floor' => $data['zone'] ?? null,
                                                            'room' => $data['unit'] ?? null,
                                                            'unit_id' => $roomTypeFloorRow->id ?? null,
                                                            'asset_category_id' => $serviceRow->id ?? null,
                                                            'asset_name_id' => $assetNameRow->id ?? null,
                                                            'asset_number' => $data['asset_number'] ?? null,
                                                            'last_ip' => $this->getCurrentIpAddress(),
                                                            'barcode_value' => $randomNumber ?? null,
                                                            'barcode_select' => 0,
                                                            'barcode_img_str' => $dataUrl ?? null,
                                                            'asset_symbol' => $data['asset_symbol'] ?? null,
                                                            'building_id' => $propertyBuildingRow->id ?? null,
                                                            'purchase_date' => $purchaseDate ?? null,
                                                            'model_number' => $data['model_number'] ?? null,
                                                            'manufacturer_name' => $data['manufacturer_name'] ?? null,
                                                            'asset_status' => $assetStatus ?? null,
                                                            'asset_damage_date' => $statusDate ?? null
                                                        ];

                                                        $newUpdatedAsset = $this->updateAssetInformationsById($assetRow->id, $assetArray);

                                                        if($newUpdatedAsset){
                                                            $newAssetHistoryId = $this->saveAssetHistory('edited', $this->userId, $assetRow->id, null, $this->getCurrentDateTime());

                                                            if($newAssetHistoryId){
                                                                array_push($bulkArray, $assetRow->id);
                                                            }

                                                            else{
                                                                $bulkArrayErrors = ['map_status' => false, 'backend_status' => true, 'project_id' => $this->projectId, 'user_id' => $this->userId, 'bulk_import_id' => $this->bulkImportDetailsId, 'sheet' => ResultType::Assets->value, 'identifier' => $data['asset_number'], 'errors' => ValidationBukImport::AssetHistoryNotSaved->value, 'value' => $data['building_name'], 'row_status' => ModelAction::NotInserted->value, 'created_by' => $this->userId];
                                                                $bulkImportErrorId = $this->saveBulkImportError($bulkArrayErrors);

                                                                if(!$bulkImportErrorId){
                                                                    Log::info("AssetImportJob error: Cannot save the bulk import error row for Asset sheet : Project ID: ".$this->projectId.", Property Name: ".$data['property_name']); 
                                                                }    
                                                            }
                                                        }

                                                        else{
                                                            $bulkArrayErrors = ['map_status' => false, 'backend_status' => true, 'project_id' => $this->projectId, 'user_id' => $this->userId, 'bulk_import_id' => $this->bulkImportDetailsId, 'sheet' => ResultType::Assets->value, 'identifier' => $data['asset_number'], 'errors' => ValidationBukImport::AssetNotUpdated->value, 'value' => $buildingName, 'row_status' => ModelAction::NotInserted->value, 'created_by' => $this->userId];
                                                            $bulkImportErrorId = $this->saveBulkImportError($bulkArrayErrors);

                                                            if(!$bulkImportErrorId){
                                                                Log::info("AssetImportJob error: Cannot save the bulk import error row for Asset sheet : Project ID: ".$this->projectId.", Property Name: ".$data['property_name']); 
                                                            }  
                                                        }
                                                    }

                                                    else{
                                                        $bulkArrayErrors = ['map_status' => false, 'backend_status' => true, 'project_id' => $this->projectId, 'user_id' => $this->userId, 'bulk_import_id' => $this->bulkImportDetailsId, 'sheet' => ResultType::Assets->value, 'identifier' => $data['asset_number'], 'errors' => ValidationBukImport::AssetNameNotSaved->value, 'value' => $data['asset_name'], 'row_status' => ModelAction::NotInserted->value, 'created_by' => $this->userId];
                                                        $bulkImportErrorId = $this->saveBulkImportError($bulkArrayErrors);

                                                        if(!$bulkImportErrorId){
                                                            Log::info("AssetImportJob error: Cannot save the bulk import error row for Asset sheet : Project ID: ".$this->projectId.", Property Name: ".$data['property_name']); 
                                                        }  
                                                    }
                                                }

                                                else{
                                                    $bulkArrayErrors = ['map_status' => false, 'backend_status' => true, 'project_id' => $this->projectId, 'user_id' => $this->userId, 'bulk_import_id' => $this->bulkImportDetailsId, 'sheet' => ResultType::Assets->value, 'identifier' => $data['asset_number'], 'errors' => ValidationBukImport::RoomTypeFloorNotExist->value, 'value' => $data['unit'], 'row_status' => ModelAction::NotInserted->value, 'created_by' => $this->userId];
                                                    $bulkImportErrorId = $this->saveBulkImportError($bulkArrayErrors);

                                                    if(!$bulkImportErrorId){
                                                        Log::info("AssetImportJob error: Cannot save the bulk import error row for Asset sheet : Project ID: ".$this->projectId.", Property Name: ".$data['property_name']); 
                                                    }  
                                                }
                                            }
                                        }

                                        else{
                                            $bulkArrayErrors = ['map_status' => false, 'backend_status' => true, 'project_id' => $this->projectId, 'user_id' => $this->userId, 'bulk_import_id' => $this->bulkImportDetailsId, 'sheet' => ResultType::Assets->value, 'identifier' => $data['asset_number'], 'errors' => ValidationBukImport::NoQRCodeGenerated->value, 'value' => $data['asset_symbol'], 'row_status' => ModelAction::NotInserted->value, 'created_by' => $this->userId];
                                            $bulkImportErrorId = $this->saveBulkImportError($bulkArrayErrors);

                                            if(!$bulkImportErrorId){
                                                Log::info("AssetImportJob error: Cannot save the bulk import error row for Asset sheet : Project ID: ".$this->projectId.", Property Name: ".$data['property_name']); 
                                            }  
                                        }
                                    }

                                    else{
                                        $bulkArrayErrors = ['map_status' => false, 'backend_status' => true, 'project_id' => $this->projectId, 'user_id' => $this->userId, 'bulk_import_id' => $this->bulkImportDetailsId, 'sheet' => ResultType::Assets->value, 'identifier' => $data['asset_number'], 'errors' => ValidationBukImport::ServiceNotExist->value, 'value' => $data['service_type'], 'row_status' => ModelAction::NotInserted->value, 'created_by' => $this->userId];
                                        $bulkImportErrorId = $this->saveBulkImportError($bulkArrayErrors);

                                        if(!$bulkImportErrorId){
                                            Log::info("AssetImportJob error: Cannot save the bulk import error row for Asset sheet : Project ID: ".$this->projectId.", Property Name: ".$data['property_name']); 
                                        }  
                                    }
                                }
                                
                                else{
                                    $bulkArrayErrors = ['map_status' => false, 'backend_status' => true, 'project_id' => $this->projectId, 'user_id' => $this->userId, 'bulk_import_id' => $this->bulkImportDetailsId, 'sheet' => ResultType::Assets->value, 'identifier' => $data['asset_number'], 'errors' => ValidationBukImport::PropertyBuildingNotExist->value, 'value' => $buildingName, 'row_status' => ModelAction::NotInserted->value, 'created_by' => $this->userId];
                                    $bulkImportErrorId = $this->saveBulkImportError($bulkArrayErrors);

                                    if(!$bulkImportErrorId){
                                        Log::info("AssetImportJob error: Cannot save the bulk import error row for Asset sheet : Project ID: ".$this->projectId.", Property Name: ".$data['property_name']); 
                                    }
                                }
                            }

                            else{
                                $bulkArrayErrors = ['map_status' => false, 'backend_status' => true, 'project_id' => $this->projectId, 'user_id' => $this->userId, 'bulk_import_id' => $this->bulkImportDetailsId, 'sheet' => ResultType::Assets->value, 'identifier' => $data['asset_number'], 'errors' => ValidationBukImport::PropertyNotExist->value, 'value' => $data['property_name'], 'row_status' => ModelAction::NotInserted->value, 'created_by' => $this->userId];
                                $bulkImportErrorId = $this->saveBulkImportError($bulkArrayErrors);

                                if(!$bulkImportErrorId){
                                    Log::info("AssetImportJob error: Cannot save the bulk import error row for Asset sheet : Project ID: ".$this->projectId.", Property Name: ".$data['property_name']); 
                                }
                            }
                        }
                    }
                }

                $implodedValue = isset($bulkArray) && count($bulkArray) > 0 ? $this->implodeDataFromField($bulkArray) : null;
                $bulkImportList = $this->getBulkImportListByValues('bulk_import_id', $this->bulkImportDetailsId);
                $result = null;

                $array = [
                    'assets' => $implodedValue,
                    'project_id' => $this->projectId,
                    'bulk_import_id' => $this->bulkImportDetailsId,
                    'created_by' => !isset($bulkImportList) ? $this->userId : $bulkImportList->created_by,
                    'updated_by' => isset($bulkImportList) ? $this->userId : null,
                ];
                
                if(isset($bulkImportList)){
                    $result = $this->updateBulkImportListByValues('bulk_import_id', $this->bulkImportDetailsId, $array);
                }

                else{
                    $result = $this->saveBulkImportList($array);
                }

                if(!$result){
                    Log::info("AssetImportJob error: We cannot do any action on bulk import table for assets column (Same data)"); 
                }
            } 
            
            catch (\Throwable $th) {
                Log::error("AssetImportJob error: ".$th);
            }
        }
    }
?>
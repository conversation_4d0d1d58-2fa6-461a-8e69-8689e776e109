@extends('layouts.app')
@section('styles')
@endsection
@section('content')
<div class="contents">
   <div class="container-fluid">
      <div class="row">
         <div class="col-lg-12">
            <div class="page-title-wrap">
                <!-- {{ Breadcrumbs::render('add-user') }} -->
                <div class="page-title d-flex justify-content-between">
                    <div class="page-title__left">
                        <div class="d-flex align-items-center user-member__title justify-content-center mr-sm-25">
                            <h4 class="text-capitalize fw-500 breadcrumb-title">
                        <a href="Javascript:history.back()"><i class="las la-arrow-left"></i></a> {{__('user_management_module.user_button.edit_user')}}</h4>
                         </div>
                    </div>
                </div>
            </div>
         </div>
      </div>
   </div>
   <div class="container-fluid">
      <div class=" checkout wizard1 wizard7 global-shadow px-sm-50 px-20 py-sm-50 py-30 mb-30 bg-white radius-xl w-100">
         <div class="row justify-content-center">
            <div class="col-xl-8">
               <div class="checkout-progress-indicator content-center col-md-10">
                  <div class="checkout-progress">
                     <div class="step current" id="1">
                        <span>1</span>
                        <span>{{__('user_management_module.common.user_info')}}</span>
                     </div>
                     <div class="current"><img src="{{ asset('img/svg/checkout.svg') }}" alt="img" class="svg"></div>
                     <div class="step" id="2">
                        <span>2</span>
                        <span>{{__('user_management_module.common.user_role')}}</span>
                     </div>
                     <div class="current"><img src="{{ asset('img/svg/checkout.svg') }}" alt="img" class="svg"></div>
                     <div class="step" id="3">
                        <span>3</span>
                        <span>{{__('user_management_module.common.user_previleges')}}</span>
                     </div>
                     <div class="current"><img src="{{ asset('img/svg/checkout.svg') }}" alt="img" class="svg"></div>
                     <div class="step" id="4">
                        <span>4</span>
                        <span>{{__('user_management_module.common.confirm')}}</span>
                     </div>
                  </div>
               </div>
               <!-- checkout -->
               <div class="row justify-content-center">
                  <div class="col-xl-7 col-lg-8 col-sm-10">
                     <div class="card checkout-shipping-form px-30 pt-2 pb-30 border-0">
                        <div class="card-header border-bottom-0 align-content-start pb-sm-0 pb-1 px-0">
                           <h4 class="fw-400">{{__('user_management_module.common.user_info')}}</h4>
                        </div>
                        <div class="card-body px-0 pb-0">
                             <div class="edit-profile__body">
                                <?php
                                 $form_id = 'user_create_form_edit';
                                 ?>
                                 @if($data['u_data']->user_type == 'sp_worker' || $data['u_data']->user_type == 'team_leader')
                                 <?php
                                 $form_id = 'user_create_form_edit_worker';
                                 ?>
                                 @endif
                                <form method="post" id="{{ $form_id }}" class="edit_user_form" action="{{ route('users.edit.role', Crypt::encryptString($data['u_data']->id))}}" enctype="multipart/form-data" autocomplete="off">
                                 @csrf
                                 <input type="hidden" id="user_id" name="user_id" value="{{ $data['u_data']->id }}">
                                 <input type="hidden" id="service_provider_hidde" name="service_provider" value="{{ $data['u_data']->service_provider }}">


                                 <input type="hidden" id="supervisor_id_hidden" name="hidden_supervisor_id" value="{{ $data['u_data']->supervisor_id }}">
                                 <input type="hidden" id="sp_admin_id_hidden" name="sp_admin_id" value="{{ $data['u_data']->sp_admin_id }}">
                                 <div class="account-profile d-flex align-items-center mb-4 ">
                                    <div class="pro_img_wrapper">
                                       <input id="file_upload" type="file" name="profile_img" class="d-none" accept="image/*">
                                       <!-- Profile picture image-->
                                       <input type="hidden" name="isImageRemove" value="">
                                       <label for="file_upload">                                       
                                       <img class="ap-img__main rounded-circle wh-120 bg-lighter d-flex" src="{{($data['u_data']->profile_img) ?ImagesUploadHelper::displayImage($data['u_data']->profile_img, 'uploads/profile_images') : '/img/upload.png' }}" alt="profile" id="output_pic">
                                       <span class="cross" id="remove_pro_pic" >
                                       <span data-feather="camera" ></span>
                                       </span>
                                       </label>
                                       <?php
                                          if(trim($data['u_data']->profile_img) != "")
                                          {
                                             $hideclass = '';
                                          }
                                          else
                                          {
                                             $hideclass = 'hide';
                                          }
                                       ?>
                                       <span class="remove-img text-white btn-danger rounded-circle <?=$hideclass;?>" data-toggle="modal" data-target="#confirmDeletePhoto">
                                         <span data-feather="x"></span>
                                       </span>
                                    </div>
                                    <div class="account-profile__title">
                                       <h6 class="fs-15 ml-20 fw-500 text-capitalize">{{__('user_management_module.user_forms.label.photo')}}</h6>
                                    </div>
                                 </div>


                                  <div class="form-group mb-20">
                                      <div class="usertype_option">
                                         <label for="user_type">
                                         {{__('user_management_module.user_forms.label.user_type')}} <small class="required">*</small>
                                         </label>
                                         <input type="hidden" name="user_type" id="user_type" value="{{$data['u_data']->user_type}}">

                                         <select class="form-control select2" id="user_type" name="user_type" required disabled>
                                            <option value="" selected disabled>{{__('user_management_module.user_forms.label.user_type')}}</option>
                                            @foreach($data['usertypeList'] as $user_type)
                                            @if($user_type->slug=="supervisor" || $user_type->slug=="sp_worker" || $user_type->slug=="team_leader")
                                            @if(App::getLocale()=='en')
                                                   <option @if(isset($data['u_data']->user_type) && ($data['u_data']->user_type==$user_type->slug)) selected @endif value="{{$user_type->slug}}">{{$user_type->name}}</option>
                                                @else
                                                   <option @if(isset($data['u_data']->user_type) && ($data['u_data']->user_type==$user_type->slug)) selected @endif value="{{$user_type->slug}}">{{$user_type->name_ar}}</option>
                                                @endif
                                            @endif
                                            @endforeach
                                         </select>
                                      </div>

                                   </div>

                                   @if(Auth::user()->user_type != 'sp_admin')
                                   <div class="form-group mb-20 company_info">
                                      <div class="service_provider">
                                         <label for="service_provider">
                                         {{__('user_management_module.user_forms.label.company_name')}} <small class="required">*</small>
                                         </label>
                                         <input type="hidden" name="service_provider" value="{{$data['u_data']->service_provider}}" >
                                         <select class="form-control select2" id="service_provider" name="service_provider" disabled>
                                            <option value="" selected disabled>{{__('user_management_module.user_forms.label.company_name')}}</option>
                                            @foreach($data['companyList'] as $company)
                                              <option value="{{$company->id}}" @if($data['u_data']->service_provider==$company->id) selected @endif>{{$company->name}} - {{$company->service_provider_id}}</option>
                                            @endforeach
                                         </select>
                                      </div>

                                   </div>
                                   @else


                                   <div class="form-group mb-15 worker_info">
                                      <div class="usertype_option">
                                          <div id="supervisor_id_label">
                                                <label for="supervisor_id">
                                             {{__('user_management_module.user_forms.label.worker_admin')}} <small class="required">*</small>
                                             </label>
                                          </div>
                                         
                                         <select class="form-control" id="supervisor_id" name="supervisor_id[]" required multiple>
                                             <option disabled>{{__('user_management_module.user_forms.label.worker_admin')}}</option>
                                            @if(isset($data['sp_supervisor_list']))
                                            @foreach($data['sp_supervisor_list'] as $sp_supervisor)
                                              <option value="{{$sp_supervisor->id}}" @if(in_array($sp_supervisor->id, explode(',',$data['u_data']->supervisor_id))) selected @endif>{{$sp_supervisor->name}}</option>
                                            @endforeach
                                            @endif
                                         </select>
                                      </div>
                                      <div id="supervisor_id_error"></div>
                                   </div>
                                   @endif
                                   @if(Auth::user()->user_type != 'sp_admin')
                                   <div class="form-group mb-20 sup_info">
                                      <div class="usertype_option">
                                         <label for="user_type">
                                         {{__('user_management_module.user_forms.label.employee_admin')}} <small class="required">*</small>
                                         </label>
                                         <select class="form-control" id="sp_admin_id" >
                                            <option value="" selected disabled>{{__('user_management_module.user_forms.label.employee_admin')}}</option>
                                           @if(isset($data['sp_admin_list']))
                                            @foreach($data['sp_admin_list'] as $sp_admin)
                                              <option value="{{$sp_admin->id}}" @if($data['u_data']->sp_admin_id==$sp_admin->id) selected @endif>{{$sp_admin->name}}</option>
                                            @endforeach
                                            @endif
                                         </select>
                                      </div>
                                   </div>
                                   @endif

                                    <div class="form-group mb-20 building_admin_info">
                                      <div class="usertype_option">
                                         <label for="building_admin">
                                         {{__('user_management_module.user_forms.label.employee_admin')}} <small class="required">*</small>
                                         </label>
                                         <select class="form-control" id="building_admin" >
                                            <option value="" selected disabled>{{__('user_management_module.user_forms.label.employee_admin')}}</option>
                                            @if(isset($data['building_manager_list']))

                                              @foreach($data['building_manager_list'] as $building_manager)
                                                <option value="{{$building_manager->id}}" @if($data['u_data']->sp_admin_id==$building_manager->id) selected @endif>{{$building_manager->name}}</option>
                                              @endforeach

                                            @endif
                                         </select>
                                      </div>

                                   </div>

                                   <div class="form-group mb-20 spga_admin_info">
                                      <div class="usertype_option">
                                         <label for="spga_admin">
                                         {{__('user_management_module.user_forms.label.employee_admin')}} <small class="required">*</small>
                                         </label>
                                         <select class="form-control" id="spga_admin" >
                                            <option value="" selected disabled>{{__('user_management_module.user_forms.label.employee_admin')}}</option>
                                            @if(isset($data['spga_admin_list']))

                                              @foreach($data['spga_admin_list'] as $spga_admin)
                                                <option value="{{$spga_admin->id}}" @if($data['u_data']->sp_admin_id==$spga_admin->id) selected @endif>{{$spga_admin->name}}</option>
                                              @endforeach

                                            @endif
                                         </select>
                                      </div>

                                   </div>
                                   <input type="hidden" name="sp_admin_id" id="employee_admin_id" value="{{$data['u_data']->sp_admin_id}}">


                                   <div class="form-group mb-20 user_info">


                                   @if($data['u_data']->user_type == 'sp_worker')

                                   <label for="emp_name" id="user_type_name">{{__('user_management_module.user_forms.label.worker_name')}} <small class="required">*</small></label>
                                   
                                   @elseif($data['u_data']->user_type == 'team_leader')

                                   <label for="emp_name" id="user_type_name">{{__('user_management_module.team_leader_module.team_leader_name')}} <small class="required">*</small></label>
                                   @else
                                   <label for="emp_name" id="user_type_name">{{__('user_management_module.user_forms.label.emp_name')}} <small class="required">*</small></label>

                                   @endif
                                      <input type="text" class="form-control" name="name" id="name" placeholder="{{__('user_management_module.user_forms.place_holder.emp_name')}}" value="{{($data['u_data']->name) ? $data['u_data']->name  : '' }}">
                                   </div>
                                   <div class="form-group mb-20 user_info">

                                      <div class="employee_label">
                                      @if($data['u_data']->user_type == 'sp_worker')
                                      <label for="worker_id">{{__('user_management_module.user_forms.label.worker_id')}} <i class="fas fa-question-circle" onfocus="theFocus(this);"  data-toggle="tooltip" data-placement="top" title="{{__('user_management_module.common.worker_id_description')}}"></i><small class="required">*</small></label>
                                      @elseif($data['u_data']->user_type == 'team_leader')
                                      <label for="worker_id">{{__('user_management_module.team_leader_module.team_leader_id')}} <i class="fas fa-question-circle" onfocus="theFocus(this);"  data-toggle="tooltip" data-placement="top" title="{{__('user_management_module.common.worker_id_description')}}"></i><small class="required">*</small></label>
                                      @else
                                      <label for="emp_id">{{__('user_management_module.user_forms.label.emp_id')}}
                                    </label>
                                      @endif
                                    </div>
                                      <input type="text" maxlength="10" class="form-control" name="emp_id" onkeyup="getEmpiddata()" id="emp_id" placeholder="{{__('user_management_module.user_forms.place_holder.emp_id')}}" value="{{($data['u_data']->emp_id) ? $data['u_data']->emp_id  : '' }}">
                                   </div>
                                   <div class="form-group mb-20 user_info email_box">
                                      <label for="emp_email">{{__('user_management_module.user_forms.label.emp_email')}} <small class="required">*</small></label>
                                      <input type="email" class="form-control" id="email" name="email" placeholder="<EMAIL>" value="{{($data['u_data']->email) ? $data['u_data']->email  : '' }}">
                                   </div>

                                    <div class="form- group mb-20 user_info nationality_select">
                                       <div class="">
                                          <label for="nationality_id">
                                          {{__('user_management_module.user_forms.label.nationality')}} <small class="required">*</small>
                                          </label>
                                          <select class="form-control" id="nationality_id" name="nationality_id">
                                             <option value="" selected disabled>{{__('user_management_module.user_forms.label.choose_a_nationality')}}</option>

                                             @foreach($data['nationalities'] as $nationality)
                                                @if(App::getLocale()=='en')
                                                <option value="{{$nationality->id}}" {{ $data['u_data']->country_id == $nationality->id ? 'selected' : '' }}>{{$nationality->name_en}}</option>
                                                @else
                                                <option value="{{$nationality->id}}" {{ $data['u_data']->country_id == $nationality->id ? 'selected' : '' }}>{{$nationality->name_ar}}</option>
                                                @endif
                                             @endforeach

                                          </select>
                                       </div>
                                       <div id="nationality-id-error"></div>
                                    </div>

                                    <div class="form- group mb-20 user_info favorite_language_select">
                                       <div class="">
                                          <label for="favorite_language">
                                          {{__('user_management_module.user_forms.label.favorite_language')}}
                                          </label>
                                          <select class="form-control" id="favorite_language" name="favorite_language">
                                             <option value="" selected disabled>{{__('user_management_module.user_forms.label.choose_a_favorite_language')}}</option>
                                             <option value="en" {{ $data['u_data']->favorite_language == "en" ? 'selected' : '' }}>{{__('user_management_module.user_forms.label.english')}}</option>
                                             <option value="ar" {{ $data['u_data']->favorite_language == "ar" ? 'selected' : '' }}>{{__('user_management_module.user_forms.label.arabic')}}</option>
                                             <option value="ur" {{ $data['u_data']->favorite_language == "ur" ? 'selected' : '' }}>{{__('user_management_module.user_forms.label.urdu')}}</option>

                                          </select>
                                       </div>
                                       <div id="favorite-language-error"></div>
                                    </div>

                                    <div class="form- group mb-20 user_info profession_select">
                                       <div class="">
                                          <label for="profession_id">
                                          {{__('user_management_module.user_forms.label.select_profession_heading')}} <small class="required">*</small>
                                          </label>
                                          <select class="form-control" id="profession_id" name="profession_id">
                                             <option value="" selected disabled>{{__('user_management_module.user_forms.label.choose_a_profession')}}</option>

                                             @foreach($data['workerProfessions'] as $profession)
                                                @if(App::getLocale()=='en')
                                                <option value="{{$profession->id}}" {{ $data['u_data']->profession_id == $profession->id ? 'selected' : '' }}>{{$profession->profession_en}}</option>
                                                @else
                                                <option value="{{$profession->id}}" {{ $data['u_data']->profession_id == $profession->id ? 'selected' : '' }}>{{$profession->profession_ar}}</option>
                                                @endif
                                             @endforeach

                                          </select>
                                       </div>
                                       <div id="profession-id-error"></div>
                                    </div>

                                   <div class="form-group mb-20 user_info profession" {{ $data['u_data']->profession_id == 10 ? 'style=display:block' : 'style=display:none' }}>
                                      <label for="emp_dept">
                                        <span class="emp_dept_label">{{__('user_management_module.user_forms.label.emp_dept')}}</span>
                                        <span class="worker_info">{{__('user_management_module.user_forms.label.profession')}} <small class="required">*</small></span>
                                      </label>
                                      <input type="text" class="form-control" name="emp_dept" id="emp_dept" placeholder="{{__('user_management_module.user_forms.place_holder.emp_dept')}}" value="{{($data['u_data']->emp_dept) ? $data['u_data']->emp_dept  : '' }}" {{ $data['u_data']->profession_id == 10 ? 'required' : '' }}>
                                   </div>
                                   <div class="form-group mb-20 user_info">
                                      <label for="emp_phone_number">{{__('user_management_module.user_forms.label.emp_phone')}}
                                       {{-- <small class="required phone_required_mark">*</small> --}}
                                    </label>
                                      <div class="input-group mb-3 phone-ltr">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text" id="basic-addon1">+966</span>
                                        </div>
                                        <input type="tel" class="form-control" id="phone" name="phone" placeholder="576428964" value="{{($data['u_data']->phone) ? $data['u_data']->phone  : '' }}">
                                        </div>

                                   </div>
                                    <!-- OS3-2355 Adding new fields for workers -->
                                    @include('components.forms.sp_worker_fields')
                                    <!-- OS3-2355 Adding new fields for workers  End -->

                                   <!-- <div class="form-group mb-20 user_info" id="country_div">
                                      <div class="countryOption">
                                         <label for="countryOption">
                                         {{__('user_management_module.user_forms.label.emp_country')}}<small class="required">*</small>
                                         </label>
                                         <select class="form-control" id="country_id" name="country_id" data-url="{{ route('users.ajax-city-list') }}">
                                            <option value="" selected disabled>{{__('user_management_module.user_forms.place_holder.emp_country')}}</option>
                                            @foreach($data['countryList'] as $country)
                                                @if(App::getLocale()=='en')
                                                   <option value="{{$country->id}}" {{ ($data['u_data']->country_id==$country->id) ? 'selected' : '' }}>{{$country->name_en}}</option>

                                                @else
                                                   <option value="{{$country->id}}" {{ ($data['u_data']->country_id==$country->id) ? 'selected' : '' }}>{{$country->name_ar}}</option>

                                                @endif
                                            @endforeach
                                         </select>
                                      </div>
                                      <div id="country_id_error"></div>
                                   </div> -->

                                   <input type="hidden" name="country_id" id="country_id1" value="1">

                                   <!-- <div class="form-group mb-20 user_info" id="city_div">
                                      <div class="cityOption">
                                         <label for="cityOption">
                                         {{__('user_management_module.user_forms.label.emp_city')}}  <small class="required">*</small>
                                         </label>
                                         <select class="form-control" id="city_id" name="city_id" >
                                              <option value="" selected disabled>{{__('user_management_module.user_forms.place_holder.emp_city')}}</option>
                                           @foreach($data['cityList'] as $city)
                                           @if(App::getLocale()=='en')
                                           <option {{ ($data['u_data']->city_id==$city->id) ? 'selected' : '' }} value="{{$city->id}}">{{$city->name_en}}</option>

                                                        @else
                                                        <option {{ ($data['u_data']->city_id==$city->id) ? 'selected' : '' }} value="{{$city->id}}">{{$city->name_ar}}</option>

                                                        @endif
                                                @endforeach
                                         </select>
                                      </div>
                                      <div id="city_option_erro"></div>
                                   </div> -->

                                   <input type="hidden" name="city_id" id="city_id1" value="1">

                                   <!-- <input type="hidden" name="user_type" id="user_type" value="{{$data['u_data']->user_type}}"> -->
                                   <input type="hidden" id="ajax_check_useremail_unique" value="{{route('users.ajax_check_unique_useremail_edit')}}">
                                   <input type="hidden" id="ajax_check_userphone_unique_edit" value="{{route('users.ajax_check_userphone_unique_edit')}}">

                                   <input type="hidden" id="ajax_check_employee_id_unique_edit" value="{{route('users.ajax_check_unique_emp_id')}}">


                                   <div class="button-group d-flex pt-25 justify-content-end">
                                     <a href="{{ route('users.list') }}" class="btn btn-light btn-default btn-sm btn-squared fw-400 text-capitalize radius-md">{{__('user_management_module.user_button.cancel')}}</a>
                                     <button onclick="custom_submit()" type="button" class="btn btn-primary btn-default btn-squared text-capitalize radius-md shadow2">{{__('user_management_module.user_button.save_next')}}
                                      </button>

                                      <button hidden id="now_submit" type="submit" class="btn btn-primary btn-default btn-squared text-capitalize radius-md shadow2">{{__('user_management_module.user_button.save_next')}}
                                      </button>
                                   </div>
                                </form>
                             </div>
                        </div>
                     </div>
                     <!-- ends: card -->
                  </div>
                  <!-- ends: col -->
               </div>
            </div>
            <!-- ends: col -->
         </div>
      </div>
      <!-- End: .global-shadow-->
   </div>
</div>
<!-- CONFIRM DELETE Photo MODAL START -->

<div class="modal new-member  bouncein-new" id="confirmDeletePhoto" role="dialog" tabindex="-1"
        aria-labelledby="staticBackdropLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-md">
            <div class="modal-content  radius-xl  bouncein-new">

                <div class="modal-body">
                    <div class="create-asset-modal">
                        <h2 class="mb-3 fs-20"><i class="fa fa-exclamation-circle mr-1 text-warning"
                                aria-hidden="true"></i>
                            {{ __('data_properties.property_forms.label.sure_remove_photo') }} </h2>
                    </div>

                        <div class="button-group d-flex justify-content-end pt-25">
                            <div class="button-group d-flex justify-content-end pt-25">
                                <button type="button" class="btn btn-light   btn-squared text-capitalize"
                                    data-dismiss="modal" aria-label="Close">
                                    {{ __('data_properties.property_button.cancel') }}

                                </button>
                                <button type="button" class="btn btn-danger btn-default btn-squared text-capitalize confirm_remove_photo" data-dismiss="modal" aria-label="Close">
                                    {{ __('data_properties.property_button.remove') }}
                                </button>

                            </div>

                        </div>
                </div>
            </div>
        </div>
    </div>
</div>

    <!-- CONFIRM DELETE Photo MODAL ENDS -->
@endsection

@section('scripts')
<script type="text/javascript" src="{{asset('js/admin/users/create.js')}}"></script>
<script type="text/javascript">
   $('.sup_info').hide();
   $('.worker_info').hide();
   $('.company_info').hide();
   $('.building_admin_info').hide();
   $('.spga_admin_info').hide();
   var selected_user_type_val = $('#user_type').val();//alert(selected_user_type_val);
   var user_type_val = $('#user_type').val();

  $('#user_type').on('change', function() {
      var user_type = $(this).val();
      if(user_type == 'sp_worker' ) {
         $("#user_create_form_edit").prop('id','user_create_form_edit_worker');
         $(".phone_required_mark").hide();
         $('.user-country-city-block').hide();
         $('#supervisor_id_label').html(`  <label for="for="supervisor_id"">{{__('user_management_module.user_forms.label.worker_admin')}}<small class="required">*</small></label>`);
      
         $('#supervisor_id option:first').text(translations.user_management_module.user_forms.label.worker_admin);
      }
      else if(user_type == 'team_leader' ) {
         $("#user_create_form_edit").prop('id','user_create_form_edit_worker');
         $(".phone_required_mark").hide();
         $('.user-country-city-block').hide();
         $('#supervisor_id_label').html(`  <label for="for="supervisor_id"">{{__('user_management_module.user_forms.label.worker_admin')}}<small class="required">*</small></label>`);
      
         $('#supervisor_id option:first').text(translations.user_management_module.user_forms.label.worker_admin);

         $('#supervisor_id_label').html(`  <label for="for="supervisor_id"">{{__('user_management_module.team_leader_module.team_leader_supervisor')}}<small class="required">*</small></label>`);
         $('#supervisor_id option:first').text(translations.user_management_module.user_forms.label.team_leader_supervisor);
      }
       else {
         $("#user_create_form_edit_worker").prop('id','user_create_form_edit');
         $(".phone_required_mark").show();
         $('.user-country-city-block').show()
      }
  });

  $('#supervisor_id').on('select2:select', function (e) {
    sessionStorage.setItem('edituser_supervisor_id', JSON.stringify($(this).val()));
   });

   $('#supervisor_id').on('select2:unselecting', function (e) {
         let value_cnt1 = $(this).val();
            value_cnt1 = jQuery.grep(value_cnt1, function(value) {
                  return value != e.params.args.data.id;
            });
            sessionStorage.setItem('edituser_supervisor_id', JSON.stringify(value_cnt1));
   });


   var storedusernameValues = sessionStorage.getItem('edituser_name');
         if(storedusernameValues)
         {
            $("#name").val(JSON.parse(storedusernameValues));
         }
         var storedusebuildingadminValues = sessionStorage.getItem('edituser_building_admin');
         if(storedusebuildingadminValues)
         {
            $("#building_admin").val(JSON.parse(storedusebuildingadminValues)).trigger('change');
         }
         var storeduserempidValues = sessionStorage.getItem('edituser_emp_id');
         if(storeduserempidValues)
         {
            $("#emp_id").val(JSON.parse(storeduserempidValues));
         }
         var storeduseremailValues = sessionStorage.getItem('edituser_email');
         if(storeduseremailValues)
         {
            $("#email").val(JSON.parse(storeduseremailValues));
         }
         var storeduserempdeptValues = sessionStorage.getItem('edituser_emp_dept');
         if(storeduserempdeptValues)
         {
            $("#emp_dept").val(JSON.parse(storeduserempdeptValues));
         }
         var storeduserphoneValues = sessionStorage.getItem('edituser_phone');
         if(storeduserphoneValues)
         {
            $("#phone").val(JSON.parse(storeduserphoneValues));
         }
         var storedusersupervisoridValues = sessionStorage.getItem('edituser_supervisor_id');
         if(storedusersupervisoridValues)
         {
            $('#supervisor_id').val(JSON.parse(storedusersupervisoridValues)).trigger('change');
         }

         function getEmpiddata(){
   sessionStorage.setItem('edituser_emp_id', JSON.stringify($('#emp_id').val()));
}

   $('#name').keyup(function() {
      sessionStorage.setItem('edituser_name', JSON.stringify($(this).val()));
   });

   $('#emp_id').keyup(function() {
      sessionStorage.setItem('edituser_emp_id', JSON.stringify($(this).val()));
   });

   $('#email').keyup(function() {
      sessionStorage.setItem('edituser_email', JSON.stringify($(this).val()));
   });

   $('#emp_dept').keyup(function() {
      sessionStorage.setItem('edituser_emp_dept', JSON.stringify($(this).val()));
   });

   $('#phone').keyup(function() {
      sessionStorage.setItem('edituser_phone', JSON.stringify($(this).val()));
   });

  //alert(selected_user_type_val);
  $("#user_type").on("change", function () {
    var user_type_val = $(this).val();//alert();
    if(user_type_val=='sp_worker')
    {
      //$('.company_info').show();
      $('.worker_info').show();
      $("#emp_dept").attr("placeholder", translations.user_management_module.user_forms.label.profession);
      $('.emp_dept_label').hide();
      //$('#service_provider').prop("required", true);
      $('#supervisor_id').prop("required", true);
      $('.email_box').hide();
      $('#email').attr("type","hidden");
      $('#country_div').hide();
      $('#city_div').hide();
    }
    else if(user_type_val=='team_leader')
    {
      //$('.company_info').show();
      $('.worker_info').show();
      $("#emp_dept").hide();
      $('.emp_dept_label').hide();
      //$('#service_provider').prop("required", true);
      $('#supervisor_id').prop("required", true);
      $('.email_box').hide();
      $('#email').attr("type","hidden");
      $('#country_div').hide();
      $('#city_div').hide();

      
         $('#supervisor_id_label').html(`  <label for="for="supervisor_id"">{{__('user_management_module.team_leader_module.team_leader_supervisor')}}<small class="required">*</small></label>`);
         $('#supervisor_id option:first').text(translations.user_management_module.user_forms.label.team_leader_supervisor);
    }
    else
    {
      //$('.company_info').hide();
      $('.worker_info').hide();
      $('.emp_dept_label').show();
      $("#emp_dept").attr("placeholder", translations.user_management_module.user_forms.label.emp_dept);
      //$('#service_provider').prop("required", false);
      $('#supervisor_id').prop("required", false);
      $('#email').attr("type","email");
      $('.email_box').show();
      $('#country_div').show();
      $('#city_div').show();
    }
  });
  if(selected_user_type_val=='sp_worker')
  {
    $('.worker_info').show();
    $("#emp_dept").attr("placeholder", translations.user_management_module.user_forms.label.profession);
    $('.emp_dept_label').hide();
    $('.email_box').hide();
    $('#email').attr("type","hidden");
    $('#country_div').hide();
    $('#city_div').hide();
    $('#supervisor_id').prop("required", true);
    $(".phone_required_mark").hide();

    $('#supervisor_id_label').html(`  <label for="for="supervisor_id"">{{__('user_management_module.user_forms.label.worker_admin')}}<small class="required">*</small></label>`);
      
      $('#supervisor_id option:first').text(translations.user_management_module.user_forms.label.worker_admin);
  }

  else if(selected_user_type_val=='team_leader')
  {
    $('.worker_info').show();
    $("#emp_dept").hide();
    $('.emp_dept_label').hide();
    $('.email_box').hide();
    $('#email').attr("type","hidden");
    $('#country_div').hide();
    $('#city_div').hide();
    $('#supervisor_id').prop("required", true);
    $(".phone_required_mark").hide();

    
         $('#supervisor_id_label').html(`  <label for="for="supervisor_id"">{{__('user_management_module.team_leader_module.team_leader_supervisor')}}<small class="required">*</small></label>`);
         $('#supervisor_id option:first').text(translations.user_management_module.user_forms.label.team_leader_supervisor);
  }
  else
  {
    $('.worker_info').hide();
    $('.emp_dept_label').show();
    $("#emp_dept").attr("placeholder", translations.user_management_module.user_forms.label.emp_dept);
    $('#email').attr("type","email");
    $('.email_box').show();
    $('#country_div').show();
    $('#city_div').show();
    $(".phone_required_mark").show();
    $('.profession').show();
  }
  if(selected_user_type_val=='sp_admin')
  {
    $('.company_info').show();
  }
  else
  {
    $('.company_info').hide();
  }
  if(selected_user_type_val=='sp_admin')
      {
        $('#user_type_name').html(translations.data_service_provider.serviceProvider_forms.label.service_provider_name);
        $('#2').hide();
        $('#2').next('.current').hide();
        $('#3').hide();
        $('#3').next('.current').hide();
        $('#4 span:first').html('2');
        var sp_admin_company_list=$('#sp_admin_company_list').html();
        $('#service_provider').html(sp_admin_company_list);
      }
      if(selected_user_type_val=='admin')
      {
        $('#2').hide();
        $('#2').next('.current').hide();
        $('#3').hide();
        $('#3').next('.current').hide();
        $('#4 span:first').html('2');
      }
      if(user_type_val=='admin')
      {
        $('#2').hide();
        $('#2').next('.current').hide();
        $('#3').hide();
        $('#3').next('.current').hide();
        $('#4 span:first').html('2');
      }


      if(user_type_val=='building_manager')
      {
        $('#3').hide();
        $('#3').next('.current').hide();
        $('#4 span:first').html('3');
      }


      if(user_type_val=='spga_employee')
      {
        $('.spga_admin_info').show();
        $('#spga_admin').prop("required", true);
      }
      else
      {
        $('.spga_admin_info').hide();
        $('#spga_admin').prop("false", true);
      }

      if(user_type_val=='building_manager_employee')
      {
        $('.building_admin_info').show();
        $('#building_admin').prop("required", true);
      }
      else
      {
        $('.building_admin_info').hide();
        $('#building_admin').prop("required", false);
      }

   if(user_type_val=='sp_admin'||user_type_val=='supervisor')
      {
        $('.company_info').show();
      }
      else
      {
         $('.company_info').hide();
      }

      if(user_type_val=='supervisor')
      {
        $('.sup_info').show();
      }
      else
      {
        $('.sup_info').hide();
      }
      if(user_type_val=='sp_worker' || user_type_val=='team_leader')
      {
        $('.worker_info').show();
      }
      else
      {
        $('.worker_info').hide();
      }

   $("#user_type").on("change", function () {
    var user_type_val = $(this).val();
    //alert(user_type_val);
    // if(user_type_val=='admin'||user_type_val=='spga_employee'||user_type_val=='building_manager'||user_type_val=='building_manager_employee'||user_type_val=='sp_admin')
    if(user_type_val!='')
    {
      $('.user_info').show();
      if(user_type_val=='sp_admin')
      {
        $('#user_type_name').html(translations.data_service_provider.serviceProvider_forms.label.service_provider_name);
        $('#2').hide();
        $('#3').hide();
      }

      if(user_type_val=='sp_admin'||user_type_val=='supervisor'||user_type_val=='sp_worker'||user_type_val=='team_leader')
      {
        $('.company_info').show();
      }

      if(user_type_val=='supervisor')
      {
        $('.sup_info').show();
      }
      if(user_type_val=='sp_worker')
      {
        $('.worker_info').show();
        $('.emp_dept_label').hide();
        $('.email_box').hide();
        $('#email').attr("type","hidden");
      }
      else if(user_type_val=='team_leader')
      {
        $('.worker_info').show();
        $('.emp_dept_label').hide();
        $('.email_box').hide();
        $('#email').attr("type","hidden");
      }
      else
      {
        $('.worker_info').hide();
        $('.emp_dept_label').show();
        $('#email').attr("type","email");
        $('.email_box').show();
      }

    }

});

</script>
<script type="text/javascript">
$("#user_type,#service_provider,#supervisor_id,#sp_admin_id,#building_admin,#spga_admin,#country_id,#city_id").select2({
   placeholder:translations.data_contract.contract_forms.place_holder.please_choose,
   dropdownCssClass: "tag",
   language: { noResults: () => translations.general_sentence.validation.No_results_found,}
});

$("#profession_id").select2({
   placeholder:translations.user_management_module.user_forms.label.choose_a_profession,
   dropdownCssClass: "tag",
   language: { noResults: () => translations.general_sentence.validation.No_results_found,}
});

$("#nationality_id").select2({
   placeholder:translations.user_management_module.user_forms.label.choose_a_nationality,
   dropdownCssClass: "tag",
   language: { noResults: () => translations.general_sentence.validation.No_results_found,}
});

$("#favorite_language").select2({
   placeholder:translations.user_management_module.user_forms.label.choose_a_favorite_language,
   dropdownCssClass: "tag",
   language: { noResults: () => translations.general_sentence.validation.No_results_found,}
});

<?php

if(Auth::user()->user_type == 'sp_admin') {
   ?>
   $('.company_info').hide();
   <?php
} else {
   ?>
   $('.company_info').show();
   <?php
}

?>


function custom_submit() {
      var user_type = $('#user_type').val();
      console.log(user_type);
      if(user_type == 'supervisor' ) {
         swal({
            title: translations.general_sentence.modal.edit_warning_title,
            text: translations.general_sentence.modal.edit_warning_sp,
            icon: "warning",
            buttons: true,
            dangerMode: true,
            showCancelButton: true,
            confirmButtonText:  translations.general_sentence.swal_buttons.confirm,
            cancelButtonText:  translations.general_sentence.swal_buttons.cancel,
            /*

            buttons: [
                translations.general_sentence.swal_buttons.cancel,
                translations.general_sentence.swal_buttons.confirm,
            ],
            */
        },
        function(willDelete) {

            if (willDelete) {
               console.log("submitting...");
               form =  $('#user_create_form_edit');
               // console.log(form);
               // form.submit();

               $('#now_submit').click();

               // $('.edit_user_form').submit();
            }
        });
      }

      else {
         $('#now_submit').click();
      }

   }


if(localStorage['values']){

localStorage['values'].clear();

}
$('.search-select-multiple').select2({
    dropdownAutoWidth: true,
    multiple: true,
    width: '100%',
    height: '30px',
    placeholder: "Select",
    allowClear: true
});
$('.select2-search__field').css('width', '100%');

   $(document).ready(function() {
      $('#emp_dept').attr('maxlength', 20); // Set the new max-length attribute
      $('.profession_select').hide();
      $('.nationality_select').hide();
      $('.favorite_language_select').hide();
      //$('#emp_dept').prop('required', false); // Make input not required
      if(user_type_val!='')
      {
         if(user_type_val=='sp_worker')
         {
            //$('.profession').hide();
            $('.profession_select').show();
            $('.nationality_select').show();
            $('.favorite_language_select').show();
         }
         else
         {
            $('.profession_select').hide();
            $('.nationality_select').hide();
            $('.favorite_language_select').hide();
         }
      }
      $('#profession_id').on('change', function() {
         var selectedOption = $(this).val();

         if (selectedOption === '10') {
               $('.profession').show();
               $('#emp_dept').prop('required', true); // Make input required
               $('#emp_dept').attr('maxlength', 15); // Set the new max-length attribute
               $('#emp_dept').val('');
         } else {
               $('.profession').hide();
               $('#emp_dept').prop('required', false); // Make input not required
               $('#emp_dept').attr('maxlength', 20); // Set the new max-length attribute
               $('#emp_dept').val('');
         }
      });
   });
</script>
@endsection

<?php
    namespace App\Jobs\BulkImport;
    use Illuminate\Bus\Queueable;
    use Illuminate\Bus\Batchable;
    use Illuminate\Contracts\Queue\ShouldBeUnique;
    use Illuminate\Contracts\Queue\ShouldQueue;
    use Illuminate\Foundation\Bus\Dispatchable;
    use Illuminate\Queue\InteractsWithQueue;
    use Illuminate\Queue\SerializesModels;
    use Illuminate\Support\Facades\Log;
    use App\Http\Traits\ProjectDetailTrait;
    use App\Http\Traits\BulkImportErrorTrait;
    use App\Http\Traits\PriorityTrait;
    use App\Http\Traits\BulkImportListTrait;
    use App\Http\Traits\BulkImportTrait;
    use App\Enums\ResultType;
    use App\Enums\ModelAction;
    use App\Enums\ValidationBukImport;

    class PrioritiesImportJob implements ShouldQueue{
        use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, Batchable, ProjectDetailTrait, BulkImportErrorTrait, PriorityTrait, BulkImportListTrait, BulkImportTrait;
        public $list;
        public $projectId;
        public $bulkImportDetailsId;
        public $projectUserId;
        public $userId;

        /**
         * Create a new job instance.
         *
         * @return void
         */
        public function __construct($list, $projectId, $bulkImportDetailsId, $projectUserId, $userId){
            $this->list = $list;
            $this->projectId = $projectId;
            $this->bulkImportDetailsId = $bulkImportDetailsId;
            $this->projectUserId = $projectUserId;
            $this->userId = $userId;
        }

        /**
         * Execute the job.
         *
         * @return void
         */
        public function handle(){
            try {
                $bulkArray = [];
                $project = $this->getProjectDetailInformationByProjectId($this->projectId);

                if(is_null($project)){
                    Log::info("PrioritiesImportJob error: No project found with this projectId : ".$this->projectId); 
                }

                elseif(!isset($this->list)){
                    Log::info("PrioritiesImportJob error: The priorities list is empty"); 
                }

                else{
                    foreach($this->list as $data){
                        $mapChecking = $this->fullPrioritiesValidation($data);

                        if(!is_null($mapChecking) && $mapChecking[0]['status'] <> 'success'){
                            $bulkArrayErrors = ['map_status' => true, 'backend_status' => false, 'project_id' => $this->projectId, 'user_id' => $this->userId, 'bulk_import_id' => $this->bulkImportDetailsId, 'sheet' => ResultType::PrioritiesLevels->value, 'identifier' => $data['priority_name'], 'row_status' => ModelAction::NotInserted->value, 'created_by' => $this->userId];
                            $bulkImportErrorId = $this->saveBulkImportError($bulkArrayErrors);

                            if(!$bulkImportErrorId){
                                Log::info("PrioritiesImportJob error: Cannot save the bulk import error row for Priorities sheet : Project ID: ".$this->projectId.", Priority Name: ".$data['priority_name']); 
                            }
                        }

                        else{
                            $priorityRow = $this->getPriorityDataByValues("priority_level", $data['priority_name'], $this->projectUserId);

                            if(!isset($priorityRow)){
                                $array = [
                                    'user_id' => $this->projectUserId ?? null,
                                    'priority_level' => $data['priority_name'] ?? null,
                                    'service_window' => $data ['service_window'] ?? null,
                                    'service_window_type' => $data['service_window_type'] ?? null,
                                    'response_time' => $data['response_time'] ?? null,
                                    'response_time_type' => $data['response_time_type'] ?? null
                                ];

                                $newPriorityId = $this->savePriority($array);

                                if($newPriorityId){
                                    array_push($bulkArray, $newPriorityId);
                                }

                                else{
                                    $bulkArrayErrors = ['map_status' => false, 'backend_status' => true, 'project_id' => $this->projectId, 'user_id' => $this->userId, 'bulk_import_id' => $this->bulkImportDetailsId, 'sheet' => ResultType::PrioritiesLevels->value, 'identifier' => $data['priority_name'], 'row_status' => ModelAction::NotInserted->value, 'errors' => ValidationBukImport::PriorityNotSaved->value, 'value' => $data['priority_name'], 'created_by' => $this->userId];
                                    $bulkImportErrorId = $this->saveBulkImportError($bulkArrayErrors);

                                    if(!$bulkImportErrorId){
                                        Log::info("PrioritiesImportJob error: Cannot save the bulk import error row for Priorities sheet : Project ID: ".$this->projectId.", Priority Name: ".$data['priority_name']); 
                                    }
                                }
                            }

                            else{
                                $priorityRowId = $priorityRow->id;

                                $array = [
                                    'priority_level' => $data['priority_name'] ?? null,
                                    'service_window' => $data ['service_window'] ?? null,
                                    'service_window_type' => $data['service_window_type'] ?? null,
                                    'response_time' => $data['response_time'] ?? null,
                                    'response_time_type' => $data['response_time_type'] ?? null
                                ];

                                $newUpdatedPriority = $this->updatePriorityByValues($this->projectUserId, $priorityRowId, $array);

                                if($newUpdatedPriority){
                                    array_push($bulkArray, $priorityRowId);
                                }

                                else{
                                    $bulkArrayErrors = ['map_status' => false, 'backend_status' => true, 'project_id' => $this->projectId, 'user_id' => $this->userId, 'bulk_import_id' => $this->bulkImportDetailsId, 'sheet' => ResultType::PrioritiesLevels->value, 'identifier' => $data['priority_name'], 'row_status' => ModelAction::NotInserted->value, 'errors' => ValidationBukImport::PriorityNotUpdated->value, 'value' => $data['priority_name'], 'created_by' => $this->userId];
                                    $bulkImportErrorId = $this->saveBulkImportError($bulkArrayErrors);

                                    if(!$bulkImportErrorId){
                                        Log::info("PrioritiesImportJob error: Cannot save the bulk import error row for Priorities sheet : Project ID: ".$this->projectId.", Priority Name: ".$data['priority_name']); 
                                    }
                                }
                            }
                        }
                    }
                }

                $implodedValue = isset($bulkArray) && count($bulkArray) > 0 ? $this->implodeDataFromField($bulkArray) : null;
                $bulkImportList = $this->getBulkImportListByValues('bulk_import_id', $this->bulkImportDetailsId);
                $result = null;

                $array = [
                    'priorities' => $implodedValue,
                    'project_id' => $this->projectId,
                    'bulk_import_id' => $this->bulkImportDetailsId,
                    'created_by' => !isset($bulkImportList) ? $this->userId : $bulkImportList->created_by,
                    'updated_by' => isset($bulkImportList) ? $this->userId : null,
                ];
                
                if(isset($bulkImportList)){
                    $result = $this->updateBulkImportListByValues('bulk_import_id', $this->bulkImportDetailsId, $array);
                }

                else{
                    $result = $this->saveBulkImportList($array);
                }

                if(!$result){
                    Log::info("PrioritiesImportJob error: We cannot do any action on bulk import table for priorities column (Same data)"); 
                }
            } 
            
            catch (\Throwable $th) {
                Log::error("PrioritiesImportJob error: ".$th);
            }
        }
    }
?>
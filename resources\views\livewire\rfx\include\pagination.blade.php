<div class="card-body pt-0">
<div class="d-flex justify-content-md-between flex-column flex-md-row justify-content-center align-items-center mt-4 gap-10">
<div class="">
<ul class="atbd-pagination d-flex justify-content-between">
<li>
<div class="paging-option">
<div class="dataTables_length d-flex">
<label class="d-flex align-items-center mb-0">
<select wire:model="perPage" class="custom-select custom-select-sm form-control form-control-sm mx-2" style="min-height: 35px;">
<option value="5">5</option>
<option value="10">10</option>
<option value="25">25</option>
<option value="50">50</option>
<option value="100">100</option>
</select>
<span class="no-wrap"> {{ __('rfx.pagination.entries_per_page') }} </span>
</label>
</div>
</div>
</li>
</ul>
</div>

@if($total > 0)
<div class="">
<div class="user-pagination">
<div class="user-pagination new-pagination">
<div class="d-flex justify-content-sm-end justify-content-end">
<nav>
<span class="relative z-0 inline-flex rounded-md d-flex new-pagination-section flex-wrap">
@if($pagination->current_page > 1)
<span>
<button type="button" class="border-0" wire:click="previousPage">
<i class="iconsax icon-bold fs-16 mr-0 rotate-ar-y d-inline-block" icon-name="chevron-left"></i>
</button>
</span>
@else
<span>
<button class="border-0 disabled" disabled>
<i class="iconsax icon-bold fs-16 mr-0 rotate-ar-y d-inline-block" icon-name="chevron-left"></i>
</button>
</span>
@endif

@php
$start = max(1, $pagination->current_page - 2);
$end = min($pagination->last_page, $pagination->current_page + 2);
@endphp

@if($start > 1)
<span>
<button type="button" class="border-0" wire:click="gotoPage(1)">1</button>
</span>
@if($start > 2)
<span>
<button class="border-0 disabled" disabled>...</button>
</span>
@endif
@endif

@for($i = $start; $i <= $end; $i++)
@if($i == $pagination->current_page)
<span>
<button class="border-0 current-page" disabled>{{ $i }}</button>
</span>
@else
<span>
<button type="button" class="border-0" wire:click="gotoPage({{ $i }})">{{ $i }}</button>
</span>
@endif
@endfor

@if($end < $pagination->last_page)
@if($end < $pagination->last_page - 1)
<span>
<button class="border-0 disabled" disabled>...</button>
</span>
@endif
<span>
<button type="button" class="border-0" wire:click="gotoPage({{ $pagination->last_page }})">{{ $pagination->last_page }}</button>
</span>
@endif

@if($pagination->current_page < $pagination->last_page)
<span>
<button type="button" class="border-0" wire:click="nextPage">
<i class="iconsax icon-bold fs-16 mr-0 rotate-ar-y d-inline-block" icon-name="chevron-right"></i>
</button>
</span>
@else
<span>
<button class="border-0 disabled" disabled>
<i class="iconsax icon-bold fs-16 mr-0 rotate-ar-y d-inline-block" icon-name="chevron-right"></i>
</button>
</span>
@endif
</span>
</nav>
</div>
</div>
</div>
</div>

<div>
<p class="text-sm text-gray-700 leading-5 mb-0">
<span>{{ __('rfx.pagination.showing') }}</span>
<span class="font-medium">{{ ($pagination->current_page - 1) * $pagination->per_page + 1 }}</span>
<span>{{ __('rfx.pagination.to') }}</span>
<span class="font-medium">{{ min($pagination->current_page * $pagination->per_page, $pagination->total) }}</span>
<span>{{ __('rfx.pagination.of') }}</span>
<span class="font-medium">{{ $pagination->total }}</span>
<span>{{ __('rfx.pagination.results') }}</span>
</p>
</div>
@endif
</div>
</div>

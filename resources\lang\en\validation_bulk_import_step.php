<?php
    return [
        'email_taken' => 'This email is taken by another user',
        'user_company_not_saved' => 'We cannot save the user company for technical reason',
        'user_not_saved' => 'We cannot save the user for technical reason',
        'no_service_provider_added' => 'No service provider added to set company to this user',
        'no_user_updated' => 'We cannot update the user for technical reason',
        'priority_not_saved' => 'We cannot save the priority for technical reason',
        'priority_not_updated' => 'We cannot update the priority for technical reason',
        'service_not_saved' => 'We cannot save the service for technical reason',
        'service_not_updated' => 'We cannot update the service for technical reason',
        'priority_not_exist' => 'No priority found',
        'building_count_error' => 'The data with building type not complex should have only 1 building',
        'property_not_saved' => 'We cannot save the property for technical reason',
        'property_not_updated' => 'We cannot update the property for technical reason',
        'property_building_not_saved' => 'We cannot save the property building for technical reason',
        'room_type_not_saved' => 'We cannot save the room type for technical reason',
        'room_type_floor_not_saved' => 'We cannot save the room type floor for technical reason',
        'room_type_floor_not_updated' => 'We cannot update the room type floor for technical reason',
        'rooms_not_updated' => 'No rooms updated',
        'property_building_not_exist' => 'No property building found',
        'service_not_exist' => 'No service found',
        'no_qr_code_generated' => 'No QR code generated',
        'room_type_floor_not_exist' => 'No room type floor found',
        'asset_name_not_saved' => 'We cannot save the asset name for technical reason',
        'asset_name_service_not_saved' => 'We cannot save the asset name and service for technical reason',
        'asset_not_saved' => 'We cannot save the asset for technical reason',
        'asset_history_not_saved' => 'We cannot save the asset history for technical reason',
        'asset_not_updated' => 'We cannot update the asset for technical reason',
        'property_not_exist' => 'No property found',
        'user_not_affected' => 'The user is not correctly assigned',
        'admin_already_found' => 'There is an existing admin in the project, so this user will be ignored',
    ];
?>
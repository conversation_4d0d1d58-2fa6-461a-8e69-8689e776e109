@extends('layouts.app')
@section('styles')
<style type="text/css">

</style>
@endsection
@section('content')
<div class="contents crm">
    <div class="container-fluid">
        <div class="col-lg-12">
            <div class="row justify-content-sm-between align-items-center justify-content-center my-3 flex-sm-row flex-column">
                <div class="page-title-wrap p-0">
                    <div class="page-title d-flex justify-content-between">
                        <div class="page-title__left justify-content-sm-between align-items-center justify-content-center">
                            <div class="user-member__title mr-sm-25 ml-0">
                                <h4 class="text-capitalize fw-500 breadcrumb-title fs-16">
                                    Profit & Loss Summary
                                </h4>
                            </div>
                        </div>
                    </div>
                    <div>
                        <ul class="atbd-breadcrumb nav">
                            <li class="atbd-breadcrumb__item">
                                <a>Dashboard</a>
                                <span class="breadcrumb__seperator">
                                    <span class="la la-angle-right"></span>
                                </span>
                            </li>
                            <li class="atbd-breadcrumb__item">
                                <a>Report</a>
                                <span class="breadcrumb__seperator">
                                    <span class="la la-angle-right"></span>
                                </span>
                            </li>
                            <li class="atbd-breadcrumb__item">
                                <a>Profit & Loss Summary</a>
                            </li>
                        </ul>
                    </div>
                </div>

                <div class="d-flex gap-10 breadcrumb_right_icons">
                    <div class="d-flex gap-10 breadcrumb_right_icons">
                        <button class="btn btn-white btn-default text-center svg-20 px-2 wh-45">
                            <i class="iconsax icon text-new-primary fs-22 colorRed mr-0" icon-name="download-1"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>


        <div class="table-responsive">

            <div class="card mb-3">
                <div class="card-body">
                    <form wire:submit.prevent="applyFilters" class="fs-14">
                        <div class="d-flex justify-content-end gap-10">

                            <div data-select2-id="106" class="pr-3">
                                <label class="text-osool fw-600">Year</label>
                                <select class="form-control select2-new">
                                    <option selected="selected" value="">Select Year</option>
                                    <option value="Invoice">2025</option>
                                    <option value="Invoice">2026</option>
                                    <option value="Invoice">2027</option>
                                    <option value="Invoice">2028</option>
                                    <option value="Invoice">2029</option>
                                </select>
                            </div>

                            <div>
                                <label for="" class="d-md-block d-none">&nbsp;</label>
                                <div class="d-flex gap-10 justify-content-end">
                                    <button class="btn btn-white btn-default text-center svg-20 px-2 wh-45">
                                        <i class="bg-inprogress bg-opacity-loss btn btn-sm d-flex fs-18 icon iconsax radius-md text-white wh-45" data-toggle="tooltip" data-placement="top" title="Apply" icon-name="search-normal-2"></i>
                                    </button>
                                    <button class="btn btn-white btn-default text-center svg-20 px-2 wh-45">
                                        <i class="bg-secondary bg-opacity-loss btn btn-sm d-flex fs-18 icon iconsax radius-md text-white wh-45" data-toggle="tooltip" data-placement="top" title="Reset" icon-name="trash"></i>
                                    </button>

                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <div id="printableArea">
                <div class="row">
                    <div class="col-6 pl-0">
                        <input type="hidden" value="All Category Transaction Report of Jun-2025 to Jan-2025" id="filename" />
                        <div class="card p-4 mb-3 h-110">
                            <h5 class="report-text mb-0">Report :</h5>
                            <p class="report-text mb-0">Account Statement Summary</p>
                        </div>
                    </div>

                    <div class="col-6 pr-0">
                        <div class="card p-4 mb-3 h-110">
                            <h5 class="report-text mb-0">Duration :</h5>
                            <p class="report-text mb-0">Jun-2025 to Jan-2025</p>
                        </div>
                    </div>

                </div>
            </div>
        </div>


        <div class="card">
            <div>
                <div class="py-4 px-3 border-0">
                    <h5>Income</h5>
                </div>
            </div>

            <div class="card-body px-0 pt-0 pb-0">
                <div class="userDatatable projectDatatable project-table bg-white w-100 border-0">
                    <div class="table-responsive">
                        <table class="table mb-0 radius-0 th-osool">

                            <thead>
                                <tr class="userDatatable-header">
                                    <th>
                                        Category
                                    </th>
                                    <th>
                                        <i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i> Jan-Mar
                                    </th>
                                    <th>
                                        <i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i> Apr-Jun
                                    </th>
                                    <th>
                                        <i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i> Jul-Sep
                                    </th>
                                    <th>
                                        <i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i> Oct-Dec
                                    </th>
                                    <th>
                                        <i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i> Total
                                    </th>
                                </tr>
                            </thead>

                            <tbody class="sort-table ui-sortable">
                                <tr>
                                    <td>
                                        <span class="userDatatable-content">Revenue :</span>
                                    </td>
                                </tr>

                                <tr class="ui-sortable-handle" style="opacity: 1;">
                                    <td>
                                        <div class="d-flex userDatatable-content mb-0 align-items-center">
                                            <span>Test</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex userDatatable-content mb-0 align-items-center">
                                            <span> 0.00 ﷼</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex userDatatable-content mb-0 align-items-center">
                                            <span>632.95K ﷼</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex userDatatable-content mb-0 align-items-center">
                                            <span>32.01K ﷼</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex userDatatable-content mb-0 align-items-center">
                                            <span>0.00 ﷼</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex userDatatable-content mb-0 align-items-center">
                                            <span>664.96K ﷼</span>
                                        </div>
                                    </td>
                                </tr>

                                <tr class="ui-sortable-handle" style="opacity: 1;">
                                    <td>
                                        <div class="d-flex userDatatable-content mb-0 align-items-center">
                                            <span>Test cat</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex userDatatable-content mb-0 align-items-center">
                                            <span> 0.00 ﷼</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex userDatatable-content mb-0 align-items-center">
                                            <span>284.5K ﷼</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex userDatatable-content mb-0 align-items-center">
                                            <span>21.54K ﷼</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex userDatatable-content mb-0 align-items-center">
                                            <span>0.00 ﷼</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex userDatatable-content mb-0 align-items-center">
                                            <span>306.04K ﷼</span>
                                        </div>
                                    </td>
                                </tr>

                                <tr class="ui-sortable-handle" style="opacity: 1;">
                                    <td>
                                        <div class="d-flex userDatatable-content mb-0 align-items-center">
                                            <span>Test</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex userDatatable-content mb-0 align-items-center">
                                            <span> 0.00 ﷼</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex userDatatable-content mb-0 align-items-center">
                                            <span>22.91K ﷼</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex userDatatable-content mb-0 align-items-center">
                                            <span>27.24K ﷼</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex userDatatable-content mb-0 align-items-center">
                                            <span>0.00 ﷼</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex userDatatable-content mb-0 align-items-center">
                                            <span>50.15K ﷼</span>
                                        </div>
                                    </td>
                                </tr>

                                <tr>
                                    <td>
                                        <span class="userDatatable-content">Invoice :</span>
                                    </td>
                                </tr>

                                <tr>
                                    <td colspan="6">
                                        <span class="userDatatable-content">Total Income = Revenue + Invoice</span>
                                    </td>
                                </tr>

                                <tr class="ui-sortable-handle" style="opacity: 1;">
                                    <td>
                                        <div class="d-flex userDatatable-content mb-0 align-items-center">
                                            <span>Total Income</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex userDatatable-content mb-0 align-items-center">
                                            <span> 0.00 ﷼</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex userDatatable-content mb-0 align-items-center">
                                            <span>940.35K ﷼</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex userDatatable-content mb-0 align-items-center">
                                            <span>80.79K ﷼</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex userDatatable-content mb-0 align-items-center">
                                            <span>0.00 ﷼</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex userDatatable-content mb-0 align-items-center">
                                            <span>1.02M ﷼</span>
                                        </div>
                                    </td>
                                </tr>

                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <div>
                <div class="pt-4 pb-2 px-3 border-0">
                    <h5>Expense</h5>
                </div>
            </div>

            <div class="card-body px-0 pt-0 pb-0">
                <div class="userDatatable projectDatatable project-table bg-white w-100 border-0">
                    <div class="table-responsive">
                        <table class="table mb-0 radius-0 th-osool">

                            <thead>
                                <tr class="userDatatable-header">
                                    <th>
                                        Category
                                    </th>
                                    <th>
                                        <i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i> Jan-Mar
                                    </th>
                                    <th>
                                        <i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i> Apr-Jun
                                    </th>
                                    <th>
                                        <i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i> Jul-Sep
                                    </th>
                                    <th>
                                        <i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i> Oct-Dec
                                    </th>
                                    <th>
                                        <i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i> Total
                                    </th>
                                </tr>
                            </thead>

                            <tbody class="sort-table ui-sortable">
                                <tr>
                                    <td>
                                        <span class="userDatatable-content">Payment :</span>
                                    </td>
                                </tr>

                                <tr class="ui-sortable-handle" style="opacity: 1;">
                                    <td>
                                        <div class="d-flex userDatatable-content mb-0 align-items-center">
                                            <span>Test</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex userDatatable-content mb-0 align-items-center">
                                            <span> 0.00 ﷼</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex userDatatable-content mb-0 align-items-center">
                                            <span>102.5K ﷼</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex userDatatable-content mb-0 align-items-center">
                                            <span>0.00 ﷼</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex userDatatable-content mb-0 align-items-center">
                                            <span>0.00 ﷼</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex userDatatable-content mb-0 align-items-center">
                                            <span>102.5K ﷼</span>
                                        </div>
                                    </td>
                                </tr>

                                <tr class="ui-sortable-handle" style="opacity: 1;">
                                    <td>
                                        <div class="d-flex userDatatable-content mb-0 align-items-center">
                                            <span>Bill :</span>
                                        </div>
                                    </td>
                                </tr>

                                <tr class="ui-sortable-handle" style="opacity: 1;">
                                    <td>
                                        <div class="d-flex userDatatable-content mb-0 align-items-center">
                                            <span>Employee Salary :</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex userDatatable-content mb-0 align-items-center">
                                            <span> 0.00 ﷼</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex userDatatable-content mb-0 align-items-center">
                                            <span> 0.00 ﷼</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex userDatatable-content mb-0 align-items-center">
                                            <span> 0.00 ﷼</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex userDatatable-content mb-0 align-items-center">
                                            <span> 0.00 ﷼</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex userDatatable-content mb-0 align-items-center">
                                            <span> 0.00 ﷼</span>
                                        </div>
                                    </td>
                                </tr>

                                <tr>
                                    <td colspan="6">
                                        <span class="userDatatable-content">Total Expense = Payment + Bill + Employee Salary</span>
                                    </td>
                                </tr>

                                <tr class="ui-sortable-handle" style="opacity: 1;">
                                    <td>
                                        <div class="d-flex userDatatable-content mb-0 align-items-center">
                                            <span>Total Expenses</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex userDatatable-content mb-0 align-items-center">
                                            <span> 0.00 ﷼</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex userDatatable-content mb-0 align-items-center">
                                            <span>102.5K ﷼</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex userDatatable-content mb-0 align-items-center">
                                            <span>0.00 ﷼</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex userDatatable-content mb-0 align-items-center">
                                            <span>0.00 ﷼</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex userDatatable-content mb-0 align-items-center">
                                            <span>102.5K ﷼</span>
                                        </div>
                                    </td>
                                </tr>

                            </tbody>
                        </table>
                    </div>
                </div>
            </div>




            <div class="userDatatable projectDatatable project-table bg-white w-100 border-0 mt-4">
                <div class="table-responsive">
                    <table class="table mb-0 radius-0 th-osool">


                        <tbody class="sort-table ui-sortable">

                            <tr>
                                <td colspan="6">
                                    <div class="border-0">
                                        <span class="userDatatable-content">Net Profit = Total Income - Total Expense</span>
                                    </div>
                                </td>
                            </tr>

                            <tr class="ui-sortable-handle" style="opacity: 1;">
                                <td>
                                    <div class="mb-0 min-w-150 userDatatable-content">
                                        <span>Net Profit</span>
                                    </div>
                                </td>
                                <td>
                                    <div class="mb-0 min-w-100px userDatatable-content">
                                        <span> 0.00 ﷼</span>
                                    </div>
                                </td>
                                <td>
                                    <div class="mb-0 min-w-100px userDatatable-content">
                                        <span>837.85K ﷼</span>
                                    </div>
                                </td>
                                <td>
                                    <div class="mb-0 min-w-100px userDatatable-content">
                                        <span>80.79K ﷼</span>
                                    </div>
                                </td>
                                <td>
                                    <div class="mb-0 min-w-100px userDatatable-content">
                                        <span>0.00 ﷼</span>
                                    </div>
                                </td>
                                <td>
                                    <div class="mb-0 min-w-100px userDatatable-content">
                                        <span>918.64K ﷼</span>
                                    </div>
                                </td>
                            </tr>

                        </tbody>
                    </table>
                </div>
            </div>


            <div class="card-body pt-0">
                <div class="d-flex justify-content-md-between flex-column flex-md-row justify-content-center align-items-center mt-4 gap-10">
                    <div>
                        <p class="text-sm text-gray-700 leading-5 mb-0">
                            <span>Showing</span>
                            <span class="font-medium">1</span>
                            <span>to</span>
                            <span class="font-medium">2</span>
                            <span>of</span>
                            <span class="font-medium">2</span>
                            <span>Entries</span>
                        </p>
                    </div>
                </div>
            </div>
        </div>

    </div>



</div>




<!-- Modal -->
<div class="modal fade" id="create-revenue" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel">Create Revenue</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="form-group col-md-6">
                        <label for="date" class="form-label">Date</label><span class="text-danger">*</span>
                        <div class="form-icon-user">
                            <input class="form-control datepicker" required="required" placeholder="Select Date" name="date" type="text" id="date" />
                        </div>
                    </div>
                    <div class="form-group col-md-6">
                        <label for="amount" class="form-label">Amount</label><span class="text-danger">*</span>
                        <div class="form-icon-user">
                            <input class="form-control" required="required" placeholder="Enter Amount" step="0.01" min="0" name="amount" type="number" id="amount" />
                        </div>
                    </div>
                    <div class="form-group col-md-6">
                        <label for="account_id" class="form-label">Account</label><span class="text-danger">*</span>
                        <select class="form-control select2-new" required="required" id="account_id" name="account_id">
                            <option selected="selected" value="">Select Account</option>
                            <option value="27">sss cash</option>
                            <option value="113">Saudi National Bank Mohammed Firdaus0</option>
                        </select>
                    </div>
                    <div class="form-group col-md-6">
                        <label for="customer_id" class="form-label">Customer</label><span class="text-danger">*</span>
                        <select class="form-control select2-new" required="required" id="customer_id" name="customer_id">
                            <option selected="selected" value="">Select Customer</option>
                            <option value="30">Customer 1</option>
                            <option value="31">Customer 2</option>
                            <option value="32">Customer 3</option>
                            <option value="33">Customer 4</option>
                            <option value="34">Customer 5</option>
                            <option value="35">Customer 6</option>
                            <option value="36">Customer 7</option>
                            <option value="37">Customer 8</option>
                            <option value="38">Customer 9</option>
                            <option value="87">Fouzan</option>
                        </select>
                    </div>
                    <div class="form-group col-md-6">
                        <label for="category_id" class="form-label">Category</label><span class="text-danger">*</span>
                        <select class="form-control select2-new" required="required" id="category_id" name="category_id">
                            <option selected="selected" value="">Select Category</option>
                            <option value="18">Test cat</option>
                        </select>
                    </div>
                    <div class="form-group col-md-6">
                        <label for="reference" class="form-label">Reference</label><span class="text-danger">*</span>
                        <div class="form-icon-user">
                            <input class="form-control" placeholder="Enter Reference" required="required" name="reference" type="text" id="reference" />
                        </div>
                    </div>
                    <div class="form-group col-md-12">
                        <label for="description" class="form-label">Description</label><span class="text-danger">*</span>
                        <textarea class="form-control" rows="3" required="required" name="description" cols="50" id="description"></textarea>
                    </div>
                    <div class="form-group col-md-12">
                        <label for="add_receipt" class="form-label">Payment Receipt</label>
                        <input type="file" id="imageInput" multiple accept="image/*" class="d-none">
                        <label class="btn btn-default bg-new-primary" for="imageInput">
                            <i class="las la-upload fs-16"></i> Upload Files </label>
                        <div id="imagePreview" class="preview-container file-upload-new"></div>
                    </div>
                </div>

            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary">Save changes</button>
            </div>
        </div>
    </div>
</div>




<!-- Modal Bank account Details -->
<div class="modal fade" id="revenue-details" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel">Bank Account Details</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="userDatatable projectDatatable project-table bg-white w-100 border-0">
                    <div class="table-responsive">
                        <table class="table mb-0 radius-0 th-borderless">
                            <tbody class="sort-table ui-sortable">
                                <tr>
                                    <th>
                                        Date
                                    </th>
                                    <td>
                                        <div class="d-flex userDatatable-content mb-0 align-items-center">
                                            <span>Bank Of Baroda</span>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <th>
                                        Amount
                                    </th>
                                    <td>
                                        <div class="d-flex userDatatable-content mb-0 align-items-center">
                                            <span>Bank Of Baroda</span>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <th>
                                        Account
                                    </th>
                                    <td>
                                        <div class="d-flex userDatatable-content mb-0 align-items-center">
                                            <span>Bank Of Baroda</span>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <th>
                                        Customer
                                    </th>
                                    <td>
                                        <div class="d-flex userDatatable-content mb-0 align-items-center">
                                            <span>Cutomer 1</span>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <th>
                                        Category
                                    </th>
                                    <td>
                                        <div class="d-flex userDatatable-content mb-0 align-items-center">
                                            <span>Bank Of Baroda</span>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <th>
                                        Reference
                                    </th>
                                    <td>
                                        <div class="d-flex userDatatable-content mb-0 align-items-center">
                                            <span>Bank Of Baroda</span>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <th>
                                        Description
                                    </th>
                                    <td>
                                        <div class="d-flex userDatatable-content mb-0 align-items-center max-w-360">
                                            <span>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam</span>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <th>
                                        Payment Receipt
                                    </th>
                                    <td>
                                        <div class="d-flex align-items-center p-2 gap-10">
                                            <div class="view-img wo-img-div rounded" style="background: url('https://images.pexels.com/photos/11181151/pexels-photo-11181151.jpeg'); background-size: cover; background-position: center;">
                                                <img onclick="chatImageClick(this)" src="https://images.pexels.com/photos/11181151/pexels-photo-11181151.jpeg" alt="Maintenance Request Image" class="uploaded-image" width="100%">
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

</div>
</div>







@endsection

@section('scripts')
<script type="text/javascript">
    $(".datepicker").datepicker();
    $("#selectUsers,#statusList").select2();
</script>
<script>
    $(document).ready(function() {
        let currentFiles = [];

        const maxFiles = 1;
        const maxSizeMB = 2;
        const allowedTypes = ['image/jpeg', 'image/png', 'image/webp'];

        $('#imageInput').on('change', function() {
            const newFiles = Array.from(this.files);
            let totalFiles = currentFiles.length + newFiles.length;

            if (totalFiles > maxFiles) {
                alert(`Only ${maxFiles} images allowed.`);
                this.value = ''; // reset input
                return;
            }

            newFiles.forEach((file, index) => {
                if (!allowedTypes.includes(file.type)) {
                    alert(`Invalid file type: ${file.name}`);
                    return;
                }

                if (file.size > maxSizeMB * 1024 * 1024) {
                    alert(`File too large: ${file.name}`);
                    return;
                }

                currentFiles.push(file); // track only valid files

                const reader = new FileReader();
                reader.onload = function(e) {
                    const imgBox = $(`
          <div class="image-box d-flex justify-content-between align-items-center border radius-xl" data-name="${file.name}" data-size="${file.size}">
            <img src="${e.target.result}" alt="Image Preview">
            <button class="remove-btn d-center"><i class="iconsax" icon-name="x"></i></button>
          </div>
        `);
                    $('#imagePreview').append(imgBox);
                };
                reader.readAsDataURL(file);
            });

            this.value = ''; // Clear the file input to allow re-upload of same files
        });

        $('#imagePreview').on('click', '.remove-btn', function() {
            const box = $(this).closest('.image-box');
            const name = box.data('name');
            const size = box.data('size');

            // Remove file from tracking array
            currentFiles = currentFiles.filter(file => !(file.name === name && file.size === size));

            box.remove();
        });
    });
</script>
@endsection
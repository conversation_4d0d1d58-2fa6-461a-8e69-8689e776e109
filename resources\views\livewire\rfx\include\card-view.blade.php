    <div class="card-body pt-0">
    <div class="row">

        @forelse($listdata as $key=>$item)
        <div class="col-md-4 mb-3">
            <div class="radius-xl new-shadow">
                <div class="d-flex p-3 align-items-center">
                    <div class="mr-3 wh-60 fs-16 d-center bg-warning radius-xl text-white">
                        {{strtoupper(substr($item['title'], 0, 1))}}
                    </div>
                    <div>
                        <h2 class="fw-500 mb-1 fs-14 text-new-primary">{{$item['title']}}</h2>
                        <p class="mb-0">
                            {{$item['location']}}
                        </p>
                        <div class="{{ $item['status'] === 'active' ? 'bg-win text-white' : 'bg-secondary text-white' }} text-white d-inline-block py-1 px-3 fw-600 rounded text-center"> {{ucwords(str_replace('_',' ',$item['status']))}} </div>
                    </div>
                </div>
                <p class="mb-1 px-2 border-top py-3 d-flex">
                    <button class="btn p-0 btn-default h-20 d-center gap-5 flex-fill"  href="javascript:void(0);" wire:click="goToEdit('{{ Crypt::encrypt($item['id']) }}')"><i class="iconsax icon fs-14 mr-0 text-sool" icon-name="edit-1"></i> <span class="fs-14 text-dark fw-600">Edit</span></button>
                    <button class="btn p-0 btn-default h-20 d-center gap-5 flex-fill border-right border-left" href="javascript:void(0);" wire:click="goToView('{{ Crypt::encrypt($item['id']) }}')"><i class="iconsax icon fs-14 mr-0 text-sool" icon-name="eye"></i> <span class="fs-14 text-dark fw-600">View </span></button>
                    <button class="btn p-0 btn-default h-20 d-center gap-5 flex-fill" href="javascript:void(0);" wire:click="openDeleteModal({{ $item['id'] }}, '{{ $item['title'] }}')"><i class="iconsax icon fs-14 mr-0 text-sool" icon-name="trash"></i> <span class="fs-14 text-dark fw-600">Delete </span></button>
                </p>
            </div>
        </div>
        @empty
               <div class="d-flex flex-column align-items-center" style="    width: 100%;
    text-align: center;">
                              <i class="iconsax icon fs-48 text-muted mb-3" icon-name="user-search"></i>
                              <h6 class="text-muted">{{ __('rfx.no_data_found') }}</h6>
                              @if($search)
                                  <p class="text-muted">{{ __('rfx.try_adjusting_search') }}</p>
                              @endif
                          </div>
        @endforelse
    </div>
</div>
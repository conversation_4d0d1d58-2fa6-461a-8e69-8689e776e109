
<?php

    /*
    |--------------------------------------------------------------------------
    | Notifications  module
    |--------------------------------------------------------------------------
    |
    | language translate for create, edit , list and delte
    | english verion file
    | update@sobhon
    |
     */

    return [

        'common'                            => [
            'notifications'                                   => 'Notifications',
            'see_all_notifications'                           => 'See All Notifications',
            'load_more_notifications'                         => 'Load more notifications',
            'Read_all_notification'                           => 'Read all notifications',
            'Are_you_sure_you_want_to_read_all_notifications' => 'Are you sure you want to read all notifications?',
            'All_notifications_has_been_read'                 => 'All notifications has been read',
            'No_unread_notifications'                         => 'No unread notifications',
            'notice'                                          => 'Notice: Notifications are cleared after 30 days',
            'unreaded_notifications'                          => 'Unreaded notifications',
        ],

        'lists'                             => [
            'request_received'                    => 'Request Received',
            'scheduled_new_workerorder'           => 'Scheduled work order has been received',
            'is_now_on_hold'                      => 'is now on hold',
            'is_closed'                           => 'is closed',
            'is_now_in_progress'                  => 'is now in progress',
            'is_created'                          => 'is created',
            'hours_ago'                           => 'hours ago',
            'new_work_order_created'              => ':wo New work order has been received',
            'send_wo_reminder_to_sp'              => 'Reminder: :wo is not closed, kindly complete process of closing.',
            'worker_assigned_by_bm'               => ':wo has an update',
            'sp_has_marked_wo_completed'          => ':wo has been done by :done_by ',
            'sp_has_approved_wo'                  => ':wo has an update',
            'bm_has_approved_and_evaluated_wo'    => ':wo has been closed',
            //  'wo_completed_wo' => ':wo has been closed',// @flip1@ change content when worker complete the wo
            'wo_completed_wo_po'                  => ':wo has an update',
            'wo_completed_wo'                     => ':wo has been done by :done_by ',
            'bm_has_automatically_approved_wo'    => ':wo has been closed',
            'bm_has_reopend_wo'                   => ':wo work order has been re-opened by :done_by',
            'sp_not_aggres_on_reopend_wo'         => ':wo has an update',
            'sp_has_automatically_approved_wo'    => ':wo has an update',
            'sps_has_assigned'                    => ':wo has been assigned to you',
            'bm_has_agrees_with_sp_wo_has_closed' => ':wo has been closed',
            'sp_has_an_issue'                     => ':wo has an update',
            'bm_has_did_not_agreed_on_workorder'  => ':wo has an update',
            'sp_respond_to_bm_rejection'          => ':wo has an update',
            'bm_respond_to_sp_rejection'          => ':wo has an update',
            'sp_has_did_not_agreed_on_workorder'  => ':wo has an update',
            'sp_has_agreed_on_workorder'          => ':wo has an update',
            'sp_has_edited_target_date'           => ':wo has an update',
            'bm_has_agreed_on_workorder'          => ':wo has an update',
            'bm_work_order_rejected'              => ':wo has an update',
            'new_chat_message'                    => ':wo received a new chat',
            'new_maintenance_request'             => ':wo New maintenance request received',
            'deleted'                             => 'Deleted',
            'unsupported_file'                    => 'Unsupported File',
            'file_size'                           => 'File size should not be greater than 512 kb',
            'wo_started_wo'                       => ':wo has an update',
            'wo_paused_wo'                        => ':wo is now on hold',
            'wo_restarted_wo'                     => ':wo is now in progress',
            'sent_to_project_owner'               => ':wo has an update',
        ],

        'workorder_timeline'                => [
            'tenant_reschedule_maintenance'     => 'Tenant <strong>:name</strong> requested a reschedule to :date, pending Building Manager approval',
            'bm_approve_reschedule_maintenance' => 'Building Manager <strong>:BM_Name</strong> approved the rescheduling date & time to appear at :date',
            'bm_decline_reschedule_maintenance' => 'Building Manager <strong>:BM_Name</strong> declined the rescheduling date & time, work order will trigger at last scheduled time',
        ],
        'tenant_reschedule_maintenance'     => ':name Request to reschedule work order #:wo_id',
        'work_order_scheduled_message'      => 'This work order is scheduled, but it cannot be started yet. Please wait for the scheduled trigger time to be able to start.',
        'bm_approve_reschedule_maintenance' => 'Your request has been approved for rescheduling :desc.',
        'bm_decline_reschedule_maintenance' => 'Your request for rescheduling has been declined. :desc.',
        'new_wo_assigned_title' => 'A new work order has been assigned',
        'new_scheduled_wo_assigned_title' => 'A scheduled work order has been assigned',
        'new_wo_assigned_body' => 'You have been assigned to a new work order: :description',
        'worker_assigned_message' => 'Worker <strong>:worker_name</strong> has been assigned to the work order by <strong>:assigner_name</strong>',
        'worker_approve_reschedule_maintenance'=>'A scheduled work order has been assigned',
        'reschedule_update' => 'Reschedule Request Updated',
    'psp'  => [
        'registration_approved' => [
            'subject'  => 'Your Registration is Approved!',
            'greeting' => 'Hello :name,',
            'message'  => 'Your company has been successfully registered as a vendor.',
            'action'   => 'View Dashboard',
            'footer'   => 'Thank you for using Osool platform.',
        ],
        'compliance'            => [
            'subject' => 'Compliance Requirements Update',
            'message' => 'Please check the latest compliance guidelines to ensure your company remains compliant.',
            'action'  => 'View Compliance Guidelines',
            'footer'  => 'Stay compliant and avoid any issues.',
        ],
        'renewal'               => [
            'subject' => 'Renewal Reminder',
            'message' => 'Your renewal deadline is approaching on :date. Please renew in time.',
            'action'  => 'Renew Now',
            'footer'  => 'Ensure your vendor status remains active.',
        ],

        'crm_projects' => [
            'due_in_2_days' => 'Heads up! Your milestone is due in 2 days. Time to wrap things up.',
            'due_tomorrow' => 'Final push! Your milestone is due tomorrow. Don’t miss the deadline.',
        ],

        'milestone_notifications' => [
            // Platform notifications
            'approaching' => 'Milestone ":milestone_name" in ":project_name" is due in :days days. Check progress.',
            'due_today' => '":milestone_name" is due today. Finalize and update.',
            'overdue' => 'Milestone ":milestone_name" is overdue. Please take action.',
            'completed' => '":milestone_name" marked as completed.',

            // Email notifications
            'email' => [
                'header' => 'Milestone Notification',
                'greeting' => 'Dear Team Member,',
                'closing' => 'Please take the necessary actions to ensure project success.',
                'regards' => 'Best regards,',
                'team' => 'Osool Team',
                'milestone_details' => 'Milestone Details:',
                'milestone_name' => 'Milestone Name',
                'project_name' => 'Project Name',
                'due_date' => 'Due Date',
                'completion_date' => 'Completion Date',

                'approaching' => [
                    'subject' => 'Milestone ":milestone_name" is due in :days days',
                    'body' => 'Reminder that milestone ":milestone_name" in project ":project_name" is due in :days days (:due_date). Please ensure related tasks are on track.'
                ],
                'due_today' => [
                    'subject' => 'Milestone ":milestone_name" is due today',
                    'body' => 'Milestone ":milestone_name" in project ":project_name" is due today (:due_date). Finalize tasks and update status.'
                ],
                'overdue' => [
                    'subject' => 'Milestone ":milestone_name" is overdue',
                    'body' => 'Milestone ":milestone_name" was due on :due_date and is still open. Please update the status or explain the delay.'
                ],
                'completed' => [
                    'subject' => 'Milestone ":milestone_name" completed',
                    'body' => 'Milestone ":milestone_name" in project ":project_name" was marked completed on :completion_date. Well done!'
                ]
            ]
        ]
    ]
];

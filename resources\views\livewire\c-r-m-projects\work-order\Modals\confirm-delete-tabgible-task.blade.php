<div>
    <div class="modal fade delete" id="confirmDeleteTangibleTaskModal" tabindex="-1" role="dialog" aria-labelledby="deleteModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-sm modal-dialog-centered" role="document">
            <div class="modal-content radius-xl">
                <div class="modal-body">
                    <div class="text-center">
                        <h1 class="text-loss mb-4"><i class="las la-exclamation-circle fs-60"></i></h1>
                        <h5 class="mb-3">@lang('CRMProjects.common.are_you_sure')</h5>
                  
                    
                     <p>
                       
                       @lang('CRMProjects.common.delete_tangible_task_message') <strong>{{data_get($taskDelete, 'title', '') }}</strong>!
                        @lang('CRMProjects.common.this_action_cannot_be_undone')
                    </p>

                    </div>
                </div>
                <div class="modal-footer justify-content-between border-0 gap-10">
                    <button type="button" class="btn bg-hold-light text-white flex-fill radius-xl" data-dismiss="modal" wire:click="cancelDelete">@lang('No, Keep It')</button>
                    <button type="button" wire:loading.attr="disabled"
                    wire:loading.class="btn-loading" wire:click="deleteTangibleTask" class="btn bg-loss flex-fill radius-xl text-white">@lang('Yes, Delete It')</button>
                </div>
            </div>
        </div>
    </div>
</div>




@extends('layouts.app_crm')
@section('styles')
    <style>
        .action_icon {
            color: #212529 !important;
        }
    </style>
@endsection
@section('content')
    @livewire('leads.leads-details', ['leadID' => $leadId])
@endsection
<script src="{{ asset('js/livewire/manage-modals.js') }}"></script>
<script src="{{ asset('js/livewire/manage-tooltip.js') }}"></script>
<script src="{{ asset('js/livewire/manage-select2.js') }}"></script>
<script src="{{ asset('js/livewire/manage-modals.js') }}"></script>
<script src="{{ asset('js/livewire/manage-loader.js') }}"></script>
<script src="{{ asset('js/livewire/error-messages.js') }}"></script>
<script src="{{ asset('js/livewire/manage-datepicker.js') }}"></script>
<script src="/js/livewire/allow-float-1.js"></script>
<script src="/js/livewire/copy-billing-to-shipping.js"></script>
<script src="/js/livewire/manage-loaders.js"></script>
@section('scripts')
    <script src="{{ asset('new_theme/js/functions.js') }}"></script>
@endsection

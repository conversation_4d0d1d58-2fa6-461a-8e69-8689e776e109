<div>
    <div class="contents crm">
        <div class="container-fluid">
            <div class="col-lg-12">
                <div class="row justify-content-sm-between align-items-center justify-content-center my-3 flex-sm-row flex-column">
                    <div class="page-title-wrap p-0">
                        <div class="page-title d-flex justify-content-between">
                            <div class="page-title__left justify-content-sm-between align-items-center justify-content-center">
                                <div class="user-member__title mr-sm-25 ml-0">
                                    <h4 class="text-capitalize fw-500 breadcrumb-title fs-16">
                                        {{ __('vendors.navigation.manage_vendors') }}
                                    </h4>
                                </div>
                            </div>
                        </div>
                        <div>
                            <ul class="atbd-breadcrumb nav">
                                <li class="atbd-breadcrumb__item">
                                    <a>{{ __('vendors.navigation.dashboard') }}</a>
                                    <span class="breadcrumb__seperator">
                                        <span class="la la-angle-right"></span>
                                    </span>
                                </li>
                                <li class="atbd-breadcrumb__item">
                                    <a>{{ __('vendors.navigation.manage_vendors') }}</a>
                                </li>
                            </ul>
                        </div>
                    </div>

                    <div class="d-flex gap-10 breadcrumb_right_icons">
                        <div class="d-flex gap-10 breadcrumb_right_icons">
                            <button class="btn btn-white btn-default text-center svg-20 wh-45" wire:click="switchView('{{ $view === 'list' ? 'cards' : 'list' }}')">
                                @if($view === 'list')
                                    <i class="iconsax icon text-new-primary fs-22 colorRed mr-0" icon-name="layout-3"></i>
                                @else
                                    <i class="iconsax icon text-new-primary fs-22 colorRed mr-0" icon-name="task-list"></i>
                                @endif
                            </button>
                            <button class="btn btn-default btn-primary w-100 no-wrap" type="button" wire:click="openCreateModal" aria-expanded="false">
                                <i class="las la-plus fs-16"></i>{{ __('vendors.buttons.create') }}
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="table-responsive">
                <div class="card">
                    <div class="">
                        <div class="card-header py-4 px-3 border-0 d-flex justify-content-between align-items-center">
                            <h6 class="text-capitalize text-osool fw-500 mb-3 mb-sm-0">{{ __('vendors.common.vendors') }}</h6>

                            <div class="d-flex gap-10 table-search">
                                <div class="position-relative">
                                    <input type="text" class="form-control" placeholder="{{ __('vendors.search.placeholder') }}" wire:model.debounce.300ms="search">
                                    <i class="iconsax field-icon fs-18 mr-0" icon-name="search-normal-2"></i>
                                </div>
                                <button wire:click="export" wire:loading.attr="disabled" class="btn btn-export text-dark">
                                    <i class="iconsax icon fs-22 mr-0" icon-name="upload-1"></i> {{ __('vendors.buttons.export') }}
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="card-body px-0 pt-0">
                        @if($view === 'list')
                            @include('livewire.accounting.finance-vendor.list-view')
                        @else
                            @include('livewire.accounting.finance-vendor.card-view')
                        @endif
                    </div>

                    <div class="card-body pt-0">
                        <div class="d-flex justify-content-md-between flex-column flex-md-row justify-content-center align-items-center mt-4 gap-10">
                            <div class="">
                                <ul class="atbd-pagination d-flex justify-content-between">
                                    <li>
                                        <div class="paging-option">
                                            <div class="dataTables_length d-flex">
                                                <label class="d-flex align-items-center mb-0">
                                                    <select wire:model="perPage" class="custom-select custom-select-sm form-control form-control-sm mx-2" style="min-height: 35px;">
                                                        <option value="5">5</option>
                                                        <option value="10">10</option>
                                                        <option value="25">25</option>
                                                        <option value="50">50</option>
                                                        <option value="100">100</option>
                                                    </select>
                                                    <span class="no-wrap"> {{ __('vendors.pagination.entries_per_page') }} </span>
                                                </label>
                                            </div>
                                        </div>
                                    </li>
                                </ul>
                            </div>

                            @if($total > 0)
                            <div class="">
                                <div class="user-pagination">
                                    <div class="user-pagination new-pagination">
                                        <div class="d-flex justify-content-sm-end justify-content-end">
                                            <nav>
                                                <span class="relative z-0 inline-flex rounded-md d-flex new-pagination-section flex-wrap">
                                                    @if($pagination->current_page > 1)
                                                        <span>
                                                            <button type="button" class="border-0" wire:click="previousPage">
                                                                <i class="iconsax icon-bold fs-16 mr-0 rotate-ar-y d-inline-block" icon-name="chevron-left"></i>
                                                            </button>
                                                        </span>
                                                    @else
                                                        <span>
                                                            <button class="border-0 disabled" disabled>
                                                                <i class="iconsax icon-bold fs-16 mr-0 rotate-ar-y d-inline-block" icon-name="chevron-left"></i>
                                                            </button>
                                                        </span>
                                                    @endif

                                                    @php
                                                        $start = max(1, $pagination->current_page - 2);
                                                        $end = min($pagination->last_page, $pagination->current_page + 2);
                                                    @endphp

                                                    @if($start > 1)
                                                        <span>
                                                            <button type="button" class="border-0" wire:click="gotoPage(1)">1</button>
                                                        </span>
                                                        @if($start > 2)
                                                            <span>
                                                                <button class="border-0 disabled" disabled>...</button>
                                                            </span>
                                                        @endif
                                                    @endif

                                                    @for($i = $start; $i <= $end; $i++)
                                                        @if($i == $pagination->current_page)
                                                            <span>
                                                                <button class="border-0 current-page" disabled>{{ $i }}</button>
                                                            </span>
                                                        @else
                                                            <span>
                                                                <button type="button" class="border-0" wire:click="gotoPage({{ $i }})">{{ $i }}</button>
                                                            </span>
                                                        @endif
                                                    @endfor

                                                    @if($end < $pagination->last_page)
                                                        @if($end < $pagination->last_page - 1)
                                                            <span>
                                                                <button class="border-0 disabled" disabled>...</button>
                                                            </span>
                                                        @endif
                                                        <span>
                                                            <button type="button" class="border-0" wire:click="gotoPage({{ $pagination->last_page }})">{{ $pagination->last_page }}</button>
                                                        </span>
                                                    @endif

                                                    @if($pagination->current_page < $pagination->last_page)
                                                        <span>
                                                            <button type="button" class="border-0" wire:click="nextPage">
                                                                <i class="iconsax icon-bold fs-16 mr-0 rotate-ar-y d-inline-block" icon-name="chevron-right"></i>
                                                            </button>
                                                        </span>
                                                    @else
                                                        <span>
                                                            <button class="border-0 disabled" disabled>
                                                                <i class="iconsax icon-bold fs-16 mr-0 rotate-ar-y d-inline-block" icon-name="chevron-right"></i>
                                                            </button>
                                                        </span>
                                                    @endif
                                                </span>
                                            </nav>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div>
                                <p class="text-sm text-gray-700 leading-5 mb-0">
                                    <span>{{ __('vendors.pagination.showing') }}</span>
                                    <span class="font-medium">{{ ($pagination->current_page - 1) * $pagination->per_page + 1 }}</span>
                                    <span>{{ __('vendors.pagination.to') }}</span>
                                    <span class="font-medium">{{ min($pagination->current_page * $pagination->per_page, $pagination->total) }}</span>
                                    <span>{{ __('vendors.pagination.of') }}</span>
                                    <span class="font-medium">{{ $pagination->total }}</span>
                                    <span>{{ __('vendors.pagination.results') }}</span>
                                </p>
                            </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{-- Delete Confirmation Modal --}}
    @livewire('common.delete-confirm')

    {{-- Create/Edit Vendor Modal --}}
    @include('livewire.common.super-modal-v1', [
        'component' => 'accounting.finance-vendor.modals.create',
        'modalId' => 'createVendorModal',
    ])
</div>

@push('scripts')
<script>
    document.addEventListener('livewire:load', function () {
        // Initialize any JavaScript components if needed
    });

    // Toast notification handler
    window.addEventListener('show-toastr', event => {
        toastr.options = {
            "closeButton": true,
            "progressBar": true,
            "positionClass": "toast-top-right",
            "timeOut": "3000"
        };

        if (event.detail.type === 'success') {
            toastr.success(event.detail.message);
        } else if (event.detail.type === 'error') {
            toastr.error(event.detail.message);
        }
    });

    window.addEventListener('export-start', event => {
        showLoader();
    });

    window.addEventListener('export-end', event => {
        hideLoader();
    });

    window.addEventListener('redirect-to-export', event => {
        window.open(event.detail.url, '_blank');
    });



    function showLoader() {
        if (document.getElementById("overlayer")) {
            document.getElementById("overlayer").style.display = "block";
        }
        let loaderElements = document.getElementsByClassName("loader-overlay");
        if (loaderElements.length > 0) {
            loaderElements[0].style.display = "block";
        }
    }

    function hideLoader() {
        if (document.getElementById("overlayer")) {
            document.getElementById("overlayer").style.display = "none";
        }
        let loaderElements = document.getElementsByClassName("loader-overlay");
        if (loaderElements.length > 0) {
            loaderElements[0].style.display = "none";
        }
    }
</script>
@endpush

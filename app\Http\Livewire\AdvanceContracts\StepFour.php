<?php

namespace App\Http\Livewire\AdvanceContracts;

use App\Enums\Role;
use Livewire\Component;
use App\Enums\Proficiency;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Log;
use App\Models\AdvanceContractDraft;
use App\Repositories\AdvanceContractRepository;

class StepFour extends Component
{
    public $uuid;
    public $roles = [];
    public $roleOptions = [];
    public $proficiencyOptions = [];
    public $route_prefix;
    public $isVariation = false;
    public $currentStep = 4;

    public function mount(AdvanceContractRepository $advanceContractRepository, $uuid = null, $prefix = null)
    {
        // Set initial role options
        $this->roleOptions = Role::labels();
        $this->proficiencyOptions = Proficiency::limitedLabels();
        $this->route_prefix = $prefix;
        $this->isVariation = request()->routeIs('variation.*');
        // Set default empty roles
        $this->roles = [
            $this->emptyRole(),
        ];

        // If uuid is provided, load existing data
        if ($uuid) {
            $this->uuid = $uuid;

            $draft = $advanceContractRepository->findByUuid($this->uuid);;

            if ($draft) {
                $data = $draft->workforce_team_data;
                // Populate the roles array with the existing data
                if (is_array($data)) {
                    $this->roles = $data;
                }
            }
        }
    }

    public function emptyRole()
    {
        return [
            'role' => '',
            'proficiency' => '',
            'quantity' => '',
            'deduction_rate' => '',
            'working_days' => '',
            'localization_target' => '',
            'working_hours' => '',
            'attendance_mandatory' => false,
            'minimum_wage' => 0,
            'uniform_and_tools' => false,
        ];
    }

    public function addRole()
    {
        $this->roles[] = $this->emptyRole();
    }

    public function removeRole($index)
    {
        unset($this->roles[$index]);
        $this->roles = array_values($this->roles); // Reset index
    }

    public function rules()
    {
        return [
            'roles.*.role' => 'required|string',
            'roles.*.proficiency' => 'required|string',
            'roles.*.quantity' => 'required|integer|min:1',
            'roles.*.deduction_rate' => 'required|numeric|min:0|max:100',
            'roles.*.working_days' => 'required|integer|min:1',
            'roles.*.localization_target' => 'required|numeric|min:0|max:100',
            'roles.*.working_hours' => 'required|integer|min:1|max:60',
            'roles.*.attendance_mandatory' => 'boolean',
            'roles.*.minimum_wage' => 'required|integer',
            'roles.*.uniform_and_tools' => 'boolean',
        ];
    }

    public function submit(AdvanceContractRepository $advanceContractRepository)
    {
        $this->validate();
        
        if (!$this->uuid) {   
            $this->dispatchBrowserEvent('show-toastr', ['type' => 'error', 'message' => __('general_sentence.s_global.auth.something_wrong_try_again') ]);
            return;
        }
    
        $draft =    $advanceContractRepository->findByUuid($this->uuid);
    
        if (!$draft) {  
            $this->dispatchBrowserEvent('show-toastr', ['type' => 'error', 'message' => __('general_sentence.s_global.auth.something_wrong_try_again')]);
            return;
        }
    
        $draft->workforce_team_data = $this->roles;
        $draft->save();

        // Mark current step as saved in session
        $savedSteps = session()->get("contract_saved_steps_{$this->uuid}", []);
        if (!in_array($this->currentStep, $savedSteps)) {
            $savedSteps[] = $this->currentStep;
        }
        session()->put("contract_saved_steps_{$this->uuid}", $savedSteps);

        $this->dispatchBrowserEvent('show-toastr', ['type' => 'success', 'message' => __('advance_contracts.general.workforce_team_saved')]);
        
        return redirect()->route($this->route_prefix . 'extras', ['uuid' => $this->uuid]);
    }
    

    public function render()
    {
        return view('livewire.advance-contracts.step-four');
    }
}

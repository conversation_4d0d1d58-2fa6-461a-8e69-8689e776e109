<div>
    <div class="contents crm crm-dashboard expanded">
        <div class="container-fluid">
            <div class="col-lg-12">
                <div class="row justify-content-sm-between align-items-center justify-content-center">
                    <div class="page-title-wrap">
                        <div class="page-title d-flex justify-content-between">
                            <div
                                class="page-title__left justify-content-sm-between align-items-center justify-content-center">
                                <div class="user-member__title mr-sm-25 ml-0">
                                    <h4 class="text-capitalize fw-500 breadcrumb-title fs-16">
                                        @lang('CRMProjects.common.project_details')
                                    </h4>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!--====End Design for Export PDF===-->
                </div>
            </div>
            <div class="row">

                <div class="col-lg-12">
                    <div class="row">
                        @if ($this->settingsState['basic_details'] == 1)
                            <div class="col-md-6 col-sm-12 align-items-streched mb-lg-0 mb-md-0 mb-30 d-flex aos-init aos-animate"
                                data-aos="fade-up" data-aos-delay="200" data-aos-duration="1000">
                                <div class="card broder-0 d-flex align-items-streched w-100 h-100 dash-crm-logo">
                                    <div class="card-body py-30 d-flex flex-column justify-content-between">
                                        <div class="d-flex justify-content-between">
                                            <div class="text-white pr-3">

                                                <h6 class="text-white mb-2 fs-20">
                                                    {{ $projectData['title'] . '-' . $projectData['id'] }}
                                                    @if ($projectData['status'] == 'Draft')
                                                        <small
                                                            class="py-1 px-2 bg-draft rounded text-dark status-dashboard fs-12 d-inline-block">
                                                            @lang('CRMProjects.common.draft')
                                                        </small>
                                                    @elseif ($projectData['status'] == 'OnHold')
                                                        <small
                                                            class="py-1 px-2 bg-warning rounded text-white status-dashboard fs-12 d-inline-block">
                                                            @lang('CRMProjects.common.onhold')
                                                        </small>
                                                    @elseif($projectData['status'] == 'Finished')
                                                        <small
                                                            class="py-1 px-2 bg-win rounded text-white status-dashboard fs-12 d-inline-block">
                                                            @lang('CRMProjects.common.finished')
                                                        </small>
                                                    @else
                                                        <small
                                                            class="py-1 px-2 bg-hold rounded text-white status-dashboard fs-12 d-inline-block">
                                                            @lang('CRMProjects.common.ongoing')
                                                        </small>
                                                    @endif

                                                </h6>
                                                <p class="project-description site-scrollbar">
                                                    {{ $projectData['description'] }} </p>
                                            </div>


                                            <div class="crm-logo position-relative">
                                                <div
                                                    class="logo-inner d-flex justify-content-center align-items-center position-relative">
                                                    <span
                                                        class="wh-50 rounded-circle d-center fs-20 shadow-lg text-white  FLName_avatar">{{ mb_substr($projectData['title'], 0, 1, 'UTF-8') }}
                                                    </span>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="d-flex justify-content-between mt-3 px-3 py-2 radius-xl align-items-center"
                                            style="background: rgba(0, 0, 0, .1);">
                                            <div class="d-flex text-white gap-10">
                                                <div>
                                                    <small>@lang('CRMProjects.priority_level'):</small>
                                                    <p class="mb-0">
                                                        @if (!empty($projectData['priority_level']))
                                                            @lang('CRMProjects.priority_level_dropdown.' . $projectData['priority_level'])
                                                        @endif
                                                    </p>
                                                </div>
                                                <div>
                                                    <small>@lang('CRMProjects.project_type'):</small>
                                                    <p class="mb-0">
                                                        @if (!empty($projectData['project_type']))
                                                            @lang('CRMProjects.project_type_dropdown.' . $projectData['project_type'])
                                                        @endif
                                                    </p>
                                                </div>
                                                <div>
                                                    <small>@lang('CRMProjects.common.start_date'):</small>
                                                    <p class="mb-0">{{ $projectData['start_date'] }}</p>
                                                </div>
                                                <div>
                                                    <small>@lang('CRMProjects.common.end_date'):</small>
                                                    <p class="mb-0">{{ $projectData['end_date'] }}</p>
                                                </div>
                                                <div>
                                                    <small>@lang('CRMProjects.common.total_membres'):</small>
                                                    <p class="mb-0">
                                                        {{ isset($projectData['users']) && $projectData['users'] ? count($projectData['users']) : 0 }}
                                                    </p>
                                                </div>
                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 d-flex align-items-stretch">
                                <div class="row d-flex align-items-stretch flex-grow-1">
                                    <div class="col-sm-6 mb-3 d-flex note aos-init aos-animate" data-aos="fade-up"
                                        data-aos-delay="400" data-aos-duration="1000">
                                        <div
                                            class="card broder-0 d-flex align-items-streched w-100 h-100 bgRedLight overflow-hidden py-3">
                                            <div class="bg_circle_shape1 bgRed opacity-7"></div>
                                            <div class="bg_circle_shape2 bgRed opacity-7"></div>
                                            <div class="dash-circle wh-70 bgRed position-absolute rounded-circle"></div>
                                            <div class="d-flex-center px-4 h-100">
                                                <div class="d-flex justify-content-between w-100">
                                                    <div>
                                                        <span
                                                            class="wh-40 bg-white rounded d-inline-flex d-flex-center mb-2 position-relative"><i
                                                                class="iconsax icon fs-18 colorRed"
                                                                icon-name="calendar-1"></i></span>
                                                        <p class="fs-16 mb-0 colorRed">@lang('CRMProjects.common.days_left') </p>
                                                    </div>
                                                    <h3 class="fw-500">{{ abs($daysleft) }}</h3>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-6 mb-3 d-flex note aos-init aos-animate" data-aos="fade-up"
                                        data-aos-delay="600" data-aos-duration="1000">
                                        <div
                                            class="card broder-0 d-flex align-items-streched w-100  h-100 bgGreenLight overflow-hidden py-3">
                                            <div class="bg_circle_shape1 bgGreen opacity-3"></div>
                                            <div class="bg_circle_shape2 bgGreen opacity-3"></div>
                                            <div
                                                class="dash-circle wh-70 bgGreen opacity-5 position-absolute rounded-circle">
                                            </div>
                                            <div class="d-flex-center px-4 h-100">
                                                <div class="d-flex justify-content-between w-100">
                                                    <div>
                                                        <span
                                                            class="wh-40 bg-white rounded d-inline-flex d-flex-center mb-2 position-relative"><i
                                                                class="iconsax icon fs-18 colorGreen"
                                                                icon-name="money-out"></i></span>
                                                        <p class="fs-16 mb-0 colorGreen">@lang('CRMProjects.common.budget')</p>
                                                    </div>
                                                    <h3 class="fw-500">{{ $projectData['budget'] ?? 0 }} </h3>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-6 mb-3 mb-sm-0 d-flex note aos-init aos-animate"
                                        data-aos="fade-up" data-aos-delay="800" data-aos-duration="1000">
                                        <div
                                            class="card broder-0 d-flex align-items-streched w-100  h-100 bgOrangeLight overflow-hidden py-3">
                                            <div class="bg_circle_shape1 bgOrange opacity-3"></div>
                                            <div class="bg_circle_shape2 bgOrange opacity-3"></div>
                                            <div
                                                class="dash-circle wh-70 bgOrange opacity-5 position-absolute rounded-circle">
                                            </div>
                                            <div class="d-flex-center px-4 h-100">
                                                <div class="d-flex justify-content-between w-100">
                                                    <div>
                                                        <span
                                                            class="wh-40 bg-white rounded d-inline-flex d-flex-center mb-2 position-relative"><i
                                                                class="iconsax icon fs-18 colorOrange"
                                                                icon-name="tick-square"></i></span>
                                                        <p class="fs-16 mb-0 colorOrange">@lang('CRMProjects.common.total_task')</p>
                                                    </div>
                                                    <h3 class="fw-500">{{ $projectData['total_task'] ?? 0 }} </h3>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-6 d-flex note aos-init aos-animate" data-aos="fade-up"
                                        data-aos-delay="400" data-aos-duration="1000">
                                        <div
                                            class="card broder-0 d-flex align-items-streched w-100 h-100 bgBlueLight overflow-hidden py-3">
                                            <div class="bg_circle_shape1 bgBlue opacity-3"></div>
                                            <div class="bg_circle_shape2 bgBlue opacity-3"></div>
                                            <div
                                                class="dash-circle wh-70 bgBlue opacity-5 position-absolute rounded-circle">
                                            </div>
                                            <div class="d-flex-center px-4 h-100">
                                                <div class="d-flex justify-content-between w-100">
                                                    <div>
                                                        <span
                                                            class="wh-40 bg-white rounded d-inline-flex d-flex-center mb-2 position-relative"><i
                                                                class="iconsax icon fs-18 colorBlue"
                                                                icon-name="message-text"></i></span>
                                                        <p class="fs-16 mb-0 colorBlue">@lang('CRMProjects.common.comments') </p>
                                                    </div>
                                                    <h3 class="fw-500">{{ $projectData['total_comment'] ?? 0 }} </h3>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endif
                        @if ($this->settingsState['progress'] == 1)
                            <div class="col-md-12 col-sm-12 mb-lg-0 mb-md-0 mt-4">
                                <div class="card broder-0 d-flex align-items-streched w-100 mb-3">
                                    <div class="card-header">
                                        <h6>@lang('CRMProjects.common.progress_last_week_tasks')</h6>
                                    </div>
                                    <div class="card-body py-30 d-flex justify-content-center">
                                        <canvas id="tasksOverviewChart"></canvas>
                                    </div>
                                </div>
                            </div>
                        @endif

                        @if ($this->settingsState['member'] == 1)
                            <div class="col-md-4 col-sm-6 mb-3 mt-4">
                                <div class="card   h-100 broder-0 d-flex align-items-streched w-100 ">
                                    <div class="card-header d-flex justify-content-between">
                                        <h6>@lang('CRMProjects.common.team_members') ({{ count($projectData['users'] ?? []) }})</h6>
                                    </div>
                                    <div class="card-body">

                                        @if (!empty($projectData['users']))
                                            @foreach ($projectData['users'] as $user)
                                                <div
                                                    class="d-flex align-items-top position-relative cursor-pointer mb-3">
                                                    <div class="user-profile mr-2">

                                                        <img src="{{ $user['avatar'] ?? asset('uploads/profile_images/dummy_profile_image.png') }}"
                                                            class="wh-40 radius-xl">

                                                    </div>
                                                    <div class="text-osool">
                                                        <p class="mb-0 text-osool d-flex align-items-center">
                                                            {{ $user['name'] }}</p>
                                                        <small class="mb-0 text-osool d-flex align-items-center">
                                                            {{ $user['email'] }}- {{ $user['complate_task'] ?? 0 }}
                                                        </small>
                                                    </div>
                                                </div>
                                            @endforeach

                                        @endif



                                    </div>
                                </div>
                            </div>
                        @endif
                        @if ($this->settingsState['client'] == 1)
                            <div class="col-md-4  col-sm-6 mb-3 mt-4">
                                <div class="card  h-100 broder-0 d-flex align-items-streched w-100  ">
                                    <div class="card-header d-flex justify-content-between">
                                        <h6>@lang('CRMProjects.common.clients') ({{ count($projectData['clients'] ?? []) }})</h6>
                                    </div>
                                    <div class="card-body">
                                        @if (!empty($projectData['clients']))
                                            @foreach ($projectData['clients'] as $client)
                                                <div
                                                    class="d-flex align-items-top position-relative cursor-pointer mb-3">
                                                    <div class="user-profile mr-2">
                                                        <img src="{{ $user['avatar'] ?? asset('uploads/profile_images/dummy_profile_image.png') }}"
                                                            class="wh-40 radius-xl">
                                                    </div>
                                                    <div class="text-osool">
                                                        <p class="mb-0 text-osool d-flex align-items-center">
                                                            {{ $client['name'] }}</p>
                                                        <small class="mb-0 text-osool d-flex align-items-center">
                                                            {{ $client['email'] }}-
                                                            {{ $client['complate_task'] ?? 0 }}
                                                        </small>
                                                    </div>
                                                </div>
                                            @endforeach

                                        @endif

                                    </div>
                                </div>
                            </div>
                        @endif
                        @if ($this->settingsState['vendor'] == 1)
                            <div class="col-md-4 col-sm-6 mb-3 mt-4">
                                <div class="card h-100 broder-0 d-flex align-items-streched w-100 ">
                                    <div class="card-header d-flex justify-content-between">
                                        <h6>@lang('CRMProjects.common.vendors') ({{ count($projectData['vendors'] ?? []) }})</h6>
                                    </div>
                                    <div class="card-body">
                                        @if (!empty($projectData['vendors']))
                                            @foreach ($projectData['vendors'] as $vendor)
                                                <div
                                                    class="d-flex align-items-top position-relative cursor-pointer mb-3">
                                                    <div class="user-profile mr-2">
                                                        <img src="{{ $user['avatar'] ?? asset('uploads/profile_images/dummy_profile_image.png') }}"
                                                            class="wh-40 radius-xl">
                                                    </div>
                                                    <div class="text-osool">
                                                        <p class="mb-0 text-osool d-flex align-items-center">
                                                            {{ $vendor['name'] }}</p>
                                                        <small class="mb-0 text-osool d-flex align-items-center">
                                                            {{ $vendor['email'] }}-
                                                            {{ $vendor['complate_task'] ?? 0 }}
                                                        </small>
                                                    </div>
                                                </div>
                                            @endforeach

                                        @endif

                                    </div>
                                </div>
                            </div>
                        @endif
                        @if ($this->settingsState['milestone'] == 1)
                            <div class="col-lg-12 mb-3">

                                <div class="card">
                                    <div class="">
                                        <div
                                            class="card-header py-4 px-3 border-0 d-flex justify-content-between align-items-center">
                                            <h6 class="text-capitalize fw-500 mb-3 mb-sm-0 flex-fill">
                                                @lang('CRMProjects.common.milestones') ({{ count($milestones_list) }})
                                            </h6>
                                        </div>
                                    </div>

                                    <div class="card-body px-0 pt-0">
                                        @if (count($milestones_list) > 0)
                                            <div
                                                class="userDatatable projectDatatable project-table bg-white w-100 border-0">
                                                <div class="table-responsive">
                                                    <table class="table mb-0 radius-0">
                                                        <thead>
                                                            <tr class="userDatatable-header">
                                                                <th>
                                                                    @lang('CRMProjects.common.name')
                                                                </th>
                                                                <th>
                                                                    @lang('CRMProjects.common.status')
                                                                </th>
                                                                <th>
                                                                    @lang('CRMProjects.common.start_date')
                                                                </th>
                                                                <th>
                                                                    @lang('CRMProjects.common.end_date')
                                                                </th>
                                                                <th>
                                                                    @lang('CRMProjects.common.cost')
                                                                </th>
                                                                <th>
                                                                    @lang('CRMProjects.common.progress')
                                                                </th>
                                                            </tr>
                                                        </thead>
                                                        <tbody class="sort-table ui-sortable">
                                                            @foreach ($milestones_list as $milestone)
                                                                <tr class="ui-sortable-handle" style="opacity: 1;">
                                                                    <td>
                                                                        <div
                                                                            class="d-flex userDatatable-content mb-0 align-items-center">
                                                                            <span>{{ $milestone['title'] }}</span>
                                                                        </div>
                                                                    </td>
                                                                    <td>

                                                                        @if ($milestone['status'] == 'incomplete')
                                                                            <small
                                                                                class="py-1 px-2 bg-hold rounded text-white">
                                                                                @lang('CRMProjects.common.incomplete')
                                                                            </small>
                                                                        @else
                                                                            <small
                                                                                class="py-1 px-2 bg-success rounded text-white">
                                                                                @lang('CRMProjects.common.complete')
                                                                            </small>
                                                                        @endif

                                                                    </td>
                                                                    <td>
                                                                        <div
                                                                            class="d-flex userDatatable-content mb-0 align-items-center">
                                                                            <span>
                                                                                {{ Helper::formatDateForLocale($milestone['start_date']) }}</span>
                                                                        </div>
                                                                    </td>
                                                                    <td>
                                                                        <div
                                                                            class="d-flex userDatatable-content mb-0 align-items-center">
                                                                            <span>{{ Helper::formatDateForLocale($milestone['end_date']) }}</span>
                                                                        </div>
                                                                    </td>
                                                                    <td>
                                                                        <div
                                                                            class="d-flex userDatatable-content mb-0 align-items-center">
                                                                            <span>{!! $currency !!}
                                                                                {{ Helper::human_readable_number($milestone['cost']) }}</span>
                                                                        </div>
                                                                    </td>
                                                                    <td>


                                                                        <div class="progress">
                                                                            <div class="progress-bar"
                                                                                role="progressbar"
                                                                                style="width: {{ $milestone['progress'] }}%"
                                                                                aria-valuenow="{{ $milestone['progress'] }}"
                                                                                aria-valuemin="0" aria-valuemax="100">
                                                                            </div>
                                                                        </div>
                                                                        <span
                                                                            class="fs-12">{{ $milestone['progress'] ?? 0 }}%</span>
                                                                    </td>
                                                                </tr>
                                                            @endforeach


                                                        </tbody>
                                                    </table>

                                                </div>
                                            </div>
                                        @else
                                            <div class="row">
                                                <div class="PropertyListEmpty">
                                                    <img src="{{ asset('empty-icon/no-projects.svg') }}"
                                                        class="fourth_img" alt="">
                                                    <h4 class="first_title">@lang('CRMProjects.common.no_milestone_yet')</h4>
                                                    <h6 class="second_title">@lang('CRMProjects.common.milestone_list_will_appear')</h6>
                                                </div>
                                            </div>
                                        @endif

                                    </div>
                                </div>
                            </div>
                        @endif

                        @if ($this->settingsState['attachment'] == 1)
                            <div class="col-lg-12 mb-3">
                                <div class="card">
                                    <div class="">
                                        <div
                                            class="card-header py-4 px-3 border-0 d-flex justify-content-between align-items-center">
                                            <h6 class="text-capitalize fw-500 mb-3 mb-sm-0 flex-fill">
                                                @lang('CRMProjects.common.files')
                                                ({{ Count($projectData['files'] ?? []) }})</h6>
                                            <div></div>
                                        </div>
                                    </div>



                                    <div wire:loading wire:target="attachmentFiles" class="card-body px-0 pt-0">
                                        <div
                                            class="userDatatable projectDatatable project-table bg-white w-100 border-0">
                                            <div class="table-responsive">
                                                <div class="item-inner mx-4">
                                                    <div class="item-content">
                                                        <div class="image-upload pt-4">
                                                            <label for="file_upload" class="">
                                                                <div class="h-100">
                                                                    <div class="dplay-tbl">
                                                                        <div class="dplay-tbl-cell">
                                                                            <div
                                                                                class="upload-bg wh-50 d-flex-center rounded-circle mx-auto position-relative">
                                                                                <i class="iconsax icon fs-22 fs-22 mr-0 text-dark position-relative"
                                                                                    icon-name="upload-2"></i>
                                                                            </div>
                                                                            <p class="drag_drop_txt">
                                                                                @lang('CRMProjects.common.file_uploading')
                                                                                <span
                                                                                    class="required">@lang('CRMProjects.common.uploading')</span>
                                                                                @lang('CRMProjects.common.might_take_time')
                                                                            </p>
                                                                        </div>
                                                                    </div>
                                                                </div>

                                                            </label>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    @if (Count($projectData['files'] ?? [] > 0))
                                        <div class="card-body CRMprojectLog ">
                                            <div
                                                class="userDatatable projectDatatable project-table bg-white w-100 border-0">
                                                <div class="table-responsive">
                                                    <table class="table mb-0 radius-0">
                                                        <thead>
                                                            <tr>
                                                                <th>@lang('lead.attachments.file_name')</th>
                                                                <th>@lang('lead.attachments.upload_date')</th>
                                                                <th>@lang('lead.attachments.actions')</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            @forelse ($projectData['files'] ?? [] as $file)
                                                                <tr>
                                                                    <td>{{ $file['file_name'] }}</td>
                                                                    <td>
                                                                        @if (isset($file['created_at']))
                                                                            {{ Helper::formatDateForLocale($file['created_at']) }}
                                                                        @else
                                                                            N/A
                                                                        @endif
                                                                    </td>
                                                                    <td class="d-flex align-items-center gap-2">
                                                                        <a class="btn btn-link p-0"
                                                                            href="{{ $file['file_path'] }}"
                                                                            target="_blank" download>
                                                                            <svg width="18" height="18"
                                                                                viewBox="0 0 24 24" fill="none"
                                                                                xmlns="http://www.w3.org/2000/svg"
                                                                                style="stroke: green;">
                                                                                <path
                                                                                    d="M12 3V16M12 16L8 12M12 16L16 12M4 20H20"
                                                                                    stroke-width="2"
                                                                                    stroke-linecap="round"
                                                                                    stroke-linejoin="round" />
                                                                            </svg>
                                                                        </a>
                                                                    </td>
                                                                </tr>
                                                            @empty
                                                                <tr>
                                                                    <td colspan="6">
                                                                        @include('livewire.sales.common.no-data-tr')
                                                                    </td>
                                                                </tr>
                                                            @endforelse
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                        </div>
                                    @endif
                                </div>

                            </div>
                        @endif
                        @if ($this->settingsState['task'] == 1)
                            <div class="card" style="background-color:transparent !important">
                                <div class="card-header"
                                    style="padding: 25px 35px !important; background-color:#ffffff !important">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div class="row">
                                            <h5 class="mb-0">@lang('Task')</h5>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <section class="section">
                                        <div class="row">
                                            <div class="col-sm-12">
                                                <div class="row kanban-wrapper horizontal-scroll-cards"
                                                    data-toggle="dragula">
                                                    @foreach ($stages as $item)
                                                        <div class="col" id="backlog">
                                                            <div class="card card-list">
                                                                <div class="card-header">
                                                                    <div class="float-end">
                                                                        <button
                                                                            class="btn-submit btn btn-md btn-primary btn-icon px-1  py-0">
                                                                            <span
                                                                                class="badge badge-secondary rounded-pill count">{{ $item['count'] }}</span>
                                                                        </button>
                                                                    </div>
                                                                    <h4 class="mb-0">{{ $item['name'] }}</h4>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    @endforeach

                                                </div>
                                                <!-- [ sample-page ] end -->
                                            </div>
                                        </div>
                                    </section>
                                </div>
                            </div>
                        @endif
                        @if ($this->settingsState['bug_report'] == 1)
                            <div class="card" style="background-color:transparent !important">
                                <div class="card-header"
                                    style="padding: 25px 35px !important; background-color:#ffffff !important">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div class="row">
                                            <h5 class="mb-0">@lang('Bug Report')</h5>

                                        </div>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <section class="section">
                                        <div class="row">
                                            <div class="col-sm-12">
                                                <div class="row kanban-wrapper horizontal-scroll-cards"
                                                    data-toggle="dragula">
                                                    @foreach ($bug_stages as $item)
                                                        <div class="col" id="backlog">
                                                            <div class="card card-list">
                                                                <div class="card-header">
                                                                    <div class="float-end">
                                                                        <button
                                                                            class="btn-submit btn btn-md btn-primary btn-icon px-1  py-0">
                                                                            <span
                                                                                class="badge badge-secondary rounded-pill count">{{ $item['count'] }}</span>
                                                                        </button>
                                                                    </div>
                                                                    <h4 class="mb-0">{{ $item['name'] }}</h4>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    @endforeach
                                                </div>
                                                <!-- [ sample-page ] end -->
                                            </div>
                                        </div>
                                    </section>
                                </div>
                            </div>
                        @endif
                        @if ($this->settingsState['invoice'] == 1)
                            <div class="col-lg-12 mb-3">

                                <div class="card">
                                    <div class="">
                                        <div
                                            class="card-header py-4 px-3 border-0 d-flex justify-content-between align-items-center">
                                            <h6 class="text-capitalize fw-500 mb-3 mb-sm-0 flex-fill">
                                                @lang('Invoice') ({{ count($invoices) }})
                                            </h6>
                                        </div>
                                    </div>

                                    <div class="card-body px-0 pt-0">
                                        @if (count($invoices) > 0)
                                            <div
                                                class="userDatatable projectDatatable project-table bg-white w-100 border-0">
                                                <div class="table-responsive">
                                                    <table class="table mb-0 radius-0">
                                                        <thead>
                                                            <tr class="userDatatable-header">
                                                                <th>
                                                                    @lang('Invoice')
                                                                </th>
                                                                <th>
                                                                    @lang('Issue Date')
                                                                </th>
                                                                <th>
                                                                    @lang('Due Date')
                                                                </th>
                                                                <th>
                                                                    @lang('Due Amount')
                                                                </th>
                                                                <th>
                                                                    @lang('Status')
                                                                </th>
                                                            </tr>
                                                        </thead>
                                                        <tbody class="sort-table ui-sortable">
                                                            @foreach ($invoices as $invoice)
                                                                <tr class="ui-sortable-handle" style="opacity: 1;">
                                                                    <td>
                                                                        <div
                                                                            class="d-flex userDatatable-content mb-0 align-items-center">
                                                                            <span>{{ $invoice['invoice_id'] }}</span>
                                                                        </div>
                                                                    </td>
                                                                    <td>
                                                                        <div
                                                                            class="d-flex userDatatable-content mb-0 align-items-center">
                                                                            <span>
                                                                                {{ Helper::formatDateForLocale($invoice['issue_date']) }}</span>
                                                                        </div>
                                                                    </td>
                                                                    <td>
                                                                        <div
                                                                            class="d-flex userDatatable-content mb-0 align-items-center">
                                                                            <span>{{ Helper::formatDateForLocale($invoice['due_date']) }}</span>
                                                                        </div>
                                                                    </td>
                                                                    <td>
                                                                        <div
                                                                            class="d-flex userDatatable-content mb-0 align-items-center">
                                                                            <span>{!! $currency !!}
                                                                                {{ Helper::human_readable_number($invoice['due_amount']) }}</span>
                                                                        </div>
                                                                    </td>
                                                                    <td>
                                                                        <div
                                                                            class="d-flex userDatatable-content mb-0 align-items-center">
                                                                            <span>{{ $invoice['status'] }}</span>
                                                                        </div>
                                                                    </td>
                                                                </tr>
                                                            @endforeach


                                                        </tbody>
                                                    </table>

                                                </div>
                                            </div>
                                        @else
                                            <div class="row">
                                                <div class="PropertyListEmpty">
                                                    <img src="{{ asset('empty-icon/no-projects.svg') }}"
                                                        class="fourth_img" alt="">
                                                    <h4 class="first_title">@lang('No Invoice Yet')</h4>
                                                    <h6 class="second_title">@lang('The Invoice list will appear here')</h6>
                                                </div>
                                            </div>
                                        @endif

                                    </div>
                                </div>
                            </div>
                        @endif
                        @if ($this->settingsState['bill'] == 1)
                            <div class="col-lg-12 mb-3">

                                <div class="card">
                                    <div class="">
                                        <div
                                            class="card-header py-4 px-3 border-0 d-flex justify-content-between align-items-center">
                                            <h6 class="text-capitalize fw-500 mb-3 mb-sm-0 flex-fill">
                                                @lang('Bill') ({{ count($bills) }})
                                            </h6>
                                        </div>
                                    </div>

                                    <div class="card-body px-0 pt-0">
                                        @if (count($bills) > 0)
                                            <div
                                                class="userDatatable projectDatatable project-table bg-white w-100 border-0">
                                                <div class="table-responsive">
                                                    <table class="table mb-0 radius-0">
                                                        <thead>
                                                            <tr class="userDatatable-header">
                                                                <th>
                                                                    @lang('Bill')
                                                                </th>
                                                                <th>
                                                                    @lang('Vendor')
                                                                </th>
                                                                <th>
                                                                    @lang('Bill Date')
                                                                </th>
                                                                <th>
                                                                    @lang('Due Date')
                                                                </th>
                                                                <th>
                                                                    @lang('Due Amount')
                                                                </th>
                                                                <th>
                                                                    @lang('Status')
                                                                </th>
                                                            </tr>
                                                        </thead>
                                                        <tbody class="sort-table ui-sortable">
                                                            @foreach ($bills as $bill)
                                                                <tr class="ui-sortable-handle" style="opacity: 1;">
                                                                    <td>
                                                                        <div
                                                                            class="d-flex userDatatable-content mb-0 align-items-center">
                                                                            <span>{{ $bill['bill_number'] }}</span>
                                                                        </div>
                                                                    </td>
                                                                    <td>
                                                                        <div
                                                                            class="d-flex userDatatable-content mb-0 align-items-center">
                                                                            <span>{{ $bill['vendor_name'] }}</span>
                                                                        </div>
                                                                    </td>
                                                                    <td>
                                                                        <div
                                                                            class="d-flex userDatatable-content mb-0 align-items-center">
                                                                            <span>{{ Helper::formatDateForLocale($bill['bill_date']) }}</span>
                                                                        </div>
                                                                    </td>
                                                                    <td>
                                                                        <div
                                                                            class="d-flex userDatatable-content mb-0 align-items-center">
                                                                            <span>{{ Helper::formatDateForLocale($bill['due_date']) }}</span>
                                                                        </div>
                                                                    </td>
                                                                    <td>
                                                                        <div
                                                                            class="d-flex userDatatable-content mb-0 align-items-center">
                                                                            <span>{!! $currency !!}
                                                                                {{ Helper::human_readable_number($bill['due_amount']) }}</span>
                                                                        </div>
                                                                    </td>
                                                                    <td>
                                                                        <div
                                                                            class="d-flex userDatatable-content mb-0 align-items-center">
                                                                            <span>{{ $bill['status'] }}</span>
                                                                        </div>
                                                                    </td>
                                                                </tr>
                                                            @endforeach


                                                        </tbody>
                                                    </table>

                                                </div>
                                            </div>
                                        @else
                                            <div class="row">
                                                <div class="PropertyListEmpty">
                                                    <img src="{{ asset('empty-icon/no-projects.svg') }}"
                                                        class="fourth_img" alt="">
                                                    <h4 class="first_title">@lang('No Bill Yet')</h4>
                                                    <h6 class="second_title">@lang('The Bill list will appear here')</h6>
                                                </div>
                                            </div>
                                        @endif

                                    </div>
                                </div>
                            </div>
                        @endif
                        @if ($this->settingsState['retainer'] == 1)
                            <div class="col-lg-12 mb-3">

                                <div class="card">
                                    <div class="">
                                        <div
                                            class="card-header py-4 px-3 border-0 d-flex justify-content-between align-items-center">
                                            <h6 class="text-capitalize fw-500 mb-3 mb-sm-0 flex-fill">
                                                @lang('Retainer') ({{ count($retainers) }})
                                            </h6>
                                        </div>
                                    </div>

                                    <div class="card-body px-0 pt-0">
                                        @if (count($retainers) > 0)
                                            <div
                                                class="userDatatable projectDatatable project-table bg-white w-100 border-0">
                                                <div class="table-responsive">
                                                    <table class="table mb-0 radius-0">
                                                        <thead>
                                                            <tr class="userDatatable-header">
                                                                <th>
                                                                    @lang('Retainer')
                                                                </th>
                                                                <th>
                                                                    @lang('Customer')
                                                                </th>
                                                                <th>
                                                                    @lang('Account Type	')
                                                                </th>
                                                                <th>
                                                                    @lang('Issue Date')
                                                                </th>
                                                                <th>
                                                                    @lang('Due Amount')
                                                                </th>
                                                                <th>
                                                                    @lang('Status')
                                                                </th>
                                                            </tr>
                                                        </thead>
                                                        <tbody class="sort-table ui-sortable">
                                                            @foreach ($retainers as $retainer)
                                                                <tr class="ui-sortable-handle" style="opacity: 1;">
                                                                    <td>
                                                                        <div
                                                                            class="d-flex userDatatable-content mb-0 align-items-center">
                                                                            <span>{{ $retainer['retainer_id'] }}</span>
                                                                        </div>
                                                                    </td>
                                                                    <td>
                                                                        <div
                                                                            class="d-flex userDatatable-content mb-0 align-items-center">
                                                                            <span>{{ $retainer['customer'] }}</span>
                                                                        </div>
                                                                    </td>
                                                                    <td>
                                                                        <div
                                                                            class="d-flex userDatatable-content mb-0 align-items-center">
                                                                            <span>{{ $retainer['account_type'] }}</span>
                                                                        </div>
                                                                    </td>
                                                                    <td>
                                                                        <div
                                                                            class="d-flex userDatatable-content mb-0 align-items-center">
                                                                            <span>{{ Helper::formatDateForLocale($retainer['issue_date']) }}</span>
                                                                        </div>
                                                                    </td>
                                                                    <td>
                                                                        <div
                                                                            class="d-flex userDatatable-content mb-0 align-items-center">
                                                                            <span>{!! $currency !!}
                                                                                {{ Helper::human_readable_number($retainer['due_amount']) }}</span>
                                                                        </div>
                                                                    </td>
                                                                    <td>
                                                                        <div
                                                                            class="d-flex userDatatable-content mb-0 align-items-center">
                                                                            <span>{{ $retainer['status'] }}</span>
                                                                        </div>
                                                                    </td>
                                                                </tr>
                                                            @endforeach


                                                        </tbody>
                                                    </table>

                                                </div>
                                            </div>
                                        @else
                                            <div class="row">
                                                <div class="PropertyListEmpty">
                                                    <img src="{{ asset('empty-icon/no-projects.svg') }}"
                                                        class="fourth_img" alt="">
                                                    <h4 class="first_title">@lang('No Retainer Yet')</h4>
                                                    <h6 class="second_title">@lang('The Retainer list will appear here')</h6>
                                                </div>
                                            </div>
                                        @endif

                                    </div>
                                </div>
                            </div>
                        @endif
                        @if ($this->settingsState['proposal'] == 1)
                            <div class="col-lg-12 mb-3">

                                <div class="card">
                                    <div class="">
                                        <div
                                            class="card-header py-4 px-3 border-0 d-flex justify-content-between align-items-center">
                                            <h6 class="text-capitalize fw-500 mb-3 mb-sm-0 flex-fill">
                                                @lang('Proposal') ({{ count($proposals) }})
                                            </h6>
                                        </div>
                                    </div>

                                    <div class="card-body px-0 pt-0">
                                        @if (count($proposals) > 0)
                                            <div
                                                class="userDatatable projectDatatable project-table bg-white w-100 border-0">
                                                <div class="table-responsive">
                                                    <table class="table mb-0 radius-0">
                                                        <thead>
                                                            <tr class="userDatatable-header">
                                                                <th>
                                                                    @lang('Proposal')
                                                                </th>
                                                                <th>
                                                                    @lang('Customer')
                                                                </th>
                                                                <th>
                                                                    @lang('Account Type	')
                                                                </th>
                                                                <th>
                                                                    @lang('Issue Date')
                                                                </th>
                                                                <th>
                                                                    @lang('Status')
                                                                </th>
                                                            </tr>
                                                        </thead>
                                                        <tbody class="sort-table ui-sortable">
                                                            @foreach ($proposals as $proposal)
                                                                <tr class="ui-sortable-handle" style="opacity: 1;">
                                                                    <td>
                                                                        <div
                                                                            class="d-flex userDatatable-content mb-0 align-items-center">
                                                                            <span>{{ $proposal['proposal_number'] }}</span>
                                                                        </div>
                                                                    </td>
                                                                    <td>
                                                                        <div
                                                                            class="d-flex userDatatable-content mb-0 align-items-center">
                                                                            <span>{{ $proposal['customer'] }}</span>
                                                                        </div>
                                                                    </td>
                                                                    <td>
                                                                        <div
                                                                            class="d-flex userDatatable-content mb-0 align-items-center">
                                                                            <span>{{ $proposal['account_type'] }}</span>
                                                                        </div>
                                                                    </td>
                                                                    <td>
                                                                        <div
                                                                            class="d-flex userDatatable-content mb-0 align-items-center">
                                                                            <span>{{ Helper::formatDateForLocale($proposal['issue_date']) }}</span>
                                                                        </div>
                                                                    </td>
                                                                    <td>
                                                                        <div
                                                                            class="d-flex userDatatable-content mb-0 align-items-center">
                                                                            <span>{{ $proposal['status'] }}</span>
                                                                        </div>
                                                                    </td>
                                                                </tr>
                                                            @endforeach
                                                        </tbody>
                                                    </table>

                                                </div>
                                            </div>
                                        @else
                                            <div class="row">
                                                <div class="PropertyListEmpty">
                                                    <img src="{{ asset('empty-icon/no-projects.svg') }}"
                                                        class="fourth_img" alt="">
                                                    <h4 class="first_title">@lang('No Proposal Yet')</h4>
                                                    <h6 class="second_title">@lang('The Proposal list will appear here')</h6>
                                                </div>
                                            </div>
                                        @endif

                                    </div>
                                </div>
                            </div>
                        @endif
                        @if ($this->settingsState['activity'] == 1)
                            <div class="col-lg-12 mb-3">

                                <div class="card">
                                    <div class="">
                                        <div
                                            class="card-header py-4 px-3 border-0 d-flex justify-content-between align-items-center">
                                            <h6 class="text-capitalize fw-500 mb-3 mb-sm-0 flex-fill">
                                                @lang('CRMProjects.common.activity')
                                            </h6>
                                            <div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="card-body CRMprojectLog ">
                                        @if (!empty($projectData['activities']))
                                            @foreach ($projectData['activities'] as $activite)
                                                @php
                                                    $logLine = App\Http\Traits\CRMProjects\ActiviteLogManagment::getLogLine(
                                                        $activite,
                                                    );
                                                @endphp

                                                @if (Arr::get($logLine, 'display', false))
                                                    <div
                                                        class="d-flex justify-content-between border-bottom py-3 align-items-center">
                                                        <div class="d-flex align-items-center gap-10">
                                                            @php
                                                                $iconClass = Arr::get(
                                                                    $logLine,
                                                                    'icon.icon_class',
                                                                    'default-icon-class',
                                                                );
                                                                $iconName = Arr::get(
                                                                    $logLine,
                                                                    'icon.icon_name',
                                                                    'default-icon-name',
                                                                );
                                                            @endphp
                                                            <div
                                                                class="wh-35 rounded border d-center {{ $iconClass }}">
                                                                <i class="iconsax"
                                                                    icon-name="{{ $iconName }}"></i>
                                                            </div>

                                                            <div class="mr-3 text-dark">
                                                                {{ Arr::get($logLine, 'log_type', 'N/A') }}
                                                            </div>
                                                            <div>
                                                                {{ Arr::get($logLine, 'log_info', 'No info available') }}
                                                            </div>
                                                        </div>
                                                        <span>
                                                            {{ Arr::get($logLine, 'loged_at', 'Date not available') }}
                                                        </span>
                                                    </div>
                                                @endif
                                            @endforeach
                                        @else
                                            <div class="form-group col-12">
                                                <div class="item-inner">
                                                    <div class="item-content">
                                                        <div class="image-upload border-0 radius-xl">
                                                            <label class="mb-2 mt-4">
                                                                <div class="h-100">
                                                                    <div class="dplay-tbl">
                                                                        <div class="dplay-tbl-cell">
                                                                            <div
                                                                                class="upload-bg wh-50 d-flex-center rounded-circle mx-auto position-relative">
                                                                                <i class="iconsax icon fs-22 fs-22 mr-0 text-dark position-relative"
                                                                                    icon-name="clock"></i>
                                                                            </div>
                                                                            <p class="drag_drop_txt mt-3">
                                                                                @lang('CRMProjects.common.no_activity_yet')</p>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </label>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                        @endif
                                    </div>
                                </div>
                            </div>
                        @endif

                        @if ($this->settingsState['documents'] == 1)
                            <div class="col-lg-12 mb-3">
                                <div class="card">
                                    <div class="">
                                        <div
                                            class="card-header py-4 px-3 border-0 d-flex justify-content-between align-items-center">
                                            <h6 class="text-capitalize fw-500 mb-3 mb-sm-0 flex-fill">
                                                @lang('CRMProjects.common.documents')
                                                ({{ count($documents) }})</h6>
                                        </div>
                                    </div>
                                    <div class="card-body px-0 pt-0">
                                        <div
                                            class="userDatatable projectDatatable project-table bg-white w-100 border-0">
                                            <div class="table-responsive">
                                                <table class="table mb-0 radius-0">
                                                    <thead>
                                                        <tr class="userDatatable-header">

                                                            <th>
                                                                @lang('CRMProjects.common.subject')
                                                            </th>
                                                            <th>
                                                                @lang('CRMProjects.common.user')
                                                            </th>
                                                            <th>
                                                                @lang('CRMProjects.common.type')
                                                            </th>
                                                            <th>
                                                                @lang('CRMProjects.common.status')
                                                            </th>
                                                        </tr>
                                                    </thead>
                                                    <tbody class="sort-table ui-sortable">
                                                        @forelse ($documents??[] as $item)
                                                            <tr class="ui-sortable-handle" style="opacity: 1;">

                                                                <td>
                                                                    <div
                                                                        class="d-flex userDatatable-content mb-0 align-items-center">
                                                                        <span>{{ $item['subject'] }}</span>
                                                                    </div>
                                                                </td>
                                                                <td>
                                                                    <div
                                                                        class="d-flex userDatatable-content mb-0 align-items-center">
                                                                        <span>{{ $item['user_name'] }}</span>
                                                                    </div>
                                                                </td>
                                                                <td>
                                                                    <div
                                                                        class="d-flex userDatatable-content mb-0 align-items-center">
                                                                        <span>{{ $item['type'] }}</span>
                                                                    </div>

                                                                </td>
                                                                <td>
                                                                    <div
                                                                        class="d-flex userDatatable-content mb-0 align-items-center">
                                                                        <span>{{ $item['status'] }}</span>
                                                                    </div>

                                                                </td>
                                                            </tr>
                                                        @empty
                                                            <tr>
                                                                <td colspan="6">
                                                                    @include('livewire.sales.common.no-data-tr')
                                                                </td>
                                                            </tr>
                                                        @endforelse
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                    </div>
                    @endif
                    @if ($this->settingsState['procurement'] == 1)
                        <div class="card">
                            <div class="card-header">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h5 class="mb-0">@lang('Procurement')</h5>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="card">
                                    <div class="">
                                        <div
                                            class="card-header py-4 px-3 border-0 d-flex justify-content-between align-items-center">
                                            <h6 class="text-capitalize fw-500 mb-3 mb-sm-0 flex-fill">
                                                @lang('RFxs') ({{ count($rfxs) }})
                                            </h6>
                                        </div>
                                    </div>

                                    <div class="card-body px-0 pt-0">
                                        @if (count($rfxs) > 0)
                                            <div
                                                class="userDatatable projectDatatable project-table bg-white w-100 border-0">
                                                <div class="table-responsive">
                                                    <table class="table mb-0 radius-0">
                                                        <thead>
                                                            <tr class="userDatatable-header">
                                                                <th>
                                                                    @lang('Location')
                                                                </th>
                                                                <th>
                                                                    @lang('Title')
                                                                </th>
                                                                <th>
                                                                    @lang('Start Date')
                                                                </th>
                                                                <th>
                                                                    @lang('End Date')
                                                                </th>
                                                                <th>
                                                                    @lang('Status')
                                                                </th>
                                                            </tr>
                                                        </thead>
                                                        <tbody class="sort-table ui-sortable">
                                                            @foreach ($rfxs as $rfx)
                                                                <tr class="ui-sortable-handle" style="opacity: 1;">
                                                                    <td>
                                                                        <div
                                                                            class="d-flex userDatatable-content mb-0 align-items-center">
                                                                            <span>{{ $rfx['location'] }}</span>
                                                                        </div>
                                                                    </td>
                                                                    <td>
                                                                        <div
                                                                            class="d-flex userDatatable-content mb-0 align-items-center">
                                                                            <span>{{ $rfx['title'] }}</span>
                                                                        </div>
                                                                    </td>
                                                                    <td>
                                                                        <div
                                                                            class="d-flex userDatatable-content mb-0 align-items-center">
                                                                            <span>{{ Helper::formatDateForLocale($rfx['start_date']) }}</span>
                                                                        </div>
                                                                    </td>
                                                                    <td>
                                                                        <div
                                                                            class="d-flex userDatatable-content mb-0 align-items-center">
                                                                            <span>{{ Helper::formatDateForLocale($rfx['end_date']) }}</span>
                                                                        </div>
                                                                    </td>
                                                                    <td>
                                                                        <div
                                                                            class="d-flex userDatatable-content mb-0 align-items-center">
                                                                            <span>{{ $rfx['status'] }}</span>
                                                                        </div>
                                                                    </td>
                                                                </tr>
                                                            @endforeach
                                                        </tbody>
                                                    </table>

                                                </div>
                                            </div>
                                        @else
                                            <div class="row">
                                                <div class="PropertyListEmpty">
                                                    <img src="{{ asset('empty-icon/no-projects.svg') }}"
                                                        class="fourth_img" alt="">
                                                    <h4 class="first_title">@lang('No Rfx Yet')</h4>
                                                    <h6 class="second_title">@lang('The Rfx list will appear here')</h6>
                                                </div>
                                            </div>
                                        @endif

                                    </div>
                                </div>
                                <div class="card">
                                    <div class="">
                                        <div
                                            class="card-header py-4 px-3 border-0 d-flex justify-content-between align-items-center">
                                            <h6 class="text-capitalize fw-500 mb-3 mb-sm-0 flex-fill">
                                                @lang('RFx Application') ({{ count($rfxApplications) }})
                                            </h6>
                                        </div>
                                    </div>

                                    <div class="card-body px-0 pt-0">
                                        @if (count($rfxApplications) > 0)
                                            <div
                                                class="userDatatable projectDatatable project-table bg-white w-100 border-0">
                                                <div class="table-responsive">
                                                    <table class="table mb-0 radius-0">
                                                        <thead>
                                                            <tr class="userDatatable-header">
                                                                <th>
                                                                    @lang('Name')
                                                                </th>
                                                                <th>
                                                                    @lang('Stage')
                                                                </th>
                                                                <th>
                                                                    @lang('Proposal')
                                                                </th>
                                                                <th>
                                                                    @lang('Rating')
                                                                </th>
                                                                <th>
                                                                    @lang('Created At')
                                                                </th>
                                                            </tr>
                                                        </thead>
                                                        <tbody class="sort-table ui-sortable">
                                                            @foreach ($rfxApplications as $rfxApplication)
                                                                <tr class="ui-sortable-handle" style="opacity: 1;">
                                                                    <td>
                                                                        <div
                                                                            class="d-flex userDatatable-content mb-0 align-items-center">
                                                                            <span>{{ $rfxApplication['name'] }}</span>
                                                                        </div>
                                                                    </td>
                                                                    <td>
                                                                        <div
                                                                            class="d-flex userDatatable-content mb-0 align-items-center">
                                                                            <span>{{ $rfxApplication['stage'] }}</span>
                                                                        </div>
                                                                    </td>
                                                                    <td>
                                                                        <div
                                                                            class="d-flex userDatatable-content mb-0 align-items-center">
                                                                            <a href="{{ $rfxApplication['proposal'] }}"
                                                                                target="_blank" download>Download</a>
                                                                        </div>
                                                                    </td>
                                                                    <td>
                                                                        <div
                                                                            class="d-flex userDatatable-content mb-0 align-items-center">
                                                                            <span>{{ $rfxApplication['rating'] }}</span>
                                                                        </div>
                                                                    </td>
                                                                    <td>
                                                                        <div
                                                                            class="d-flex userDatatable-content mb-0 align-items-center">
                                                                            <span>{{ Helper::formatDateForLocale($rfxApplication['created_at']) }}</span>
                                                                        </div>
                                                                    </td>
                                                                </tr>
                                                            @endforeach
                                                        </tbody>
                                                    </table>

                                                </div>
                                            </div>
                                        @else
                                            <div class="row">
                                                <div class="PropertyListEmpty">
                                                    <img src="{{ asset('empty-icon/no-projects.svg') }}"
                                                        class="fourth_img" alt="">
                                                    <h4 class="first_title">@lang('No Rfx Application Yet')</h4>
                                                    <h6 class="second_title">@lang('The Rfx Application list will appear here')</h6>
                                                </div>
                                            </div>
                                        @endif

                                    </div>
                                </div>
                                <div class="card">
                                    <div class="">
                                        <div
                                            class="card-header py-4 px-3 border-0 d-flex justify-content-between align-items-center">
                                            <h6 class="text-capitalize fw-500 mb-3 mb-sm-0 flex-fill">
                                                @lang('RFx Applicant') ({{ count($rfxApplicants) }})
                                            </h6>
                                        </div>
                                    </div>

                                    <div class="card-body px-0 pt-0">
                                        @if (count($rfxApplicants) > 0)
                                            <div
                                                class="userDatatable projectDatatable project-table bg-white w-100 border-0">
                                                <div class="table-responsive">
                                                    <table class="table mb-0 radius-0">
                                                        <thead>
                                                            <tr class="userDatatable-header">
                                                                <th>
                                                                    @lang('Name')
                                                                </th>
                                                                <th>
                                                                    @lang('Gender')
                                                                </th>
                                                                <th>
                                                                    @lang('Country')
                                                                </th>
                                                                <th>
                                                                    @lang('State')
                                                                </th>
                                                                <th>
                                                                    @lang('City')
                                                                </th>
                                                                <th>
                                                                    @lang('Proposal')
                                                                </th>
                                                            </tr>
                                                        </thead>
                                                        <tbody class="sort-table ui-sortable">
                                                            @foreach ($rfxApplicants as $rfxApplicant)
                                                                <tr class="ui-sortable-handle" style="opacity: 1;">
                                                                    <td>
                                                                        <div
                                                                            class="d-flex userDatatable-content mb-0 align-items-center">
                                                                            <span>{{ $rfxApplicant['name'] }}</span>
                                                                        </div>
                                                                    </td>
                                                                    <td>
                                                                        <div
                                                                            class="d-flex userDatatable-content mb-0 align-items-center">
                                                                            <span>{{ $rfxApplicant['gender'] }}</span>
                                                                        </div>
                                                                    </td>
                                                                    <td>
                                                                        <div
                                                                            class="d-flex userDatatable-content mb-0 align-items-center">
                                                                            <span>{{ $rfxApplicant['country'] }}</span>
                                                                        </div>
                                                                    </td>
                                                                    <td>
                                                                        <div
                                                                            class="d-flex userDatatable-content mb-0 align-items-center">
                                                                            <span>{{ $rfxApplicant['state'] }}</span>
                                                                        </div>
                                                                    </td>
                                                                    <td>
                                                                        <div
                                                                            class="d-flex userDatatable-content mb-0 align-items-center">
                                                                            <span>{{ $rfxApplicant['city'] }}</span>
                                                                        </div>
                                                                    </td>
                                                                    <td>
                                                                        <div
                                                                            class="d-flex userDatatable-content mb-0 align-items-center">
                                                                            <a href="{{ $rfxApplicant['proposal'] }}"
                                                                                target="_blank" download>Download</a>
                                                                        </div>
                                                                    </td>

                                                                </tr>
                                                            @endforeach
                                                        </tbody>
                                                    </table>

                                                </div>
                                            </div>
                                        @else
                                            <div class="row">
                                                <div class="PropertyListEmpty">
                                                    <img src="{{ asset('empty-icon/no-projects.svg') }}"
                                                        class="fourth_img" alt="">
                                                    <h4 class="first_title">@lang('No Rfx Applicant Yet')</h4>
                                                    <h6 class="second_title">@lang('The Rfx Applicant list will appear here')</h6>
                                                </div>
                                            </div>
                                        @endif

                                    </div>
                                </div>
                                <div class="card">
                                    <div class="">
                                        <div
                                            class="card-header py-4 px-3 border-0 d-flex justify-content-between align-items-center">
                                            <h6 class="text-capitalize fw-500 mb-3 mb-sm-0 flex-fill">
                                                @lang('Vendor On Boarding') ({{ count($vendorOnBoards) }})
                                            </h6>
                                        </div>
                                    </div>

                                    <div class="card-body px-0 pt-0">
                                        @if (count($vendorOnBoards) > 0)
                                            <div
                                                class="userDatatable projectDatatable project-table bg-white w-100 border-0">
                                                <div class="table-responsive">
                                                    <table class="table mb-0 radius-0">
                                                        <thead>
                                                            <tr class="userDatatable-header">
                                                                <th>
                                                                    @lang('Name')
                                                                </th>
                                                                <th>
                                                                    @lang('Email')
                                                                </th>
                                                                <th>
                                                                    @lang('Mobile No')
                                                                </th>
                                                                <th>
                                                                    @lang('Location')
                                                                </th>
                                                                <th>
                                                                    @lang('Applied at')
                                                                </th>
                                                                <th>
                                                                    @lang('Joining At')
                                                                </th>
                                                                <th>
                                                                    @lang('Status')
                                                                </th>
                                                            </tr>
                                                        </thead>
                                                        <tbody class="sort-table ui-sortable">
                                                            @foreach ($vendorOnBoards as $vendor)
                                                                <tr class="ui-sortable-handle" style="opacity: 1;">
                                                                    <td>
                                                                        <div
                                                                            class="d-flex userDatatable-content mb-0 align-items-center">
                                                                            <span>{{ $vendor['name'] }}</span>
                                                                        </div>
                                                                    </td>
                                                                    <td>
                                                                        <div
                                                                            class="d-flex userDatatable-content mb-0 align-items-center">
                                                                            <span>{{ $vendor['email'] }}</span>
                                                                        </div>
                                                                    </td>
                                                                    <td>
                                                                        <div
                                                                            class="d-flex userDatatable-content mb-0 align-items-center">
                                                                            <span>{{ $vendor['phone'] }}</span>
                                                                        </div>
                                                                    </td>
                                                                    <td>
                                                                        <div
                                                                            class="d-flex userDatatable-content mb-0 align-items-center">
                                                                            <span>{{ $vendor['location'] }}</span>
                                                                        </div>
                                                                    </td>
                                                                    <td>
                                                                        <div
                                                                            class="d-flex userDatatable-content mb-0 align-items-center">
                                                                            <span>{{ Helper::formatDateForLocale($vendor['created_at']) }}</span>
                                                                        </div>
                                                                    </td>
                                                                    <td>
                                                                        <div
                                                                            class="d-flex userDatatable-content mb-0 align-items-center">
                                                                            <span>{{ Helper::formatDateForLocale($vendor['joining_date']) }}</span>
                                                                        </div>
                                                                    </td>
                                                                    <td>
                                                                        <div
                                                                            class="d-flex userDatatable-content mb-0 align-items-center">
                                                                            <span>{{ $vendor['status'] }}</span>
                                                                        </div>
                                                                    </td>
                                                                </tr>
                                                            @endforeach
                                                        </tbody>
                                                    </table>

                                                </div>
                                            </div>
                                        @else
                                            <div class="row">
                                                <div class="PropertyListEmpty">
                                                    <img src="{{ asset('empty-icon/no-projects.svg') }}"
                                                        class="fourth_img" alt="">
                                                    <h4 class="first_title">@lang('No Vendor On Boarding Yet')</h4>
                                                    <h6 class="second_title">@lang('The Vendor On Boarding list will appear here')</h6>
                                                </div>
                                            </div>
                                        @endif

                                    </div>
                                </div>
                                <div class="card">
                                    <div class="card-header">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <h5 class="mb-0">@lang('InterViews')</h5>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="card-body">
                                        <div>
                                            <div class="table-responsive">
                                                <div class="card-body">
                                                    <div id='calendar' class='calendar'></div>
                                                </div>

                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </div>
                        </div>
                    @endif
                </div>
            </div>

        </div>
    </div>
</div>






<script src="{{ asset('vendor_assets/js/Chart.min.js') }}"></script>
<script src="{{ asset('js/charts_dashboard.js') }}"></script>

@if ($this->settingsState['procurement'] == 1)
    @push('scripts')
        <script type="text/javascript">
            (function() {
                var etitle;
                var etype;
                var etypeclass;
                var calendar = new FullCalendar.Calendar(document.getElementById('calendar'), {
                    headerToolbar: {
                        left: 'prev,next today',
                        center: 'title',
                        right: 'dayGridMonth,timeGridWeek,timeGridDay'
                    },
                    buttonText: {
                        timeGridDay: "{{ __('Day') }}",
                        timeGridWeek: "{{ __('Week') }}",
                        dayGridMonth: "{{ __('Month') }}"
                    },
                    themeSystem: 'bootstrap',
                    slotDuration: '00:10:00',
                    navLinks: true,
                    droppable: true,
                    selectable: true,
                    selectMirror: true,
                    editable: true,
                    dayMaxEvents: true,
                    handleWindowResize: true,
                    events: @json($arrSchedule),
                });
                calendar.render();
            })();
        </script>
    @endpush
@endif
</div>
@include('livewire.c-r-m-projects.partials.details.balde_js_script')

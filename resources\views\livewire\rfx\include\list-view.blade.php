<div class="card-body px-0 pt-0">
   <div class="userDatatable projectDatatable project-table bg-white w-100 border-0">
      <div class="table-responsive">
         <table class="table mb-0 radius-0 th-osool">
            <thead>
               <tr class="userDatatable-header">
                  <th wire:click="sortBy('id')" class="cursor-pointer">
                       <i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i>
                       @lang('rfx.table.no')
                   </th>
                   <th wire:click="sortBy('location')" class="cursor-pointer">
                       <i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i>
                       @lang('rfx.table.location')
                   </th>

                   <th wire:click="sortBy('title')" class="cursor-pointer">
                       <i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i>
                       @lang('rfx.table.title')
                   </th>

                   <th wire:click="sortBy('start_date')" class="cursor-pointer">
                       <i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i>
                       @lang('rfx.table.start_date')
                   </th>

                   <th wire:click="sortBy('end_date')" class="cursor-pointer">
                       <i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i>
                       @lang('rfx.table.end_date')
                   </th>

                   <th wire:click="sortBy('status')" class="cursor-pointer">
                       <i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i>
                       @lang('rfx.table.status')
                   </th>

                   <th wire:click="sortBy('created_at')" class="cursor-pointer">
                       <i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i>
                       @lang('rfx.table.credited_at')
                   </th>

                   <th>@lang('rfx.table.action')</th>
               </tr>
            </thead>
            <tbody class="sort-table ui-sortable">
               @forelse($listdata as $key=>$item)
               <tr class="ui-sortable-handle" style="opacity: 1;">
                  <td>{{$key+1}}</td>
                  <td>
                     <div class="d-flex userDatatable-content mb-0 align-items-center">
                        <span>{{$item['location']}}</span>
                     </div>
                  </td>
                  <td>
                     <div class="d-flex userDatatable-content mb-0 align-items-center">
                        <span>{{$item['title']}}</span>
                     </div>
                  </td>
                  <td>
                     <div class="d-flex userDatatable-content mb-0 align-items-center">
                        <span>{{date('d/m/Y',strtotime($item['start_date']))}}</span>
                     </div>
                  </td>
                  <td>
                     <div class="d-flex userDatatable-content mb-0 align-items-center">
                        <span>{{date('d/m/Y',strtotime($item['end_date']))}}</span>
                     </div>
                  </td>
                  <td>
                    <div class="{{ $item['status'] === 'active' ? 'bg-win text-white' : 'bg-secondary text-white' }} text-white d-inline-block py-1 px-3 fw-600 rounded text-center"> {{ucwords(str_replace('_',' ',$item['status']))}} </div>
                  </td>
                  <td>
                     <div class="d-flex userDatatable-content mb-0 align-items-center">
                        <span>{{date('d/m/Y',strtotime($item['created_at']))}}</span>
                     </div>
                  </td>
                  
                  <td>
                     <div class="d-inline-block">
                        <ul class="mb-0 d-flex flex-wrap gap-10 align-items-center">
                           <li>
                           <a href="javascript:void(0);" wire:click="goToView('{{ Crypt::encrypt($item['id']) }}')">
                           <i class="iconsax icon text-osool fs-18" icon-name="eye"></i>
                           </a>
                           </li>
                           <li>
                           <a href="javascript:void(0);" wire:click="goToEdit('{{ Crypt::encrypt($item['id']) }}')">
                           <i class="iconsax icon text-new-primary fs-18" icon-name="edit-1"></i>
                           </a>
                           </li>
                           <li>
                           <a href="javascript:void(0);" wire:click="openDeleteModal({{ $item['id'] }}, '{{ $item['title'] }}')">
                           <i class="iconsax icon text-delete fs-18" icon-name="trash"></i>
                           </a>
                           </li>
                           <!-- <li>
                           <a href="javascript:void(0);" title="Copy Link">
                           <i class="iconsax icon text-osool fs-18 mr-0" icon-name="link-1"></i>
                           </a>
                           </li> -->
                        </ul>
                     </div>
                  </td>
               </tr>

               @empty
                  <tr>
                      <td colspan="8" class="text-center py-4">
                          <div class="d-flex flex-column align-items-center">
                              <i class="iconsax icon fs-48 text-muted mb-3" icon-name="user-search"></i>
                              <h6 class="text-muted">{{ __('rfx.no_data_found') }}</h6>
                              @if($search)
                                  <p class="text-muted">{{ __('rfx.try_adjusting_search') }}</p>
                              @endif
                          </div>
                      </td>
                  </tr>
               @endforelse
            </tbody>
         </table>
      </div>
   </div>
</div>
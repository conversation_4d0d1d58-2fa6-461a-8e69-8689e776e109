<div class="tab-pane fade show active" id="customer-project" role="tabpanel" aria-labelledby="contact-tab">
    <div class="card">
        <div class="">
            <div class="card-header py-4 px-3 border-0 d-flex justify-content-between align-items-center">
                <h6 class="text-capitalize text-osool fw-500 mb-3 mb-sm-0">{{ __('accounting.customers.tabs.projects') }}</h6>
                <div class="d-flex gap-10 table-search">
                    <div class="position-relative">
                        <input type="text" class="form-control" placeholder="{{ __('accounting.customers.forms.search_placeholder') }}" 
                               wire:model.live.debounce.300ms="search">
                        <i class="iconsax field-icon fs-18 mr-0" icon-name="search-normal-2"></i>
                    </div>
                    <!-- <button class="btn btn-export text-dark"><i class="iconsax icon fs-22 mr-0" icon-name="upload-1"></i> Export</button> -->
                </div>
            </div>
        </div>
        <div class="card-body px-0 pt-0">
            @if($loading)
                <div class="d-flex justify-content-center align-items-center py-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="sr-only">{{ __('accounting.customers.messages.loading') }}</span>
                    </div>
                </div>
            @elseif($error)
                <div class="alert alert-danger mx-3" role="alert">
                    {{ $error }}
                </div>
            @else
                <div class="userDatatable projectDatatable project-table bg-white w-100 border-0">
                    <div class="table-responsive">
                        <table class="table mb-0 radius-0 th-osool">
                            <thead>
                                <tr class="userDatatable-header">
                                    <th wire:click="sortBy('name')" style="cursor: pointer;">
                                        <i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i> 
                                        {{ __('accounting.customers.table.headers.name') }}
                                        @if($sortField === 'name')
                                            @if($sortDirection === 'asc')
                                                <i class="iconsax icon fs-12" icon-name="arrow-up-3"></i>
                                            @else
                                                <i class="iconsax icon fs-12" icon-name="arrow-down-2"></i>
                                            @endif
                                        @endif
                                    </th>
                                    <th wire:click="sortBy('stage')" style="cursor: pointer;">
                                        <i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i> 
                                        {{ __('accounting.customers.table.headers.stage') }}
                                        @if($sortField === 'stage')
                                            @if($sortDirection === 'asc')
                                                <i class="iconsax icon fs-12" icon-name="arrow-up-3"></i>
                                            @else
                                                <i class="iconsax icon fs-12" icon-name="arrow-down-2"></i>
                                            @endif
                                        @endif
                                    </th>
                                    <th wire:click="sortBy('start_date')" style="cursor: pointer;">
                                        <i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i> 
                                        {{ __('accounting.customers.table.headers.start_date') }}
                                        @if($sortField === 'start_date')
                                            @if($sortDirection === 'asc')
                                                <i class="iconsax icon fs-12" icon-name="arrow-up-3"></i>
                                            @else
                                                <i class="iconsax icon fs-12" icon-name="arrow-down-2"></i>
                                            @endif
                                        @endif
                                    </th>
                                    <th wire:click="sortBy('end_date')" style="cursor: pointer;">
                                        <i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i> 
                                        {{ __('accounting.customers.table.headers.end_date') }}
                                        @if($sortField === 'end_date')
                                            @if($sortDirection === 'asc')
                                                <i class="iconsax icon fs-12" icon-name="arrow-up-3"></i>
                                            @else
                                                <i class="iconsax icon fs-12" icon-name="arrow-down-2"></i>
                                            @endif
                                        @endif
                                    </th>
                                    <th><i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i> {{ __('accounting.customers.table.headers.description') }}</th>
                                    <th><i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i> {{ __('accounting.customers.table.headers.action') }}</th>
                                </tr>
                            </thead>
                            <tbody class="sort-table ui-sortable">
                                @forelse($projects as $project)
                                    <tr class="ui-sortable-handle" style="opacity: 1;">
                                        <td>
                                            <div class="userDatatable-inline-title">
                                                <span>{{ $project['name'] ?? '' }}</span>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="userDatatable-inline-title">
                                                <span>{{ $project['stage'] ?? '' }}</span>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex userDatatable-content mb-0 align-items-center">
                                                <span class="no-wrap">{{ $project['start_date'] ? \Carbon\Carbon::parse($project['start_date'])->format('d-m-Y') : '' }}</span>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex userDatatable-content mb-0 align-items-center">
                                                <span class="no-wrap">{{ $project['end_date'] ? \Carbon\Carbon::parse($project['end_date'])->format('d-m-Y') : '' }}</span>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="userDatatable-inline-title">
                                                <span class="max-td">{{ $project['description'] ?? '' }}</span>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-inline-block">
                                                <ul class="mb-0 d-flex gap-10">
                                                    <li>
                                                        <a href="javascript:void(0);" wire:click="viewProject({{ $project['id'] }})">
                                                            <i class="iconsax icon text-osool fs-18" icon-name="eye"></i>
                                                        </a>
                                                    </li>
                                                </ul>
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="6" class="text-center py-4">
                                            {{ __('accounting.customers.messages.no_projects_found') }}
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            @endif
        </div>

        @if(!$loading && !$error && $total > 0)
            <div class="card-body pt-0">
                <div class="d-flex justify-content-md-between flex-column flex-md-row justify-content-center align-items-center mt-4 gap-10">
                    <div class="">
                        <ul class="atbd-pagination d-flex justify-content-between">
                            <li>
                                <div class="paging-option">
                                    <div class="dataTables_length d-flex">
                                        <label class="d-flex align-items-center mb-0">
                                            <select aria-controls="workorder_table" wire:model.live.debounce.250ms="perPage" class="custom-select custom-select-sm form-control form-control-sm mx-2" style="min-height: 35px;">
                                                <option value="5">5</option>
                                                <option value="10">10</option>
                                                <option value="25">25</option>
                                                <option value="50">50</option>
                                                <option value="100">100</option>
                                            </select>
                                            <span class="no-wrap"> {{ __('accounting.customers.pagination.entries_per_page') }} </span>
                                        </label>
                                    </div>
                                </div>
                            </li>
                        </ul>
                    </div>
                    <div class="">
                        <div class="user-pagination">
                            <div class="user-pagination new-pagination">
                                <div class="d-flex justify-content-sm-end justify-content-end">
                                    <nav>
                                        <span class="relative z-0 inline-flex rounded-md d-flex new-pagination-section flex-wrap">
                                            <span class="">
                                                <span aria-disabled="{{ $currentPage <= 1 ? 'true' : 'false' }}" aria-label="&laquo; Previous">
                                                    <button class="border-0 {{ $currentPage <= 1 ? 'disabled' : '' }}" 
                                                            {{ $currentPage <= 1 ? 'disabled' : '' }}
                                                            wire:click="previousPage">
                                                        <i class="iconsax icon-bold fs-16 mr-0 rotate-ar-y d-inline-block" icon-name="chevron-left"></i>
                                                    </button>
                                                </span>
                                            </span>

                                            @for($page = 1; $page <= $lastPage; $page++)
                                                <span wire:key="paginator-page-{{ $page }}">
                                                    <button type="button" 
                                                            class="border-0 {{ $currentPage == $page ? 'current-page' : '' }}" 
                                                            {{ $currentPage == $page ? 'disabled' : '' }}
                                                            wire:click="gotoPage({{ $page }})">
                                                        {{ $page }}
                                                    </button>
                                                </span>
                                            @endfor

                                            <span>
                                                <button type="button" 
                                                        class="border-0 {{ $currentPage >= $lastPage ? 'disabled' : '' }}"
                                                        {{ $currentPage >= $lastPage ? 'disabled' : '' }}
                                                        wire:click="nextPage">
                                                    <i class="iconsax icon-bold fs-16 mr-0 rotate-ar-y d-inline-block" icon-name="chevron-right"></i>
                                                </button>
                                            </span>
                                        </span>
                                    </nav>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div>
                        <p class="text-sm text-gray-700 leading-5 mb-0">
                            <span>{{ __('accounting.customers.pagination.showing') }}</span>
                            <span class="font-medium">{{ ($currentPage - 1) * $perPage + 1 }}</span>
                            <span>{{ __('accounting.customers.pagination.to') }}</span>
                            <span class="font-medium">{{ min($currentPage * $perPage, $total) }}</span>
                            <span>{{ __('accounting.customers.pagination.of') }}</span>
                            <span class="font-medium">{{ $total }}</span>
                            <span>{{ __('accounting.customers.pagination.results') }}</span>
                        </p>
                    </div>
                </div>
            </div>
        @endif
    </div>
</div>

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class WorkTimeFrame extends Model
{
    use HasFactory;
    protected $table = 'work_time_frame';

    const UPDATED_AT = null;

    public function workOrder()
    {
        return $this->belongsTo(WorkOrder::class, 'user_id', 'project_user_id');
    }


    protected $appends = ['total_open_days'];

    public function getTotalOpenDaysAttribute()
    {
        return $this->sunday + $this->monday + $this->tuesday +
               $this->wednesday + $this->thursday + $this->friday +
               $this->saturday;
    }
}

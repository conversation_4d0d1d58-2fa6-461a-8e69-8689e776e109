<div>
    <div class="contents crm">
        <div class="container-fluid">
            <div class="col-lg-12">
                <div class="row justify-content-sm-between align-items-center justify-content-center my-3 flex-sm-row flex-column">
                    <div class="page-title-wrap p-0">
                        <div class="page-title d-flex justify-content-between">
                            <div class="page-title__left justify-content-sm-between align-items-center justify-content-center">
                                <div class="user-member__title mr-sm-25 ml-0">
                                    <h4 class="text-capitalize fw-500 breadcrumb-title fs-16">
                                        @lang('purchase.create.title')
                                    </h4>
                                </div>
                            </div>
                        </div>
                        <div>
                            <ul class="atbd-breadcrumb nav">
                                <li class="atbd-breadcrumb__item">
                                    <a>@lang('purchase.create.breadcrumb.dashboard')</a>
                                    <span class="breadcrumb__seperator">
                                        <span class="la la-angle-right"></span>
                                    </span>
                                </li>
                                <li class="atbd-breadcrumb__item">
                                    <a>@lang('purchase.create.breadcrumb.purchase_order')</a>
                                    <span class="breadcrumb__seperator">
                                        <span class="la la-angle-right"></span>
                                    </span>
                                </li>
                                <li class="atbd-breadcrumb__item">
                                    <a>@lang('purchase.create.breadcrumb.create')</a>
                                </li>
                            </ul>
                        </div>
                    </div>

                    <div class="d-flex gap-10 breadcrumb_right_icons">
                        <button class="btn btn-default btn-primary no-wrap"
                                wire:click="submit"
                                wire:loading.attr="disabled"
                                >

                            <i class="las la-save fs-16"></i> @lang('purchase.create.form.save')
                        </button>
                    </div>
                </div>
            </div>

            @if (session()->has('message'))
                <div class="alert alert-danger">
                    {{ session('message') }}
                </div>
            @endif
            @if ($errors->has('form_error'))
                <div class="alert alert-danger">
                    {{ $errors->first('form_error') }}
                </div>
            @endif
            <div class="card">
                <div class="card-header d-flex justify-content-start gap-5 text-osool border-0">
                    <h6>@lang('purchase.create.title') - {{ $purchaseNumber }}</h6>
                </div>

                <div class="px-4">
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="form-group">
                                <label class="form-label text-dark fw-600">@lang('purchase.create.form.invoice_number')</label>
                                <input class="form-control" value="{{ $purchaseNumber }}" disabled />
                            </div>
                        </div>

                        <div class="col-lg-4">
                            <div class="form-group">
                                <label class="form-label text-dark fw-600">@lang('purchase.create.form.billing_type')</label>
                                <select class="form-control" wire:model="formData.billing_type">
                                    @foreach($dropdownData['billing_types'] as $key => $value)
                                        <option value="{{ $key }}">{{ $value }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>

                        <div class="col-lg-4">
                            <div class="form-group">
                                <label class="form-label text-dark fw-600">@lang('purchase.create.form.vendor')</label>
                                <select class="form-control" wire:model="formData.vendor_id" required>
                                    <option value="">@lang('purchase.create.form.choose_option')</option>
                                    @foreach($dropdownData['vendors'] as $vendor)
                                        <option value="{{ $vendor['id'] }}">{{ $vendor['name'] }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>

                        <div class="col-lg-4">
                            <div class="form-group">
                                <label class="form-label text-dark fw-600">@lang('purchase.create.form.warehouse')</label>
                                <select class="form-control" wire:model="formData.warehouse_id" required>
                                    <option value="">@lang('purchase.create.form.choose_option')</option>
                                    @foreach($dropdownData['warehouses'] as $warehouse)
                                        <option value="{{ $warehouse['id'] }}">{{ $warehouse['name'] }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>

                        <div class="col-lg-4">
                            <div class="form-group">
                                <label class="form-label text-dark fw-600">@lang('purchase.create.form.category')</label>
                                <select class="form-control" wire:model="formData.category_id" required>
                                    <option value="">@lang('purchase.create.form.choose_option')</option>
                                    @foreach($dropdownData['categories'] as $category)
                                        <option value="{{ $category['id'] }}">{{ $category['name'] }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>

                        <div class="col-lg-2">
                            <div class="form-group">
                                <label class="form-label text-dark fw-600">@lang('purchase.create.form.purchase_date')</label>
                                <div class="position-relative">
                                    <input type="date" class="form-control" wire:model="formData.purchase_date" required>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-2">
                            <div class="form-group">
                                <label class="form-label text-dark fw-600">@lang('purchase.create.form.due_date')</label>
                                <div class="position-relative">
                                    <input type="date" class="form-control" wire:model="formData.due_date" required>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-2">
                            <div class="form-group">
                                <label class="form-label text-dark fw-600">@lang('purchase.create.form.status')</label>
                                <select class="form-control" wire:model="formData.status">
                                    @foreach(array_values($dropdownData['statuses']) as $key => $value)
                                        <option value="{{ $key }}" @if($key == $formData['status']) selected @endif>
                                            {{ $value }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card mt-3 px-4 py-3">
                <div class="d-flex justify-content-between align-items-center">
                    <h6>@lang('purchase.create.form.item_details')</h6>
                    <button class="btn btn-default btn-primary no-wrap" wire:click="addItem">
                        <i class="iconsax icon fs-22 text-white mr-2" icon-name="add"></i>
                        @lang('purchase.create.form.add_item')
                    </button>
                </div>

                <div class="bg-white border-0 project-table projectDatatable mt-4 userDatatable w-100">
                    <div class="table-responsive card">
                        <table class="table mb-0 radius-0 th-osool">
                            <thead>
                            <tr class="userDatatable-header">
                                <th>@lang('purchase.create.form.item')</th>
                                <th>@lang('purchase.create.form.quantity')</th>
                                <th>@lang('purchase.create.form.price')</th>
                                <th>@lang('purchase.create.form.discount') (%)</th>
                                <th>@lang('purchase.create.form.tax') (%)</th>
                                <th>@lang('purchase.create.form.description')</th>
                                <th>@lang('purchase.create.form.price')</th>
                                <th></th>
                            </tr>
                            </thead>
                            <tbody class="sort-table ui-sortable">
                            @foreach($formData['items'] as $index => $item)
                                <tr class="ui-sortable-handle">
                                    <td>
                                        <select class="form-control" wire:model="formData.items.{{ $index }}.item" required>
                                            <option value="">@lang('purchase.create.form.choose_option')</option>
                                            @foreach($dropdownData['items'] as $product)
                                                <option value="{{ $product['id'] }}">{{ $product['name'] }}</option>
                                            @endforeach
                                        </select>
                                        <select class="form-control mt-2" wire:model="formData.items.{{ $index }}.product_type">
                                            @foreach($dropdownData['item_types'] as $key => $value)
                                                <option value="{{ $key }}">{{ $value }}</option>
                                            @endforeach
                                        </select>
                                    </td>
                                    <td>
                                        <input type="number" class="form-control" wire:model="formData.items.{{ $index }}.quantity" min="1" required>
                                    </td>
                                    <td>
                                        <input type="number" step="0.01" class="form-control" wire:model="formData.items.{{ $index }}.price" min="0" required>
                                    </td>
                                    <td>
                                        <input type="number" step="0.01" class="form-control" wire:model="formData.items.{{ $index }}.discount" min="0">
                                    </td>
                                    <td>
                                        <input type="number" step="0.01" class="form-control" wire:model="formData.items.{{ $index }}.tax" min="0">
                                    </td>
                                    <td>
                                        <textarea class="form-control" wire:model="formData.items.{{ $index }}.description"></textarea>
                                    </td>
                                    <td>
                                        ${{
            number_format(
                floatval($item['quantity'] ?? 0) *
                floatval($item['price'] ?? 0),
                2
            )
        }}
                                    </td>
                                    <td>
                                        @if($index > 0)
                                            <button class="btn btn-sm btn-danger" wire:click="removeItem({{ $index }})">
                                                @lang('purchase.create.form.remove_item')
                                            </button>
                                        @endif
                                    </td>
                                </tr>
                            @endforeach
                            <tr class="ui-sortable-handle fw-600 text-osool">
                                <td colspan="6"></td>
                                <td>
                                    <div class="my-2">@lang('purchase.create.form.sub_total'): ${{ number_format($subTotal, 2) }}</div>
                                    <div class="mb-4">@lang('purchase.create.form.total_discount'): ${{ number_format($totalDiscount, 2) }}</div>
                                </td>
                            </tr>
                            <tr class="bg-new-primary fw-700 text-white">
                                <td colspan="6"></td>
                                <td>
                                    <div>@lang('purchase.create.form.total'): ${{ number_format($grandTotal, 2) }}</div>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>



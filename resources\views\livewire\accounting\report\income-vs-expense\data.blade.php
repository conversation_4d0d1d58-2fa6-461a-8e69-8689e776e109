<div>
    <div class="card mt-3">
        <div class="card-body">
            <div style="height: 300px;width: 100%;">
                <canvas id="monthlyChart"></canvas>
            </div>
        </div>
    </div>

    <div class="card mt-3">
        <div class="card-body">
            <div class="userDatatable projectDatatable project-table bg-white w-100 border-0">
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>@lang('accounting.category')</th>
                                <th>January</th>
                                <th>February</th>
                                <th>March</th>
                                <th>April</th>
                                <th>May</th>
                                <th>June</th>
                                <th>July</th>
                                <th>August</th>
                                <th>September</th>
                                <th>October</th>
                                <th>November</th>
                                <th>December</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td colspan="13" class="text-dark"><span>@lang('accounting.income') : </span></td>
                            </tr>
                            <tr>
                                <td>@lang('accounting.revenue')</td>
                                @foreach (@$apiData['revenueIncomeTotal']??[] as $item)
                                    <td>
                                        <div class="d-flex align-items-center gap-10">
                                            {!! $currency !!}
                                            <span
                                                class="text-new-primary">{{ Helper::human_readable_number($item) }}</span>
                                        </div>
                                    </td>
                                @endforeach
                            </tr>
                            <tr>
                                <td>@lang('accounting.invoice')</td>
                                @foreach (@$apiData['invoiceIncomeTotal']??[] as $item)
                                    <td>
                                        <div class="d-flex align-items-center gap-10">
                                            {!! $currency !!}
                                            <span
                                                class="text-new-primary">{{ Helper::human_readable_number($item) }}</span>
                                        </div>
                                    </td>
                                @endforeach
                            </tr>
                            <tr>
                                <td colspan="13" class="text-dark"><span>@lang('accounting.expense') : </span></td>
                            </tr>
                            <tr>
                                <td>@lang('accounting.payment')</td>
                                @foreach (@$apiData['paymentExpenseTotal']??[] as $item)
                                    <td>
                                        <div class="d-flex align-items-center gap-10">
                                            {!! $currency !!}
                                            <span
                                                class="text-new-primary">{{ Helper::human_readable_number($item) }}</span>
                                        </div>
                                    </td>
                                @endforeach
                            </tr>
                            <tr>
                                <td>@lang('accounting.bill')</td>
                                @foreach (@$apiData['billExpenseTotal']??[] as $item)
                                    <td>
                                        <div class="d-flex align-items-center gap-10">
                                            {!! $currency !!}
                                            <span
                                                class="text-new-primary">{{ Helper::human_readable_number($item) }}</span>
                                        </div>
                                    </td>
                                @endforeach
                            </tr>
                            <tr>
                                <td>@lang('accounting.purchase')</td>
                                @foreach (@$apiData['purchaseExpenseTotal']??[] as $item)
                                    <td>
                                        <div class="d-flex align-items-center gap-10">
                                            {!! $currency !!}
                                            <span
                                                class="text-new-primary">{{ Helper::human_readable_number($item) }}</span>
                                        </div>
                                    </td>
                                @endforeach
                            </tr>
                            <tr>
                                <td>@lang('accounting.employee_salary')</td>
                                @foreach (@$apiData['EmpSalary']??[] as $item)
                                    <td>
                                        <div class="d-flex align-items-center gap-10">
                                            {!! $currency !!}
                                            <span
                                                class="text-new-primary">{{ Helper::human_readable_number($item) }}</span>
                                        </div>
                                    </td>
                                @endforeach
                            </tr>
                            <tr>
                                <td colspan="13" class="text-dark">
                                    <span>@lang('accounting.profit_calc')</span>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <h6>@lang('accounting.profit')</h6>
                                </td>
                                @foreach (@$apiData['profit']??[] as $item)
                                    <td>
                                        <div class="d-flex align-items-center gap-10">
                                            {!! $currency !!}
                                            <span
                                                class="text-new-primary">{{ Helper::human_readable_number($item) }}</span>
                                        </div>
                                    </td>
                                @endforeach
                            </tr>
                        </tbody>
                    </table>

                </div>
            </div>
        </div>

    </div>
</div>
<script src="{{ asset('vendor_assets/js/Chart.min.js') }}"></script>
<script src="/js/livewire/manage-loader.js"></script>

<script>
    let monthlyChart = null;

    function initChart(data) {
        // Shadow plugin
        Chart.plugins.register({
            beforeDatasetsDraw: function(chart) {
                const ctx = chart.chart.ctx;
                ctx.save();
                ctx.shadowColor = 'rgba(0, 0, 0, 0.2)';
                ctx.shadowBlur = 10;
                ctx.shadowOffsetX = 0;
                ctx.shadowOffsetY = 4;
            },
            afterDatasetsDraw: function(chart) {
                chart.chart.ctx.restore();
            }
        });

        var ctx = document.getElementById('monthlyChart').getContext('2d');
        // Destroy existing chart if it exists
        if (monthlyChart !== null) {
            monthlyChart.destroy();
        }

        monthlyChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: ['January', 'February', 'March', 'April', 'May', 'June',
                    'July', 'August', 'September', 'October', 'November', 'December'
                ],
                datasets: [{
                    label: 'Amount (S.A.R)',
                    data,
                    backgroundColor: 'rgba(30, 144, 255, 0.3)',
                    borderColor: 'rgba(30, 144, 255, 1)',
                    borderWidth: 2,
                    pointBackgroundColor: '#fff',
                    pointBorderColor: 'rgba(30, 144, 255, 1)',
                    pointRadius: 5,
                    fill: true,
                    lineTension: 0.3
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    yAxes: [{
                        ticks: {
                            beginAtZero: true,
                            callback: function(value) {
                                return value.toLocaleString();
                            }
                        },
                        scaleLabel: {
                            display: true,
                            labelString: 'Amount (S.A.R)'
                        },
                        gridLines: {
                            drawBorder: false
                        }
                    }],
                    xAxes: [{
                        gridLines: {
                            drawBorder: false
                        }
                    }]
                },
                legend: {
                    display: false
                },
                tooltips: {
                    mode: 'index',
                    intersect: false
                }
            }
        });
    }

    initChart(@json($apiData['profit']));

    window.addEventListener('updateChart', () => {
        initChart(event.detail.chartData);
        hideLoader();
    });
</script>

<?php

namespace App\Services;

use Log;
use setasign\Fpdi\Fpdi;
use App\Http\Traits\ReportTrait;
use App\Services\ReportDataService;

class PdfGenerationService
{
    use ReportTrait;
    public function generatePdf($request,$project_user_id,$user,$service_provider_ids,$filename,$report_id,$report_number,$start_date,$end_date,$project_image_link)
    {
        //try {

            // Dependency injection and services
            $reportDataService = app(ReportDataService::class);

            $buildingManagers = isset($request->building_managers) && ($request->building_managers != null) && !in_array('All', $request->building_managers) ? $request->building_managers : [0];
            $supervisor = isset($request->Supervisor) && ($request->Supervisor != null) && !in_array('All', $request->Supervisor) ? $request->Supervisor : [0];
            $assigned_to_users = $reportDataService->getAssignedUsers(
                $buildingManagers,
                $supervisor
            );

            $lang = $reportDataService->setLocale($request->language);

            $request->building_managers = $reportDataService->normalizeManagers($buildingManagers);

            Log::info('Report generation started');

            // Fetch required data using repository
            $summaryData = $this->getReportSummarryData($request,$lang,$project_user_id,$user,$service_provider_ids,$assigned_to_users,$report_number,$start_date,$end_date);
            $reportHtml = $this->generateHtml($summaryData, $lang,$project_image_link);
            //dd($reportHtml);
            // Process the report HTML in chunks
            $finalHtml = $this->getChunkpdfhtml($reportHtml);

            Log::info('Before Loading HTML and after combining chunks of reports');

              //Set Generate File and store in uci
              $file_link = $this->getGenerateReportPdf($filename,$finalHtml,$report_id);
              //Send Email
              $this->getSendReportEmail($filename,$report_id,$file_link,$report_number,$summaryData['preventive_work_orders_count'],$summaryData['reactive_work_orders_count'],$start_date,$end_date,$lang,$user);

        // } catch (\Exception $e) {
        //     Log::error("Error in PDF generation: " . $e->getMessage());
        //     throw $e;
        // }
    }


    public function generateHtml(array $summaryData, string $lang,$project_image_link)
    {
                Log::info('Report log started html');
                  // Temporarily increase the memory limit to 5GB
                  ini_set('memory_limit', '10G');
                  set_time_limit(1800);

                  $viewFile = "applications.admin.reports.PdfReport_" . ($lang === 'ar' ? 'ar' : 'en');
                  $reportHtml = view($viewFile , ['lang'=>$summaryData['lang'],
                  'assigned_to_users'=>$summaryData['assigned_to_users'],
                  'project_image_link'=>$project_image_link,
                  'final_work_order_counts_by_status'=>$summaryData['final_work_order_counts_by_status'], 'user'=>$summaryData['user'], 'preventive_workorders_servicetype'=>$summaryData['preventive_workorders_servicetype'], 'reactive_workorders_servicetype'=>$summaryData['reactive_workorders_servicetype'], 'merged_workorders_servicetype'=>$summaryData['merged_workorders_servicetype'], 
                  'workorders_servicetype_total'=>$summaryData['workorders_servicetype_total'], 
                  'WorkOrdersServicetypeTotalReactive'=>$summaryData['WorkOrdersServicetypeTotalReactive'],
                  'WorkOrdersServicetypeTotalPreventive'=>$summaryData['WorkOrdersServicetypeTotalPreventive'],
                  'data_reactive'=>$summaryData['data_reactive'], 'data_preventive'=>$summaryData['data_preventive'],'start_date'=>$summaryData['start_date'], 'end_date'=>$summaryData['end_date'],'generated_at'=> $summaryData['generated_at'], 'request_type'=>$summaryData['request_type'],  'maintenance_type'=>$summaryData['maintenance_type'], 'reactive_work_orders_count'=> $summaryData['reactive_work_orders_count'], 'preventive_work_orders_count'=> $summaryData['preventive_work_orders_count'], 'project_name'=> $summaryData['project_name'], 'report_no'=>  $summaryData['report_no'],'job_rating'=>$summaryData['job_rating'],
                  'reactive_job_rating'=>$summaryData['reactive_job_rating'],
                  'preventive_job_rating'=>$summaryData['preventive_job_rating'],
                  'wo_completed_on_time'=>$summaryData['wo_completed_on_time'],'response_time'=>$summaryData['response_time']])->render();

                  Log::info('Report log ended');
//dd($reportHtml);
        return $reportHtml;
    }
}

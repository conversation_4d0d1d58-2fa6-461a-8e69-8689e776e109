<?php
    namespace App\Http\Traits;
    use Illuminate\Support\Facades\Log;
    use App\Http\Helpers\ReportQueryHelper;
    use App\Http\Traits\TempBulkImportTrait;
    use App\Http\Traits\FunctionsTrait;
    use App\Models\Priorities;
    use App\Enums\ModelAction;

    trait PriorityTrait{
        use TempBulkImportTrait, FunctionsTrait;

        public function getPriorityInformationsByKeyValues($key, $value) {
            try {
                return Priorities::where($key, $value)
                ->where('is_deleted', 'no')
                ->select('id', 'priority_level', 'service_window', 'service_window_type', 'response_time', 'response_time_type')
                ->first();
            } 
            
            catch (\Throwable $th) {
                Log::error("getPriorityInformationsByKeyValues error: ".$th);
            }
        }

        public function getPriorityDataByValues($key, $value, $projectUserId) {
            try {
                return Priorities::where('user_id', $projectUserId)
                ->where($key, $value)
                ->where('is_deleted', 'no')
                ->first();
            } 
            
            catch (\Throwable $th) {
                Log::error("getPriorityDataByValues error: ".$th);
            }
        }

        public function manageEntredPriorityLevel($priorityName, $projectUserId) {
            try {
                $priority = $this->getPriorityDataByValues("priority_level", $priorityName, $projectUserId);

                if(isset($priority)){
                    return ModelAction::Update->value;
                }

                else{
                    return ModelAction::Insert->value;
                }
            } 
            
            catch (\Throwable $th) {
                Log::error("manageEntredPriorityLevel error: ".$th);
            }
        }

        public function savePriority($array) {
            try {
                return Priorities::insertGetId($array);
            } 
            
            catch (\Throwable $th) {
                Log::error("savePriority error: ".$th);
            }
        }

        public function getPriorityInformationsById($priorityId) {
            try {
                return Priorities::where('id', '=', $priorityId)
                ->where('is_deleted', 'no')
                ->first();
            } 
            
            catch (\Throwable $th) {
                Log::error("getPriorityInformationsById Error: ".$th);
            }
        }

        public function updatePriorityByValues($projectUserId, $priorityId, $array) {
            try {
                return Priorities::where('user_id', $projectUserId)
                ->where('id', $priorityId)
                ->update($array);
            } 
            
            catch (\Throwable $th) {
                Log::error("updatePriorityByValues error: ".$th);
            }
        }

        public function deletePriorityByValue($key, $value) {
            try {
                return Priorities::whereIn($key, $value)
                ->delete();
            } 
            
            catch (\Throwable $th) {
                Log::error("deletePriorityByValue error: ".$th);
            }
        }

        public function getPrioritiesListByValues($key, $value) {
            try {
                return Priorities::where($key, $value)
                ->where('is_deleted', 'no')
                ->select('id', 'priority_level')
                ->get();
            } 
            
            catch (\Throwable $th) {
                Log::error("getPrioritiesListByValues error: ".$th);
            }
        }

        public function getPaginatedPrioritiesListVByValues($key, $value, $perPage) {
            try {
                return Priorities::select('id', 'priority_level', 'service_window', 'service_window_type', 'response_time', 'response_time_type')
                ->whereIn($key, $value)
                ->paginate($perPage, ['*'], 'page'); 
            } 
            
            catch (\Throwable $th) {
                Log::error("getPaginatedPrioritiesListVByValues error: ".$th);
            }
        }
    }
?>
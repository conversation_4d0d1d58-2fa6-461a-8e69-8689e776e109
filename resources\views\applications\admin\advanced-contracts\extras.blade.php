@extends('layouts.app')
@section('styles')
@endsection
@section('content')

@include('components.contract-steps', ['currentStep' => "5", 'isVariationOrder' => $isVariationOrder,'savedSteps' => $savedSteps])
        <div class="col-lg-8">
            <livewire:advance-contracts.step-five :uuid="$uuid" :prefix="$route_prefix"/>
        </div>
    </div>
    <!-- End: .global-shadow-->
</div>

</div>
</div>



@endsection

@section('scripts')
<script type="text/javascript">
    $('.datepicker').datepicker();
    $('.select2-select').select2();

    $(document).ready(function () {
    // Row template as a variable (you can also clone from a hidden template if preferred)
    const rowHtml = `
    <tr>
        <td><input type="text" class="form-control" placeholder="Lorem Ipsum" name=""></td>
        <td><input type="text" class="form-control" placeholder="Lorem Ipsum" name=""></td>
        <td><input type="text" class="form-control" placeholder="Lorem Ipsum" name=""></td>
        <td><input type="text" class="form-control" placeholder="Lorem Ipsum" name=""></td>
        <td>
            <ul class="mb-0 d-flex flex-wrap">
                <li>
                    <a href="javascript:void(0);" class="remove">
                        <i class="iconsax icon text-delete fs-18 mr-0" icon-name="trash"></i>
                    </a>
                </li>
            </ul>
        </td>
    </tr>
    `;

    // Add row on button click
    $('#addSLA').click(function () {
        $('#tableBody').append(rowHtml);
    });

    // Remove row on delete button click using event delegation
    $('#tableBody').on('click', '.remove', function () {
        $(this).closest('tr').remove();
    });
});
</script>
@endsection
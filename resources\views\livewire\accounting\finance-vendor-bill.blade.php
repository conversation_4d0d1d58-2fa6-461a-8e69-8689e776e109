<div>
    <div class="card">
        <div class="">
            <div class="card-header py-4 px-3 border-0 d-flex justify-content-between align-items-center">
                <h6 class="text-capitalize text-osool fw-500 mb-3 mb-sm-0">{{ __('vendors.common.bills') }}</h6>

                <div class="d-flex gap-10 table-search">
                    <div class="position-relative">
                        <input type="text" class="form-control" placeholder="{{ __('vendors.common.search') }}" wire:model.debounce.300ms="search">
                        <i class="iconsax field-icon fs-18 mr-0" icon-name="search-normal-2"></i>
                    </div>
                    <!-- <button class="btn btn-export text-dark"><i class="iconsax icon fs-22 mr-0" icon-name="upload-1"></i> Export</button> -->
                </div>
            </div>
        </div>

        @if($loading)
            <div class="card-body text-center py-5">
                <div class="spinner-border text-primary" role="status">
                    <span class="sr-only">{{ __('vendors.common.loading') }}</span>
                </div>
                <p class="mt-2">{{ __('vendors.common.loading_bills') }}</p>
            </div>
        @elseif($error)
            <div class="card-body text-center py-5">
                <i class="iconsax icon fs-48 text-danger mb-3" icon-name="info-circle"></i>
                <h6 class="text-danger">{{ $error }}</h6>
                <button class="btn btn-primary mt-2" wire:click="fetchVendorBills">
                    {{ __('vendors.buttons.retry') }}
                </button>
            </div>
        @elseif($bills->isEmpty())
            <div class="card-body text-center py-5">
                <i class="iconsax icon fs-48 text-muted mb-3" icon-name="document-text"></i>
                <h6 class="text-muted">{{ __('vendors.common.no_bills_found') }}</h6>
                <p class="text-muted">{{ __('vendors.common.no_bills_description') }}</p>
            </div>
        @else
            <div class="card-body px-0 pt-0">
                <div class="userDatatable projectDatatable project-table bg-white w-100 border-0">
                    <div class="table-responsive">
                        <table class="table mb-0 radius-0 th-osool">
                            <thead>
                                <tr class="userDatatable-header">
                                    <th>
                                        {{ __('vendors.common.bill') }}
                                    </th>
                                    <th wire:click="sortBy('issued_at')" style="cursor: pointer;">
                                        <i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i> 
                                        {{ __('vendors.common.bill_date') }}
                                        @if($sortField === 'issued_at')
                                            <i class="fas fa-sort-{{ $sortDirection === 'asc' ? 'up' : 'down' }} ml-1"></i>
                                        @endif
                                    </th>
                                    <th wire:click="sortBy('due_at')" style="cursor: pointer;">
                                        <i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i> 
                                        {{ __('vendors.common.due_date') }}
                                        @if($sortField === 'due_at')
                                            <i class="fas fa-sort-{{ $sortDirection === 'asc' ? 'up' : 'down' }} ml-1"></i>
                                        @endif
                                    </th>
                                    <th wire:click="sortBy('amount')" style="cursor: pointer;">
                                        <i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i> 
                                        {{ __('vendors.common.due_amount') }}
                                        @if($sortField === 'amount')
                                            <i class="fas fa-sort-{{ $sortDirection === 'asc' ? 'up' : 'down' }} ml-1"></i>
                                        @endif
                                    </th>
                                    <th wire:click="sortBy('status')" style="cursor: pointer;">
                                        <i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i> 
                                        {{ __('vendors.common.status') }}
                                        @if($sortField === 'status')
                                            <i class="fas fa-sort-{{ $sortDirection === 'asc' ? 'up' : 'down' }} ml-1"></i>
                                        @endif
                                    </th>
                                    <th>
                                        <i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i> 
                                        {{ __('vendors.common.action') }}
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="sort-table ui-sortable">
                                @foreach($bills as $bill)
                                    <tr class="ui-sortable-handle" style="opacity: 1;">
                                        <td>
                                            <div class="userDatatable-inline-title">
                                                <a href="javascript:void(0);" wire:click="viewBill({{ $bill['id'] ?? 0 }})">
                                                    <span class="fw-400 badge badge-round badge-primary badge-lg badge-outlined">
                                                        {{ $bill['bill_number'] ?? 'N/A' }}
                                                    </span>
                                                </a>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex userDatatable-content mb-0 align-items-center">
                                                <span>{{ isset($bill['bill_date']) ? \Carbon\Carbon::parse($bill['bill_date'])->format('d-m-Y') : 'N/A' }}</span>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex userDatatable-content mb-0 align-items-center">
                                                <span>{{ isset($bill['due_date']) ? \Carbon\Carbon::parse($bill['due_date'])->format('d-m-Y') : 'N/A' }}</span>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center gap-10">
                                                <img src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSx34eO8RbZlsetN1iAQtxuOLnLOlyJbJmU7Q&s" width="15" />
                                                <span class="text-new-primary">{{ number_format($bill['due_amount'] ?? 0, 2) }}</span>
                                            </div>
                                        </td>
                                        <td>
                                            @php
                                                $status = $bill['status'] ?? 'draft';
                                                $statusClass = match($status) {
                                                    'paid' => 'bg-opacity-success text-success',
                                                    'partial' => 'bg-opacity-warning text-warning',
                                                    'overdue' => 'bg-opacity-danger text-danger',
                                                    default => 'bg-opacity-loss text-loss'
                                                };
                                            @endphp
                                            <span class="badge-new {{ $statusClass }} rounded">
                                                {{ __('vendors.status.' . strtolower($status)) }}
                                            </span>
                                        </td>
                                        <td>
                                            <div class="d-inline-block">
                                                <ul class="mb-0 d-flex gap-10">
                                                    <li>
                                                        <a href="javascript:void(0);" wire:click="viewBill({{ $bill['id'] ?? 0 }})" title="{{ __('vendors.buttons.view') }}">
                                                            <i class="iconsax icon text-osool fs-18" icon-name="eye"></i>
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <a href="javascript:void(0);" wire:click="editBill({{ $bill['id'] ?? 0 }})" title="{{ __('vendors.buttons.edit') }}">
                                                            <i class="iconsax icon text-new-primary fs-18" icon-name="edit-1"></i>
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <a href="javascript:void(0);" wire:click="confirmDelete({{ $bill['id'] ?? 0 }})" title="{{ __('vendors.buttons.delete') }}">
                                                            <i class="iconsax icon text-delete fs-18" icon-name="trash"></i>
                                                        </a>
                                                    </li>
                                                </ul>
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            @if($total > 0)
                <div class="card-body pt-0">
                    <div class="d-flex justify-content-md-between flex-column flex-md-row justify-content-center align-items-center mt-4 gap-10">
                        <div class="">
                            <ul class="atbd-pagination d-flex justify-content-between">
                                <li>
                                    <div class="paging-option">
                                        <div class="dataTables_length d-flex">
                                            <label class="d-flex align-items-center mb-0">
                                                <select wire:model="perPage" class="custom-select custom-select-sm form-control form-control-sm mx-2" style="min-height: 35px;">
                                                    <option value="5">5</option>
                                                    <option value="10">10</option>
                                                    <option value="25">25</option>
                                                    <option value="50">50</option>
                                                    <option value="100">100</option>
                                                </select>
                                                <span class="no-wrap"> {{ __('vendors.common.entries_per_page') }} </span>
                                            </label>
                                        </div>
                                    </div>
                                </li>
                            </ul>
                        </div>

                        <div class="">
                            <div class="user-pagination">
                                <div class="user-pagination new-pagination">
                                    <div class="d-flex justify-content-sm-end justify-content-end">
                                        <nav>
                                            <span class="relative z-0 inline-flex rounded-md d-flex new-pagination-section flex-wrap">
                                                @if($pagination->current_page > 1)
                                                    <span>
                                                        <button type="button" class="border-0" wire:click="previousPage">
                                                            <i class="iconsax icon-bold fs-16 mr-0 rotate-ar-y d-inline-block" icon-name="chevron-left"></i>
                                                        </button>
                                                    </span>
                                                @else
                                                    <span>
                                                        <button class="border-0 disabled" disabled>
                                                            <i class="iconsax icon-bold fs-16 mr-0 rotate-ar-y d-inline-block" icon-name="chevron-left"></i>
                                                        </button>
                                                    </span>
                                                @endif

                                                @php
                                                    $start = max(1, $pagination->current_page - 2);
                                                    $end = min($pagination->last_page, $pagination->current_page + 2);
                                                @endphp

                                                @if($start > 1)
                                                    <span>
                                                        <button type="button" class="border-0" wire:click="gotoPage(1)">1</button>
                                                    </span>
                                                    @if($start > 2)
                                                        <span>
                                                            <button class="border-0 disabled" disabled>...</button>
                                                        </span>
                                                    @endif
                                                @endif

                                                @for($i = $start; $i <= $end; $i++)
                                                    @if($i == $pagination->current_page)
                                                        <span>
                                                            <button class="border-0 current-page" disabled>{{ $i }}</button>
                                                        </span>
                                                    @else
                                                        <span>
                                                            <button type="button" class="border-0" wire:click="gotoPage({{ $i }})">{{ $i }}</button>
                                                        </span>
                                                    @endif
                                                @endfor

                                                @if($end < $pagination->last_page)
                                                    @if($end < $pagination->last_page - 1)
                                                        <span>
                                                            <button class="border-0 disabled" disabled>...</button>
                                                        </span>
                                                    @endif
                                                    <span>
                                                        <button type="button" class="border-0" wire:click="gotoPage({{ $pagination->last_page }})">{{ $pagination->last_page }}</button>
                                                    </span>
                                                @endif

                                                @if($pagination->current_page < $pagination->last_page)
                                                    <span>
                                                        <button type="button" class="border-0" wire:click="nextPage">
                                                            <i class="iconsax icon-bold fs-16 mr-0 rotate-ar-y d-inline-block" icon-name="chevron-right"></i>
                                                        </button>
                                                    </span>
                                                @else
                                                    <span>
                                                        <button class="border-0 disabled" disabled>
                                                            <i class="iconsax icon-bold fs-16 mr-0 rotate-ar-y d-inline-block" icon-name="chevron-right"></i>
                                                        </button>
                                                    </span>
                                                @endif
                                            </span>
                                        </nav>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div>
                            <p class="text-sm text-gray-700 leading-5 mb-0">
                                <span>{{ __('vendors.common.showing') }}</span>
                                <span class="font-medium">{{ ($pagination->current_page - 1) * $pagination->per_page + 1 }}</span>
                                <span>{{ __('vendors.common.to') }}</span>
                                <span class="font-medium">{{ min($pagination->current_page * $pagination->per_page, $pagination->total) }}</span>
                                <span>{{ __('vendors.common.of') }}</span>
                                <span class="font-medium">{{ $pagination->total }}</span>
                                <span>{{ __('vendors.common.results') }}</span>
                            </p>
                        </div>
                    </div>
                </div>
            @endif
        @endif
    </div>
</div>

@push('scripts')
<script>
    // Toast notification handler
    window.addEventListener('show-toastr', event => {
        toastr.options = {
            "closeButton": true,
            "progressBar": true,
            "positionClass": "toast-top-right",
            "timeOut": "3000"
        };

        if (event.detail.type === 'success') {
            toastr.success(event.detail.message);
        } else if (event.detail.type === 'error') {
            toastr.error(event.detail.message);
        }
    });

    // Delete confirmation handler
    window.addEventListener('show-delete-confirmation', event => {
        Swal.fire({
            title: event.detail.title,
            text: event.detail.text,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: event.detail.confirmButtonText,
            cancelButtonText: event.detail.cancelButtonText
        }).then((result) => {
            if (result.isConfirmed) {
                @this.call('deleteBill', event.detail.id);
            }
        });
    });
</script>
@endpush

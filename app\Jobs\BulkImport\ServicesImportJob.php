<?php
    namespace App\Jobs\BulkImport;
    use Illuminate\Bus\Queueable;
    use Illuminate\Bus\Batchable;
    use Illuminate\Contracts\Queue\ShouldBeUnique;
    use Illuminate\Contracts\Queue\ShouldQueue;
    use Illuminate\Foundation\Bus\Dispatchable;
    use Illuminate\Queue\InteractsWithQueue;
    use Illuminate\Queue\SerializesModels;
    use Illuminate\Support\Facades\Log; 
    use App\Http\Traits\ProjectDetailTrait;
    use App\Http\Traits\BulkImportErrorTrait;
    use App\Http\Traits\ServiceTrait;
    use App\Http\Traits\BulkImportListTrait;
    use App\Http\Traits\BulkImportTrait;
    use App\Http\Traits\PriorityTrait;
    use App\Http\Traits\FunctionsTrait;
    use App\Enums\ResultType;
    use App\Enums\ModelAction;
    use App\Enums\Status;
    use App\Enums\ValidationBukImport;

    class ServicesImportJob implements ShouldQueue{
        use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, Batchable, ProjectDetailTrait, BulkImportErrorTrait, ServiceTrait, BulkImportListTrait, BulkImportTrait, PriorityTrait, FunctionsTrait;
        public $list;
        public $projectId;
        public $bulkImportDetailsId;
        public $projectUserId;
        public $userId;

        /**
         * Create a new job instance.
         *
         * @return void
         */
        public function __construct($list, $projectId, $bulkImportDetailsId, $projectUserId, $userId){
            $this->list = $list;
            $this->projectId = $projectId;
            $this->bulkImportDetailsId = $bulkImportDetailsId;
            $this->projectUserId = $projectUserId;
            $this->userId = $userId;
        }

        /**
         * Execute the job.
         *
         * @return void
         */
        public function handle(){
            try {
                $bulkArray = [];
                $project = $this->getProjectDetailInformationByProjectId($this->projectId);

                if(is_null($project)){
                    Log::info("ServicesImportJob error: No project found with this projectId : ".$this->projectId); 
                }

                elseif(!isset($this->list)){
                    Log::info("ServicesImportJob error: The services list is empty"); 
                }

                else{
                    foreach($this->list as $data){
                        $mapChecking = $this->fullServicesValidation($data);

                        if(!is_null($mapChecking) && $mapChecking[0]['status'] <> 'success'){
                            $bulkArrayErrors = ['map_status' => true, 'backend_status' => false, 'project_id' => $this->projectId, 'user_id' => $this->userId, 'bulk_import_id' => $this->bulkImportDetailsId, 'sheet' => ResultType::Services->value, 'identifier' => $data['service_name'], 'row_status' => ModelAction::NotInserted->value, 'created_by' => $this->userId];
                            $bulkImportErrorId = $this->saveBulkImportError($bulkArrayErrors);

                            if(!$bulkImportErrorId){
                                Log::info("ServicesImportJob error: Cannot save the bulk import error row for Services sheet : Project ID: ".$this->projectId.", Service Name: ".$data['service_name']); 
                            }
                        }

                        else{
                            $priorityRow = $this->getPriorityDataByValues("priority_level", $data['priority'], $this->projectUserId);

                            if(isset($priorityRow)){
                                $serviceRow = $this->getSpeceficServiceInformationsByValues('asset_category', $data['service_name'], $this->projectUserId);

                                if(!isset($serviceRow)){
                                    $array = [
                                        'user_id' => $this->projectUserId ?? null,
                                        'asset_category' => $data['service_name'] ?? null,
                                        'service_type' => $data['service_type'] ?? null,
                                        'priority_id' => $priorityRow->id ?? null,
                                        'status' => Status::Active->value ?? null,
                                        'last_ip' => $this->getCurrentIpAddress()
                                    ];

                                    $newServiceId = $this->saveService($array);

                                    if($newServiceId){
                                        $serviceAttachedData = $this->getServiceInformationsByValues('id', $newServiceId);
                                        $serviceAttachedData->priority()->attach($priorityRow->id);
                                        array_push($bulkArray, $newServiceId);
                                    }

                                    else{
                                        $bulkArrayErrors = ['map_status' => false, 'backend_status' => true, 'project_id' => $this->projectId, 'user_id' => $this->userId, 'bulk_import_id' => $this->bulkImportDetailsId, 'sheet' => ResultType::Services->value, 'identifier' => $data['service_name'], 'row_status' => ModelAction::NotInserted->value, 'errors' => ValidationBukImport::ServiceNotSaved->value, 'value' => $data['service_name'], 'created_by' => $this->userId];
                                        $bulkImportErrorId = $this->saveBulkImportError($bulkArrayErrors);

                                        if(!$bulkImportErrorId){
                                            Log::info("ServicesImportJob error: Cannot save the bulk import error row for Services sheet : Project ID: ".$this->projectId.", Service Name: ".$data['service_name']); 
                                        }
                                    }
                                }

                                else{
                                    $serviceRowId = $serviceRow->id;

                                    $array = [
                                        'asset_category' => $data['service_name'] ?? null,
                                        'service_type' => $data['service_type'] ?? null,
                                        'priority_id' => $priorityRow->id ?? null,
                                        'status' => Status::Active->value ?? null,
                                        'last_ip' => $this->getCurrentIpAddress()
                                    ];

                                    $newUpdatedService = $this->updateServiceById($this->projectUserId, $serviceRowId, $array);

                                    if($newUpdatedService){
                                        $serviceAttachedData = $this->getServiceInformationsByValues('id', $serviceRowId);
                                        $serviceAttachedData->priority()->sync($priorityRow->id);
                                        array_push($bulkArray, $serviceRowId);
                                    }

                                    else{
                                        $bulkArrayErrors = ['map_status' => false, 'backend_status' => true, 'project_id' => $this->projectId, 'user_id' => $this->userId, 'bulk_import_id' => $this->bulkImportDetailsId, 'sheet' => ResultType::Services->value, 'identifier' => $data['service_name'], 'row_status' => ModelAction::NotInserted->value, 'errors' => ValidationBukImport::ServiceNotUpdated->value, 'value' => $data['service_name'], 'created_by' => $this->userId];
                                        $bulkImportErrorId = $this->saveBulkImportError($bulkArrayErrors);

                                        if(!$bulkImportErrorId){
                                            Log::info("ServicesImportJob error: Cannot save the bulk import error row for Services sheet : Project ID: ".$this->projectId.", Service Name: ".$data['service_name']); 
                                        }
                                    }
                                }
                            }

                            else{
                                $bulkArrayErrors = ['map_status' => false, 'backend_status' => true, 'project_id' => $this->projectId, 'user_id' => $this->userId, 'bulk_import_id' => $this->bulkImportDetailsId, 'sheet' => ResultType::Services->value, 'identifier' => $data['service_name'], 'row_status' => ModelAction::NotInserted->value, 'errors' => ValidationBukImport::PropertyNotExist->value, 'value' => $data['priority'], 'created_by' => $this->userId];
                                $bulkImportErrorId = $this->saveBulkImportError($bulkArrayErrors);

                                if(!$bulkImportErrorId){
                                    Log::info("ServicesImportJob error: Cannot save the bulk import error row for Services sheet : Project ID: ".$this->projectId.", Service Name: ".$data['service_name']); 
                                }
                            }
                        }
                    }
                }

                $implodedValue = isset($bulkArray) && count($bulkArray) > 0 ? $this->implodeDataFromField($bulkArray) : null;
                $bulkImportList = $this->getBulkImportListByValues('bulk_import_id', $this->bulkImportDetailsId);
                $result = null;

                $array = [
                    'services' => $implodedValue,
                    'project_id' => $this->projectId,
                    'bulk_import_id' => $this->bulkImportDetailsId,
                    'created_by' => !isset($bulkImportList) ? $this->userId : $bulkImportList->created_by,
                    'updated_by' => isset($bulkImportList) ? $this->userId : null,
                ];
                
                if(isset($bulkImportList)){
                    $result = $this->updateBulkImportListByValues('bulk_import_id', $this->bulkImportDetailsId, $array);
                }

                else{
                    $result = $this->saveBulkImportList($array);
                }

                if(!$result){
                    Log::info("ServicesImportJob error: We cannot do any action on bulk import table for services column (Same data)"); 
                }
            } 
             
            catch (\Throwable $th) {
                Log::error("ServicesImportJob error: ".$th);
            }
        }
    }
?>
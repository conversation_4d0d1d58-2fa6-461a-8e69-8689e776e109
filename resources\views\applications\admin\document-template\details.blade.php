@extends('layouts.app')
@section('styles')
    <style>
        .disabled-upload {
            pointer-events: none;
            cursor: not-allowed;
            opacity: 0.6;
        }
    </style>
@endsection
@section('content')
    <div class="contents crm">
        <div class="container-fluid">
            <livewire:manage-document.template.details.bread-crumb :id="$id" />


            <div class="">
                <div class="row">
                    <div class="col-md-3 pr-md-0 mb-3 mb-md-0">
                        <div class="card  sticky-sidebar">
                            <ul class="list-group crm nav-stages">
                                <li class="list-group-item active">
                                    <a href="#info-section" class="d-flex justify-content-between align-items-center">
                                        @lang('document_module.info')
                                        <i class="las la-angle-right"></i> </a>
                                </li>
                                <li class="list-group-item">
                                    <a href="#attachments-section"
                                        class="d-flex justify-content-between align-items-center"> @lang('document_module.attachments') <i
                                            class="las la-angle-right"></i> </a>
                                </li>
                                <li class="list-group-item">
                                    <a href="#comments-section" class="d-flex justify-content-between align-items-center">
                                        @lang('document_module.comments') <i class="las la-angle-right"></i> </a>
                                </li>
                                <li class="list-group-item">
                                    <a href="#note-section" class="d-flex justify-content-between align-items-center">
                                        @lang('document_module.notes')
                                        <i class="las la-angle-right"></i> </a>
                                </li>
                            </ul>
                        </div>
                    </div>


                    <div class="col-md-9 mb-md-0 mb-30 scroll-right">
                        <livewire:manage-document.template.details.info :id="$id" :data="$response['info']"
                            :counts="[
                                'attachments' => count($response['attachments']),
                                'comments' => count($response['comments']),
                                'notes' => count($response['notes']),
                            ]" />
                        <livewire:manage-document.template.details.attachment :id="$id" :data="$response['attachments']" />
                        <livewire:manage-document.template.details.comment :id="$id" :data="$response['comments']" />
                        <livewire:manage-document.template.details.note :id="$id" :data="$response['notes']" />
                    </div>


                </div>

            </div>
        </div>
    </div>
    </div>
    @livewire('common.delete-confirm-v1')
@endsection
@section('scripts')
    <script src="/js/livewire/manage-select2.js"></script>
    <script src="/js/livewire/manage-tooltip.js"></script>
    <script src="/js/livewire/manage-toastr-messages.js"></script>
    <script src="/js/livewire/manage-loader.js"></script>
    <script src="/js/livewire/manage-modals.js"></script>
    <script src="/js/livewire/pages/documents.js"></script>
@endsection

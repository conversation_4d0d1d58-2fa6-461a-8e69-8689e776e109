<?php

namespace App\Http\Livewire\CRMProjects;


use Livewire\Component;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Log;
use App\Services\CRM\Projects\ProjectServices;
use App\Services\CRM\CRMUserService;
use App\Services\CRM\Sales\DocumentTypeService;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Storage;
use App\Services\CRM\CRMUsers;
use Illuminate\Support\Facades\Http;
use Livewire\WithFileUploads;
use Livewire\TemporaryUploadedFile;
use App\Enums\FileType;
use App\Enums\Language;
use App\Http\Traits\ExportTrait;
use App\Http\Traits\FunctionsTrait;
use App\Http\Helpers\Helper;
    use Auth;
    use App\Jobs\MilestoneJob;
    use App\Enums\SwalType;
use App\Models\Exports;

use Illuminate\Support\Facades\Crypt;
class ProjectDetails extends Component {

    use WithFileUploads,ExportTrait,FunctionsTrait;

    public $projectID;
    public $encryptedId;
    public $workspaceSlug;
    public $projectData = [];
    public $attachment;
    public $chartData = [];
    public $daysleft = '';
    public $start_date;
    public $filenametoDelete;
    public $progress=0;
    public $end_date,$budget,$description,$name;
    public $input_name;
    public $users = [];
    public $project_type ;
    public $projectType = [];
    public $priorityLevel = [];
    public $priority_level ;
    public $projectExists = false;
    public $isModalOpen = false;

    public $attachments = [];
    public $documentTypes = [];
    public $projects = [];
    public $DocumentsPaginated = [];
    public $files = [];
    public $isDeleteModalOpen = false;

    public $filePaths = [];
    public $folderKey = 'uploads';
    public $maxFileSize = 5120;

    public $attachmentFiles;
        public $currentDate;

    public  $user;
    public $activeTab = 'details'; // default view

    public $settingsState = [
        'basic_details' => 0,
        'member' => 0,
        'client' => 0,
        'vendor' => 0,
        'milestone' => 0,
        'activity' => 0,
        'attachment' => 0,
        'task' => 0,
        'bug_report' => 0,
        'invoice' => 0,
        'bill' => 0,
        'timesheet' => 0,
        'documents' => 0,
        'progress' => 0,
        'password_protected' => 0,
        'password' => '',
        'retainer' => 0,
        'proposal' => 0,
        'procurement' => 0,
        'backToList'
    ];

    public $milestoneData = [];

    public $status = 'incomplete';
    public $statuss;
    public $cost;
    public $summary;

    public $fileData = [];
    public $type, $user_id, $subject, $notes,$project,$document_id;

        public $projectDetails;



    protected $listeners = ['refreshList','updateProject','openModal', 'closeModal', 'fetchData','openModalDelete' ,'fetchDocuments','fetchUserList','editDocument', 'deleteDocument','createDocument','currentPage'];


    public $selectedUsersForInvite = [];
    public $usersAlreadyInvited = [];
    public $selectedvendorsForShare = [];
    public $vendorsAlreadyInProject = [];
    public $selectedclientsForShare = [];
    public $clientssAlreadyInProject = [];
    public $clients = [];
    public $vendors = [];
    public $fileType = 'pdf';
    public $fileLanguage = 'en';
    public $bmas_list = [];
    public $assignedBMAList = [];
    public $projectAccessUserId = 0;
    public $projectAccessDeleteMessage = '';
    public $projectAccessUserName = '';
    public $projectAccessUserType = '';
    public $componentKey;
    public $selectedBuildingsManager = [];
    public $allBuildingsManager = [];
    public $deleteAssignBMA = [
        'bma_id'=>'',
        'bma_name'=>'',

    ];


    public $currentPage = [];
    public function currentPage($data)
    {
        $this->currentPage[$data['functionName']] = $data['page'];
    }


    public function mount($projectID = null) {


        $this->componentKey = uniqid();
        $this->workspaceSlug = auth()->user()->workspace ?? 'default-workspace';

        if (!$projectID) {

            return redirect()->route('CRMProjects.list');
        }

        $this->projectID = $projectID;
        $this->encryptedId = encrypt($projectID);

        $this->fetchData();
        $this->fetchDocuments();

        $this->milestoneData=$this->projectData['milestones'] ?? [];
        $project_setting=$this->projectData['project_setting'] ?? [];
        foreach ($this->settingsState as $key => $value) {
            if (isset($project_setting[$key])) {
                if ($project_setting[$key] == 'on') {
                    $this->settingsState[$key]=1;
                }else{
                    $this->settingsState[$key]=0;
                }
            }else{
                $this->settingsState[$key]=0;
            }
        }

        $this->usersAlreadyInvited =  collect($this->projectData['users'] ?? [])->pluck('email')->toArray();
        $this->clientssAlreadyInProject =  collect($this->projectData['clients'] ?? [])->pluck('email')->toArray();
        $this->vendorsAlreadyInProject =  collect($this->projectData['vendors'] ?? [])->pluck('email')->toArray();


        $this->setFileType(FileType::PDF->value);
            $this->setFileLanguage(Language::English->value);

    }
    public function loadTab($tab)
    {
        $this->dispatchBrowserEvent( 'show-loader' );
        $this->activeTab = $tab;
    }

    public function getProjectType(): array
    {
        $crmProjectsService = app( ProjectServices::class );
        $response = $crmProjectsService->getProjectType( $this->workspaceSlug );
        if ( isset( $response[ 'status' ] ) && $response[ 'status' ] === 'success' ) {
            return $response[ 'data' ]?? [];
        }
        return [];
    }
    public function getPriorityLevel(): array
    {
        $crmProjectsService = app( ProjectServices::class );
        $response = $crmProjectsService->getPriorityLevel( $this->workspaceSlug );
        if ( isset( $response[ 'status' ] ) && $response[ 'status' ] === 'success' ) {
            return $response[ 'data' ]?? [];
        }
        return [];
    }

    public function loadAttachments($page = 1)
    {
        $page = $this->currentPage['fetchDocuments'] ?? $page;
        $service = app(ProjectServices::class);
        $response = $service->getFiles($this->workspaceSlug, $this->projectID,$page);
        if (isset($response['status']) && $response['status'] === 'success') {
            if (isset($response['data'])) {
                $this->files = $response['data'];
            }
        } else {
            session()->flash('error', 'Failed to load attachments.');
        }

    }




    public function fetchData() {

        try {
            $crmProjectsService = app(ProjectServices::class);
            $response = $crmProjectsService->getProjectDetails($this->workspaceSlug, $this->projectID);
            if (isset($response['error']) && $response['error'] === true) {
                $this->projectExists = false;
                return;

            }
            if (isset($response['status']) && $response['status'] === 'success') {
                $this->projectData = $response['data']['project'] ?? [];
                $this->chartData = $response['data']['chartData'] ?? [];
                $this->daysleft = $response['data']['daysleft'] ?? [];
                $project_setting=$response['data']['project']['project_setting'] ?? [];

                $this->users =  $response['data']['project']['all_users'] ?? [];
                $this->clients = $response['data']['project']['all_clients'] ?? [];
                $this->vendors =  $response['data']['project']['all_vendors'] ?? [];
                $this->allBuildingsManager = $response['data']['project']['bma_users'] ?? [];
                $this->projectType =  $response['data']['project']['project_types'] ?? [];
                $this->priorityLevel =  $response['data']['project']['priority_levels'] ?? [];
                $this->documentTypes =  $response['data']['project']['document_types'] ?? [];
                $this->assignedBMAList =  $response['data']['project']['get_assigned_users'] ?? [];
                foreach ($this->settingsState as $key => $value) {
                    if (isset($project_setting[$key])) {
                        if ($project_setting[$key] == 'on') {
                            $this->settingsState[$key]=1;
                        }else{
                            $this->settingsState[$key]=0;
                        }
                    }else{
                        $this->settingsState[$key]=0;
                    }
                }

                $this->milestoneData=$response['data']['project']['milestones'];
                $this->projectExists = true;
            }
        } catch (\Exception $e) {

            \Log::info("Get Project Details : " . $e->getMessage());
            $this->projectExists = false;
        }
    }



    public function resetForm()
    {
        $this->reset([
            'type',
            'user_id',
            'subject',
            'description',

        ]);
    }


    public function getUserList(): array

    {
           $crmProjectsService = app( ProjectServices::class );
           $response = $crmProjectsService->getAllUsers( $this->workspaceSlug );
           if ( isset( $response[ 'status' ] ) && $response[ 'status' ] === 'success' ) {
               return $response[ 'data' ]?? [];
           }
           return [];
       }
   





        public function confirmDelete()
        {
            $successMessage = __( 'CRMProjects.common.file_deleted_success' );
            if ( $this->FileToDelete  ) {
                $this->dispatchBrowserEvent( 'close-modal', [ 'modalId' => 'confirm-delete-file' ] );
                $this->dispatchBrowserEvent( 'show-loader' );
                $crmProjectsService = app( ProjectServices::class );
                $response = $crmProjectsService->deleteFiles($this->workspaceSlug,$this->projectID, $this->FileToDelete);


                if ( $response[ 'status' ] === 'success' ) {

                    $this->fetchData();
                    $this->dispatchBrowserEvent('hide-loader');
                    $this->dispatchBrowserEvent( 'show-toastr', [
                        'type' => 'success',
                        'message' => $successMessage
                    ] );

                } else {
                    $errorMessage = __('CRMProjects.common.error_message');
                       $this->dispatchBrowserEvent('show-toastr', [
                           'type' => 'error',
                           'message' => $errorMessage
                       ]);
                  $this->dispatchBrowserEvent('hide-loader');
                }
            }




        }




    public function editProject($id)
    {
        $this->reset(['name', 'description', 'start_date', 'end_date', 'statuss', 'budget', 'project_type']);
            $this->priority_level = $this->projectData['priority_level'];
            $this->project_type = $this->projectData['project_type'];
            $this->name = $this->projectData['title'];
            $this->description = $this->projectData['description'];
            $this->start_date = $this->projectData['start_date'];
            $this->end_date = $this->projectData['end_date'];
            $this->budget =  $this->projectData['cost'];
            $this->statuss =  $this->projectData['status'];
            $this->id= $id;

            $SelectplaceholderText = __( 'CRMProjects.common.choose_user' )?? __( 'CRMProjects.common.please_select' );
            $this->dispatchBrowserEvent( 'open-modal', [ 'modalId' => 'edit-project' ] );
            $this->dispatchBrowserEvent( 'initSelect2', [ 'selectId' => 'usersListforeditProject', 'placeholderText'=>$SelectplaceholderText ] );

    }


    public function updateProject() {


            $this->validate([
                'priority_level' => 'required',
                'project_type' => 'required',
                'name' => 'required',
                'budget' => 'required|numeric|gt:0',
                'start_date' => 'required|date',
                'end_date' => 'required|date|after_or_equal:start_date',
                'description' => 'required|string|max:5000',
            ],[
                'end_date.after_or_equal' => __('CRMProjects.common.start_end_validation_date'),
                'budget.gt' => __('CRMProjects.common.budget_must_be_positive'),
                'description.max' => __('CRMProjects.common.description_max_limit'),
            ]);

            $crmProjectsService = app( ProjectServices::class );
            $response = $crmProjectsService->updateProject( $this->workspaceSlug,  $this->projectID, [
                'priority_level' =>  $this->priority_level,
                'project_type' =>  $this->project_type,
                'name' => $this->name,
                'budget' =>$this->budget,
                'start_date' => Carbon::parse($this->start_date)->format('Y-m-d'),
                'end_date' => Carbon::parse($this->end_date)->format('Y-m-d'),
                'status'=> $this->statuss,
                'description' => $this->description,
            ] );
            if ($response['status'] === 'success') {
                $this->fetchData();
                $this->dispatchBrowserEvent('reloadPage');
                $this->dispatchBrowserEvent( 'close-modal', [ 'modalId' => 'edit-project' ] );
                $this->dispatchBrowserEvent('show-toastr', [
                    'type' => 'success',
                    'message' => __('CRMProjects.common.project_updated_successfully')
                ]);
            } else {
                $this->dispatchBrowserEvent('show-toastr', [
                    'type' => 'error',
                    'message' =>  $response['message']
                ]);
            }

            $this->dispatchBrowserEvent( 'hide-loader' );


    }

    public function openModal()
    {
        $this->attachment = null;
        $this->isModalOpen = true;
    }

    public function closeModal()
    {
        $this->isModalOpen = false;
        $this->attachment = null;
        $this->isFileSelected = false;

    }

    public function download($url)
    {
        try {

            $response = Http::get($url);

            if ($response->successful()) {
                $fileName = basename(parse_url($url, PHP_URL_PATH));
                $localPath = 'app/' . $fileName;

                Storage::disk('local')->put($localPath, $response->body());

                return Storage::disk('local')->download($localPath);
            } else {
                return response()->json(['error' => 'Failed to download the file'], 400);
            }

        } catch (\Exception $e) {
            return response()->json(['error' => 'Error: ' . $e->getMessage()], 500);
        }

    }



    public function updatedAttachmentFiles()
    {
        $this->dispatchBrowserEvent('show-loader');

        $successMessage = __('CRMProjects.common.file_uploaded_successfully');
        $errorMessage = __('CRMProjects.common.failed_to_upload_file');
        $validationErrorMessage = __('CRMProjects.common.upload_file_global_validation_failed');

        try {
            $this->validate([
                'attachmentFiles' => 'required|file|mimes:pdf,jpg,png|max:2048',
            ]);

            $filename = $this->attachmentFiles->getClientOriginalName();
            $filePath = $this->attachmentFiles->storeAs('uploads/projects', $filename, 'local');
            $absolutePath = storage_path('app/' . $filePath);

            $service = app(ProjectServices::class);
            $response = $service->uploadSingleFile($this->workspaceSlug, $this->projectID, $absolutePath, $filename);

            if (isset($response['error'])) {
                $this->dispatchBrowserEvent('show-toastr', [
                    'type' => 'error',
                    'message' => $errorMessage,
                ]);
            } else {
                $this->dispatchBrowserEvent('show-toastr', [
                    'type' => 'success',
                    'message' => $successMessage,
                ]);
            }

            $this->attachmentFiles = '';
            $this->fetchData();
        } catch (\Illuminate\Validation\ValidationException $e) {
            $this->attachmentFiles = '';
            $this->dispatchBrowserEvent('show-toastr', [
                'type' => 'error',
                'message' => $validationErrorMessage,
            ]);
        } catch (\Throwable $th) {
            $this->attachmentFiles = '';
            $this->fetchData();
            $this->dispatchBrowserEvent('show-toastr', [
                'type' => 'error',
                'message' => $errorMessage,
            ]);
            \Log::info('updatedAttachmentFiles Issue');
        }

        $this->dispatchBrowserEvent('hide-loader');
    }




    public function openInviteUserModal( $id )
    {

        $this->projectID = $id;
        $this->dispatchBrowserEvent( 'show-loader' );
        $SelectplaceholderText = __( 'CRMProjects.common.choose_user' )?? __( 'CRMProjects.common.please_select' );
        $this->dispatchBrowserEvent( 'open-modal', [ 'modalId' => 'invite-users' ] );
       $this->dispatchBrowserEvent( 'initSelect2', [ 'selectId' => 'selectedUsersForInvite', 'placeholderText'=>$SelectplaceholderText ] );
       $this->dispatchBrowserEvent( 'hide-loader' );

    }

    public function openShareToClientModal( $id )
 {
    $this->projectID = $id;
    $this->dispatchBrowserEvent( 'show-loader' );

        $SelectplaceholderText = __( 'CRMProjects.common.choose_client' )?? __( 'CRMProjects.common.please_select' );
        $this->dispatchBrowserEvent( 'open-modal', [ 'modalId' => 'share-clients' ] );
        $this->dispatchBrowserEvent( 'initSelect2', [ 'selectId' => 'selectedclientsForShare', 'placeholderText'=>$SelectplaceholderText ] );
        $this->dispatchBrowserEvent( 'hide-loader' );
    }

    public function openShareToVendorModal( $id)
 {
    $this->projectID = $id;
    $this->dispatchBrowserEvent( 'show-loader' );
        $SelectplaceholderText = __( 'CRMProjects.common.choose_vendor' )?? __( 'CRMProjects.common.please_select' );
        $this->dispatchBrowserEvent( 'open-modal', [ 'modalId' => 'share-vendors' ] );
        $this->dispatchBrowserEvent( 'initSelect2', [ 'selectId' => 'selectedvendorsForShare', 'placeholderText'=>$SelectplaceholderText ] );
        $this->dispatchBrowserEvent( 'hide-loader' );
    }
    public function getClientsList(): array
    {
           $crmProjectsService = app( ProjectServices::class );
           $response = $crmProjectsService->getAllClients( $this->workspaceSlug );
           if ( isset( $response[ 'status' ] ) && $response[ 'status' ] === 'success' ) {
               return $response[ 'data' ] ?? [];
           }
           return [];
       }

       public function getAllVendors(): array
    {
           $crmProjectsService = app( ProjectServices::class );
           $response = $crmProjectsService->getAllVendors( $this->workspaceSlug );
           if ( isset( $response[ 'status' ] ) && $response[ 'status' ] === 'success' ) {
               return $response[ 'data' ] ?? [];
           }
           return [];
       }
       public function shareToClients() {

        $this->validate([
            'selectedclientsForShare' => 'required',

        ]);
        $this->dispatchBrowserEvent( 'close-modal', [ 'modalId' => 'share-clients' ] );
        $this->dispatchBrowserEvent( 'show-loader' );
        $successMessage = __( 'CRMProjects.common.project_shared_with_users_successfully' );
        $errorMessage = __( 'CRMProjects.common.failed_to_share_project' );


        if ( $this->projectID ) {

            $crmProjectsService = app( ProjectServices::class );
            $response = $crmProjectsService->shareWithClients( $this->workspaceSlug,  $this->projectID, $this->selectedclientsForShare );

            $message = ( $response[ 'status' ] === 'success' ) ? $successMessage : $errorMessage;
          $type = ( $response[ 'status' ] === 'success' ) ? 'success' : 'error';

        $this->dispatchBrowserEvent( 'show-toastr', [
            'type' => $type,
            'message' => $message
        ] );

        $this->selectedclientsForShare=[];

        $this->fetchData();
        $this->dispatchBrowserEvent( 'hide-loader' );

        } else {
            $this->dispatchBrowserEvent('show-toastr', [
                'type' => 'error',
                'message' =>  $errorMessage
            ]);
            $this->dispatchBrowserEvent( 'close-modal', [ 'modalId' => 'share-clients' ] );
            $this->dispatchBrowserEvent( 'hide-loader' );
        }
    }
    public function fetchDocuments( $page = 1)
    {
        $page = $this->currentPage['fetchDocuments'] ?? $page;
        $service =   app(ProjectServices::class);
        $data = $service->documentsByProject($this->workspaceSlug,$this->projectID, $page);
        if (@$data['status'] == "success") {
            $this->DocumentsPaginated = $data['data'] ?? [];
        } else {
            $this->DocumentsPaginated  = [];
        }
    }




    public function fetchProjectList()
    {
        $service =  app(ProjectServices::class);
        $data = $service->getProjects($this->workspaceSlug);
        if (@$data['status'] == "success") {
            $this->projects = $data['data'] ?? [];
        }
    }


       /**
     * manage case start
     */
    public function editDocument($id)
    {
        $service = app(ProjectServices::class);
        $document = $service->getDocumentById($this->workspaceSlug, $this->projectID, $id);
        if ($document) {
            $this->document_id=$document;
            // dd($this->document_id['data']);
            $this->subject = $this->document_id['data']['subject'];
            $this->type = $this->document_id['data']['type']['id'];
            $this->user_id = $this->document_id['data']['user_id'];
            $this->description = $this->document_id['data']['description'];
        $this->dispatchBrowserEvent('open-modal', ['modalId' => 'editDocument']);
        }
    }


    public function createDocument()
    {
        $this->validate([
            'subject' => 'required',
            'type' => 'required',
            'user_id' => 'required',
            'description' => 'required',
        ]);

        $service =  app(ProjectServices::class);

        if (!empty($this->document_id['data']['id'])) {
                    $successMessage = __( 'CRMProjects.common.document_update_successfully' );
        $errorMessage = __( 'CRMProjects.common.failed_to_create_document' );
            // Update existing document
            $response = $service->updateDocumentByProject($this->workspaceSlug, $this->projectID, [
                'subject' => $this->subject,
                'type' => $this->type,
                'user_id' => $this->user_id ?? 0,
                'description' => $this->description,
            ], $this->document_id['data']['id']);
        } else {
                     $successMessage = __( 'CRMProjects.common.document_created_successfully' );
        $errorMessage = __( 'CRMProjects.common.failed_to_create_document' );
            // Create a new document
            $response = $service->createDocumentByProject($this->workspaceSlug,$this->projectID, [
                'subject' => $this->subject,
                'type' => $this->type,
                'user_id' => $this->user_id ?? 0,
                'description' => $this->description,
            ]);
        }

        if ($response['status'] === 'success') {
            $this->fetchDocuments();
            $this->resetForm();
            // $this->isModalOpen = false;
            $this->dispatchBrowserEvent('show-toastr', [
                'type' => 'success',
                'message' => $successMessage
            ]);
        } else {
            $this->dispatchBrowserEvent('show-toastr', [
                'type' => 'error',
                'message' =>  $errorMessage
            ]);
        }
        
        $this->fetchData();
        $this->dispatchBrowserEvent('close-modal', ['modalId' => 'createDocumentModal']);
        $this->dispatchBrowserEvent('close-modal', ['modalId' => 'editDocument']);

        // $this->dispatchBrowserEvent('hide-loader');
    }


    public function openModalDelete($id,$subject){
        $this->input_name=$subject;
        $this->DocumentToDelete=$id;
        $this->dispatchBrowserEvent('show-delete-modal');
    }


    public function confirmDeleteDocument()
    {
        if ($this->DocumentToDelete) {
            $service =  app(ProjectServices::class);
            $response = $service->deleteDocumentByProject($this->workspaceSlug,$this->projectID, $this->DocumentToDelete);

            if ($response['status'] === 'success') {
            $successMessage = __( 'CRMProjects.common.document_deleted_success' );

                $this->dispatchBrowserEvent('show-toastr', [
                    'type' => 'success',
                    'message' => $successMessage
                ]);
            } else {
                            $errorMessage = __('CRMProjects.common.error_message');

                $this->dispatchBrowserEvent('show-toastr', [
                    'type' => 'error',
                    'message' =>  $errorMessage
                ]);
            }
        }
        $this->fetchDocuments();
        $this->dispatchBrowserEvent('close-confirm-modal');
    }

    public function cancelDeleteDocument()
    {
        $this->dispatchBrowserEvent('close-confirm-modal');
    }

    public function openModalDocument()
    {
        $this->document_id = null;
        $this->isModalOpen = true;
        $this->dispatchBrowserEvent('open-modal');
    }


    public function closeModalDocument()
    {
        $this->document_id = null;
        $this->isModalOpen = false;
        $this->resetForm();
        $this->dispatchBrowserEvent('close-modal');

    }
    /**
     * manage case end
     */


    public function shareToVendors() {

        $this->validate([
            'selectedvendorsForShare' => 'required',

        ]);
        $this->dispatchBrowserEvent( 'close-modal', [ 'modalId' => 'share-vendors' ] );
        $this->dispatchBrowserEvent( 'show-loader' );
     $successMessage = __( 'CRMProjects.common.project_shared_with_vendor_successfully' );
        $errorMessage = __( 'CRMProjects.common.failed_to_share_project' );

        if ( $this->projectID ) {
            $crmProjectsService = app( ProjectServices::class );
            $response = $crmProjectsService->shareWithVendors( $this->workspaceSlug,  $this->projectID, $this->selectedvendorsForShare );
            $message = ( $response[ 'status' ] === 'success' ) ? $successMessage : $errorMessage;
          $type = ( $response[ 'status' ] === 'success' ) ? 'success' : 'error';

        $this->dispatchBrowserEvent( 'show-toastr', [
            'type' => $type,
            'message' => $message
        ] );

        $this->selectedvendorsForShare=[];

        $this->fetchData();
        $this->dispatchBrowserEvent( 'hide-loader' );


        } else {
            $this->dispatchBrowserEvent('show-toastr', [
                'type' => 'error',
                'message' =>  $errorMessage
            ]);
            $this->dispatchBrowserEvent( 'close-modal', [ 'modalId' => 'share-vendors' ] );
            $this->dispatchBrowserEvent( 'hide-loader' );
        }



    }

    public function getUserIdByemail(string $email)
    {
        $crmProjectsService = app(ProjectServices::class);
        $response = $crmProjectsService->getUserIdByEmail($this->workspaceSlug, $email);
        if (isset($response['status']) && $response['status'] === 'success') {
            return $response['data']['id'] ?? 0;
        }

        return 0;
    }

            public function openCancelProjectAccessModal($email,$name,$type){

                $this->dispatchBrowserEvent( 'show-loader' );
               $this->projectAccessUserId=$this->getUserIdByemail($email);
               $this->projectAccessUserName=$name;
               $this->projectAccessUserType=$type;
               switch ($type) {
                case 'vendor':
                    $this->projectAccessDeleteMessage = __('CRMProjects.common.cancel_share_vendor');
                    break;
                case 'client':
                    $this->projectAccessDeleteMessage = __('CRMProjects.common.cancel_share_client');
                    break;
                case 'user':
                    $this->projectAccessDeleteMessage = __('CRMProjects.common.delete_invitation_user');
                    break;
                default:
                    $this->projectAccessDeleteMessage = __('CRMProjects.common.generic_delete_access') ;
                    break;

                }

                  $this->dispatchBrowserEvent( 'open-modal', [ 'modalId' => 'CancelProjectAccessModal' ] );
                  $this->dispatchBrowserEvent( 'hide-loader' );

              }

              public function CancelProjectAccess(){
                $this->dispatchBrowserEvent('close-modal', ['modalId' => 'CancelProjectAccessModal']);
                $this->dispatchBrowserEvent('show-loader');

                $errorMessage = __( 'CRMProjects.common.failed_to_complete_operation' );
                $successMessage = '';

                if (!empty($this->projectAccessUserId) && $this->projectAccessUserId !== 0) {
                    $crmProjectsService = app(ProjectServices::class);

                    switch ($this->projectAccessUserType) {
                        case 'vendor':
                            $successMessage = __( 'CRMProjects.common.share_vendor_cancelled_successfully' );
                            $response = $crmProjectsService->CancelShareVendor($this->workspaceSlug, $this->projectID, $this->projectAccessUserId);
                            break;
                        case 'client':
                            $successMessage = __( 'CRMProjects.common.share_client_cancelled_successfully' );
                            $response = $crmProjectsService->CancelShareClient($this->workspaceSlug, $this->projectID, $this->projectAccessUserId);
                            break;
                        case 'user':
                            $successMessage = __( 'CRMProjects.common.invitation_user_cancelled_successfully' );
                            $response = $crmProjectsService->CancelInvitation($this->workspaceSlug, $this->projectID, $this->projectAccessUserId);
                            break;
                        default:
                            $response = null;
                            break;
                    }

                    if ($response && isset($response['status']) && $response['status'] === 'success') {
                        $this->dispatchBrowserEvent('show-toastr', [
                            'type' => 'success',
                            'message' => $successMessage
                        ]);
                    } else {
                        $this->dispatchBrowserEvent('show-toastr', [
                            'type' => 'error',
                            'message' => $errorMessage
                        ]);
                    }

                } else {
                    $this->dispatchBrowserEvent('show-toastr', [
                        'type' => 'error',
                        'message' => $errorMessage
                    ]);
                }

                $this->dispatchBrowserEvent('hide-loader');
                $this->fetchData();
            }


/* Inite Users */




       public function inviteUsers() {

            $this->validate([
                'selectedUsersForInvite' => 'required',

            ]);

         $this->dispatchBrowserEvent( 'close-modal', [ 'modalId' => 'invite-users' ] );
         $this->dispatchBrowserEvent( 'show-loader' );
         $successMessage = __( 'CRMProjects.common.users_invited_successfully' );
         $errorMessage = __( 'CRMProjects.common.failed_to_invite_user' );
         if ( $this->projectID ) {
             $crmProjectsService = app( ProjectServices::class );
             $response = $crmProjectsService->inviteUsers( $this->workspaceSlug,  $this->projectID, $this->selectedUsersForInvite );
             $message = ( $response[ 'status' ] === 'success' ) ? $successMessage : $errorMessage;
          $type = ( $response[ 'status' ] === 'success' ) ? 'success' : 'error';

         $this->dispatchBrowserEvent( 'show-toastr', [
             'type' => $type,
             'message' => $message
         ] );

         $this->selectedUsersForInvite=[];
         $this->fetchData();
         $this->dispatchBrowserEvent( 'hide-loader' );


         } else {
             $this->dispatchBrowserEvent( 'show-toastr', [
                 'type' => 'error',
                 'message' => $errorMessage
             ] );
             $this->fetchData();
             $this->dispatchBrowserEvent( 'hide-loader' );
         }

         }
         public function copyURL()
         {
             try {
                 $url = route('publicProjectDetails', ['id' => Crypt::encrypt($this->projectID)]);

                 $this->dispatchBrowserEvent('copy-url-to-clipboard', [
                     'url' => $url,
                     'successMessage' => __('CRMProjects.common.URL_copied_to_clipboard!'),
                     'errorMessage' => __('CRMProjects.common.failed_Copie_URL_to_clipboard')
                 ]);
             } catch (\Exception $e) {
                 $this->dispatchBrowserEvent('show-toastr', [
                     'type' => 'error',
                     'message' => __('CRMProjects.common.failed_Copie_URL_to_clipboard')
                 ]);
             }
         }

    public function render() {
        if (!$this->projectExists) {
            return view('livewire.c-r-m-projects.empty-project');
        }
        return view('livewire.c-r-m-projects.project-details',
        [
            'projectData' => $this->projectData,
            'settingsState'=>$this->settingsState,
            'daysleft' => $this->daysleft,
            'milestones_list'=> $this->milestoneData,
            'files_list'=> $this->fileData,
            'chartData' => $this->chartData

      ]);
    }


    public function saveSettings()
    {

        $this->dispatchBrowserEvent( 'close-modal', [ 'modalId' => 'projectSettingModal' ] );
        $this->dispatchBrowserEvent( 'show-loader' );
        $onSettings=[];
        foreach ($this->settingsState as $key => $value) {
            if ($value) {
                $onSettings[$key]='on';
            }
        }
        if(isset($this->settingsState['password'])){
        $onSettings['password']=$this->settingsState['password'];
        }
        $crmProjectsService = app(ProjectServices::class);
        $response = $crmProjectsService->saveProjectSetting($this->workspaceSlug, $this->projectID,$onSettings );

        $successMessage = __( 'CRMProjects.common.project_setting_successfully' );
        $errorMessage = __( 'CRMProjects.common.failed_to_project_setting' );

        if ($response['status'] === 'success') {
            $this->fetchData();

            $this->dispatchBrowserEvent('show-toastr', [
                'type' => 'success',
                'message' => $response['message']
            ]);
        } else {
            $this->dispatchBrowserEvent('show-toastr', [
                'type' => 'error',
                'message' =>  $response['message']
            ]);
        }
        $this->dispatchBrowserEvent('hide-loader');
    }


    public function openFileModal( $id)
    {
        $this->fileId = $id;
        $this->dispatchBrowserEvent( 'open-modal', [ 'modalId' => 'deleteFileConfirmModal' ] );
    }


    /* public function deleteFile()
    {
        if ($this->fileId) {
            $this->dispatchBrowserEvent( 'close-modal', [ 'modalId' => 'deleteFileConfirmModal' ] );
            $this->dispatchBrowserEvent( 'show-loader' );
            $crmProjectsService = app( ProjectServices::class );
            $response = $crmProjectsService->fileDelete( $this->workspaceSlug, $this->projectID, $this->fileId );

            if ( $response[ 'status' ] === 'success' ) {

                $this->fetchData();
                $this->dispatchBrowserEvent('hide-loader');
                $this->dispatchBrowserEvent( 'show-toastr', [
                    'type' => 'success',
                    'message' => 'Delete'
                ] );
            } else {
            $errorMessage = __('CRMProjects.common.error_message');
            $this->dispatchBrowserEvent('show-toastr', [
                'type' => 'error',
                'message' => $errorMessage
            ]);
            $this->dispatchBrowserEvent('hide-loader');
            }
        }
        $this->dispatchBrowserEvent('page-refresh');
    } */


    public function openCreateMilestone()
    {
        $this->reset([
            'name',
            'status',
            'cost',
            'summary',
            'start_date',
            'end_date'
        ]);
        $this->dispatchBrowserEvent('open-modal', ['modalId' => 'create-milestone']);
    }


    public function saveMilestone()
    {

         $this->validate([
            'name' => 'required|string',
            'status' => 'required|string',
            'cost' => 'required|numeric',
            'summary' => 'required|string',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
        ]);
        $this->dispatchBrowserEvent( 'close-modal', [ 'modalId' => 'create-milestone' ] );
        $this->dispatchBrowserEvent( 'show-loader' );
        $successMessage = __( 'CRMProjects.common.milestone_created_successfully' );
        $errorMessage = __( 'CRMProjects.common.failed_to_create_milestone' );


        $data = [
            'title' => $this->name,
            'status' =>$this->status,
            'cost' => $this->cost,
            'summary' => $this->summary,
            'start_date' => $this->start_date,
            'end_date' => $this->end_date
        ];
         $crmProjectsService = app( ProjectServices::class );
         $response = $crmProjectsService->createMilestone( $this->workspaceSlug, $this->projectID,  $data );
         if (!isset($response['status']) || $response['status'] !== 'success') {
            Log::info('Failed to create project', [
                'data' => $data,
                'response' => $response,
            ]);


        }
        $message = ( $response[ 'status' ] === 'success' ) ? $successMessage : $errorMessage;
        $type = ( $response[ 'status' ] === 'success' ) ? 'success' : 'error';

        $this->dispatchBrowserEvent( 'show-toastr', [
            'type' => $type,
            'message' => $message
        ] );

        $this->resetCreateForm();
        $this->fetchData();
        $this->dispatchBrowserEvent('hide-loader');

    }
    public function openMilestoneModal( $id ,$name)
    {

        $this->milestoneId = $id;
        $this->name = $name;

        $this->dispatchBrowserEvent( 'open-modal', [ 'modalId' => 'deleteConfirmModalMilstone' ] );
    }

    public function openEditModal( $milestoneId )
    {
        $crmProjectsService = app(ProjectServices::class);
        $response = $crmProjectsService->getMilestoneShow($this->workspaceSlug, $milestoneId);
        if (isset($response['status']) && $response['status'] === 'success') {

            $this->name =  $response['data']['title'];
            $this->status =  $response['data']['status'];
            $this->cost =  $response['data']['cost'];
            $this->summary =  $response['data']['summary'];
            $this->start_date=$response['data']['start_date'];
            $this->end_date=$response['data']['end_date'];
            $this->progress=$response['data']['progress']??0;
            $this->milestoneId =  $milestoneId;


        }
        $this->dispatchBrowserEvent( 'open-modal', [ 'modalId' => 'edit-milestone' ] );
    }

    public function updateMilestone()
    {
         $this->validate([
            'name' => 'required|string',
            'status' => 'required|string',
            'cost' => 'required|numeric',
            'summary' => 'required|string',
            'start_date' => 'required|string',
            'end_date' => 'required|string',
        ]);
        $this->dispatchBrowserEvent( 'close-modal', [ 'modalId' => 'edit-milestone' ] );
        $this->dispatchBrowserEvent( 'show-loader' );
        $successMessage = __( 'CRMProjects.common.milestone_update_successfully' );
        $errorMessage = __( 'CRMProjects.common.failed_to_create_milestone' );


        $data = [
            'title' => $this->name,
            'status' =>$this->status,
            'cost' => $this->cost,
            'summary' => $this->summary,
            'start_date' => $this->start_date,
            'end_date' => $this->end_date,
            'progress' => $this->progress,
        ];
         $crmProjectsService = app( ProjectServices::class );
         $response = $crmProjectsService->updateMilestone( $this->workspaceSlug, $this->milestoneId,  $data );

         if (!isset($response['status']) || $response['status'] !== 'success') {
            Log::info('Failed to update project', [
                'data' => $data,
                'response' => $response,
            ]);
        }
        $message = ( $response[ 'status' ] === 'success' ) ? $successMessage : $errorMessage;
        $type = ( $response[ 'status' ] === 'success' ) ? 'success' : 'error';

        $this->dispatchBrowserEvent( 'show-toastr', [
            'type' => $type,
            'message' => $message
        ] );

        $this->resetCreateForm();
        $this->fetchData();
        $this->dispatchBrowserEvent('hide-loader');
    }




        public function setFileType($value) {
            try {
                $this->fileType = $value;
            } 
            
            catch (\Throwable $th) {
                Log::error("setFileType error: ".$th);
            }
        }

        public function setFileLanguage($value) {
            try {
                $this->fileLanguage = $value;
            } 
            
            catch (\Throwable $th) {
                Log::error("setFileLanguage error: ".$th);
            }
        }


   public function initProjectDetails() {
            try {
                if(!isset($this->projectDetails)){
                    $this->projectDetails = isset($this->projectId) ? $this->getUserByvalues($this->projectId) : null;
                }
            } 
            catch (\Throwable $th) {
                Log::error("initProjectDetails error: ".$th);
            }
        }

        public function initCurrentDate() {
            try {
                $this->currentDate = $this->getCurrentDate();
            } 
            
            catch (\Throwable $th) {
                Log::error("initCurrentDate error: ".$th);
            }
        }

        public function submitExportForm() {
            try {
                $randomNumber = $this->generateRandomNumber(10000000, 99999999);
                $fileName =  $this->fileType == 'pdf' ? $randomNumber.'.pdf' : $randomNumber.'.csv';
                $exportName = 'EXP'.$this->generateRandomNumber(1000, 9999);
                $countItems = count($this->milestoneData);
                $exportType =  $this->fileType == 'pdf' ? 'pdf' : 'csv';
                $requestedAt = $this->getCurrentDateTime();
                $startDate = now();                
                $endDate =  $this->changeDateFormat('Y-m-d h:i:s', $this->currentDate);
                $root = $_SERVER["DOCUMENT_ROOT"];
                $exportId = $this->saveExports(Auth::user()->id, $fileName, $countItems, $exportName, $exportType, $requestedAt, $startDate, $endDate, "pending", Auth::user()->project_id, 'milestone');
                if($exportId){
                    $export_no = Exports::find($exportId)->export_no; 
                    $job = MilestoneJob::dispatch($export_no,$exportId,$this->milestoneData, Auth::user(), $root, $startDate, $endDate, $this->fileType, $this->fileLanguage, $fileName,  $countItems);
                    if($job){
                        $this->callSwalByType(SwalType::Success->value);
                    }
                    else{
                        $this->callSwalByType(SwalType::Error->value);
                    }
                }
                else{
                    $this->callSwalByType(SwalType::Error->value);
                }
            }
            catch (\Throwable $th) {
                Log::error("submitExportForm error: ".$th);
            }
        }

        public function callSwalByType($type) {
            try {
                $this->dispatchBrowserEvent('result', $type);
            } 
            
            catch (\Throwable $th) {
                Log::error("callSwalByType error: ".$th);
            }
        }





    public function deleteMilestone()
    {
        if ($this->milestoneId) {
            $this->dispatchBrowserEvent( 'close-modal', [ 'modalId' => 'deleteConfirmModalMilstone' ] );
            $this->dispatchBrowserEvent( 'show-loader' );
            $crmProjectsService = app( ProjectServices::class );
            $response = $crmProjectsService->deleteMilestone( $this->workspaceSlug, $this->milestoneId );

            $successMessage = __( 'CRMProjects.common.milestone_deleted_success' );
            if ( $response[ 'status' ] === 'success' ) {
                $this->dispatchBrowserEvent( 'show-toastr', [
                    'type' => 'success',
                    'message' => $successMessage
                ] );
            } else {
            $errorMessage = __('CRMProjects.common.error_message');
                $this->dispatchBrowserEvent('show-toastr', [
                    'type' => 'error',
                    'message' => $errorMessage
                ]);
            }
        }
        $this->resetCreateForm();
        $this->fetchData();
        $this->dispatchBrowserEvent('hide-loader');
    }



    public function resetCreateForm()
    {
        $this->name = '';
        $this->status = '';
        $this->cost = '';
        $this->summary = '';
        $this->start_date = '';
        $this->end_date = '';
        $this->resetErrorBag();
    }

    public function openDeleteModal( $id ,$name)
 {
        $this->projectID = $id;
        $this->name = $name;


        $this->dispatchBrowserEvent( 'open-modal', [ 'modalId' => 'deleteConfirmModal' ] );

    }

    public function openCreatedocument()
    {
        $this->reset([
            'subject',
            'type',
            'user_id',
            'description'
        ]);
        $this->dispatchBrowserEvent('open-modal', ['modalId' => 'createDocumentModal']);
    }


    public function submitDocumentForm()
    {
        $this->emitTo('c-r-m-projects.documents', 'submit', [
            'subject' => $this->subject,
            'type' => $this->type,
            'user' => $this->user,
            'description' => $this->description,
        ]);
        $this->fetchData();
        $this->dispatchBrowserEvent('reloadPage');

    }

    public function openDeleteFileModal( $id ,$name)
 {
            $this->FileToDelete = $id;
            $this->filenametoDelete = $name;


        $this->dispatchBrowserEvent( 'open-modal', [ 'modalId' => 'confirm-delete-file' ] );


    }


    public function updateDocumentForm()
    {
        $this->emitTo('c-r-m-projects.documents', 'update', [
            'subject' => $this->subject,
            'type' => $this->type,
            'user' => $this->user,
            'description' => $this->description,
        ]);
        $this->dispatchBrowserEvent('hide-loader');
        $this->fetchData();
        $this->dispatchBrowserEvent('close-modal');
    }


    public function deleteDocument()
    {
        $this->emitTo('c-r-m-projects.documents', 'delete');
        $this->dispatchBrowserEvent('hide-loader');
        $this->fetchData();
        $this->dispatchBrowserEvent('close-modal');
        $this->dispatchBrowserEvent('reloadPage');
    }



    public function delete( )
 {
        if ( $this->projectID  ) {
            $this->dispatchBrowserEvent( 'close-modal', [ 'modalId' => 'deleteConfirmModal' ] );
            $this->dispatchBrowserEvent( 'show-loader' );
            $crmProjectsService = app( ProjectServices::class );
            $response = $crmProjectsService->deleteProject( $this->workspaceSlug, $this->projectID );

            $successMessage = __( 'CRMProjects.common.project_deleted_success' );
            if ( $response[ 'status' ] === 'success' ) {

                $this->fetchData();
                $this->dispatchBrowserEvent('hide-loader');
                $this->dispatchBrowserEvent( 'show-toastr', [
                    'type' => 'success',
                    'message' => $successMessage
                ] );
                return redirect()->route('CRMProjects.list');
            } else {
                $errorMessage = __('CRMProjects.common.error_message');


            if ($response['message'] === "There are some Task and Bug on Project, please remove it first!") {
                $errorMessage = __('CRMProjects.common.error_task_bug_on_project');
            }

            $this->dispatchBrowserEvent('show-toastr', [
                'type' => 'error',
                'message' => $errorMessage
            ]);
            $this->dispatchBrowserEvent('hide-loader');
            }
        }

    }

    
    public function showListBMA(){
    
            $SelectplaceholderText = __( 'CRMProjects.common.choose_user' )?? __( 'CRMProjects.common.please_select' );
            $this->dispatchBrowserEvent( 'open-modal', [ 'modalId' => 'assign-bma' ] );
            $this->dispatchBrowserEvent( 'initSelect2', [ 'selectId' => 'selectedBuildingsManager', 'placeholderText'=>$SelectplaceholderText ] );
    }
    public function assignBMAToProject(){
        $this->validate([
            'selectedBuildingsManager' => 'required',
           
        ]);
     
     $this->dispatchBrowserEvent( 'close-modal', [ 'modalId' => 'assign-bma' ] );
     $this->dispatchBrowserEvent( 'show-loader' );
     $successMessage = __( 'CRMProjects.common.bma_assigned_successfully' );
     $errorMessage = __( 'CRMProjects.common.failed_to_assign_bma' );
     if ( $this->projectID ) {
         $crmProjectsService = app( ProjectServices::class );
         $response = $crmProjectsService->assignBMA( $this->workspaceSlug,  $this->projectID, $this->selectedBuildingsManager );
      
        if (isset($response['status']) && $response['status'] === 'success')  {
            $this->dispatchBrowserEvent( 'show-toastr', [
                'type' => 'success',
                'message' => $successMessage
            ] );
        }else{
            $this->dispatchBrowserEvent( 'show-toastr', [
                'type' => 'error',
                'message' => $response['message'] ??  $errorMessage
            ] );

        }
     
      $this->selectedBuildingsManager=[];
      $this->fetchData();
      $this->dispatchBrowserEvent( 'hide-loader' );
      
     } else {
         $this->dispatchBrowserEvent( 'show-toastr', [
             'type' => 'error',
             'message' => $errorMessage
         ] );
         $this->fetchData();
         $this->dispatchBrowserEvent( 'hide-loader' );
     }
       

     
    }
    // public function getAssignedBMA()

    // {
    //        $crmProjectsService = app( ProjectServices::class );
    //        $response = $crmProjectsService->getAssignedBMA( $this->workspaceSlug,$this->projectID );
    //         // dd($response);
    //        if ( isset( $response[ 'status' ] ) && $response[ 'status' ] === 'success' ) {

    //          $this->assignedBMAList =  data_get($response,'data',[]);
             
    //        }else
    //        {
    //         $this->assignedBMAList =  [];
        
    //        }
          
    //    }

       public function openDeletModalAssingBMA($id,$name){
        $this->dispatchBrowserEvent( 'show-loader' );
        $this->deleteAssignBMA['bma_id']=$id;
        $this->deleteAssignBMA['bma_name']=$name;
                $this->dispatchBrowserEvent( 'open-modal', [ 'modalId' => 'deleteAssignBMA' ] );
        $this->dispatchBrowserEvent( 'hide-loader' );
       }

       public function unassignBMA(){
        $successMessage = __( 'CRMProjects.common.bma_unassigned_successfully' );
        $errorMessage = __( 'CRMProjects.common.failed_to_unassign_bma' );
        $this->dispatchBrowserEvent( 'close-modal', [ 'modalId' => 'deleteAssignBMA' ] );
        $this->dispatchBrowserEvent( 'show-loader' );
       $bmaID= $this->deleteAssignBMA['bma_id'] ?? '';
        $service =  app(ProjectServices::class);
            $response = $service->deleteAssignBMA($this->workspaceSlug,$this->projectID, $bmaID);
       
            if (isset($response['status']) && $response['status'] === 'success')  {
                $this->dispatchBrowserEvent( 'show-toastr', [
                    'type' => 'success',
                    'message' => $successMessage
                ] );
            }else{
                $this->dispatchBrowserEvent( 'show-toastr', [
                    'type' => 'error',
                    'message' => $response['message'] ??  $errorMessage
                ] );
    
            }
            $this->fetchData();
        $this->dispatchBrowserEvent( 'hide-loader' );


       }

}

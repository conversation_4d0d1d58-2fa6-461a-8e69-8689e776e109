<div class="tab-pane fade show active" id="customer-details" role="tabpanel" aria-labelledby="home-tab">
    <div class="col-lg-12 px-0">
        <div class="row mb-3">
            {{-- Customer Info --}}
            <div class="col-md-4 mb-sm-0 mb-3 pr-md-0">
                <div class="p-3 address-box radius-xl h-100 card">
                    <h6 class="text-dark mb-3 fs-14">@lang('accounting.customer_info')</h6>
                    <table class="table mb-0 table-borderless new-header td-pl-0 table-address">
                        <tbody>
                            <tr>
                                <td><span class="fs-12 fw-50 text-dark fw-600">@lang('accounting.name')</span></td>
                                <td>{{ $listdata['name'] }}</td>
                            </tr>
                            <tr>
                                <td><span class="fs-12 fw-50 text-dark fw-600">@lang('accounting.email')</span></td>
                                <td>{{ $listdata['email'] }}</td>
                            </tr>
                            <tr>
                                <td><span class="fs-12 fw-50 text-dark fw-600">@lang('accounting.phone')</span></td>
                                <td>{{ $listdata['contact'] }}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            {{-- Billing Address --}}
            <div class="col-md-4 mb-sm-0 mb-3 pr-md-0">
                <div class="p-3 address-box radius-xl h-100 card">
                    <h6 class="text-dark mb-3 fs-14">@lang('accounting.billing_address_title')</h6>
                    <table class="table mb-0 table-borderless new-header td-pl-0 table-address">
                        <tbody>
                            <tr>
                                <td><span class="fs-12 fw-50 text-dark fw-600">@lang('accounting.billing_address')</span></td>
                                <td>{{ $listdata['billing_address'] }}</td>
                            </tr>
                            <tr>
                                <td><span class="fs-12 fw-50 text-dark fw-600">@lang('accounting.billing_city')</span></td>
                                <td>{{ $listdata['billing_city'] }}</td>
                            </tr>
                            <tr>
                                <td><span class="fs-12 fw-50 text-dark fw-600">@lang('accounting.billing_zip')</span></td>
                                <td>{{ $listdata['billing_state'] }}</td>
                            </tr>
                            <tr>
                                <td><span class="fs-12 fw-50 text-dark fw-600">@lang('accounting.billing_country')</span></td>
                                <td>{{ $listdata['billing_country'] }}</td>
                            </tr>
                            <tr>
                                <td><span class="fs-12 fw-50 text-dark fw-600">@lang('accounting.billing_contact')</span></td>
                                <td>{{ $listdata['billing_phone'] }}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            {{-- Shipping Address --}}
            <div class="col-md-4">
                <div class="p-3 address-box radius-xl h-100 card">
                    <h6 class="text-dark mb-3 fs-14">@lang('accounting.shipping_address_title')</h6>
                    <table class="table mb-0 table-borderless new-header td-pl-0 table-address">
                        <tbody>
                            <tr>
                                <td><span class="fs-12 fw-50 text-dark fw-600">@lang('accounting.shipping_address')</span></td>
                                <td>{{ $listdata['shipping_address'] }}</td>
                            </tr>
                            <tr>
                                <td><span class="fs-12 fw-50 text-dark fw-600">@lang('accounting.shipping_city')</span></td>
                                <td>{{ $listdata['shipping_city'] }}</td>
                            </tr>
                            <tr>
                                <td><span class="fs-12 fw-50 text-dark fw-600">@lang('accounting.shipping_zip')</span></td>
                                <td>{{ $listdata['shipping_state'] }}</td>
                            </tr>
                            <tr>
                                <td><span class="fs-12 fw-50 text-dark fw-600">@lang('accounting.shipping_country')</span></td>
                                <td>{{ $listdata['shipping_country'] }}</td>
                            </tr>
                            <tr>
                                <td><span class="fs-12 fw-50 text-dark fw-600">@lang('accounting.shipping_contact')</span></td>
                                <td>{{ $listdata['shipping_phone'] }}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        {{-- Company Info --}}
        <div class="card mb-3">
            <div class="card-header d-flex justify-content-start gap-5">
                <h6>@lang('accounting.company_info')</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 col-6">
                        <label class="fw-600 text-dark">@lang('accounting.vendor_id')</label>
                        <p>{{ $listdata['customer_id'] }}</p>
                    </div>
                    <div class="col-md-3 col-6">
                        <label class="fw-600 text-dark">@lang('accounting.created_at')</label>
                        <p>{{ date('d/m/Y', strtotime($listdata['created_at'])) }}</p>
                    </div>
                    <div class="col-md-3 col-6">
                        <label class="fw-600 text-dark">@lang('accounting.balance')</label>
                        <div class="d-flex align-items-center gap-10">
                            <img src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSx34eO8RbZlsetN1iAQtxuOLnLOlyJbJmU7Q&s" width="15">
                            <span class="text-new-primary">{{ $listdata['balance'] }}</span>
                        </div>
                    </div>
                    <div class="col-md-3 col-6 mb-3">
                        <label class="fw-600 text-dark">@lang('accounting.overdue')</label>
                        <div class="d-flex align-items-center gap-10">
                            <img src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSx34eO8RbZlsetN1iAQtxuOLnLOlyJbJmU7Q&s" width="15">
                            <span class="text-new-primary">{{ $listdata['overdue'] }}</span>
                        </div>
                    </div>
                    <div class="col-md-3 col-6">
                        <label class="fw-600 text-dark">@lang('accounting.total_bills')</label>
                        <div class="d-flex align-items-center gap-10">
                            <img src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSx34eO8RbZlsetN1iAQtxuOLnLOlyJbJmU7Q&s" width="15">
                            <span class="text-new-primary">{{ $listdata['total_sum_of_invoices'] }}</span>
                        </div>
                    </div>
                    <div class="col-md-3 col-6">
                        <label class="fw-600 text-dark">@lang('accounting.invoice_qty')</label>
                        <p>{{ $listdata['invoice_quantity'] }}</p>
                    </div>
                    <div class="col-md-3 col-6">
                        <label class="fw-600 text-dark">@lang('accounting.avg_sales')</label>
                        <div class="d-flex align-items-center gap-10">
                            <img src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSx34eO8RbZlsetN1iAQtxuOLnLOlyJbJmU7Q&s" width="15">
                            <span class="text-new-primary">{{ $listdata['avg_sales'] }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


@extends('layouts.app')
@section('styles')
@endsection
@section('content')

        @include('components.contract-steps', ['currentStep' => "4", 'isVariationOrder' => $isVariationOrder,'savedSteps' => $savedSteps])
        <div class="col-lg-8">
            <livewire:advance-contracts.step-four :uuid="$uuid" :prefix="$route_prefix"/>
            <!-- ends: col -->
        </div>
    </div>
    <!-- End: .global-shadow-->
</div>

</div>
</div>



@endsection

@section('scripts')
<script type="text/javascript">
    $('.datepicker').datepicker();

    $(document).ready(function () {
    // Remove row on delete button click using event delegation
    $('#tableBody').on('click', '.remove', function () {
        $(this).closest('tr').remove();
    });
});
</script>
@endsection
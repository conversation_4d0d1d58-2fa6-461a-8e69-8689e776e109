<!-- OS3-2355 Adding new fields for workers -->
<div class="accordion radius-md mt-3 border"  id="sp_worker_section">

    <div class="border-0">
        <!-- Accordion Header -->
        <div class="card-header radius-md border-0 px-0 min-h-0" id="headingOne">
            <h2 class="mb-0 w-100">
                <button class="align-items-center radius-md border-0 btn btn-block collapsed d-flex justify-content-between p-1" type="button" data-toggle="collapse" data-target="#extra_info" aria-expanded="true" aria-controls="extra_info">
                    <span class="text-dark">{{ __('advance_contracts.worker.advanced_information') }}</span>
                    <i class="iconsax ml-2" icon-name="chevron-down"></i>
                </button>
            </h2>
        </div>

        <!-- Accordion Collapsible Body -->
        <div id="extra_info" class="collapse px-15 border-0" data-parent="#sp_worker_section">

            <div>
                <div class="form-group mb-20 mt-20">
                    <div class="salary">
                         <label for="salary">{{ __('advance_contracts.worker.salary_monthly') }}</label>
                    </div>
                    <input type="text" class="form-control" name="salary" id="salary"
                        placeholder="{{ __('advance_contracts.worker.enter_amount') }}"
                        value="{{ isset($data['u_data']->salary) ? $data['u_data']->salary : '' }}">
                </div>

                <div class="form-group mb-20">
                    <div class="attendance_target">
                         <label for="attendance_target">{{ __('user_management_module.user_forms.label.working_hours_per_day') }}</label>
                    </div>
                    <input type="text" class="form-control" name="attendance_target" id="attendance_target"
                        placeholder="{{ __('advance_contracts.worker.enter_value') }}"
                        value="{{ isset($data['u_data']->attendance_target) ? $data['u_data']->attendance_target : '' }}">
                </div>

                <div class="form-group mb-20 user_info">
                    <label for="role">{{ __('advance_contracts.worker.role') }}</label>
                    <select class="form-control" id="role" name="role">
                        <option value="" disabled {{ empty($data['u_data']->role) ? 'selected' : '' }}>{{ __('advance_contracts.worker.select_option') }}</option>
                        @foreach ($data['roleOptions'] as $value => $label)
                        <option value="{{ $value }}" {{ (old('role', $data['u_data']->role ?? '') == $value) ? 'selected' : '' }}>
                            {{ $label }}
                        </option>
                        @endforeach
                    </select>
                    <div id="role-level-error"></div>
                </div>

                <div class="form-group mb-20 user_info">
                    <label for="admin_level">{{ __('advance_contracts.worker.admin_level') }}</label>
                    <select class="form-control" id="admin_level" name="admin_level">
                        <option value="" disabled {{ empty($data['u_data']->admin_level) ? 'selected' : '' }}>{{ __('advance_contracts.worker.select_option') }}</option>
                        @foreach ($data['proficiencyOptions'] as $value => $label)
                        <option value="{{ $value }}" {{ (old('admin_level', $data['u_data']->admin_level ?? '') == $value) ? 'selected' : '' }}>
                            {{ $label }}
                        </option>
                        @endforeach
                    </select>
                    <div id="admin-level-error"></div>
                </div>


                <!-- Attendance Mandatory Radio Buttons (Inline) -->
                <div class="form-group mb-20 user_info">
                    <label class="d-block">{{ __('advance_contracts.worker.attendance_mandatory') }}</label>
                    <div class="form-check form-check-inline">
                        <input class="form-check-input" type="radio" name="attendance_mandatory" id="attendance_yes" value="1"
                            {{ isset($data['u_data']->attendance_mandatory) && $data['u_data']->attendance_mandatory == 1 ? 'checked' : '' }}>
                        <label class="form-check-label" for="attendance_yes">{{ __('advance_contracts.worker.yes') }}</label>
                    </div>
                    <div class="form-check form-check-inline">
                        <input class="form-check-input" type="radio" name="attendance_mandatory" id="attendance_no" value="0"
                            {{ isset($data['u_data']->attendance_mandatory) && $data['u_data']->attendance_mandatory == 0 ? 'checked' : '' }}>
                        <label class="form-check-label" for="attendance_no">{{ __('advance_contracts.worker.no') }}</label>
                    </div>
                    <div id="attendance-mandatory-error" class="mt-1 text-danger"></div>
                </div>

            </div>
        </div>
    </div>
</div>

<div>
    <div class = "modal fade new-member" id = "affect_users_modal" tabindex = "-1" role = "dialog" aria-labelledby = "exampleModalLabel" aria-hidden = "true" style = "background: rgba(0,0,0,.3);" data-backdrop = "static" data-keyboard = "false" wire:ignore.self>
        <div class = "modal-dialog  modal-xl modal-dialog-centered" role = "document">
            <div class = "modal-content">
                <div class = "modal-header">
                    <h5 class = "modal-title text-capitalize" id = "exampleModalLabel">@lang('import.affectation')</h5>
                    <button wire:ignore type = "button" class = "close border-0" data-dismiss = "modal" aria-label = "Close">
                        <span aria-hidden = "true">&times;</span> 
                    </button>
                </div>
                <div class = "modal-body">
                    <div class = "alert alert-warning alert-dismissible fade show py-3" role = "alert">
                        <ul>
                            <li>
                                <p>@lang('import.affectation_text')</p>
                            </li>
                        </ul>
                        <button type = "button" class = "close" data-dismiss = "alert" aria-label = "Close" style = "margin-top: -15px">
                            <span aria-hidden = "true" style = "font-size: 28px">&times;</span>
                        </button>
                    </div>
                    <div class = "table-responsive mt-2">
                        <table class = "table">
                            <thead class = "userDatatable-header">
                                <tr class = "thead-default">
                                    <th class = "text-dark text-capitalize">@lang('import.name2')</th>
                                    <th class = "text-dark text-capitalize">@lang('import.type2')</th>
                                    <th class = "text-dark text-capitalize">@lang('import.company')</th>
                                    <th class = "text-dark text-capitalize">@lang('import.admin')</th>
                                </tr>
                            </thead>
                            <tbody> 
                                @if(isset($usersList) && count($usersList) > 0)
                                    @foreach ($usersList as $key => $data)
                                        <tr wire:key = "users-item-{{ $key }}">
                                            <td>{{ $data['user_name'] ?? '-' }}</td>
                                            <td class = "text-capitalize">{{ $data['user_type'] ?? '-' }}</td>
                                            <td wire:ignore>
                                                @switch(strtolower($data['user_type']))
                                                    @case('service provider admin')
                                                        <select class = "form-control mb-3 select2-select" style = "height:50px;" name = "company" onchange = "setSelectedCompany(this, `{{ $data['email'] }}`)">
                                                            <option value = "" selected disabled>@lang('import.company')</option>

                                                            @if(isset($serviceProviderList) && $serviceProviderList->count())
                                                                @foreach ($serviceProviderList as $item)
                                                                    <option value = "{{ $item->serviceProvider->id ?? '' }}">{{ $item->serviceProvider->name.'-'. $item->serviceProvider->service_provider_id ?? '-' }}</option>  
                                                                @endforeach
                                                            @endif
                                                        </select>
                                                    @break

                                                     @case('service provider supervisor')
                                                        <select class = "form-control mb-3 select2-select" style = "height:50px;" name = "company" onchange = "setSelectedCompanySPS(this, `{{ $data['email'] }}`)">
                                                            <option value = "" selected disabled>@lang('import.company')</option>

                                                            @if(isset($serviceProviderList) && $serviceProviderList->count())
                                                                @foreach ($serviceProviderList as $item)
                                                                    <option value = "{{ $item->serviceProvider->id ?? '' }}">{{ $item->serviceProvider->name.'-'. $item->serviceProvider->service_provider_id ?? '-' }}</option>  
                                                                @endforeach
                                                            @endif
                                                        </select>
                                                    @break

                                                    @case('service provider worker')
                                                        <select class = "form-control mb-3 select2-select" style = "height:50px;" name = "company" onchange = "setSelectedCompanyWorker(this, `{{ $data['email'] }}`)">
                                                            <option value = "" selected disabled>@lang('import.company')</option>

                                                            @if(isset($serviceProviderList) && $serviceProviderList->count())
                                                                @foreach ($serviceProviderList as $item)
                                                                    <option value = "{{ $item->serviceProvider->id ?? '' }}">{{ $item->serviceProvider->name.'-'. $item->serviceProvider->service_provider_id ?? '-' }}</option>  
                                                                @endforeach
                                                            @endif
                                                        </select>
                                                    @break

                                                    @default
                                                        @lang('import.not_available')
                                                    @break
                                                        
                                                @endswitch
                                            </td>
                                            <td wire:ignore>
                                                @switch(strtolower($data['user_type']))
                                                    @case('building manager employee')
                                                        <select class = "form-control mb-3 select2-select" style = "height:50px;" name = "building_manager" onchange = "setSelectedBMA(this, `{{ $data['email'] }}`)">
                                                            <option value = "" selected disabled>@lang('import.building_manager_label')</option>

                                                            @if(isset($buildingManagerList) && $buildingManagerList->count())
                                                                @foreach ($buildingManagerList as $item)
                                                                    <option value = "{{ $item->id ?? '' }}">{{ $item->name ?? '-' }}</option>  
                                                                @endforeach
                                                            @endif
                                                        </select>
                                                    @break

                                                    @case('service provider supervisor')
                                                        <select class = "form-control mb-3 select2-select sp_admin" style = "height:50px;" name = "sp_admin_{{ $data['email'] }}" onchange = "setSelectedSPAForSPS(this, `{{ $data['email'] }}`)">
                                                            <option value = "" selected disabled>@lang('import.service_provider_admin_label')</option>
                                                        </select>
                                                    @break

                                                    @case('service provider worker')
                                                        <select class = "form-control mb-3 select2-select sp_supervisor" style = "height:50px;" name = "sp_supervisor_{{ $data['email'] }}" onchange = "setSelectedSPAForSPW(this, `{{ $data['email'] }}`)">
                                                            <option value = "" selected disabled>@lang('import.service_provider_supervisor_label')</option>
                                                        </select>
                                                    @break

                                                    @default
                                                        @lang('import.not_available')
                                                    @break
                                                @endswitch
                                            </td>
                                        </tr>
                                    @endforeach
                                @else
                                    <tr class = "text-center">
                                        <td colspan = "4">@lang("import.empty_users")</td>
                                    </tr>
                                @endif
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class = "modal-footer">
                    <div class = "button-group d-flex pt-10 justify-content-end mb-40">
                        <a href = "javascript:void(0)" class = "btn btn-danger btn-default btn-squared text-capitalize radius-md shadow2 btn-md" data-dismiss = "modal">
                            @lang('import.close')
                        </a>
                        <div wire:loading class = "text-center" wire:target = "applyAffectation">
                            <button type = "button" class = "btn btn-primary btn-default btn-squared text-capitalize radius-md shadow2 btn-md mx-auto" wire:loading.attr = "disabled">
                                <span class = "spinner-border spinner-border-sm" role = "status" aria-hidden = "true"></span> 
                                @lang('work_order.common.loading')
                            </button>
                        </div>
                        <button type = "button" class = "btn btn-primary btn-default btn-squared text-capitalize radius-md shadow2 btn-md mx-auto" wire:loading.class = "hide" wire:target = "applyAffectation" wire:click = "applyAffectation">
                            @lang('import.apply')
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
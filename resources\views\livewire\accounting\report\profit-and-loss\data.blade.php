<div>
    <div class="col-lg-12">
        <div
            class="row justify-content-sm-between align-items-center justify-content-center my-3 flex-sm-row flex-column">
            @include('applications.admin.common.breadcrumb', [
                'links' => [
                    [
                        'title' => __('accounting.report'),
                    ],
                    [
                        'title' => __('accounting.profit_n_loss_sum'),
                    ],
                ],
            ])

            <div class="d-flex gap-10 breadcrumb_right_icons">
                <div class="d-flex gap-10 breadcrumb_right_icons">
                    <button onclick="showLoader()" wire:click='downloadPdf' data-toggle="tooltip" data-placement="top" title="@lang('accounting.download_pdf')" class="btn btn-white btn-default text-center svg-20 px-2 wh-45">
                        <i class="iconsax icon text-new-primary fs-22 colorRed mr-0" icon-name="download-1"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="table-responsive">

        <div class="card mb-3">
            <div class="card-body">
                <form class="fs-14">
                    <div class="d-flex justify-content-end gap-10">

                        <div data-select2-id="106" class="pr-3">
                            <label class="text-osool fw-600">@lang('accounting.year')</label>
                            <select class="form-control" wire:model='year' data-allow-clear="false">
                                @foreach ($years as $item)
                                    <option value="{{ $item }}">{{ $item }}</option>
                                @endforeach
                            </select>
                        </div>

                        <div>
                            <label for="" class="d-md-block d-none">&nbsp;</label>
                            <div class="d-flex gap-10 justify-content-end">
                                <button type="button" onclick="showLoader()" wire:click.prevent='filter'
                                    class="btn btn-white btn-default text-center svg-20 px-2 wh-45">
                                    <i class="bg-inprogress bg-opacity-loss btn btn-sm d-flex fs-18 icon iconsax radius-md text-white wh-45"
                                        data-toggle="tooltip" data-placement="top" title="@lang('accounting.apply')"
                                        icon-name="search-normal-2"></i>
                                </button>
                                <button type="button" onclick="showLoader()" wire:click.prevent='resetFilter'
                                    class="btn btn-white btn-default text-center svg-20 px-2 wh-45">
                                    <i class="bg-secondary bg-opacity-loss btn btn-sm d-flex fs-18 icon iconsax radius-md text-white wh-45"
                                        data-toggle="tooltip" data-placement="top" title="@lang('accounting.reset')" icon-name="trash"></i>
                                </button>

                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <div id="printableArea">
            <div class="row">
                <div class="col-6 pl-0">
                    <div class="card p-4 mb-3 h-110">
                        <h5 class="report-text mb-0">@lang('accounting.report') :</h5>
                        <p class="report-text mb-0">@lang('accounting.profit_n_loss_sum')</p>
                    </div>
                </div>

                <div class="col-6 pr-0">
                    <div class="card p-4 mb-3 h-110">
                        <h5 class="report-text mb-0">@lang('accounting.duration') :</h5>
                        <p class="report-text mb-0">Jan-{{ $year }} to Dec-{{ $year }}</p>
                    </div>
                </div>

            </div>
        </div>
    </div>


    <div class="card">
        <div>
            <div class="py-4 px-3 border-0">
                <h5>@lang('accounting.income')</h5>
            </div>
        </div>

        <div class="card-body px-0 pt-0 pb-0">
            <div class="userDatatable projectDatatable project-table bg-white w-100 border-0">
                <div class="table-responsive">
                    <table class="table mb-0 radius-0 th-osool">

                        <thead>
                            <tr class="userDatatable-header">
                                <th>
                                    @lang('accounting.category')
                                </th>
                                <th>
                                    Jan-Mar
                                </th>
                                <th>
                                    Apr-Jun
                                </th>
                                <th>
                                    Jul-Sep
                                </th>
                                <th>
                                    Oct-Dec
                                </th>
                                <th>
                                    @lang('accounting.total')
                                </th>
                            </tr>
                        </thead>

                        <tbody class="sort-table ui-sortable">
                            <tr>
                                <td>
                                    <span class="userDatatable-content">@lang('accounting.revenue') :</span>
                                </td>
                            </tr>

                            @foreach (@$apiData['revenueIncomeArray']??[] as $item)
                                <tr class="ui-sortable-handle" style="opacity: 1;">
                                    <td>
                                        <div class="d-flex userDatatable-content mb-0 align-items-center">
                                            <span>{{ $item['category'] }}</span>
                                        </div>
                                    </td>
                                    @foreach ($item['amount'] as $datum)
                                        <td>
                                            <div class="d-flex userDatatable-content mb-0 align-items-center">
                                                <span>{!! $currency !!}
                                                    {{ Helper::human_readable_number($datum) }}</span>
                                            </div>
                                        </td>
                                    @endforeach
                                </tr>
                            @endforeach

                            <tr>
                                <td>
                                    <span class="userDatatable-content">@lang('accounting.invoice') :</span>
                                </td>
                            </tr>

                            @foreach (@$apiData['invoiceIncomeArray']??[] as $item)
                                <tr class="ui-sortable-handle" style="opacity: 1;">
                                    <td>
                                        <div class="d-flex userDatatable-content mb-0 align-items-center">
                                            <span>{{ $item['category'] }}</span>
                                        </div>
                                    </td>
                                    @foreach ($item['amount'] as $datum)
                                        <td>
                                            <div class="d-flex userDatatable-content mb-0 align-items-center">
                                                <span>{!! $currency !!}
                                                    {{ Helper::human_readable_number($datum) }}</span>
                                            </div>
                                        </td>
                                    @endforeach
                                </tr>
                            @endforeach

                            <tr>
                                <td colspan="6">
                                    <span class="userDatatable-content">@lang('accounting.total_income_calc')</span>
                                </td>
                            </tr>

                            <tr class="ui-sortable-handle" style="opacity: 1;">
                                <td>
                                    <div class="d-flex userDatatable-content mb-0 align-items-center">
                                        <span>@lang('accounting.total_income')</span>
                                    </div>
                                </td>
                                @foreach (@$apiData['totalIncome']??[] as $datum)
                                    <td>
                                        <div class="d-flex userDatatable-content mb-0 align-items-center">
                                            <span>{!! $currency !!}
                                                {{ Helper::human_readable_number($datum) }}</span>
                                        </div>
                                    </td>
                                @endforeach
                            </tr>

                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <div>
            <div class="pt-4 pb-2 px-3 border-0">
                <h5>@lang('accounting.expense')</h5>
            </div>
        </div>

        <div class="card-body px-0 pt-0 pb-0">
            <div class="userDatatable projectDatatable project-table bg-white w-100 border-0">
                <div class="table-responsive">
                    <table class="table mb-0 radius-0 th-osool">

                        <thead>
                            <tr class="userDatatable-header">
                                <th>
                                    @lang('accounting.category')
                                </th>
                                <th>
                                    Jan-Mar
                                </th>
                                <th>
                                    Apr-Jun
                                </th>
                                <th>
                                    Jul-Sep
                                </th>
                                <th>
                                    Oct-Dec
                                </th>
                                <th>
                                    Total
                                </th>
                            </tr>
                        </thead>

                        <tbody class="sort-table ui-sortable">
                            <tr>
                                <td>
                                    <span class="userDatatable-content">@lang('accounting.payment') :</span>
                                </td>
                            </tr>

                            @foreach (@$apiData['expenseArray']??[] as $item)
                                <tr class="ui-sortable-handle" style="opacity: 1;">
                                    <td>
                                        <div class="d-flex userDatatable-content mb-0 align-items-center">
                                            <span>{{ $item['category'] }}</span>
                                        </div>
                                    </td>
                                    @foreach ($item['amount'] as $datum)
                                        <td>
                                            <div class="d-flex userDatatable-content mb-0 align-items-center">
                                                <span>{!! $currency !!}
                                                    {{ Helper::human_readable_number($datum) }}</span>
                                            </div>
                                        </td>
                                    @endforeach
                                </tr>
                            @endforeach

                            <tr class="ui-sortable-handle" style="opacity: 1;">
                                <td>
                                    <div class="d-flex userDatatable-content mb-0 align-items-center">
                                        <span>@lang('accounting.bill') :</span>
                                    </div>
                                </td>
                            </tr>

                            @foreach (@$apiData['billExpenseArray']??[] as $item)
                                <tr class="ui-sortable-handle" style="opacity: 1;">
                                    <td>
                                        <div class="d-flex userDatatable-content mb-0 align-items-center">
                                            <span>{{ $item['category'] }}</span>
                                        </div>
                                    </td>
                                    @foreach ($item['amount'] as $datum)
                                        <td>
                                            <div class="d-flex userDatatable-content mb-0 align-items-center">
                                                <span>{!! $currency !!}
                                                    {{ Helper::human_readable_number($datum) }}</span>
                                            </div>
                                        </td>
                                    @endforeach
                                </tr>
                            @endforeach

                            <tr class="ui-sortable-handle" style="opacity: 1;">
                                <td>
                                    <div class="d-flex userDatatable-content mb-0 align-items-center">
                                        <span>@lang('accounting.employee_salary') :</span>
                                    </div>
                                </td>
                                @foreach (@$apiData['salExpenseCatAmount']??[] as $datum)
                                    <td>
                                        <div class="d-flex userDatatable-content mb-0 align-items-center">
                                            <span>{!! $currency !!}
                                                {{ Helper::human_readable_number($datum) }}</span>
                                        </div>
                                    </td>
                                @endforeach
                            </tr>

                            <tr>
                                <td colspan="6">
                                    <span class="userDatatable-content">@lang('accounting.total_expense_calc')</span>
                                </td>
                            </tr>

                            <tr class="ui-sortable-handle" style="opacity: 1;">
                                <td>
                                    <div class="d-flex userDatatable-content mb-0 align-items-center">
                                        <span>@lang('accounting.total_expenses')</span>
                                    </div>
                                </td>
                                @foreach (@$apiData['totalExpense']??[] as $datum)
                                    <td>
                                        <div class="d-flex userDatatable-content mb-0 align-items-center">
                                            <span>{!! $currency !!}
                                                {{ Helper::human_readable_number($datum) }}</span>
                                        </div>
                                    </td>
                                @endforeach
                            </tr>

                        </tbody>
                    </table>
                </div>
            </div>
        </div>




        <div class="userDatatable projectDatatable project-table bg-white w-100 border-0 mt-4">
            <div class="table-responsive">
                <table class="table mb-0 radius-0 th-osool">


                    <tbody class="sort-table ui-sortable">

                        <tr>
                            <td colspan="6">
                                <div class="border-0">
                                    <span class="userDatatable-content">@lang('accounting.net_profit_calc')</span>
                                </div>
                            </td>
                        </tr>

                        <tr class="ui-sortable-handle" style="opacity: 1;">
                            <td>
                                <div class="mb-0 min-w-150 userDatatable-content">
                                    <span>@lang('accounting.net_profit')</span>
                                </div>
                            </td>
                            @foreach (@$apiData['netProfitArray']??[] as $datum)
                                <td>
                                    <div class="d-flex userDatatable-content mb-0 align-items-center">
                                        <span>{!! $currency !!}
                                            {{ Helper::human_readable_number($datum) }}</span>
                                    </div>
                                </td>
                            @endforeach
                        </tr>

                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<?php
    namespace App\Jobs\BulkImport;
    use Illuminate\Bus\Queueable;
    use Illuminate\Bus\Batchable;
    use Illuminate\Contracts\Queue\ShouldBeUnique;
    use Illuminate\Contracts\Queue\ShouldQueue;
    use Illuminate\Foundation\Bus\Dispatchable;
    use Illuminate\Queue\InteractsWithQueue;
    use Illuminate\Queue\SerializesModels;
    use Illuminate\Support\Facades\Log;
    use App\Http\Traits\ProjectDetailTrait;
    use App\Http\Traits\BulkImportErrorTrait;
    use App\Http\Traits\BulkImportListTrait;
    use App\Http\Traits\BulkImportTrait;
    use App\Http\Traits\PropertyTrait; 
    use App\Http\Traits\RoomTypeTrait; 
    use App\Http\Traits\PropertyBuildingTrait; 
    use App\Http\Traits\RoomsTypeFloorTrait;
    use App\Http\Traits\FunctionsTrait;
    use App\Enums\ResultType; 
    use App\Enums\ModelAction;
    use App\Enums\Status;
    use App\Enums\ValidationBukImport;

    class PropertiesBuildingImportJob implements ShouldQueue{
        use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, Batchable, ProjectDetailTrait, BulkImportErrorTrait, BulkImportListTrait, BulkImportTrait, PropertyTrait, PropertyBuildingTrait, RoomTypeTrait, RoomsTypeFloorTrait, FunctionsTrait;
        public $list;
        public $projectId;
        public $bulkImportDetailsId;
        public $projectUserId;
        public $userId;

        /**
         * Create a new job instance.
         *
         * @return void
         */
        public function __construct($list, $projectId, $bulkImportDetailsId, $projectUserId, $userId){
            $this->list = $list;
            $this->projectId = $projectId;
            $this->bulkImportDetailsId = $bulkImportDetailsId;
            $this->projectUserId = $projectUserId;
            $this->userId = $userId;
        }

        /**
         * Execute the job.
         *
         * @return void
         */
        public function handle(){
            try {
                $bulkArray = [];
                $project = $this->getProjectDetailInformationByProjectId($this->projectId);

                if(is_null($project)){
                    Log::info("PropertiesBuildingImportJob error: No project found with this projectId : ".$this->projectId); 
                }

                elseif(!isset($this->list)){
                    Log::info("PropertiesBuildingImportJob error: The buildings list is empty"); 
                }

                else{
                    foreach($this->list as $data){ 
                        $mapChecking = $this->fullBuildingsValidation($data);

                        if(!is_null($mapChecking) && $mapChecking[0]['status'] <> 'success'){
                            $bulkArrayErrors = ['map_status' => true, 'backend_status' => false, 'project_id' => $this->projectId, 'user_id' => $this->userId, 'bulk_import_id' => $this->bulkImportDetailsId, 'sheet' => ResultType::PropertyBuilding->value, 'identifier' => $data['building_name'], 'row_status' => ModelAction::NotInserted->value, 'created_by' => $this->userId];
                            $bulkImportErrorId = $this->saveBulkImportError($bulkArrayErrors);

                            if(!$bulkImportErrorId){
                                Log::info("PropertiesBuildingImportJob error: Cannot save the bulk import error row for Buildings sheet : Project ID: ".$this->projectId.", Property Name: ".$data['property_name'].', Building Name: '.$data['building_name']); 
                            }
                        }

                        else{
                            $propertyRow = $this->getPropertyInformationsByValues('property_tag', $data['property_name'], $this->projectUserId);

                            if(!isset($propertyRow)){
                                $bulkArrayErrors = ['map_status' => false, 'backend_status' => true, 'project_id' => $this->projectId, 'user_id' => $this->userId, 'bulk_import_id' => $this->bulkImportDetailsId, 'sheet' => ResultType::PropertyBuilding->value, 'identifier' => $data['property_name'], 'errors' => ValidationBukImport::PropertyNotExist->value, 'value' => $data['property_name'], 'row_status' => ModelAction::NotInserted->value, 'created_by' => $this->userId];
                                $bulkImportErrorId = $this->saveBulkImportError($bulkArrayErrors);

                                if(!$bulkImportErrorId){
                                    Log::info("PropertiesBuildingImportJob error: Cannot save the bulk import error row for Buildings sheet : Project ID: ".$this->projectId.", Property Name: ".$data['property_name']); 
                                }
                            }

                            else{
                                $buildingName = $propertyRow->property_type == 'building' ? $data['property_name'] : $data['building_name'];
                                $buildingTag = $propertyRow->property_type == 'building' ? $buildingName : $data['property_name']." ".$buildingName;

                                $array = [
                                    'property_id' => $propertyRow->id ?? null,
                                    'building_name' => $buildingName ?? null,
                                    'building_tag' => $buildingTag ?? null,
                                    'last_ip' => $this->getCurrentIpAddress()
                                ]; 

                                $newPropertyBuildingId = $this->savePropertyBuilding($array);

                                if($newPropertyBuildingId){
                                    array_push($bulkArray, $newPropertyBuildingId);

                                    $array = [
                                        'room_type' => $data['unit_type'] ?? null,
                                        'user_id' => $this->projectUserId ?? null,
                                        'last_ip' => $this->getCurrentIpAddress()
                                    ];

                                    $roomTypeRow = $this->getRoomTypeInformationsByValues('room_type', $data['unit_type'], $this->projectUserId);
                                    $newRoomTypeId = isset($roomTypeRow) ? $roomTypeRow->id : $this->saveRoomType($array);

                                    if($newRoomTypeId){
                                        $array = [
                                            'room_type' => $newRoomTypeId ?? null,
                                            'room' => $data['unit'] ?? null,
                                            'floor' => $data['zone'] ?? null,
                                            'barcodeCheck' => true ?? false,
                                            'property_id' => $propertyRow->id ?? null,
                                            'building_id' => $newPropertyBuildingId
                                        ];

                                        $roomTypeFloorRow = $this->getRoomTypeFloorByDataNew($data['zone'], $data['unit'], $newRoomTypeId, $newPropertyBuildingId, $propertyRow->id);
                                        $newRoomTypeFloorId = isset($roomTypeFloorRow) ? $roomTypeFloorRow->id : $this->saveRoomTypeFloor($array);

                                        if($newRoomTypeFloorId){
                                            $array = [
                                                'barcode_value' => $this->generateRandomNumber(1000000000, 9999999999),
                                                'barcode_img_str' => base64_encode($this->secondGenerateQrCode(100, $newRoomTypeFloorId))
                                            ];

                                            $newUpdatedRoomtypeFloor = $this->updateRoomTypeFloorById($newRoomTypeFloorId, $array);

                                            if(!$newUpdatedRoomtypeFloor){
                                                $bulkArrayErrors = ['map_status' => false, 'backend_status' => true, 'project_id' => $this->projectId, 'user_id' => $this->userId, 'bulk_import_id' => $this->bulkImportDetailsId, 'sheet' => ResultType::PropertyBuilding->value, 'identifier' => $data['property_name'], 'errors' => ValidationBukImport::RoomTypeFloorNotUpdated->value, 'value' => $data['zone'], 'row_status' => ModelAction::NotInserted->value, 'created_by' => $this->userId];
                                                $bulkImportErrorId = $this->saveBulkImportError($bulkArrayErrors);

                                                if(!$bulkImportErrorId){
                                                    Log::info("PropertiesBuildingImportJob error: Cannot save the bulk import error row for Buildings sheet : Project ID: ".$this->projectId.", Property Name: ".$data['property_name']); 
                                                }
                                            }
                                        }

                                        else{
                                            $bulkArrayErrors = ['map_status' => false, 'backend_status' => true, 'project_id' => $this->projectId, 'user_id' => $this->userId, 'bulk_import_id' => $this->bulkImportDetailsId, 'sheet' => ResultType::PropertyBuilding->value, 'identifier' => $data['property_name'], 'errors' => ValidationBukImport::RoomTypeFloorNotSaved->value, 'value' => $data['zone'], 'row_status' => ModelAction::NotInserted->value, 'created_by' => $this->userId];
                                            $bulkImportErrorId = $this->saveBulkImportError($bulkArrayErrors);

                                            if(!$bulkImportErrorId){
                                                Log::info("PropertiesBuildingImportJob error: Cannot save the bulk import error row for Buildings sheet : Project ID: ".$this->projectId.", Property Name: ".$data['property_name']); 
                                            }
                                        }
                                    }

                                    else{
                                        $bulkArrayErrors = ['map_status' => false, 'backend_status' => true, 'project_id' => $this->projectId, 'user_id' => $this->userId, 'bulk_import_id' => $this->bulkImportDetailsId, 'sheet' => ResultType::PropertyBuilding->value, 'identifier' => $data['property_name'], 'errors' => ValidationBukImport::RoomTypeNotSaved->value, 'value' => $data['unit_type'], 'row_status' => ModelAction::NotInserted->value, 'created_by' => $this->userId];
                                        $bulkImportErrorId = $this->saveBulkImportError($bulkArrayErrors);

                                        if(!$bulkImportErrorId){
                                            Log::info("PropertiesBuildingImportJob error: Cannot save the bulk import error row for Buildings sheet : Project ID: ".$this->projectId.", Property Name: ".$data['property_name']); 
                                        }
                                    }
                                }

                                else{
                                    $bulkArrayErrors = ['map_status' => false, 'backend_status' => true, 'project_id' => $this->projectId, 'user_id' => $this->userId, 'bulk_import_id' => $this->bulkImportDetailsId, 'sheet' => ResultType::PropertyBuilding->value, 'identifier' => $data['property_name'], 'errors' => ValidationBukImport::PropertyBuildingNotSaved->value, 'value' => $data['building_name'], 'row_status' => ModelAction::NotInserted->value, 'created_by' => $this->userId];
                                    $bulkImportErrorId = $this->saveBulkImportError($bulkArrayErrors);

                                    if(!$bulkImportErrorId){
                                        Log::info("PropertiesBuildingImportJob error: Cannot save the bulk import error row for Buildings sheet : Project ID: ".$this->projectId.", Property Name: ".$data['property_name']); 
                                    }
                                }
                            }
                        }
                    }
                }

                $implodedValue = isset($bulkArray) && count($bulkArray) > 0 ? $this->implodeDataFromField($bulkArray) : null;
                $bulkImportList = $this->getBulkImportListByValues('bulk_import_id', $this->bulkImportDetailsId);
                $result = null;

                $array = [
                    'buildings' => $implodedValue,
                    'project_id' => $this->projectId,
                    'bulk_import_id' => $this->bulkImportDetailsId,
                    'created_by' => !isset($bulkImportList) ? $this->userId : $bulkImportList->created_by,
                    'updated_by' => isset($bulkImportList) ? $this->userId : null,
                ];
                
                if(isset($bulkImportList)){
                    $result = $this->updateBulkImportListByValues('bulk_import_id', $this->bulkImportDetailsId, $array);
                }

                else{
                    $result = $this->saveBulkImportList($array);
                }

                if(!$result){
                    Log::info("PropertiesBuildingImportJob error: We cannot do any action on bulk import table for buildings column (Same data)"); 
                }
            } 
            
            catch (\Throwable $th) {
                Log::error("PropertiesBuildingImportJob Error: ".$th);
            }
        }
    }
?>
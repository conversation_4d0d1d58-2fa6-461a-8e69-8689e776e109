<div>
    @if($showFullView)
    <div class="contents crm">
        <div class="container-fluid">
            <div class="col-lg-12">
                <div class="row justify-content-sm-between align-items-center justify-content-center my-3 flex-sm-row flex-column">
                    <div class="page-title-wrap p-0">
                        <div class="page-title d-flex justify-content-between">
                            <div class="page-title__left justify-content-sm-between align-items-center justify-content-center">
                                <div class="user-member__title mr-sm-25 ml-0">
                                    <h4 class="text-capitalize fw-500 breadcrumb-title fs-16">
                                        {{ __('accounting.manageBills') }}
                                    </h4>
                                </div>
                            </div>
                        </div>
                        <div>
                            <ul class="atbd-breadcrumb nav">
                                <li class="atbd-breadcrumb__item">
                                    <a>{{ __('customers.navigation.dashboard') }}</a>
                                    <span class="breadcrumb__seperator">
                                        <span class="la la-angle-right"></span>
                                    </span>
                                </li>
                                <li class="atbd-breadcrumb__item">
                                    <a>{{ __('accounting.manageBills') }}</a>
                                </li>
                            </ul>
                        </div>
                    </div>

                    <div class="d-flex gap-10 breadcrumb_right_icons">
                        <button class="btn btn-white btn-default text-center svg-20 wh-45" wire:click="switchView('cards')">
                            <i class="iconsax icon text-new-primary fs-22 colorRed mr-0" icon-name="layout-3"></i>
                        </button>

                        <button class="btn btn-default btn-primary w-100 no-wrap" wire:click="goToCreatePage" type="button">
                            <i class="las la-plus fs-16"></i> {{ __('accounting.create') }}
                        </button>
                    </div>
                </div>
            </div>
    @endif

    @if($showFullView)
        <div class="card mb-3" data-select2-id="108">
            <div class="card-body" data-select2-id="107">
                <form wire:submit.prevent="applyFilters" class="fs-14">
                    <div class="row">
                        <div class="col-md-2 col-6">
                            <label for="from_date" class="text-osool">@lang('accounting.fromDate')</label>
                            <div class="position-relative">
                                <input type="text" class="form-control" wire:ignore id="from_date" placeholder="@lang('accounting.enterfromDate')" />
                                <i class="iconsax field-icon" icon-name="calendar-search"></i>
                            </div>
                        </div>

                        <div class="col-md-2 col-6">
                            <label for="to_date" class="text-osool">@lang('accounting.toDate')</label>
                            <div class="position-relative">
                                <input type="text" class="form-control" wire:ignore id="to_date" placeholder="@lang('accounting.entertoDate')" />
                                <i class="iconsax field-icon" icon-name="calendar-search"></i>
                            </div>
                        </div>

                        <div class="col-md-3 col-6" data-select2-id="106">
                            <label for="searchVendor" class="text-osool">{{ __('accounting.vendor') }}</label>
                            <select class="form-control select2-new" wire:ignore id="searchVendor" name="searchVendor">
                                <option value="">{{ __('accounting.select_vendor') }}</option>
                                @foreach($vendors as $this_data)
                                <option value="{{ $this_data['id'] }}">{{ $this_data['name'] }}</option>
                                @endforeach
                            </select>
                        </div>

                        <div class="col-md-3 col-6" data-select2-id="106">
                            <label for="searchStatus" class="text-osool">{{ __('accounting.status') }}</label>
                            <select class="form-control select2-new" wire:ignore id="searchStatus" name="searchStatus">
                                <option value="">{{ __('accounting.status') }}</option>
                                @foreach($statuses as $this_data)
                                <option value="{{ $this_data }}">{{ $this_data }}</option>
                                @endforeach
                            </select>
                        </div>

                        <div class="col-md-2 col-12">
                            <label for="" class="d-md-block d-none">&nbsp;</label>
                            <div class="d-flex gap-10">
                                <button type="submit" class="btn bg-opacity-new-primary btn-sm text-new-primary radius-md px-5">
                                    {{ __('accounting.apply') }}
                                </button>
                                <button type="button" class="btn bg-opacity-loss btn-sm text-loss wh-45 radius-md" wire:click="refresh">
                                    <i class="iconsax mr-0 fs-18" icon-name="rotate-left"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    @endif

    <div class="table-responsive">
        <div class="card">
            <div class="card-header py-4 px-3 border-0 d-flex justify-content-between align-items-center">
                <h6 class="text-capitalize text-osool fw-500 mb-3 mb-sm-0">{{ __('accounting.bill') }}</h6>

                <div class="d-flex gap-10 table-search">
                    <div class="position-relative">
                        <input type="text" class="form-control" placeholder="{{ __('customers.search.placeholder') }}" wire:model.debounce.500ms="search">
                        <i class="iconsax field-icon fs-18 mr-0" icon-name="search-normal-2"></i>
                    </div>
                    <button class="btn btn-export text-dark" wire:click="export">
                        <i class="iconsax icon fs-22 mr-0" icon-name="upload-1"></i> {{ __('accounting.export') }}
                    </button>
                </div>
            </div>
            <div class="card-body px-0 pt-0">
                <div class="userDatatable projectDatatable project-table bg-white w-100 border-0">
                    <div class="table-responsive">
                        <table class="table mb-0 radius-0 th-osool">
                            <thead>
                                <tr class="userDatatable-header">
                                    <th wire:click="sortBy('id')" style="cursor: pointer;">
                                        <i class="iconsax icon fs-22" icon-name="{{ $sortField === 'id' ? ($sortDirection === 'asc' ? 'arrow-up-2' : 'arrow-down-2') : 'swap-vertical-circle' }}"></i>
                                        @lang('accounting.no')
                                    </th>
                                    <th wire:click="sortBy('bill_number')" style="cursor: pointer;">
                                        <i class="iconsax icon fs-22" icon-name="{{ $sortField === 'bill_number' ? ($sortDirection === 'asc' ? 'arrow-up-2' : 'arrow-down-2') : 'swap-vertical-circle' }}"></i>
                                        @lang('accounting.bill')
                                    </th>
                                    <th wire:click="sortBy('vendor_name')" style="cursor: pointer;">
                                        <i class="iconsax icon fs-22" icon-name="{{ $sortField === 'vendor_name' ? ($sortDirection === 'asc' ? 'arrow-up-2' : 'arrow-down-2') : 'swap-vertical-circle' }}"></i>
                                        @lang('accounting.vendor')
                                    </th>
                                    <th wire:click="sortBy('account_type')" style="cursor: pointer;">
                                        <i class="iconsax icon fs-22" icon-name="{{ $sortField === 'account_type' ? ($sortDirection === 'asc' ? 'arrow-up-2' : 'arrow-down-2') : 'swap-vertical-circle' }}"></i>
                                        @lang('accounting.account_type')
                                    </th>
                                    <th wire:click="sortBy('bill_date')" style="cursor: pointer;">
                                        <i class="iconsax icon fs-22" icon-name="{{ $sortField === 'bill_date' ? ($sortDirection === 'asc' ? 'arrow-up-2' : 'arrow-down-2') : 'swap-vertical-circle' }}"></i>
                                        @lang('accounting.bill_date')
                                    </th>
                                    <th wire:click="sortBy('due_date')" style="cursor: pointer;">
                                        <i class="iconsax icon fs-22" icon-name="{{ $sortField === 'due_date' ? ($sortDirection === 'asc' ? 'arrow-up-2' : 'arrow-down-2') : 'swap-vertical-circle' }}"></i>
                                        @lang('accounting.due_date')
                                    </th>
                                    <th wire:click="sortBy('due_amount')" style="cursor: pointer;">
                                        <i class="iconsax icon fs-22" icon-name="{{ $sortField === 'due_amount' ? ($sortDirection === 'asc' ? 'arrow-up-2' : 'arrow-down-2') : 'swap-vertical-circle' }}"></i>
                                        @lang('accounting.due_amount')
                                    </th>
                                    <th wire:click="sortBy('status')" style="cursor: pointer;">
                                        <i class="iconsax icon fs-22" icon-name="{{ $sortField === 'status' ? ($sortDirection === 'asc' ? 'arrow-up-2' : 'arrow-down-2') : 'swap-vertical-circle' }}"></i>
                                        @lang('accounting.status')
                                    </th>
                                    <th>
                                        @lang('accounting.action')
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="sort-table ui-sortable">
                                @php $i=1; @endphp
                                @forelse($data as $this_data)
                                <tr class="ui-sortable-handle">
                                    <td>
                                        <div class="d-flex userDatatable-content mb-0 align-items-center">
                                            <span>{{$i++}}</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="userDatatable-inline-title">
                                            <a href="javascript:void(0);" wire:click="goToView('{{ Crypt::encrypt($this_data['id']) }}')">
                                                <span class="fw-400 badge badge-round badge-primary badge-lg badge-outlined">{{$this_data['bill_number']}}</span>
                                            </a>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="userDatatable-inline-title">
                                            {{$this_data['vendor_name']}}
                                        </div>
                                    </td>
                                    <td>
                                        <div class="userDatatable-inline-title">
                                            {{$this_data['account_type']}}
                                        </div>
                                    </td>
                                    <td>
                                        <div class="userDatatable-inline-title">
                                            {{$this_data['bill_date']}}
                                        </div>
                                    </td>
                                    <td>
                                        <div class="userDatatable-inline-title">
                                            {{$this_data['due_date']}}
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center gap-10">
                                            <img src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSx34eO8RbZlsetN1iAQtxuOLnLOlyJbJmU7Q&amp;s" width="15">
                                            <span class="text-new-primary">{{$this_data['due_amount']}}</span>
                                        </div>
                                    </td>
                                    <td>
                                        @php
                                        $statusInfo = \App\Http\Traits\CRMProjects\FinanceManagment::getStatusInfo(data_get($this_data, 'status', '---'));
                                        @endphp
                                        <span class="py-1 px-2 {{ $statusInfo['color'] }} rounded text-white">
                                            {{ $statusInfo['label'] }}
                                        </span>
                                    </td>
                                    <td>
                                        <div class="d-inline-block">
                                            <ul class="mb-0 d-flex gap-10">
                                                <li>
                                                    <a href="javascript:void(0);" wire:click="goToView('{{ Crypt::encrypt($this_data['id']) }}')">
                                                        <i class="iconsax icon text-osool fs-18" icon-name="eye"></i>
                                                    </a>
                                                </li>
                                                <li>
                                                    <a href="javascript:void(0);" wire:click="goToEdit('{{ Crypt::encrypt($this_data['id']) }}')">
                                                        <i class="iconsax icon text-new-primary fs-18" icon-name="edit-1"></i>
                                                    </a>
                                                </li>
                                                <li>
                                                    <a href="javascript:void(0);" wire:click="openDeleteModal({{ $this_data['id'] }}, '{{ $this_data['bill_number'] }}')">
                                                        <i class="iconsax icon text-delete fs-18" icon-name="trash"></i>
                                                    </a>
                                                </li>
                                                <li>
                                                    <a title="@lang('finace_manage.common.copy')" href="javascript:void(0);" class="edit" wire:click="copyURL({{ data_get($this_data, 'id') }})">
                                                        <i class="iconsax icon text-primary fs-18" icon-name="document-copy"></i>
                                                    </a>
                                                </li>
                                                <li>
                                                    <a title="@lang('finace_manage.common.duplicate')" href="javascript:void(0);" class="edit mr-2" wire:click="OpenConfirmDuplicateModal({{ $this_data['id'] }}, '{{ $this_data['bill_number'] }}')">
                                                        <i class="iconsax icon text-copy fs-18 mr-0" icon-name="copy"></i>
                                                    </a>
                                                </li>
                                            </ul>
                                        </div>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="9" class="text-center py-4">
                                        <div class="d-flex flex-column align-items-center">
                                            <i class="iconsax icon fs-48 text-muted mb-3" icon-name="user-search"></i>
                                            <h6 class="text-muted">{{ __('accounting.no_data_found') }}</h6>
                                            @if($search)
                                                <p class="text-muted">{{ __('customers.messages.try_adjusting_search') }}</p>
                                            @endif
                                        </div>
                                    </td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>










            <div class="card-body pt-0">
                <div class="d-flex justify-content-md-between flex-column flex-md-row justify-content-center align-items-center mt-4 gap-10">
                    <div class="">
                        <ul class="atbd-pagination d-flex justify-content-between">
                            <li>
                                <div class="paging-option">
                                    <div class="dataTables_length d-flex">
                                        <label class="d-flex align-items-center mb-0">
                                            <select wire:model="perPage" class="custom-select custom-select-sm form-control form-control-sm mx-2" style="min-height: 35px;">
                                                <option value="5">5</option>
                                                <option value="10">10</option>
                                                <option value="25">25</option>
                                                <option value="50">50</option>
                                                <option value="100">100</option>
                                            </select>
                                            <span class="no-wrap"> {{ __('customers.pagination.entries_per_page') }} </span>
                                        </label>
                                    </div>
                                </div>
                            </li>
                        </ul>
                    </div>

                    @if($total > 0)
                    <div class="">
                        <div class="user-pagination">
                            <div class="user-pagination new-pagination">
                                <div class="d-flex justify-content-sm-end justify-content-end">
                                    <nav>
                                        <span class="relative z-0 inline-flex rounded-md d-flex new-pagination-section flex-wrap">
                                            @if($pagination->current_page > 1)
                                                <span>
                                                    <button type="button" class="border-0" wire:click="previousPage">
                                                        <i class="iconsax icon-bold fs-16 mr-0 rotate-ar-y d-inline-block" icon-name="chevron-left"></i>
                                                    </button>
                                                </span>
                                            @else
                                                <span>
                                                    <button class="border-0 disabled" disabled>
                                                        <i class="iconsax icon-bold fs-16 mr-0 rotate-ar-y d-inline-block" icon-name="chevron-left"></i>
                                                    </button>
                                                </span>
                                            @endif

                                            @php
                                                $start = max(1, $pagination->current_page - 2);
                                                $end = min($pagination->last_page, $pagination->current_page + 2);
                                            @endphp

                                            @if($start > 1)
                                                <span>
                                                    <button type="button" class="border-0" wire:click="gotoPage(1)">1</button>
                                                </span>
                                                @if($start > 2)
                                                    <span>
                                                        <button class="border-0 disabled" disabled>...</button>
                                                    </span>
                                                @endif
                                            @endif

                                            @for($i = $start; $i <= $end; $i++)
                                                @if($i == $pagination->current_page)
                                                    <span>
                                                        <button class="border-0 current-page" disabled>{{ $i }}</button>
                                                    </span>
                                                @else
                                                    <span>
                                                        <button type="button" class="border-0" wire:click="gotoPage({{ $i }})">{{ $i }}</button>
                                                    </span>
                                                @endif
                                            @endfor

                                            @if($end < $pagination->last_page)
                                                @if($end < $pagination->last_page - 1)
                                                    <span>
                                                        <button class="border-0 disabled" disabled>...</button>
                                                    </span>
                                                @endif
                                                <span>
                                                    <button type="button" class="border-0" wire:click="gotoPage({{ $pagination->last_page }})">{{ $pagination->last_page }}</button>
                                                </span>
                                            @endif

                                            @if($pagination->current_page < $pagination->last_page)
                                                <span>
                                                    <button type="button" class="border-0" wire:click="nextPage">
                                                        <i class="iconsax icon-bold fs-16 mr-0 rotate-ar-y d-inline-block" icon-name="chevron-right"></i>
                                                    </button>
                                                </span>
                                            @else
                                                <span>
                                                    <button class="border-0 disabled" disabled>
                                                        <i class="iconsax icon-bold fs-16 mr-0 rotate-ar-y d-inline-block" icon-name="chevron-right"></i>
                                                    </button>
                                                </span>
                                            @endif
                                        </span>
                                    </nav>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div>
                        <p class="text-sm text-gray-700 leading-5 mb-0">
                            <span>{{ __('customers.pagination.showing') }}</span>
                            <span class="font-medium">{{ ($pagination->current_page - 1) * $pagination->per_page + 1 }}</span>
                            <span>{{ __('customers.pagination.to') }}</span>
                            <span class="font-medium">{{ min($pagination->current_page * $pagination->per_page, $pagination->total) }}</span>
                            <span>{{ __('customers.pagination.of') }}</span>
                            <span class="font-medium">{{ $pagination->total }}</span>
                            <span>{{ __('customers.pagination.results') }}</span>
                        </p>
                    </div>
                    @endif
                </div>
            </div>
        </div>

    </div>

    @livewire('common.delete-confirm')
    @include('livewire.accounting.bill.modals.confirm-duplicate-document')
</div>

@push('scripts')
<script type="text/javascript">
    $(".datepicker").datepicker();
    $("#bank_type, #account_type, #wallet_type").select2();
    $(document).ready(function() {
      // Toggle active class and manage dropdown on button click
      $('.filter-button').on('click', function (e) {
        e.stopPropagation(); // Prevent the click from bubbling up to the document
        $(this).toggleClass('active'); // Toggle the 'active' class
        $(this).closest('.dropdown').find(".dropdown-menu").toggleClass('show'); // Toggle the Bootstrap dropdown
      });

      // Remove active class and close dropdown when clicking outside
      $(document).on('click', function (e) {
        if (!$(e.target).closest('.dropdown').length) {
          // If the click is outside the dropdown, remove the 'active' class and close the dropdown
          $('.filter-button').removeClass('active');
          $('.dropdown-menu').removeClass('show');
          $('.filter-button').attr('aria-expanded', 'false');
        }
      });

      // Prevent dropdown from closing when clicking inside the dropdown menu
      $('.dropdown-menu').on('click', function (e) {
        e.stopPropagation();
      });
    });
</script>

<script>
    window.addEventListener('show-edit-modal', () => {
        $('#edit_data').modal('show');
    });

    window.addEventListener('hide-edit-modal', () => {
        $('#edit_data').modal('hide');
    });

    window.addEventListener('show-view-modal', () => {
        $('#view_data').modal('show');
    });
    
</script>

<script>

    document.addEventListener('livewire:load', function () {

        $('#from_date').datepicker({
            format: 'yyyy-mm-dd',
            autoclose: true
        });

        $('#to_date').datepicker({
            format: 'yyyy-mm-dd',
            autoclose: true
        });

        $('#searchVendor').select2().on('change', function () {
            @this.set('searchVendor', $(this).val());
        });

        $('#searchStatus').select2().on('change', function () {
            @this.set('searchStatus', $(this).val());
        });

        // Reinitialize select2 after Livewire DOM update
        Livewire.hook('message.processed', () => {
            $('#searchVendor, #searchStatus').select2();
        });
    });



    $('#from_date').on('change', function () {
    const raw = new Date($(this).val());
    const formatted = raw.getFullYear() + '-' + String(raw.getMonth() + 1).padStart(2, '0') + '-' + String(raw.getDate()).padStart(2, '0');
    Livewire.find('{{ $this->id }}').set('from_date', formatted);
    });
    $('#to_date').on('change', function () {
    const raw = new Date($(this).val());
    const formatted = raw.getFullYear() + '-' + String(raw.getMonth() + 1).padStart(2, '0') + '-' + String(raw.getDate()).padStart(2, '0');
    Livewire.find('{{ $this->id }}').set('to_date', formatted);
    });  



        window.addEventListener('copyToClipboard', event => {
        const text = event.detail.text;
        navigator.clipboard.writeText(text)
        .then(() => {
        //alert('URL copied to clipboard!');
        })
        .catch(err => {
        //console.error('Failed to copy: ', err);
        });
        });
</script>
<script>
    window.addEventListener('reload-page', () => {
        // Optional: delay reload to allow toast visibility
        setTimeout(() => {
            window.location.reload();
        }, 1500); // 1.5 seconds delay
    });
</script>

<script>
    window.addEventListener('download-blob', event => {
        const { base64, fileName, mime } = event.detail;

        // Convert base64 to binary data
        const byteCharacters = atob(base64);
        const byteNumbers = new Array(byteCharacters.length);
        for (let i = 0; i < byteCharacters.length; i++) {
            byteNumbers[i] = byteCharacters.charCodeAt(i);
        }
        const byteArray = new Uint8Array(byteNumbers);
        const blob = new Blob([byteArray], { type: mime });

        // Create object URL and download
        const link = document.createElement('a');
        link.href = window.URL.createObjectURL(blob);
        link.download = fileName;
        link.click();
    });
</script>
@endpush

<?php
    namespace App\Http\Traits;
    use Illuminate\Support\Facades\Log;
    use App\Http\Helpers\ReportQueryHelper;
    use App\Http\Traits\TempBulkImportTrait;
    use App\Http\Traits\FunctionsTrait;
    use App\Http\Traits\RegionTrait;
    use App\Http\Traits\CityTrait;
    use App\Http\Helpers\Helper;
    use App\Models\Property;
    use App\Enums\ModelAction;

    trait PropertyTrait{
        use TempBulkImportTrait, FunctionsTrait, RegionTrait, CityTrait;

        public function getPropertyInformationsByValues($key, $value, $projectUserId) {
            try {
                return Property::where('user_id', $projectUserId)
                ->where(function ($query) use ($value) {
                    $query->where('property_tag', $value)
                        ->orWhere('complex_name', $value);
                })
                ->where('is_deleted', 'no')
                ->first();
            } 
            
            catch (\Throwable $th) {
                Log::error("getPropertyInformationsByValues error: ".$th);
            }
        }

        public function manageEntredProperty($propertyName, $projectUserId) {
            try {
                $property = $this->getPropertyInformationsByValues("property_tag", $propertyName, $projectUserId);

                if(isset($property)){
                    return ModelAction::Update->value;
                }

                else{
                    return ModelAction::Insert->value;
                }
            } 
            
            catch (\Throwable $th) {
                Log::error("manageEntredProperty error: ".$th);
            }
        }

        public function saveProperty($array) {
            try {
                return Property::insertGetId($array);
            } 
            
            catch (\Throwable $th) {
                Log::error("saveProperty error: ".$th);
            }
        }

        public function updatePropertyById($projectUserId, $id, $array) {
            try {
                return Property::where('user_id', $projectUserId)
                ->where('id', $id)
                ->where('is_deleted', 'no')
                ->update($array);
            } 
            
            catch (\Throwable $th) {
                Log::error("updatePropertyById error: ".$th);
            }
        }

        public function getPropertyInformationsById($propertyId) {
            try {
                return Property::where('id', $propertyId)
                ->where('is_deleted', 'no')
                ->select('region_id', 'city_id', 'property_tag', 'location', 'latitude', 'longitude', 'property_type', 'complex_name', 'buildings_count')
                ->first();
            } 
            
            catch (\Throwable $th) {
                Log::error("getPropertyInformationsById Error: ".$th);
            }
        }

        public function deleteProperyByValue($key, $value) {
            try {
                return Property::whereIn($key, $value)
                ->delete();
            } 
            
            catch (\Throwable $th) {
                Log::error("deleteProperyByValue error: ".$th);
            }
        }

        public function getPropertiesListForPmWorkOrders($user, $search, $serviceProviderId) {
            try {
                $properties = Property::with([
                    'propertyBuildings' => function ($query) use ($user) {
                        $query->select('id', 'building_name', 'property_id');
                    }
                ]);
        
                $properties = Helper::filterProperties($user, $properties, $serviceProviderId);
                $properties = $properties
                    ->orderBy('properties.id', 'asc')
                    ->get()
                    ->toArray();
        
                    $properties = array_map(function ($property) {
                        if ($property['property_type'] === 'building') {
                            $property['building_id'] = isset($property['property_buildings'][0]['id']) ? $property['property_buildings'][0]['id'] : 0;
                            $property['building_name'] = isset($property['property_buildings'][0]['building_name']) ? $property['property_buildings'][0]['building_name'] : '';
                        }
                        return $property;
                    }, $properties);
                    
                    if (!empty($search) && !is_null($search)) {
                        return array_filter($properties, function ($property) use ($search) {
                            return isset($property['building_name']) && strpos(strtolower($property['building_name']), strtolower($search)) !== false;
                        });
                    } else {
                        return $properties;
                    }
            } 
            
            catch (\Throwable $th) {
                Log::error("getPropertiesListForPmWorkOrders error: ".$th);
            }
        }

        public function getPropertiesListForWorkOrdersList($user, $serviceProviderId) {
            try {
                $properties = Property::with([
                    'propertyBuildings' => function ($query) use ($user) {
                        $query->select('id', 'building_name', 'property_id');
                    }
                ]);
        
                $properties = Helper::filterProperties($user, $properties, $serviceProviderId);
                $properties = $properties
                    ->orderBy('properties.id', 'asc')
                    ->get()
                    ->toArray();
        
                return array_map(function ($property) {
                    if ($property['property_type'] === 'building') {
                        $property['building_id'] = isset($property['property_buildings'][0]['id']) ? $property['property_buildings'][0]['id'] : 0;
                        $property['building_name'] = isset($property['property_buildings'][0]['building_name']) ? $property['property_buildings'][0]['building_name'] : '';
                    }
                    return $property;
                }, $properties);   
            } 
            
            catch (\Throwable $th) {
                Log::error("getPropertiesListForWorkOrdersList error: ".$th);
            }
        }

        public function getPropertiesListForNotifications($projectUserId) {
            try {
                return Property::select('property_buildings.id')
                ->leftJoin('property_buildings','property_buildings.property_id','properties.id')
                ->where('properties.user_id', $projectUserId)
                ->where('properties.is_deleted', 'no')
                ->pluck('property_buildings.id')
                ->toArray();
            } 
            
            catch (\Throwable $th) {
                Log::error("getPropertiesListForNotifications error: ".$th);
            }
        }

        public function getPaginatedPropertiesListVByValues($key, $value, $perPage) {
            try {
                return Property::select('region_id', 'city_id', 'property_type', 'buildings_count', 'property_tag', 'complex_name', 'latitude', 'longitude')
                ->whereIn($key, $value)
                ->paginate($perPage, ['*'], 'page'); 
            } 
            
            catch (\Throwable $th) {
                Log::error("getPaginatedPropertiesListVByValues error: ".$th);
            }
        }
    }
?>
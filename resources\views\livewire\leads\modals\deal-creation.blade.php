 <div class="row mt-20">
     <div class="col-md-6">
         <div class="form-group">
             <label for="client_name">@lang('lead.deal_name') <span class="required">*</span></label>
             <input type="text" class="form-control" id="client_name" wire:model.defer='deal_name' placeholder="@lang('lead.deal_name')">
             @error('deal_name')
                 <span class="text-danger">{{ $message }}</span>
             @enderror
         </div>
     </div>
     <div class="col-md-6">
         <div class="form-group">
             <label for="client_name">@lang('lead.price') <span class="required">*</span></label>
             <input type="text" class="form-control allow-float" id="client_name" wire:model.defer='price' placeholder="@lang('lead.price')">
             @error('price')
                 <span class="text-danger">{{ $message }}</span>
             @enderror
         </div>
     </div>
     <div class="col-md-6">
         <div class="form-group">
             <label for="client_name">@lang('lead.expected_close_date') <span class="required">*</span></label>
             <input type="text" class="form-control datepicker" id="expected_close_date" wire:model.defer='date'
                 autocomplete="off" placeholder="@lang('lead.expected_close_date')">
             @error('date')
                 <span class="text-danger">{{ $message }}</span>
             @enderror
         </div>
     </div>
     <div class="col-md-6">
         <div class="form-group">
             <label for="client_name">@lang('lead.stage') <span class="required">*</span></label>
             <select class="form-control" wire:model.defer='stage'>
                 <option></option>
                 @foreach ($stages as $item)
                     <option value="{{ $item['id'] }}">
                         {{ $item['name'] }}</option>
                 @endforeach
             </select>
             @error('stage')
                 <span class="text-danger">{{ $message }}</span>
             @enderror
         </div>
     </div>
 </div>
 <div class="show-hide-radio">
     <div class="form-group">
         <label for="client_name">@lang('lead.transfer_data') <span class="required">*</span></label>
         <div class="radio-horizontal-list d-flex">
             <div class="radio-theme-default custom-radio">
                 <input class="radio" type="radio" name="transfer_data" wire:model='transfer_data' value="all"
                     id="transfer_1" />
                 <label for="transfer_1">
                     <span class="radio-text">@lang('lead.all_data')</span>
                 </label>
             </div>
             <div class="radio-theme-default custom-radio">
                 <input class="radio" type="radio" name="transfer_data" wire:model='transfer_data'
                     value="trasfer_selected" id="transfer_2" />
                 <label for="transfer_2">
                     <span class="radio-text">@lang('lead.selected_data')</span>
                 </label>
             </div>
         </div>
         @if ($transfer_data != 'all')
             <div class="contact-type trasfer_selected">
                 <div class="form-group">
                     @foreach ($transferOptions as $key => $label)
                         <div class="form-check form-check-inline">
                             <input type="checkbox" wire:model="selectedTransfers" value="{{ $key }}"
                                 class="form-check-input" id="{{ $key }}">
                             <label class="form-check-label" for="{{ $key }}">@lang($label)</label>
                         </div>
                     @endforeach
                 </div>
             </div>
         @endif
     </div>
 </div>

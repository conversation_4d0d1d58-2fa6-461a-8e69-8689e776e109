
        <div class="card">
            <div class="card-header border-0 mb-2">
                <h6 class="fw-700 fs-16">{{ __('advance_contracts.variation_order.variation_order_history') }}</h6>
            </div>
            <div class="card-body pt-0">
                <div class="">
                <div class="table-responsive">
                        <table class="table mb-0 table-borderless dataTable no-footer table-osool-bg">
                            <thead class="userDatatable-header">
                                <tr class="warehouse-table-tr">
                                    <th class="text-osool pl-15 b-t-l-r b-b-l-r"> {{ __('advance_contracts.variation_order.request_id') }} </th>
                                    <th class="text-osool pl-15"> {{ __('advance_contracts.variation_order.status') }} </th>
                                    <th class="text-osool pl-15">{{ __('advance_contracts.variation_order.date_submitted') }}</th>
                                    <th class="text-osool pl-15">{{ __('advance_contracts.variation_order.approved_by') }}</th>
                                    <!-- <th class="text-osool pl-15">{{ __('advance_contracts.variation_order.decision_comment') }}</th> -->
                                    <th class="text-osool pl-15 b-t-r-r b-b-r-r">{{ __('advance_contracts.variation_order.changes_summary') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach ($history as $item)
                                <tr>
                                    <td>{{ $loop->iteration }}</td>
                                    <td>
                                        @php
                                            $statusClass = 'bg-light';

                                            if ($item->trashed()) {
                                                $statusClass = 'bg-osool-blue'; 
                                            } else {
                                                $statusClass = match($item->status) {
                                                    'approved' => 'bg-win',
                                                    'rejected' => 'bg-loss',
                                                    'pending' => 'bg-light',
                                                    default => 'bg-light',
                                                };
                                            }
                                        @endphp
                                        <span class="{{ $statusClass }} text-white rounded d-inline-block userDatatable-content-status active">
                                            {{ $item->getStatusLabelAttribute() }}
                                        </span>
                                    </td>
                                    <td>{{ \Carbon\Carbon::parse($item->created_at)->format('d-m-Y') }}</td>

                                    <td>
                                        @foreach($item->approvals as $approval)
                                            @php
                                                $translatedStatus = __('advance_contracts.approval.' . strtolower($approval->status ?? 'pending'));

                                                $tooltip  = "<strong>" . __('advance_contracts.approval.status') . ":</strong> " . $translatedStatus . "<br>";
                                                $tooltip .= "<strong>" . __('advance_contracts.approval.comment') . ":</strong> " . ($approval->comment ?? '-') . "<br>";
                                                $tooltip .= "<strong>" . __('advance_contracts.approval.action_time') . ":</strong> " . 
                                                            ($approval->acted_at ? $approval->acted_at->format('Y-m-d H:i') : __('advance_contracts.approval.pending'));
                                            @endphp
                                            <span 
                                                data-toggle="tooltip"
                                                data-html="true"
                                                data-placement="right"
                                                title="{!! $tooltip !!}"
                                                class="text-osool d-inline-block me-1"
                                            >
                                                {{ $approval->approver_name }}
                                            </span>
                                        @endforeach
                                    </td>
                                    <td>
                                        <a href="{{ route('variation.view', $item->uuid)}}" class="text-osool text-underline">{{ __('advance_contracts.variation_order.view') }}</a>
                                    </td>

                                </tr>
                               
                            @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

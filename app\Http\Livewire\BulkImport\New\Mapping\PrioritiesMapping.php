<?php
    namespace App\Http\Livewire\BulkImport\New\Mapping;
    use Livewire\Component;
    use Illuminate\Support\Facades\Log;
    use App\Http\Traits\FunctionsTrait;
    use App\Http\Traits\BulkImportTrait;
    use App\Http\Traits\PriorityTrait;

    class PrioritiesMapping extends Component{
        use FunctionsTrait, BulkImportTrait, PriorityTrait;

        public $showMore;
        public $perPage;
        public $countList;
        public $chunkData;
        public $projectUserId;
        public $bulkImportDetails;

        public function render(){
            $list = $this->getPaginatedPrioritiesList();
            isset($list) && count($list) > 0 ? $this->setCountList($list->total()) : $this->setCountList(0);
            return view('livewire.bulk-import.new.mapping.priorities-mapping', compact('list'));
        }

        public function initPriorities() {
            try {
                $prioritiesData = isset($this->bulkImportDetails) ? $this->bulkImportDetails->bulkImportTemp->priorities_data : null;
                $array = json_decode($prioritiesData, true);
                return collect($array);
            } 
            
            catch (\Throwable $th) {
                Log::error("initPriorities error: ".$th);
            }
        }

        public function getPaginatedPrioritiesList() {
            try {
                $priorities = $this->initPriorities();
                return $this->customPagination($priorities, $this->perPage);
            } 
            
            catch (\Throwable $th) {
                Log::error("getPaginatedPrioritiesList error: ".$th);
            }
        }

        public function managePerPage() {
            try {
                $number = $this->additionOperation($this->perPage, $this->showMore);
                $this->setPerPage($number);
            } 
            
            catch (\Throwable $th) {
                Log::error("managePerPage error: ".$th);
            }
        }

        public function setPerPage($value) {
            try {
                $this->perPage = $value;
            } 
            
            catch (\Throwable $th) {
                Log::error("setPerPage error: ".$th);
            }
        }

        public function setCountList($value) {
            try {
                $this->countList = $value;
            } 
            
            catch (\Throwable $th) {
                Log::error("setCountList error: ".$th);
            }
        }

        public function manageLoadAll() {
            try {
                $this->setPerPage($this->countList);
            } 
            
            catch (\Throwable $th) {
                Log::error("manageLoadAll error: ".$th);
            }
        }

        public function manageListStatusData() {
            try {
                $priorities = $this->initPriorities();
                $acceptedNumber = 0;
                $refusedNumber = 0;

                if(isset($priorities) && count($priorities) > 0){
                    collect($priorities)->chunk($this->chunkData)->each(function($chunk) use(&$acceptedNumber, &$refusedNumber){
                        foreach($chunk as $data){
                            $response = $this->fullPrioritiesValidation($data);

                            if(isset($response) && $response[0]['status'] == 'success'){
                                $acceptedNumber = $acceptedNumber + 1;
                            }

                            else{
                                $refusedNumber = $refusedNumber + 1;
                            }
                        }
                    });
                }

                return [
                    'acceptedNumber' => $acceptedNumber,
                    'refusedNumber' => $refusedNumber
                ];
            } 
            
            catch (\Throwable $th) {
                Log::error("manageListStatusData error: ".$th);
            }
        }
    }
?>
<?php
    namespace App\Http\Livewire\BulkImport\New\Mapping;
    use Livewire\Component;
    use Illuminate\Support\Facades\Log;
    use App\Http\Traits\FunctionsTrait;
    use App\Http\Traits\BulkImportTrait;
    use App\Http\Traits\ServiceTrait;

    class ServicesMapping extends Component{
        use FunctionsTrait, BulkImportTrait, ServiceTrait;

        public $showMore;
        public $perPage;
        public $countList;
        public $chunkData;
        public $projectUserId;
        public $bulkImportDetails;

        public function render(){
            $list = $this->getPaginatedServicesList();
            isset($list) && count($list) > 0 ? $this->setCountList($list->total()) : $this->setCountList(0);
            return view('livewire.bulk-import.new.mapping.services-mapping', compact('list'));
        }

        public function initServices() {
            try {
                $servicesData = isset($this->bulkImportDetails) ? $this->bulkImportDetails->bulkImportTemp->services_data : null;
                $array = json_decode($servicesData, true);
                return collect($array);
            } 
            
            catch (\Throwable $th) {
                Log::error("initServices error: ".$th);
            }
        }

        public function getPaginatedServicesList() {
            try {
                $services = $this->initServices();
                return $this->customPagination($services, $this->perPage);
            } 
            
            catch (\Throwable $th) {
                Log::error("getPaginatedServicesList error: ".$th);
            }
        }

        public function managePerPage() {
            try {
                $number = $this->additionOperation($this->perPage, $this->showMore);
                $this->setPerPage($number);
            } 
            
            catch (\Throwable $th) {
                Log::error("managePerPage error: ".$th);
            }
        }

        public function setPerPage($value) {
            try {
                $this->perPage = $value;
            } 
            
            catch (\Throwable $th) {
                Log::error("setPerPage error: ".$th);
            }
        }

        public function setCountList($value) {
            try {
                $this->countList = $value;
            } 
            
            catch (\Throwable $th) {
                Log::error("setCountList error: ".$th);
            }
        }

        public function manageLoadAll() {
            try {
                $this->setPerPage($this->countList);
            } 
            
            catch (\Throwable $th) {
                Log::error("manageLoadAll error: ".$th);
            }
        }

        public function manageListStatusData() {
            try {
                $services = $this->initServices();
                $acceptedNumber = 0;
                $refusedNumber = 0;

                if(isset($services) && count($services) > 0){
                    collect($services)->chunk($this->chunkData)->each(function($chunk) use(&$acceptedNumber, &$refusedNumber){
                        foreach($chunk as $data){
                            $response = $this->fullServicesValidation($data);

                            if(isset($response) && $response[0]['status'] == 'success'){
                                $acceptedNumber = $acceptedNumber + 1;
                            }

                            else{
                                $refusedNumber = $refusedNumber + 1;
                            }
                        }
                    });
                }

                return [
                    'acceptedNumber' => $acceptedNumber,
                    'refusedNumber' => $refusedNumber
                ];
            } 
            
            catch (\Throwable $th) {
                Log::error("manageListStatusData error: ".$th);
            }
        }
    }
?>
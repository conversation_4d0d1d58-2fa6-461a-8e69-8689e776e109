<?php
    namespace App\Http\Traits;
    use Illuminate\Support\Facades\Log;
    use App\Http\Helpers\ReportQueryHelper;
    use App\Http\Traits\PropertyTrait;
    use App\Http\Traits\RoomTypeTrait;
    use App\Http\Traits\RoomsTypeFloorTrait; 
    use App\Http\Traits\TempBulkImportTrait;
    use App\Http\Traits\FunctionsTrait;
    use App\Models\PropertyBuildings;
    use App\Enums\ModelAction;

    trait PropertyBuildingTrait{
        use PropertyTrait, RoomTypeTrait, RoomsTypeFloorTrait, TempBulkImportTrait, FunctionsTrait;

        public function manageEntredPropertyBuilding($propertyName, $buildingName, $projectUserId, $zone, $unit) {
            try {
                $property = $this->getPropertyInformationsByValues("property_tag", $propertyName, $projectUserId);

                if(!is_null($property)){
                    $propertyBuilding = $this->getPropertyBuildingInformationsByValues($property->id, $buildingName);

                    if(!is_null($propertyBuilding)){ 
                        $roomTypeFloor = $this->getRoomTypeFloorByData($zone, $unit, $property->id, $propertyBuilding->id);

                        if(is_null($roomTypeFloor)){
                            return ModelAction::Insert->value;
                        }
        
                        else{
                            return ModelAction::Update->value;
                        }
                    }

                    else{
                        return ModelAction::Insert->value; 
                    }
                }

                else{ 
                    return ModelAction::Insert->value; 
                }
            }
            
            catch (\Throwable $th) {
                Log::error("manageEntredPropertyBuilding error: ".$th);
            }
        }

        public function savePropertyBuilding($array) {
            try {
                return PropertyBuildings::insertGetId($array);
            } 
            
            catch (\Throwable $th) {
                Log::error("savePropertyBuilding error: ".$th);
            }
        }

        public function getPropertyBuildingInformationsById($propertyBuildingId) {
            try {
                return PropertyBuildings::where('id', '=', $propertyBuildingId)
                ->where('is_deleted', 'no')   
                ->select('property_id', 'rooms', 'building_tag')
                ->first();
            } 
            
            catch (\Throwable $th) {
                Log::error("getPropertyBuildingInformationsById Error: ".$th);
            }
        }

        
        public function getPropertyBuildingInformationsByValues($propertyId, $buildingName) {
            try {
                return PropertyBuildings::where('property_id', $propertyId)
                ->where('building_name', $buildingName) 
                ->where('is_deleted', 'no')   
                ->orderBy('id', 'DESC')
                ->first();
            } 
            
            catch (\Throwable $th) {
                Log::error("getPropertyBuildingInformationsById error: ".$th);
            }
        }

        public function updatePropertyBuildingByPropertyBuildingId($rooms, $propertyBuildingId, $buildingTag) {
            try {
                return PropertyBuildings::where('id', $propertyBuildingId)
                ->where('is_deleted', 'no') 
                ->update([
                    'rooms' => $rooms,
                    "building_tag" => $buildingTag
                ]);
            } 
            
            catch (\Throwable $th) {
                Log::error("updatePropertyBuildingByPropertyBuildingId error: ".$th);
            }
        }

        public function deletePropertyBuildingByValue($key, $value) {
            try {
                return PropertyBuildings::whereIn($key, $value)  
                ->delete();
            } 
            
            catch (\Throwable $th) {
                Log::error("deletePropertyBuildingByValue error: ".$th);
            }
        }

        public function updatePropertyBuildingByValues($propertyBuildingId, $barCodeValue, $barCodeImgStr) {
            try {
                return PropertyBuildings::where('id', $propertyBuildingId)
                ->where('is_deleted', 'no') 
                ->update([
                    'barcode_value' => $barCodeValue,
                    'barcode_img_str' => $barCodeImgStr
                ]);
            } 
            
            catch (\Throwable $th) {
                Log::error("updatePropertyBuildingByValues Error: ".$th);
            }
        }

        public function getPaginatedPropertyBuildingsListByValues($key, $value, $perPage) {
            try {
                return PropertyBuildings::select('property_id', 'building_name', 'id', 'rooms_count')
                ->where('is_deleted', 'no') 
                ->whereIn($key, $value)
                ->paginate($perPage, ['*'], 'page');
            } 
            
            catch (\Throwable $th) {
                Log::error("getPaginatedPropertyBuildingsListByValues error: ".$th);
            }
        }
    }
?>
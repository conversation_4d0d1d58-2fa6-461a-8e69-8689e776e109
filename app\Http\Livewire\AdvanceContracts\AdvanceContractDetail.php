<?php

namespace App\Http\Livewire\AdvanceContracts;

use App\Enums\Role;
use App\Models\User;
use App\Models\WorkTimeFrame;
use Livewire\Component;
use App\Models\Contracts;
use App\Enums\Proficiency;
use Illuminate\Support\Carbon;
use App\Models\WorkforceAndTeam;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Log;
use App\Models\AdvanceContractDraft;
use Illuminate\Support\Facades\Auth;
use App\Models\AssignedContractWorker;
use Illuminate\Support\Facades\Storage;
use App\Services\AdvanceContractService;

class AdvanceContractDetail extends Component
{
    public $uuid; // holds the encrypted id passed from URL
    public $data = [];
    public $contract;
    public $subcontracts;
    public $performanceIndicatorData;
    public $overallPerformance;
    public $startDate;
    public $endDate;
    public $percentage = 0;
    public $serviceProvider;
    public $cityNames = [];
    public $regionNames = [];
    public $cityNamesString = '';
    public $regionNamesString = '';
    public $propertyNames = [];
    public $propertyNamesLimited = [];
    public $serviceType;
    public $assetsName;
    public $items;
    public $installmentsCount = 0;
    public $perMonthAmount = 0;
    public $selected_properties = [];
    public $buildings ;
    public $totalUnits;
    public $totalZones;
    public $total; 
    public $totalPages; 
    public $propertyPage = 1;
    public $perPage = 5;
    public $groupedIndicators;
    public $assetCategories = [];
    public $smartAssignedServices = [];
    public $allowedRolesForVariationOrder = ['sp_admin', 'building_manager', 'admin'];
    public $create_variation_order_url;
    public $variation_order_history;
    public $has_pending_variation_order;
    public $view_variation_order;
    public $contractId;
    public $attachment;
    protected $contractService;
    public $alertMessage = null;
    public $mode = 'view'; // 'view' or 'link'
    public $selectedRole;
    public $requiredSalary;
    public $requiredAttendanceTarget;
    public $selectedProficiency;

    public $totalOpenDays = 0; // Total open days based on work time frame
    public $teamId;
    public $workerlist = [];
    public $assignedWorkers = [];
    public $selectedWorkerList = [];
    public $isOpenModal = false;
    protected $listeners = ['assignSelectedWorkers'];
    public array $teamStatusSummary = [];
    public array $roles = [];
    public array $adminLevels = [];
    // Inject the service into the component
    public function __construct()
    {
        $this->contractService = new AdvanceContractService();
    }

    /**
     * The mount method initializes the component.
     *
     * @param mixed $id Encrypted contract id from URL
     */
    public function mount($id)
    {   
        // Fetch contract data via the service
        $contractData = $this->contractService->getContractData($id);
        $this->alertMessage = $this->contractService->getApprovalMessage();
        $this->roles = Role::labels();
        $this->adminLevels = Proficiency::limitedLabels();
        // Set the values for the Livewire component
        if ($contractData) {
            $this->contractId  =  $contractData['id'];
            $contractId = $this->contractId;
            $this->overallPerformance = $contractData['overallPerformance'];
            $this->data['id'] = $contractData['id'];
            $this->contract = $contractData['contract'];
            $this->groupedIndicators = $contractData['groupedIndicators'];
            $this->buildings = $contractData['buildings'];
            $this->totalUnits = $contractData['totalUnits'];
            $this->totalZones = $contractData['totalZones'];
            $this->total = $contractData['total'];
            $this->installmentsCount = $contractData['installmentsCount'];
            $this->serviceProvider = $contractData['serviceProvider'];
            $this->workerlist = User::where('service_provider', $this->serviceProvider->id)
                                ->where('user_type', 'sp_worker')
                                ->where('status', 1)
                                ->where('is_deleted', 'no')
                                ->whereNull('deleted_at') 
                                ->whereRaw('FIND_IN_SET(?, contract_ids)', [$contractId])
                                ->get();
            $this->startDate = $contractData['startDate'];
            $this->endDate = $contractData['endDate'];
            $this->perMonthAmount =    $contractData['perMonthAmount'];
            $this->serviceType = $contractData['serviceType'];
            $this->assetsName = $contractData['assetsName'];
            $this->percentage = $contractData['percentage'];
            $this->assetCategories = $contractData['assetCategories'];
            $this->smartAssignedServices = $contractData['smartAssignedServices'];
            $this->items = $contractData['items'];
            $this->cityNames = $contractData['cityNames'];
            $this->regionNames = $contractData['regionNames'];
            $this->propertyNames = $contractData['propertyNames'];
            $this->propertyNamesLimited = $contractData['propertyNamesLimited'];
            
            $this->attachment = $this->contract->picture ? Storage::disk('oci')->url($this->contract->picture) : null;
        }
     
        // Generate the variation order URLs
        $this->create_variation_order_url = route('variation.create', $this->contract->reference_uuid);
      
        // Get the variation order history
        $this->variation_order_history = $this->contract->advanceContractDraft()
                                                ->withTrashed()
                                                ->where('is_variation_order', 1)
                                                ->whereNotNull('status')
                                                ->whereNotIn('status', ['draft', 'incomplete'])
                                                ->with(['approvals.approver', 'approvals.serviceProvider'])
                                                ->get();
       $this->has_pending_variation_order = $this->variation_order_history
                                                ->contains(function ($order) {
                                                    return $order->status === 'pending' 
                                                        && is_null($order->deleted_at) 
                                                        && $order->initiated_by === Auth::id();
                                                });
        $this->loadTeamStatus();
                        
        // If contract not found, redirect to contracts list
        if (!$this->contract) {
            return redirect()->route('data.contracts.list');
        }
    }

    public function loadTeamStatus()
    {
        $workTime = WorkTimeFrame::where('user_id', Auth::user()->project_user_id)->first();

        $totalOpenWorkingDays = $workTime ? $workTime->total_open_days : 0;
        
        // 🔄 Clear old data
        $this->teamStatusSummary = [];
        // ⏳ Check if contract has started
        if (!$this->startDate || now()->lt(Carbon::parse($this->startDate))) {
            return;
        }


        $teams = WorkforceAndTeam::where('contract_id', $this->contractId)->get();

        foreach ($teams as $team) {
            $assignedWorkers = AssignedContractWorker::with('worker')
                ->where('contract_id', $this->contractId)
                ->where('role', $team->role)
                ->where('proficiency', $team->proficiency)
                ->get();

            $assignedCount = $assignedWorkers->count();

            $belowWageCount = 0;
            $attendanceMismatch = 0;
            $hoursMismatch = 0;
            $roleAdminLevelMismatch = 0;

            $roleMonthlyHours = ($team->working_hours ?? 0) * 4; // Assuming 4 weeks per month

            foreach ($assignedWorkers as $assigned) {
                $worker = $assigned->worker;

                if (!$worker) continue;

                //Calculate Worker Monthly Hours
                $assigned->workerMonthlyHours = $worker->attendance_target ? $worker->attendance_target * $totalOpenWorkingDays * 4 : 0;

                // 💰 Salary check
                if ($worker->salary !== null && $worker->salary < $team->minimum_wage) {
                    $belowWageCount++;
                }

                // 📅 Attendance mandatory mismatch
                if ($worker->attendance_mandatory !== $team->attendance_mandatory) {
                    $attendanceMismatch++;
                }

                // ⏱️ Monthly hours mismatch
                if ($worker->attendance_target !== null && $assigned->workerMonthlyHours < $roleMonthlyHours) {
                    $hoursMismatch++;
                }

                // 🧾 Role/Admin level mismatch
                if ($worker->role !== $team->role || $worker->admin_level !== $team->proficiency) {
                    $roleAdminLevelMismatch++;
                }
            }

            $this->teamStatusSummary[] = [
                'role' => $team->role,
                'proficiency' => $team->proficiency,
                'required' => $team->quantity,
                'assigned' => $assignedCount,
                'below_wage' => $belowWageCount,
                'attendance_mismatch' => $attendanceMismatch,
                'hours_mismatch' => $hoursMismatch,
                'role_admin_mismatch' => $roleAdminLevelMismatch,
                'is_complete' => $assignedCount >= $team->quantity,
                'is_partial' => $assignedCount > 0 && $assignedCount < $team->quantity,
                'is_empty' => $assignedCount === 0,
            ];
        }
    }



    public function openModal($mode, $role, $proficiency, $requiredSalary,$requiredAttendanceTarget)
    {
        $this->mode = $mode;
        $this->selectedRole = $role;
        $this->selectedProficiency = $proficiency;
        $this->requiredSalary = $requiredSalary;
        $this->requiredAttendanceTarget = $requiredAttendanceTarget;
        $this->isOpenModal = true;
        $workTime = WorkTimeFrame::where('user_id', Auth::user()->project_user_id)->first();

        $this->totalOpenDays = $workTime ? $workTime->total_open_days : 0;

        $this->getAssignedWorkers();
    
        if ($mode === 'link') {
            $this->selectedWorkerList = $this->assignedWorkers->pluck('worker_id')->toArray();
        }
    }


    public function closeModal()
    {
        $this->isOpenModal = false;
        $this->selectedWorkerList = [];
        $this->selectedRole = '';
        $this->selectedProficiency = '';
        $this->totalOpenDays = 0;
        $this->loadTeamStatus();
    }

    public function assignSelectedWorkers($workerIds = [])
    {
        // Validate and assign workers based on $workerIds
        $this->selectedWorkerList = is_array($workerIds) ? $workerIds : [];
   
        AssignedContractWorker::where('contract_id', $this->contractId)
                ->where('role', $this->selectedRole)
                ->where('proficiency', $this->selectedProficiency)
                ->delete();

        foreach ($this->selectedWorkerList as $workerId) {
            AssignedContractWorker::firstOrCreate([
                'contract_id' => $this->contractId,
                'worker_id' => $workerId,
                'role'       =>  $this->selectedRole,
                'proficiency'=> $this->selectedProficiency,
            ]);
        }
       
        $this->getAssignedWorkers();
    }

    public function getAssignedWorkers(){
     $this->assignedWorkers = AssignedContractWorker::where('contract_id',  $this->contractId)
                                    ->where('role', $this->selectedRole)
                                    ->where('proficiency', $this->selectedProficiency)
                                    ->with('worker.country') // 👈 this is important
                                    ->get();
    }

    public function setPage($page)
    {
        $this->propertyPage = $page;
    }

    public function createVariationOrder()
    {
        if (AdvanceContractDraft::hasPendingVariationOrder($this->contractId)) {
            $this->dispatchBrowserEvent('show-toastr', ['type' => 'error', 'message' => __('advance_contracts.variation_order.variation_create_validation')]);
            return;
        }

        return redirect()->to($this->create_variation_order_url);
    }

    /**
     * Render the Livewire view.
     *
     * @return \Illuminate\View\View
     */
    public function render()
    {
        return view('livewire.advance-contracts.advance-contract-detail');
    }

    
}

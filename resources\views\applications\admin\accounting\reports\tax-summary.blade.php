@extends('layouts.app')
@section('styles')
<!-- Bootstrap Datepicker CSS -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/css/bootstrap-datepicker.min.css">
<style type="text/css">
   /* Hide the calendar grid (days) */
  .ui-datepicker-calendar {
    display: none;
  }
</style>
@endsection
@section('content')
        <div class="contents crm">
           <div class="container-fluid">
            <div class="col-lg-12">
    <div class="row justify-content-sm-between align-items-center justify-content-center my-3 flex-sm-row flex-column">
        <div class="page-title-wrap p-0">
            <div class="page-title d-flex justify-content-between">
                <div class="page-title__left justify-content-sm-between align-items-center justify-content-center">
                    <div class="user-member__title mr-sm-25 ml-0">
                        <h4 class="text-capitalize fw-500 breadcrumb-title fs-16">
                            Manage Invoice Summary
                        </h4>
                    </div>
                </div>
            </div>
            <div>
                <ul class="atbd-breadcrumb nav">
                    <li class="atbd-breadcrumb__item">
                        <a>Dashboard</a>
                        <span class="breadcrumb__seperator">
                            <span class="la la-angle-right"></span>
                        </span>
                    </li>
                    <li class="atbd-breadcrumb__item">
                        <a>Manage Invoice Summary</a>
                    </li>
                </ul>
            </div>
        </div>

        <div class="d-flex gap-10 breadcrumb_right_icons">
            <div class="dropdown">
  
                    <button class="btn btn-default btn-primary no-wrap wh-45 px-0 dropdown-toggle" type="button" id="dropdownMenuButton" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false"><i class="iconsax mr-0 fs-18" icon-name="download-1"></i></button>
  <div class="dropdown-menu" aria-labelledby="dropdownMenuButton">
    <a class="dropdown-item" href="#"><i class="las la-file-pdf"></i> PDF</a>
    <a class="dropdown-item" href="#"><i class="las la-file-excel"></i> CSV</a>
  </div>
</div>

        </div>
        <!--====End Design for Export PDF===-->
    </div>
</div>


<div class="">
        <div class="mb-3" data-select2-id="108">
            <div class="" data-select2-id="107">
                <form wire:submit.prevent="applyFilters" class="fs-14">
                    <div class="d-flex flex-wrap justify-content-sm-end align-items-center justify-content-center gap-10">
                    <div class="d-flex gap-10"> 
                        <div class="max-w-180" data-select2-id="106">
                            <label for="" class="text-osool">Status</label>
                            <select class="form-control select2-new">
                                <option value="" data-select2-id="93">Select Status</option>
                                <option value="" data-select2-id="113">All Status</option>
                                <option value="Ongoing" data-select2-id="114">Ongoing</option>
                                <option value="Finished" data-select2-id="115">Finished</option>
                                <option value="OnHold" data-select2-id="116">OnHold</option>
                            </select>
                        </div>

                        <div class="">
                            <label for="" class="">&nbsp;</label>
                            <div class="d-flex gap-10">
                                <button type="submit" class="btn bg-opacity-new-primary btn-sm text-new-primary radius-md px-5">
                                    Apply
                                </button>
                                <button type="button" class="btn bg-opacity-loss btn-sm text-loss wh-45 radius-md">
                                    <i class="iconsax mr-0 fs-18" icon-name="rotate-left"></i>
                                    <!-- Reset -->
                                </button>
                            </div>
                        </div>
                    </div>
                    </div>
                </form>
            </div>
        </div>

        <div class="row mt-3 mb-sm-0 mb-3">
            <div class="col-sm-6">
                <input type="hidden" value="All Income Summary Report of Jan-2025 to Dec-2025" id="filename">
                <div class="card p-4 mb-sm-0 mb-3">
                    <h6 class="text-dark mb-0">Report :</h6>
                    <p class="mb-0">Income Summary</p>
                </div>
            </div>
            <div class="col-sm-6">
                <div class="card p-4">
                    <h6 class="text-dark mb-0">Duration :</h6>
                    <p class="mb-0">Jan-2025 to Dec-2025</p>
                </div>
            </div>
        </div>              

    <div class="card mt-3 crm">
        <div class="card-body px-0">
            <div class="card-header border-0 d-flex justify-content-between align-items-center">
                <h6 class="text-capitalize text-osool fw-500 mb-3 mb-sm-0">Income</h6>

                <div class="d-flex gap-10 table-search">
                    <div class="position-relative">
                    <input type="text" class="form-control" placeholder="Search">
                    <i class="iconsax field-icon fs-18 mr-0" icon-name="search-normal-2"></i>
                </div>
                     <button class="btn btn-export text-dark"><i class="iconsax icon fs-22 mr-0" icon-name="upload-1"></i> Export</button>
                </div>
            </div>
             <div class="userDatatable projectDatatable project-table bg-white w-100 border-0">
            <div class="table-responsive mt-3 mb-3">
                <table class="table radius-0">
                    <thead>
                        <tr>
                            <th>Tax</th>
                            <th>January</th>
                            <th>February</th>
                            <th>March</th>
                            <th>April</th>
                            <th>May</th>
                            <th>June</th>
                            <th>July</th>
                            <th>August</th>
                            <th>September</th>
                            <th>October</th>
                            <th>November</th>
                            <th>December</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>VAT</td>
            <td>0.00 ﷼</td>
            <td>0.00 ﷼</td>
            <td>0.00 ﷼</td>
            <td>0.00 ﷼</td>
            <td>0.00 ﷼</td>
            <td>0.00 ﷼</td>
            <td>0.00 ﷼</td>
            <td>0.00 ﷼</td>
            <td>0.00 ﷼</td>
            <td>0.00 ﷼</td>
            <td>0.00 ﷼</td>
            <td>0.00 ﷼</td>
                        </tr>
                    </tbody>
                </table>

            </div>
        </div>

            <div class="card-header border-0 d-flex justify-content-between align-items-center">
                <h6 class="text-capitalize text-osool fw-500 mb-3 mb-sm-0">Expense</h6>

                <div class="d-flex gap-10 table-search">
                    <div class="position-relative">
                    <input type="text" class="form-control" placeholder="Search">
                    <i class="iconsax field-icon fs-18 mr-0" icon-name="search-normal-2"></i>
                </div>
                     <button class="btn btn-export text-dark"><i class="iconsax icon fs-22 mr-0" icon-name="upload-1"></i> Export</button>
                </div>
            </div>
             <div class="userDatatable projectDatatable project-table bg-white w-100 border-0">
                <div class="table-responsive mt-3">
                    <table class="table">
    <thead>
        <tr>
            <th>Tax</th>
            <th>January</th>
            <th>February</th>
            <th>March</th>
            <th>April</th>
            <th>May</th>
            <th>June</th>
            <th>July</th>
            <th>August</th>
            <th>September</th>
            <th>October</th>
            <th>November</th>
            <th>December</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>VAT</td>
            <td>0.00 ﷼</td>
            <td>0.00 ﷼</td>
            <td>0.00 ﷼</td>
            <td>0.00 ﷼</td>
            <td>0.00 ﷼</td>
            <td>0.00 ﷼</td>
            <td>0.00 ﷼</td>
            <td>0.00 ﷼</td>
            <td>0.00 ﷼</td>
            <td>0.00 ﷼</td>
            <td>0.00 ﷼</td>
            <td>0.00 ﷼</td>
        </tr>
    </tbody>
</table>

                </div>
            </div>
            
</div>


    </div>
          </div>
        



</div>


           </div>
        </div>







@endsection

@section('scripts')
<!-- Bootstrap Datepicker JS -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/js/bootstrap-datepicker.min.js"></script>
<script type="text/javascript">
    $(".datepicker").datepicker();
    $("#selectUsers,#statusList").select2();
</script>
<script src="{{ asset('vendor_assets/js/Chart.min.js') }}"></script>
<script>
     $('.startMonth').datepicker({
        format: "mm/yyyy",
          startView: "months",
          minViewMode: "months",
          autoclose: true,
          container: '.start-month',      
          autoclose: true,
          startDate: new Date(2025, 0),   // Jan 2020
          endDate: new Date(2025, 11)     // Dec 2025
     });
     $('.endMonth').datepicker({
        format: "mm/yyyy",
          startView: "months",
          minViewMode: "months",
          autoclose: true,
          container: '.end-month',            
          autoclose: true,
          startDate: new Date(2025, 0),   // Jan 2020
          endDate: new Date(2025, 11)     // Dec 2025
     });
   const ctx = document.getElementById("myBarChart").getContext("2d");

    // Shadow plugin for Chart.js v2.9.2
    Chart.plugins.register({
      beforeDraw: function(chart) {
        if (chart.config.type === 'bar') {
          const ctx = chart.chart.ctx;
          ctx.save();
          ctx.shadowColor = 'rgba(0, 0, 0, 0.3)';
          ctx.shadowBlur = 8;
          ctx.shadowOffsetX = 2;
          ctx.shadowOffsetY = 2;
        }
      },
      afterDraw: function(chart) {
        if (chart.config.type === 'bar') {
          chart.chart.ctx.restore();
        }
      }
    });

    new Chart(ctx, {
      type: 'bar',
      data: {
        labels: [
          "January", "February", "March", "April", "May", "June",
          "July", "August", "September", "October", "November", "December"
        ],
        datasets: [{
          label: 'Invoice',
          backgroundColor: '#01A9F3',
          data: [0.4, 1.2, 0.9, 0.7, 1.1, 0.6, 1.8, 1.3, 1.5, 0.8, 1.6, 1.0],
          borderRadius: 4
        }]
      },
      options: {
        legend: { display: false },
        scales: {
          yAxes: [{
            ticks: {
              beginAtZero: true,
              stepSize: 0.4
            },
            scaleLabel: {
              display: true,
              labelString: 'Invoice'
            }
          }],
          xAxes: [{
            scaleLabel: {
              display: true,
              labelString: 'Months'
            }
          }]
        }
      }
    });
  </script>
@endsection
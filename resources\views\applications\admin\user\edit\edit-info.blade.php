@extends('layouts.app')
@section('styles')
@endsection
@section('content')
<div class="contents">
   <div class="container-fluid">
      <div class="row">
         <div class="col-lg-12">
            <div class="page-title-wrap">
                <div class="page-title d-flex justify-content-between">
                    <div class="page-title__left">
                        <div class="d-flex align-items-center user-member__title justify-content-center mr-sm-25">
                            <h4 class="text-capitalize fw-500 breadcrumb-title">
                        <a href="{{ url()->previous() }}"><i class="las la-arrow-left"></i></a> {{__('user_management_module.user_button.edit_user')}}</h4>
                         </div>
                    </div>
                </div>
                <!--{{ Breadcrumbs::render('edit-user') }}-->
            </div>
         </div>
      </div>
   </div>
   <div class="container-fluid">
      <div class=" checkout wizard1 wizard7 global-shadow px-sm-50 px-20 py-sm-50 py-30 mb-30 bg-white radius-xl w-100">
         <div class="row justify-content-center">
            <div class="col-xl-8">
               <div class="checkout-progress-indicator content-center col-md-10">
                  <div class="checkout-progress">

                  @if($data['u_data']->user_type!="sp_worker")

                     <div class="step current" id="1">
                        <span>1</span>
                        <span>{{__('user_management_module.common.user_info')}}</span>
                     </div>
                     <div class="current"><img src="{{ asset('img/svg/checkout.svg') }}" alt="img" class="svg"></div>
                     @if($data['u_data']->user_type != "sp_admin")
                        <div class="step" id="2">
                           <span>2</span>
                           <span>{{__('user_management_module.common.user_role')}}</span>
                        </div>
                        <div class="current"><img src="{{ asset('img/svg/checkout.svg') }}" alt="img" class="svg"></div>
                        <div class="step" id="3">
                           <span>3</span>
                           @if(isset($data['u_data']->user_type) && ($data['u_data']->user_type=="sp_worker"))
                           <span>{{__('user_management_module.common.password')}}</span>
                           @else
                           <span>{{__('user_management_module.common.user_previleges')}}</span>
                           @endif
                        </div>
                        <div class="current"><img src="{{ asset('img/svg/checkout.svg') }}" alt="img" class="svg"></div>
                        @else {{-- @flip1@ add else part for show previlages --}}
                        <div class="step" id="3">
                           <span>2</span>
                           <span>{{__('user_management_module.common.user_previleges')}}</span>
                        </div>
                        <div class="current"><img src="{{ asset('img/svg/checkout.svg') }}" alt="img" class="svg"></div>
                     @endif
                     <div class="step" id="4">
                        @if(isset($data['u_data']->user_type) && ($data['u_data']->user_type=="sp_admin"))
                        <span>3</span>
                        @else
                        <span>4</span>
                        @endif
                        <span>{{__('user_management_module.common.confirm')}}</span>
                     </div>
                  @else
                     <div class="step current" id="1">
                        <span>1</span>
                        <span>{{__('user_management_module.common.user_info')}}</span>
                     </div>
                     <div class="current"><img src="{{ asset('img/svg/checkout.svg') }}" alt="img" class="svg"></div>
                     <div class="step" id="2">
                        <span>2</span>
                        <span>{{__('user_management_module.common.user_role')}}</span>
                     </div>
                     <div class="current"><img src="{{ asset('img/svg/checkout.svg') }}" alt="img" class="svg"></div>

                     <div class="step" id="3">
                        <span>3</span>
                        <span>{{__('user_management_module.common.user_previleges')}}</span>
                     </div>

                     <div class="current"><img src="{{ asset('img/svg/checkout.svg') }}" alt="img" class="svg"></div>
                     <div class="step" id="4">
                        <span>4</span>
                        <span>{{__('user_management_module.common.password')}}</span>
                     </div>
                     <div class="current"><img src="{{ asset('img/svg/checkout.svg') }}" alt="img" class="svg"></div>
                     <div class="step" id="5">
                        <span>5</span>
                        <span>{{__('user_management_module.common.confirm')}}</span>
                     </div>


                  @endif


                  </div>
               </div>
               <!-- checkout -->
               <div class="row justify-content-center">
                  <div class="col-xl-7 col-lg-8 col-sm-10">
                     <div class="card checkout-shipping-form pt-2 pb-30 border-0">
                        <div class="card-header border-bottom-0 align-content-start pb-sm-0 pb-1 px-0">
                           <h4 class="fw-400">{{__('user_management_module.common.user_info')}}</h4>
                        </div>
                        <div class="card-body px-0 pb-0">
                             <div class="edit-profile__body">
                                 <?php
                                 $form_id = 'user_create_form_edit';
                                 ?>
                                 @if($data['u_data']->user_type == 'sp_worker' && Auth::user()->user_type != 'building_manager' )
                                 <?php
                                 $form_id = 'user_create_form_edit_worker';
                                 ?>
                                 @endif
                                <form method="post" class="edit_user_form" id="{{ $form_id }}" action="{{ route('users.edit.role', Crypt::encryptString($data['u_data']->id))}}" enctype="multipart/form-data" autocomplete="off">
                                @csrf
                                <input type="hidden" id="user_id" name="user_id" value="{{ $data['u_data']->id }}">
                                <input type="hidden" id="project_id" name="project_id" value="{{ $data['u_data']->project_id }}">
                                <input type="hidden" id="service_provider" name="service_provider" value="{{ $data['u_data']->service_provider }}">
                                <div class="account-profile d-flex align-items-center mb-4 ">
                                    <div class="pro_img_wrapper">
                                       <input id="file_upload" type="file" name="profile_img" class="d-none" accept="image/*">
                                       <!-- Profile picture image-->
                                       <input type="hidden" name="isImageRemove" value="">
                                       <label for="file_upload">                                       
                                       <img class="ap-img__main rounded-circle wh-120 bg-lighter d-flex" src="{{($data['u_data']->profile_img) ? ImagesUploadHelper::displayImage($data['u_data']->profile_img, 'uploads/profile_images') : '/img/upload.png' }}" alt="profile" id="output_pic">
                                       <span class="cross" id="remove_pro_pic" >
                                       <span data-feather="camera" ></span>
                                       </span>
                                       </label>
                                       <?php
                                          if(trim($data['u_data']->profile_img) != "")
                                          {
                                             $hideclass = '';
                                          }
                                          else
                                          {
                                             $hideclass = 'hide';
                                          }
                                       ?>
                                       <span class="remove-img text-white btn-danger rounded-circle <?=$hideclass;?>" data-toggle="modal" data-target="#confirmDeletePhoto">
                                         <span data-feather="x"></span>
                                       </span>
                                    </div>
                                    <div class="account-profile__title">
                                       <h6 class="fs-15 ml-20 fw-500 text-capitalize">{{__('user_management_module.user_forms.label.photo')}}</h6>
                                    </div>
                                 </div>
                                 @if( Auth::user()->user_type != 'building_manager' && Auth::user()->user_type != 'supervisor' && $data['u_data']->user_type != 'admin')
                                 <div class="form-group mb-20">
                                      <div class="usertype_option">
                                         <label for="user_type">
                                         {{__('user_management_module.user_forms.label.user_type')}} <small class="required">*</small>
                                         </label>
                                         <input type="hidden" name ="user_type" value="{{$data['u_data']->user_type}}" >

                                         <select class="form-control" id="user_type" name="user_type" disabled required>
                                            <option value="" selected disabled>{{__('user_management_module.user_forms.label.user_type')}}</option>
                                            @foreach($data['usertypeList'] as $user_type)


                                                @if(App::getLocale()=='en')
                                                   <option @if(isset($data['u_data']->user_type) && ($data['u_data']->user_type==$user_type->slug)) selected @endif value="{{$user_type->slug}}">{{$user_type->name}}</option>
                                                @else
                                                   <option @if(isset($data['u_data']->user_type) && ($data['u_data']->user_type==$user_type->slug)) selected @endif value="{{$user_type->slug}}">{{$user_type->name_ar}}</option>
                                                @endif

                                            @endforeach
                                         </select>
                                      </div>

                                   </div>
                                   @elseif( $data['u_data']->user_type == 'admin' )
                                   <input type="hidden"  id="user_type" name="user_type" value="admin" />
                                 <div class="form-group mb-20">
                                      <div class="usertype_option">
                                         <label for="user_type">
                                         {{__('user_management_module.user_forms.label.user_type')}} <small class="required">*</small>
                                         </label>

                                         <select disabled class="form-control" id="user_type" name="user_type" required>
                                            <option value="" selected disabled>{{__('user_management_module.user_forms.label.user_type')}}</option>
                                              <option value="admin"  selected >Admin</option>
                                         </select>
                                      </div>

                                   </div>
                                   @else
                                   <input type="hidden" name="user_type" id="user_type" value="{{$data['u_data']->user_type}}">
                                   @endif

                                 @if($data['u_data']->user_type == 'sp_admin' || $data['u_data']->user_type == 'supervisor')
                                    <div class="form-group mb-25">
                                      <label for="emp_name">
                                        {{__('user_management_module.user_forms.label.company_name')}}
                                         <small class="required">*</small></label>
                                         <input type="hidden" name="service_provider" value="{{$data['u_data']->service_provider}}" >
                                       <select class="form-control" id="service_provider" name="service_provider" disabled>
                                            <option value="" selected disabled>{{__('user_management_module.user_forms.label.company_name')}}</option>
                                            @foreach($data['companyList'] as $company)
                                              <option {{ ($data['u_data']->service_provider==$company->id) ? 'selected' : '' }} value="{{$company->id}}">{{$company->name}} - {{$company->service_provider_id}}</option>
                                            @endforeach
                                       </select>
                                       </div>
                                    <div class="form-group mb-25">
                                      <label id="user_type_name" for="emp_name">
                                      {{__('user_management_module.user_forms.label.emp_name')}}
                                      <small class="required">*</small></label>
                                      <input type="text" class="form-control" name="name" id="name" value="{{($data['u_data']->name) ? $data['u_data']->name  : '' }}">
                                   </div>

                                   <div class="form-group mb-25">
                                   <label for="worker_id">{{__('user_management_module.user_forms.label.emp_id')}}
                                      <input type="text" maxlength="10"  class="form-control" name="emp_id" onkeyup="getEmpiddata()" id="emp_id" value="{{($data['u_data']->emp_id) ? $data['u_data']->emp_id  : '' }}" placeholder="{{__('user_management_module.user_forms.place_holder.emp_id')}}" >
                                   </div>
                                 @else
                                 @if(Auth::user()->user_type != "building_manager")
                                 <div class="form-group mb-20 building_admin_info">
                                      <div class="usertype_option">
                                         <label for="building_admin">
                                         {{__('user_management_module.user_forms.label.employee_admin')}} <small class="required">*</small>
                                         </label>
                                         <select class="form-control" name="building_admin" id="building_admin" >
                                            <option value="" selected disabled>{{__('user_management_module.user_forms.label.employee_admin')}}</option>
                                            @if(isset($data['building_manager_list']))
                                              @foreach($data['building_manager_list'] as $building_manager)
                                                <option value="{{$building_manager->id}}" @if(isset($data['u_data']->sp_admin_id) && ($data['u_data']->sp_admin_id == $building_manager->id)) selected @endif>{{$building_manager->name}}</option>
                                              @endforeach
                                            @endif
                                         </select>
                                         <div id="building_admin-error"></div>
                                      </div>
                                   </div>
                                    @endif
                                   <div class="form-group mb-20 worker_info">
                                      <div class="usertype_option">
                                         <label for="supervisor_id">
                                         {{__('user_management_module.user_forms.label.worker_admin')}} <small class="required">*</small>
                                         </label>
                                         <select class="form-control" id="supervisor_id" name="supervisor_id">
                                            <option value="" selected disabled>{{__('user_management_module.user_forms.label.worker_admin')}}</option>
                                            @if(!empty($data['sp_supervisor_list']))
                                            @foreach($data['sp_supervisor_list'] as $sl)
                                            <option value="{{$sl->id}}" @if(isset($data['u_data']->supervisor_id) && ($data['u_data']->supervisor_id == $sl->id)) selected @endif>{{$sl->name}}</option>
                                            @endforeach;
                                            @endif
                                         </select>
                                         <div id="supervisor_id-error"></div>
                                      </div>
                                   </div>

                                  <div class="form-group mb-25">
                                      <label for="emp_name">
                                      {{__('user_management_module.user_forms.label.emp_name')}}
                                      <small class="required">*</small></label>
                                      <input type="text" class="form-control" name="name" id="name" value="{{($data['u_data']->name) ? $data['u_data']->name  : '' }}">
                                      </div>

                                      @if($data['u_data']->user_type == 'sp_worker')
                                      <div class="form-group mb-25">
                                       <label for="worker_id">{{__('user_management_module.user_forms.label.worker_id')}} <i class="fas fa-question-circle" onfocus="theFocus(this);"  data-toggle="tooltip" data-placement="top" title="{{__('user_management_module.common.worker_id_description')}}"></i><small class="required">*</small></label>
                                          <input type="text" maxlength="10"  class="form-control" name="emp_id" onkeyup="getEmpiddata()" id="emp_id" value="{{($data['u_data']->emp_id) ? $data['u_data']->emp_id  : '' }}" placeholder="{{__('user_management_module.user_forms.place_holder.emp_id')}}">
                                       </div>
                                       @else
                                       <div class="form-group mb-25">
                                       <label for="worker_id">{{__('user_management_module.user_forms.label.emp_id')}} 
                                       </label>
                                          <input type="text" maxlength="10"  class="form-control" name="emp_id" onkeyup="getEmpiddata()" id="emp_id" value="{{($data['u_data']->emp_id) ? $data['u_data']->emp_id  : '' }}" placeholder="{{__('user_management_module.user_forms.place_holder.emp_id')}}" >
                                       </div>
                                       @endif
                                      @endif


                                   <div class="form-group mb-25 email_box">
                                      <label for="emp_email">{{__('user_management_module.user_forms.label.emp_email')}} <small class="required">*</small></label>
                                      <input type="email"  class="form-control" id="email" name="email" value="{{($data['u_data']->email) ? $data['u_data']->email  : '' }}">
                                   </div>

                                    <div class="form- group mb-20 user_info nationality_select">
                                       <div class="">
                                          <label for="nationality_id">
                                          {{__('user_management_module.user_forms.label.nationality')}} <small class="required">*</small>
                                          </label>
                                          <select class="form-control" id="nationality_id" name="nationality_id">
                                             <option value="" selected disabled>{{__('user_management_module.user_forms.label.choose_a_nationality')}}</option>

                                             @foreach($data['nationalities'] as $nationality)
                                                @if(App::getLocale()=='en')
                                                <option value="{{$nationality->id}}" {{ $data['u_data']->country_id == $nationality->id ? 'selected' : '' }}>{{$nationality->name_en}}</option>
                                                @else
                                                <option value="{{$nationality->id}}" {{ $data['u_data']->country_id == $nationality->id ? 'selected' : '' }}>{{$nationality->name_ar}}</option>
                                                @endif
                                             @endforeach

                                          </select>
                                       </div>
                                       <div id="nationality-id-error"></div>
                                    </div>

                                    <div class="form- group mb-20 user_info favorite_language_select">
                                       <div class="">
                                          <label for="favorite_language">
                                          {{__('user_management_module.user_forms.label.favorite_language')}}
                                          </label>
                                          <select class="form-control" id="favorite_language" name="favorite_language">
                                             <option value="" selected disabled>{{__('user_management_module.user_forms.label.choose_a_favorite_language')}}</option>
                                             <option value="en" {{ $data['u_data']->favorite_language == "en" ? 'selected' : '' }}>{{__('user_management_module.user_forms.label.english')}}</option>
                                             <option value="ar" {{ $data['u_data']->favorite_language == "ar" ? 'selected' : '' }}>{{__('user_management_module.user_forms.label.arabic')}}</option>
                                             <option value="ur" {{ $data['u_data']->favorite_language == "ur" ? 'selected' : '' }}>{{__('user_management_module.user_forms.label.urdu')}}</option>

                                          </select>
                                       </div>
                                       <div id="favorite-language-error"></div>
                                    </div>

                                    <div class="form- group mb-20 user_info profession_select">
                                       <div class="">
                                          <label for="profession_id">
                                          {{__('user_management_module.user_forms.label.select_profession_heading')}} <small class="required">*</small>
                                          </label>
                                          <select class="form-control" id="profession_id" name="profession_id">
                                             <option value="" selected disabled>{{__('user_management_module.user_forms.label.choose_a_profession')}}</option>

                                             @foreach($data['workerProfessions'] as $profession)
                                                @if(App::getLocale()=='en')
                                                <option value="{{$profession->id}}" {{ $data['u_data']->profession_id == $profession->id ? 'selected' : '' }}>{{$profession->profession_en}}</option>
                                                @else
                                                <option value="{{$profession->id}}" {{ $data['u_data']->profession_id == $profession->id ? 'selected' : '' }}>{{$profession->profession_ar}}</option>
                                                @endif
                                             @endforeach

                                          </select>
                                       </div>
                                       <div id="profession-id-error"></div>
                                    </div>

                                   <div class="form-group mb-20 profession" {{ $data['u_data']->profession_id == 10 ? 'style=display:block' : 'style=display:none' }}>
                                      <label for="emp_dept">
                                        <span class="emp_dept_label">{{__('user_management_module.user_forms.label.emp_dept')}} <small class="required">*</small></span>
                                      </label>
                                      <input type="text" class="form-control" name="emp_dept" id="emp_dept"value="{{($data['u_data']->emp_dept) ? $data['u_data']->emp_dept  : '' }}" {{ $data['u_data']->profession_id == 10 ? 'required' : '' }}>
                                   </div>

                                   <div class="form-group mb-25">
                                      <label for="emp_phone_number">{{__('user_management_module.user_forms.label.emp_phone')}}</label>
                                      <div class="input-group mb-3 phone-ltr">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text" id="basic-addon1">+966</span>
                                        </div>
                                        <input type="tel"  class="form-control" id="phone" name="phone" value="{{($data['u_data']->phone) ? $data['u_data']->phone  : '' }}" placeholder="576428964">
                                       </div>

                                      {{--<small id="emailHelp" class="form-text text-muted">Phone number can't be update in this page to update phone number <a href="#">click here</a></small> --}}
                                   </div>
                                    <!-- OS3-2355 Adding new fields for workers -->
                                    @include('components.forms.sp_worker_fields', ['showWorkerFields' => true])
                                    <!-- OS3-2355 Adding new fields for workers  End --> 
   
                                   @if(Auth::user()->user_type != 'supervisor' && $data['u_data']->user_type != 'sp_worker')
                                   

                                   <input type="hidden" name="country_id" id="country_id1" value="1">
                                   <input type="hidden" name="city_id" id="city_id1" value="1">

                                   @endif

                                   <input type="hidden" id="ajax_check_useremail_unique" value="{{route('users.ajax_check_unique_useremail_edit')}}">
                                   <input type="hidden" id="ajax_check_userphone_unique_edit" value="{{route('users.ajax_check_userphone_unique_edit')}}">
                                   <input type="hidden" id="user_type" value="{{$data['user_type'] ?? null}}">

                                   <input type="hidden" id="ajax_check_employee_id_unique_edit" value="{{route('users.ajax_check_unique_emp_id')}}">

                                   <div class="button-group d-flex pt-25 justify-content-end">
                                     <a href="{{ route('users.list') }}" class="btn btn-light btn-default btn-sm btn-squared fw-400 text-capitalize radius-md">{{__('user_management_module.user_button.cancel')}}</a>
                                     @if($data['u_data']->user_type == 'admin')
                                      <button onclick="custom_submit()" type="button" class="btn btn-primary btn-default btn-squared text-capitalize radius-md shadow2">{{__('user_management_module.user_button.save_next')}}
                                      </button>
                                    @else
                                       <button onclick="custom_submit()" type="button" class="btn btn-primary btn-default btn-squared text-capitalize radius-md shadow2">{{__('user_management_module.user_button.save_next')}}
                                      </button>
                                    @endif
                                   </div>
                                </form>
                             </div>
                        </div>
                     </div>
                     <!-- ends: card -->
                  </div>
                  <!-- ends: col -->
               </div>
            </div>
            <!-- ends: col -->
         </div>
      </div>
      <!-- End: .global-shadow-->
   </div>
</div>
<!-- CONFIRM DELETE Photo MODAL START -->

<div class="modal new-member  bouncein-new" id="confirmDeletePhoto" role="dialog" tabindex="-1"
        aria-labelledby="staticBackdropLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-md">
            <div class="modal-content  radius-xl  bouncein-new">

                <div class="modal-body">
                    <div class="create-asset-modal">
                        <h2 class="mb-3 fs-20"><i class="fa fa-exclamation-circle mr-1 color-warning"
                                aria-hidden="true"></i>
                            {{ __('data_properties.property_forms.label.sure_remove_photo') }} </h2>
                    </div>

                        <div class="button-group d-flex justify-content-end pt-25">
                            <div class="button-group d-flex justify-content-end pt-25">
                                <button type="button" class="btn btn-light   btn-squared text-capitalize"
                                    data-dismiss="modal" aria-label="Close">
                                    {{ __('data_properties.property_button.cancel') }}

                                </button>
                                <button type="button" class="btn btn-danger btn-default btn-squared text-capitalize confirm_remove_photo" data-dismiss="modal" aria-label="Close">
                                    {{ __('data_properties.property_button.remove') }}
                                </button>

                            </div>

                        </div>
                </div>
            </div>
        </div>
    </div>
</div>

    <!-- CONFIRM DELETE Photo MODAL ENDS -->
@endsection

@section('scripts')
<script type="text/javascript" src="{{asset('js/admin/users/create.js')}}"></script>
<script type="text/javascript">
   $("#profession_id").select2({
     placeholder:translations.user_management_module.user_forms.label.choose_a_profession,
     dropdownCssClass: "tag",
     language: { noResults: () => translations.general_sentence.validation.No_results_found,}
   });
   $("#nationality_id").select2({
     placeholder:translations.user_management_module.user_forms.label.choose_a_nationality,
     dropdownCssClass: "tag",
     language: { noResults: () => translations.general_sentence.validation.No_results_found,}
   });
   $("#favorite_language").select2({
     placeholder:translations.user_management_module.user_forms.label.choose_a_favorite_language,
     dropdownCssClass: "tag",
     language: { noResults: () => translations.general_sentence.validation.No_results_found,}
   });
  var selected_user_type_val = $('#user_type').val();
  var user_type_val = $('#user_type').val();
  var loggedin_user_type = '<?=Auth::user()->user_type;?>';
  //alert(loggedin_user_type);
  if(selected_user_type_val=='sp_admin') {
   $('#user_type_name').html(translations.data_service_provider.serviceProvider_forms.label.service_provider_name+' <small class="required">*</small>');
  }
  $('.building_admin_info').hide();
  if(selected_user_type_val=='sp_worker')
  {
   $(".phone_required_mark").hide();
   $('.profession_select').show();
   //alert(loggedin_user_type);
   if(loggedin_user_type != "supervisor")
   {
      $('.worker_info').show();
   }
   else
   {
      $('.worker_info').hide();
   }
   $("#emp_dept").attr("placeholder", translations.user_management_module.user_forms.label.profession);
   //$(".emp_dept_label").text(translations.user_management_module.user_forms.label.profession);
   $(".emp_dept_label").html(`{{__('user_management_module.user_forms.label.profession')}} <small class="required">*</small>`)
   $('.email_box').hide();
   $('#email').attr("type","hidden");
   $("#user_create_form_edit").prop('id','user_create_form_edit_worker');
  }
  else
  {
   $('.profession_select').hide();
   $(".phone_required_mark").show();
      if(selected_user_type_val=='building_manager_employee')
      {
         $('.building_admin_info').show();
      }
      $('.worker_info').hide();
      $("#emp_dept").attr("placeholder", translations.user_management_module.user_forms.label.emp_dept);
      $('.emp_dept_label').show();
      $('#email').attr("type","email");
      $('.email_box').show();
      $("#user_create_form_edit_worker").prop('id','user_create_form_edit');
  }
  function getEmpiddata(){
   sessionStorage.setItem('edituser_emp_id', JSON.stringify($('#emp_id').val()));
   }

  $('#name').keyup(function() {
      sessionStorage.setItem('edituser_name', JSON.stringify($(this).val()));
   });

   $('#emp_id').keyup(function() {
      sessionStorage.setItem('edituser_emp_id', JSON.stringify($(this).val()));
   });

   $('#email').keyup(function() {
      sessionStorage.setItem('edituser_email', JSON.stringify($(this).val()));
   });

   $('#emp_dept').keyup(function() {
      sessionStorage.setItem('edituser_emp_dept', JSON.stringify($(this).val()));
   });

   $('#phone').keyup(function() {
      sessionStorage.setItem('edituser_phone', JSON.stringify($(this).val()));
   });


   var storedusernameValues = sessionStorage.getItem('edituser_name');
         if(storedusernameValues)
         {
            $("#name").val(JSON.parse(storedusernameValues));
         }
         var storedusebuildingadminValues = sessionStorage.getItem('edituser_building_admin');
         if(storedusebuildingadminValues)
         {
            $("#building_admin").val(JSON.parse(storedusebuildingadminValues)).trigger('change');
         }
         var storeduserempidValues = sessionStorage.getItem('edituser_emp_id');
         if(storeduserempidValues)
         {
            $("#emp_id").val(JSON.parse(storeduserempidValues));
         }
         var storeduseremailValues = sessionStorage.getItem('edituser_email');
         if(storeduseremailValues)
         {
            $("#email").val(JSON.parse(storeduseremailValues));
         }
         var storeduserempdeptValues = sessionStorage.getItem('edituser_emp_dept');
         if(storeduserempdeptValues)
         {
            $("#emp_dept").val(JSON.parse(storeduserempdeptValues));
         }
         var storeduserphoneValues = sessionStorage.getItem('edituser_phone');
         if(storeduserphoneValues)
         {
            $("#phone").val(JSON.parse(storeduserphoneValues));
         }

  if(localStorage['values']){

        localStorage['values'].clear();

    }

    function custom_submit() {
      var user_type = $('#user_type').val();
      if(user_type == 'supervisor' ) {
         swal({
            title: translations.general_sentence.modal.edit_warning_title,
            text: translations.general_sentence.modal.edit_warning_sp,
            icon: "warning",
            buttons: true,
            dangerMode: true,
            showCancelButton: true,
            confirmButtonText:  translations.general_sentence.swal_buttons.confirm,
            cancelButtonText:  translations.general_sentence.swal_buttons.cancel,
            /*

            buttons: [
                translations.general_sentence.swal_buttons.cancel,
                translations.general_sentence.swal_buttons.confirm,
            ],
            */
        },
        function(willDelete) {

            if (willDelete) {
               $('.edit_user_form').submit();
            }
        });
      }
      else if(user_type == 'building_manager' ) {
         swal({
            title: translations.general_sentence.modal.edit_warning_title,
            text: translations.general_sentence.modal.edit_warning_bm,
            icon: "warning",
            buttons: true,
            dangerMode: true,
            showCancelButton: true,
            confirmButtonText:  translations.general_sentence.swal_buttons.confirm,
            cancelButtonText:  translations.general_sentence.swal_buttons.cancel,
            /*

            buttons: [
                translations.general_sentence.swal_buttons.cancel,
                translations.general_sentence.swal_buttons.confirm,
            ],
            */
        },
        function(willDelete) {

            if (willDelete) {
               $('.edit_user_form').submit();
            }
        });
      }
      else {
         $('.edit_user_form').submit();
      }

   }

</script>

<script type="text/javascript">
   //$("#service_provider,#country_id,#city_id").select2({});
  $("#country_id,#city_id").select2({});

   $(document).ready(function() {
      $('#emp_dept').attr('maxlength', 20); // Set the new max-length attribute
      //$('.profession_select').hide();
      //$('#emp_dept').prop('required', false); // Make input not required
      // if(user_type_val!='')
      // {
      //    if(user_type_val=='sp_worker')
      //    {
      //       //$('.profession').hide();
      //       $('.profession_select').show();
      //    }
      //    else
      //    {
      //       $('.profession_select').hide();
      //    }
      // }
      $('#profession_id').on('change', function() {
         var selectedOption = $(this).val();
         
         if (selectedOption === '10') {
               $('.profession').show();
               $('#emp_dept').prop('required', true); // Make input required
               $('#emp_dept').attr('maxlength', 15); // Set the new max-length attribute
               $('#emp_dept').val('');
         } else {
               $('.profession').hide();
               $('#emp_dept').prop('required', false); // Make input not required
               $('#emp_dept').attr('maxlength', 20); // Set the new max-length attribute
               $('#emp_dept').val('');
         }
      });

      isSPWorker($("#user_type").val());

      $("#user_type").on("change", function() {
         var user_type_val = $(this).val();
         if (user_type_val == 'sp_worker') {
            $("#service_provider").show();
         else{
            $("#service_provider").hide();
         }

      }

   });

   function isSPWorker(user_type_val){
      if( $("#user_type").length > 0){
         if (user_type_val == 'sp_worker') {
               $("#service_provider").show();
         }else{
               $("#service_provider").hide();
         }
      }
   }
</script>
@endsection

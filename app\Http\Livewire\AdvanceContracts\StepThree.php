<?php

namespace App\Http\Livewire\AdvanceContracts;

use Livewire\Component;
use App\Models\AssetName;
use Illuminate\Http\Request;
use App\Models\ServiceProvider;
use Akaunting\Api\Data\ItemData;
use Illuminate\Support\Facades\Log;
use App\Models\AdvanceContractDraft;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Validator;
use App\Repositories\AdvanceContractRepository;

use Akaunting\Api\Data\Providers\FormDataProvider;

class StepThree extends Component
{
    public $uuid;

    public $serviceProvider;

    public $assets = [];

    public $assetNames = [];

    public $itemsName = [];

    public $ownership = 'admin';

    public $items = [];

    public $stocks = ['admin' => [], 'service_provider' => []];

    public $isOpenModal = false;

    public $selectableItems = [];

    public $selectedItems = [];

    public $route_prefix;

    public $selectedItemsList = ['admin' => [], 'service_provider' => []];
    public $isVariation = false;
    public $currentStep = 3;

  public function rules()
    {
        return [
            'ownership' => 'required|string',

            "stocks.{$this->ownership}.*.open_stock" => 'required|numeric|min:1',
            "stocks.{$this->ownership}.*.low_stock" => 'required|numeric|min:1',
            "stocks.{$this->ownership}.*.price" => 'required|numeric|min:0',
            "stocks.{$this->ownership}.*.mandatory" => 'boolean',
            "stocks.{$this->ownership}.*.approval" => 'boolean',
            "stocks.{$this->ownership}.*.penalty" => 'required|numeric|min:1',
            "stocks.{$this->ownership}.*.penalty_type" => 'required|in:1,2',
        ];
    }


    public function mount(AdvanceContractRepository $advanceContractRepository,$uuid,$prefix = null)
    {
        $this->uuid = $uuid;
        $this->route_prefix = $prefix;
        $this->isVariation = request()->routeIs('variation.*');
        if ($uuid) {
            $draft = $advanceContractRepository->findByUuid($this->uuid);
            if ($draft) {
                $data = $draft->main_information_data;
                $kpiData = $draft->data_agreement_kpi_data;
                $this->serviceProvider = $data['contract_with'] ?? '';

                if ($draft && $draft->assets_ppm_data) {
                    $assetsPpmData = $draft->assets_ppm_data;
                }

                $this->assets = $assetsPpmData['assets'] ?? [];
                $this->ownership = $assetsPpmData['ownership'] ?? 'admin';
                $this->selectedItems = $assetsPpmData['selectedItems'] ?? null;
                $this->stocks[$this->ownership] = $assetsPpmData['stocks'][$this->ownership] ?? null;

                $serviceIds = collect($kpiData['servicesRows'] ?? [])
                    ->pluck('service_id')
                    ->map(fn($id) => (int) $id) // Ensure integer values if needed
                    ->unique()
                    ->toArray();
                $this->assetNames = AssetName::with(['categories' => function ($query) use ($serviceIds) {
                                        $query->whereIn('asset_categories.id', $serviceIds);
                                    }])
                                    ->where('user_id', Auth::user()->project_user_id)
                                    ->where('is_deleted', 'no')
                                    ->where(function ($query) use ($serviceIds) {
                                        $query->whereIn('asset_names.asset_category_id', $serviceIds)
                                            ->orWhereHas('categories', function ($subQuery) use ($serviceIds) {
                                                $subQuery->whereIn('asset_categories.id', $serviceIds);
                                            });
                                    })
                                    ->get(['id', 'asset_name', 'user_id', 'asset_symbol']);

            }
        }
       
        

    }

    public function loadSelectableItems()
    {
        $companyId = Auth::user()?->userCompany?->company_id;
        if ($this->ownership !== 'admin') {
            $serviceProviderId = $this->serviceProvider;
         
            $serviceProvider = ServiceProvider::find($serviceProviderId);
            $serviceProviderCompany = optional(
                optional(
                    optional($serviceProvider)->serviceProviderAdmin
                )->userCompany
            )->getCompany();

            if ($serviceProviderCompany) {
                $companyId = $serviceProviderCompany->id;
            }
        }

        // Prepare request headers
        $headers = ['Accept' => 'application/json'];
        if ($companyId) {
            $headers['X-Company'] = $companyId;
        }

        // Make the HTTP GET request to fetch items
        $response = Http::withHeaders(array_merge($headers, [
            'Connection' => 'keep-alive', // Keep the connection alive
        ]))
            ->withBasicAuth(
                config('akaunting.username'),
                config('akaunting.password')
            )
            ->get(config('akaunting.url').'/inventory-items', [
            'page' => 1,
            'limit' => 1000,
            ]);

        if ($response->failed()) {
            Log::error('Failed to fetch inventory items', ['response' => $response->body()]);

            return [];
        }

        $items = collect([]);
        $selectableItems = ItemData::collection($response->json('data', []));

        $item_ids = []; // You may populate this from elsewhere if needed

        // Filter selected items
        foreach ($selectableItems as $selectableItem) {
            if (in_array($selectableItem->id, $item_ids)) {
                $items->push($selectableItem);
            }
        }

        $dataProvider = new FormDataProvider([
            'items' => $items,
            'selectableItems' => $selectableItems,
        ]);

        $dropdownItems = $dataProvider->getSelectableItems()
            ->toCollection()
            ->map(function ($item) {
                return [
                    'id' => $item->id,
                    'name' => $item->name,
                    'total_stock' => $item->total_stock,
                    'low_stock' => $item->reorder_level,
                    'purchase_price' =>  intval($item->purchase_price),
                ];
            });

        return $dropdownItems;
    }

    /** Modal controls */
    public function openModal()
    {
        $this->isOpenModal = true;
        $this->selectableItems = $this->loadSelectableItems();
    }

    public function closeModal()
    {
        $this->isOpenModal = false;
    }

    /** Handle selecting and saving items */
    public function saveSelectedItems(Request $request)
    {
        try {

            $selectedItemIds = is_array($this->selectedItems) ? $this->selectedItems : [];

            // Get already selected items for this ownership
            $existingItems = collect($this->selectedItemsList[$this->ownership] ?? []);
            $existingItemIds = $existingItems->pluck('id')->all();

            $this->stocks[$this->ownership] = collect($this->stocks[$this->ownership] ?? [])
                ->filter(function ($item) use ($selectedItemIds) {
                    return in_array($item['id'], $selectedItemIds);
                })
                ->values()
                ->all();

            // Find newly selected IDs
            $newItemIds = array_diff($selectedItemIds, $existingItemIds);

            // Only process newly selected items
            $newSelectedItems = collect($this->selectableItems)
                ->whereIn('id', $newItemIds)
                ->map(function ($item) use ($request) {
                    return [
                        'id' => $item['id'],
                        'name' => $item['name'],
                        'open_stock' => $item['total_stock'],
                        'low_stock' => $item['low_stock'],
                        'mandatory' => (bool) $request->input("stocks.{$item['id']}.mandatory", false),
                        'approval' => (bool) $request->input("stocks.{$item['id']}.approval", false),
                        'penalty' => $request->input("stocks.{$item['id']}.penalty", 0),
                        'penalty_type' => $request->input("stocks.{$item['id']}.penalty_type", 1),
                        'price' => $item['purchase_price'],
                    ];
                })
                ->values();

            $this->stocks[$this->ownership] = collect($this->stocks[$this->ownership])
                ->merge($newSelectedItems)
                ->unique('id')
                ->values()
                ->all();

            $this->dispatchBrowserEvent('show-toastr', ['type' => 'success', 'message' => __('advance_contracts.general.items_saved')]);

            $this->closeModal();

        } catch (\Throwable $th) {
            // Log the error with the message and stack trace for debugging purposes
            Log::error('Error saving item: '.$th->getMessage(), [
                'exception' => $th,
            ]);
            $this->dispatchBrowserEvent('show-toastr', ['type' => 'error', 'message' => __('advance_contracts.general.items_error')]);
        }
    }


    public function deleteItem($itemId)
    {
        // Ensure the ownership is set and valid
        if (! isset($this->ownership) || ! isset($this->stocks[$this->ownership])) {
            return;
        }

        // Filter out the item from the stocks array for the current ownership
        $this->stocks[$this->ownership] = collect($this->stocks[$this->ownership])
            ->reject(function ($item) use ($itemId) {
                return $item['id'] == $itemId;
            })
            ->values() // Reset the array keys
            ->all();

        // 2. Remove from selectedItems array (for the select input)
        if (($key = array_search($itemId, $this->selectedItems)) !== false) {
            unset($this->selectedItems[$key]);
            $this->selectedItems = array_values($this->selectedItems); // reindex
        }
    }

    /** Save all form data into the contract */
    public function submit(AdvanceContractRepository $advanceContractRepository)
    {
            $data = [
                    'assets' => $this->assets,
                    'ownership' => $this->ownership,
                    'stocks' => $this->stocks,
                    'selectedItems' => $this->selectedItems,
                ];
            $validator = Validator::make($data,$this->rules());

            $validator->after(function ($validator) {
                if (!empty($this->stocks[$this->ownership])) {
                    foreach ($this->stocks[$this->ownership] as $index => $stock) {
                        if (
                            isset($stock['penalty_type'], $stock['penalty']) &&
                            $stock['penalty_type'] == 1 &&
                            $stock['penalty'] > 100
                        ) {
                            $validator->errors()->add(
                                "stocks.$this->ownership.$index.penalty",
                                __('advance_contracts.assets_ppm.penalty_percentage_validation')
                            );
                        }
                    }
                }

                // Check if ownership requires at least one stock item
                if (in_array($this->ownership, ['admin', 'service_provider'])) {
                    $stocksCount = count($this->stocks[$this->ownership] ?? []);
                    if ($stocksCount < 1) {
                        $validator->errors()->add("stocks.{$this->ownership}",  __('advance_contracts.assets_ppm.please_add_at_least_one_item'));
                    }
                }
                
            });

            if ($validator->fails()) {
                $this->setErrorBag($validator->errors());
                return;
            }
        AdvanceContractDraft::where('uuid', $this->uuid)->update([
            'assets_ppm_data' => $data,
        ]);

        // Mark current step as saved in session
        $savedSteps = session()->get("contract_saved_steps_{$this->uuid}", []);
        if (!in_array($this->currentStep, $savedSteps)) {
            $savedSteps[] = $this->currentStep;
        }
        session()->put("contract_saved_steps_{$this->uuid}", $savedSteps);
        
        $this->dispatchBrowserEvent('show-toastr', ['type' => 'success', 'message' =>  __('advance_contracts.general.assets_ppm_saved')]);

        return redirect()->route($this->route_prefix .'workforce-team', ['uuid' => $this->uuid]);
    }

    public function render()
    {
        return view('livewire.advance-contracts.step-three');
    }
}

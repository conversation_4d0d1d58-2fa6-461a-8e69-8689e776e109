@extends('layouts.app')
@section('styles')
    <link rel = "stylesheet" href = "{{ asset('new_theme/css/import.css') }}">
@endsection
@section('content')
    <div class = "contents">
        <div class = "container-fluid">
            <div class = "social-dash-wrap">
                <div class = "row">
                    <div class = "col-lg-12">
                        <div class = "breadcrumb-main">
                            <h4 class = "text-capitalize breadcrumb-title">
                                <a href = "{{ route('bulk-import.openUploadFile') }}" class = "text-dark">
                                    <i class = "las la-arrow-left"></i>
                                </a>
                                @lang('import.import_project_assets')
                            </h4>
                            <div class = "breadcrumb-action justify-content-center flex-wrap"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class = "container-fluid">
            <div class = "checkout wizard1 wizard7 global-shadow px-sm-50 px-20 py-sm-50 py-30 mb-30 bg-white radius-xl w-100">
                <div class = "row justify-content-center">
                    <div class = "col-xl-8">
                        <div class = "checkout-progress-indicator content-center col-md-10">
                            <div class = "checkout-progress">
                                <div class = "step current" id = "1">
                                    <span>1</span>
                                    <span>@lang('import.upload')</span>
                                </div>
                                 <div class = "current">
                                    <img src = "{{ asset('img/svg/checkout.svg') }}" class = "svg">
                                </div>
                                <div class = "step" id = "2">
                                    <span>2</span>
                                    <span>@lang('import.map')</span>
                                </div>
                                <div class = "current">
                                    <img src = "{{ asset('img/svg/checkout.svg') }}" class = "svg">
                                </div>
                                <div class = "step" id = "3">
                                    <span>3</span>
                                    <span>@lang('import.validate')</span>
                                </div>
                                <div class = "current">
                                    <img src = "{{ asset('img/svg/checkout.svg') }}" class = "svg">
                                </div>
                                <div class = "step" id = "4">
                                    <span>4</span>
                                    <span>@lang('import.done')</span>
                                </div>
                            </div>
                        </div>
                        <div class = "row justify-content-center">
                            <div class = "col-xl-10 col-lg-8 col-sm-10">
                                <div class = "card checkout-shipping-form pt-2 pb-30 border-0">
                                    @if(session()->has('error'))
                                        <div class = "alert alert-danger alert-dismissible fade show py-3" role = "alert">
                                            <ul>
                                                <li>
                                                    <p>{{ session()->get('error') }}</p>
                                                </li>
                                            </ul>
                                            <button type = "button" class = "close" data-dismiss = "alert" aria-label = "Close" style = "margin-top: -10px">
                                                <span aria-hidden = "true" style = "font-size: 28px">&times;</span>
                                            </button>
                                        </div>
                                    @endif
                                    @php session()->forget('error'); @endphp
                                    <div class = "row">
                                        <div class = "col-6">
                                            <div class = "form-group">
                                                <label for = "name1">
                                                    @lang("data_properties.property_forms.label.project") 
                                                    <span class = "required">*</span>
                                                </label>
                                                <input type = "text" class = "form-control" name = "projectName" id = "projectName" value = "{{ $projectName }}" disabled>
                                            </div>
                                        </div>
                                        <div class = "col-6">
                                            <div class = "form-group">
                                                <label for = "name1">
                                                    @lang("import.connected_user") 
                                                    <span class = "required">*</span>
                                                </label>
                                                <input type = "text" class = "form-control" name = "user" id = "user" value = "{{ $username }}" disabled>
                                            </div>
                                        </div>
                                        <div class = "col-6">
                                            <div class = "form-group">
                                                <label for = "name1">
                                                    @lang("import.filename_title") 
                                                    <span class = "required">*</span>
                                                </label>
                                                <input type = "text" class = "form-control" name = "filename" id = "filename" value = "{{ isset($bulkImportDetails) ? $bulkImportDetails->file_name : '-' }}" disabled>
                                            </div>
                                        </div>
                                        <div class = "col-6">
                                            <div class = "form-group">
                                                <label for = "name1">
                                                    @lang("import.filesize_title") 
                                                    <span class = "required">*</span>
                                                </label>
                                                <input type = "text" class = "form-control" name = "filesize" id = "filesize" value = "{{ isset($bulkImportDetails) ? $bulkImportDetails->file_size : '-' }}" disabled>
                                            </div>
                                        </div>
                                    </div>
                                    <div class = "alert alert-warning alert-dismissible fade show mt-30" role = "alert">
                                        <ul>
                                            <li>
                                                <p>@lang('import.double_check')</p>
                                            </li>
                                            <li>
                                                <p>@lang('import.notification_company_id')</p>
                                            </li>
                                        </ul>
                                        <button type = "button" class = "close" data-dismiss = "alert" aria-label = "Close">
                                            <span aria-hidden = "true" style = "font-size: 28px">&times;</span>
                                        </button>
                                    </div>
                                    @livewire('bulk-import.new.manage-upload-excel-file-state', ['bulkImportDetails' => $bulkImportDetails, 'token' => $token, 'projectId' => $projectId])
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
@section('scripts')
    <script src = "{{ asset('new_theme/js/functions.js') }}"> </script>
    <script>
        $("#select-sheets-form").on("submit", function(event) {
           return showSelectSheetsQuestion(event);
        });

        $("#checked_users").on("change", function() {
            manageAffectationButtonState();
        });

        document.addEventListener('closeAffectModal', () => {
            $('#affect_users_modal').modal('hide');
            resetSelect2();
        });

        document.addEventListener('spa_list', event => {
            updadeSPASelect2(JSON.parse(event.detail));
        });

        document.addEventListener('supervisor_list', event => {
            updadeSPSSelect2(JSON.parse(event.detail));
        });

        $(document).ready(function() {
            manageAffectationButtonState();
            $('.select2-select').select2(); 
        });
    </script>
@endsection
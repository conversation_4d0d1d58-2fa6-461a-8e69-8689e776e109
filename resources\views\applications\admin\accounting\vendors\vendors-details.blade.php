@extends('layouts.app')
@section('styles')
@endsection
@section('content')
        <div class="contents crm">
           <div class="container-fluid">
            <div class="col-lg-12">
    <div class="row justify-content-sm-between align-items-center justify-content-center my-3 flex-sm-row flex-column">
        <div class="page-title-wrap p-0">
            <div class="page-title d-flex justify-content-between">
                <div class="page-title__left justify-content-sm-between align-items-center justify-content-center">
                    <div class="user-member__title mr-sm-25 ml-0">
                        <h4 class="text-capitalize fw-500 breadcrumb-title fs-16">
                            Vendor Details
                        </h4>
                    </div>
                </div>
            </div>
            <div>
                <ul class="atbd-breadcrumb nav">
                    <li class="atbd-breadcrumb__item">
                        <a>Dashboard</a>
                        <span class="breadcrumb__seperator">
                            <span class="la la-angle-right"></span>
                        </span>
                    </li>
                    <li class="atbd-breadcrumb__item">
                        <a>Vendor Details</a>
                    </li>
                </ul>
            </div>
        </div>



        <div class="d-flex gap-10 breadcrumb_right_icons">
            <div class="d-flex gap-10 breadcrumb_right_icons">
                    <button class="btn btn-default btn-primary w-100 no-wrap" type="button" aria-expanded="false" data-toggle="dropdown" aria-expanded="false"><i class="las la-plus fs-16"></i>Create Bill</button>
            </div>
        </div>
        <!--====End Design for Export PDF===-->
    </div>
</div>

<div class="d-flex justify-content-end mb-3">
        <ul class="nav nav-tabs site-pills bg-white p-1 rounded mb-3 mb-sm-0" id="pills-tab" role="tablist">
             <li class="nav-item" role="presentation">
                <button class="nav-link active rounded" id="customer-details-tab" data-toggle="pill" data-target="#customer-details" type="button">Details</button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link rounded" id="customer-proposal-tab" data-toggle="pill" data-target="#customer-proposal" type="button">Bill</button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link rounded" id="customer-invoice-tab" data-toggle="pill" data-target="#customer-invoice" type="button">Purchase</button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link rounded" id="customer-revenue-tab" data-toggle="pill" data-target="#customer-revenue" type="button">Payment</button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link rounded" id="customer-project-tab" data-toggle="pill" data-target="#customer-project" type="button">Project</button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link rounded" id="statement-tab" data-toggle="pill" data-target="#customer-statement" type="button">Statement</button>
            </li>
        </ul>
</div>
<div class="">


<div class="tab-content" id="myTabContent">
  <div class="tab-pane fade show active" id="customer-details" role="tabpanel" aria-labelledby="home-tab">
      

   
    <div class="col-lg-12 px-0">
    <div class="row mb-3">
        <div class="col-md-4 mb-sm-0 mb-3 pr-md-0">
            <div class="p-3 address-box radius-xl h-100 card">
                <h6 class="text-dark mb-3 fs-14">Vendor Info</h6>
                <table class="table mb-0 table-borderless new-header td-pl-0 table-address">
                    <tbody>
                        <tr>
                            <td><span class="fs-12 fw-50 text-dark fw-600">Name</span></td>
                            <td>Abdul Rehman</td>
                        </tr>
                        <tr>
                            <td><span class="fs-12 fw-50 text-dark fw-600">Email </span></td>
                            <td><EMAIL></td>
                        </tr>
                        <tr>
                            <td><span class="fs-12 fw-50 text-dark fw-600">Phone </span></td>
                            <td>+966-909876543</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <div class="col-md-4 mb-sm-0 mb-3 pr-md-0">
            <div class="p-3 address-box radius-xl h-100 card">
                <h6 class="text-dark mb-3 fs-14">Billing Address</h6>
                <table class="table mb-0 table-borderless new-header td-pl-0 table-address">
                    <tbody>
                        <tr>
                            <td><span class="fs-12 fw-50 text-dark fw-600">Billing Address</span></td>
                            <td>Riyad</td>
                        </tr>
                        <tr>
                            <td><span class="fs-12 fw-50 text-dark fw-600">Billing City </span></td>
                            <td>riyad</td>
                        </tr>
                        <tr>
                            <td><span class="fs-12 fw-50 text-dark fw-600">Zip Code </span></td>
                            <td>101</td>
                        </tr>
                        <tr>
                            <td><span class="fs-12 fw-50 text-dark fw-600">Billing Country </span></td>
                            <td>Saudi Arabia</td>
                        </tr>
                        <tr>
                            <td><span class="fs-12 fw-50 text-dark fw-600">Billing Contact </span></td>
                            <td></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <div class="col-md-4">
            <div class="p-3 address-box radius-xl h-100 card">
                <h6 class="text-dark mb-3 fs-14">Shipping Address</h6>
                <table class="table mb-0 table-borderless new-header td-pl-0 table-address">
                    <tbody>
                        <tr>
                            <td><span class="fs-12 fw-50 text-dark fw-600">Billing Address</span></td>
                            <td>Riyad</td>
                        </tr>
                        <tr>
                            <td><span class="fs-12 fw-50 text-dark fw-600">Billing City </span></td>
                            <td>riyad</td>
                        </tr>
                        <tr>
                            <td><span class="fs-12 fw-50 text-dark fw-600">Zip Code </span></td>
                            <td>101</td>
                        </tr>
                        <tr>
                            <td><span class="fs-12 fw-50 text-dark fw-600">Billing Country </span></td>
                            <td>Saudi Arabia</td>
                        </tr>
                        <tr>
                            <td><span class="fs-12 fw-50 text-dark fw-600">Billing Contact </span></td>
                            <td></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

    
        <div class="card mb-3" data-select2-id="108">
            <div class="card-header d-flex justify-content-start gap-5">
                <h6>Company Info</h6>
            </div>
            <div class="card-body" data-select2-id="107">
                <div class="row">
                    <div class="col-md-3 col-6">
                        <label class="fw-600 text-dark">Vendor ID</label>
                        <p>20050 W 12 Mile</p>
                    </div>
                    <div class="col-md-3 col-6">
                        <label class="fw-600 text-dark">Date of Creation</label>
                        <p>10,0000</p>
                    </div>
                    <div class="col-md-3 col-6">
                        <label class="fw-600 text-dark">Balance</label>
                        <div class="d-flex align-items-center gap-10">
                            <img src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSx34eO8RbZlsetN1iAQtxuOLnLOlyJbJmU7Q&s" width="15"><span class="text-new-primary">25000</span>
                        </div>
                    </div>
                    <div class="col-md-3 col-6 mb-3">
                        <label class="fw-600 text-dark">Overdue</label>
                        <div class="d-flex align-items-center gap-10">
                            <img src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSx34eO8RbZlsetN1iAQtxuOLnLOlyJbJmU7Q&s" width="15"><span class="text-new-primary">25000</span>
                        </div>
                    </div>
                    <div class="col-md-3 col-6">
                        <label class="fw-600 text-dark">Total Sum of Bills</label>
                        <div class="d-flex align-items-center gap-10">
                            <img src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSx34eO8RbZlsetN1iAQtxuOLnLOlyJbJmU7Q&s" width="15"><span class="text-new-primary">25000</span>
                        </div>
                    </div>
                    <div class="col-md-3 col-6">
                        <label class="fw-600 text-dark">Quantity of Bills</label>
                        <p>3</p>
                    </div>
                    <div class="col-md-3 col-6">
                        <label class="fw-600 text-dark">Average Sales</label>
                        <div class="d-flex align-items-center gap-10">
                            <img src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSx34eO8RbZlsetN1iAQtxuOLnLOlyJbJmU7Q&s" width="15"><span class="text-new-primary">25000</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>


  </div>
  <div class="tab-pane fade" id="customer-proposal" role="tabpanel" aria-labelledby="proposal-tab">

    <div class="card">
    <div class="">
        <div class="card-header py-4 px-3 border-0 d-flex justify-content-between align-items-center">
            <h6 class="text-capitalize text-osool fw-500 mb-3 mb-sm-0">Bills</h6>

            <div class="d-flex gap-10 table-search">
                <div class="position-relative">
                    <input type="text" class="form-control" placeholder="Search">
                    <i class="iconsax field-icon fs-18 mr-0" icon-name="search-normal-2"></i>
                </div>
                <!-- <button class="btn btn-export text-dark"><i class="iconsax icon fs-22 mr-0" icon-name="upload-1"></i> Export</button> -->
            </div>
        </div>
    </div>
    <div class="card-body px-0 pt-0">
        <div class="userDatatable projectDatatable project-table bg-white w-100 border-0">
            <div class="table-responsive">
                <table class="table mb-0 radius-0 th-osool">
                    <thead>
                        <tr class="userDatatable-header">
                            <th>
                                Bill
                            </th>
                            <th><i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i> Bill Date</th>
                            <th><i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i> Due Date</th>
                            <th><i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i> Due Amount</th>
                            <th><i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i> Status</th>
                            <th><i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i> Action</th>
                        </tr>
                    </thead>
                    <tbody class="sort-table ui-sortable">
                        <tr class="ui-sortable-handle" style="opacity: 1;">
                            <td>
                                <div class="userDatatable-inline-title"><a href=""><span class="fw-400 badge badge-round badge-primary badge-lg badge-outlined">PS-1890999474</span></a></div>
                            </td>
                            <td>
                                <div class="d-flex userDatatable-content mb-0 align-items-center">
                                    <span>09-09-2025</span>
                                </div>
                            </td>
                            <td>
                                <div class="d-flex userDatatable-content mb-0 align-items-center">
                                    <span>30-09-2025</span>
                                </div>
                            </td>
                            <td>
                                <div class="d-flex align-items-center gap-10">
                                    <img src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSx34eO8RbZlsetN1iAQtxuOLnLOlyJbJmU7Q&amp;s" width="15" /><span class="text-new-primary">25000</span>
                                </div>
                            </td>
                            <td>
                                <span class="badge-new bg-opacity-loss text-loss rounded">Drafts</span>
                            </td>
                            <td>
                                <div class="d-inline-block">
                                    <ul class="mb-0 d-flex gap-10">
                                        <!-- <li>
                                            <a href="">
                                                <i class="iconsax icon text-osool fs-18" icon-name="change-shape-1"></i>
                                            </a>
                                        </li> -->
                                        <li>
                                            <a href="">
                                                <i class="iconsax icon text-osool fs-18" icon-name="eye"></i>
                                            </a>
                                        </li>
                                        <li>
                                            <a href="javascript:void(0);" wire:click="openEditModal(588)">
                                                <i class="iconsax icon text-new-primary fs-18" icon-name="edit-1"></i>
                                            </a>
                                        </li>
                                        <li>
                                            <a href="javascript:void(0);" wire:click="openDeleteModal(588, 'Beyond Tech1 Copy test task')">
                                                <i class="iconsax icon text-delete fs-18" icon-name="trash"></i>
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                            </td>
                        </tr>
                    
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <div class="card-body pt-0">
        <div class="d-flex justify-content-md-between flex-column flex-md-row justify-content-center align-items-center mt-4 gap-10">
            <div class="">
                <ul class="atbd-pagination d-flex justify-content-between">
                    <li>
                        <div class="paging-option">
                            <div class="dataTables_length d-flex">
                                <label class="d-flex align-items-center mb-0">
                                    <select aria-controls="workorder_table" wire:model.live.debounce.250ms="perPage" class="custom-select custom-select-sm form-control form-control-sm mx-2" style="min-height: 35px;">
                                        <option value="5">5</option>
                                        <option value="10">10</option>
                                        <option value="25">25</option>
                                        <option value="50">50</option>
                                        <option value="100">100</option>
                                    </select>
                                    <span class="no-wrap"> Entries Per Page </span>
                                </label>
                            </div>
                        </div>
                    </li>
                </ul>
            </div>
            <div class="">
                <div class="user-pagination">
                    <div class="user-pagination new-pagination">
                        <div class="d-flex justify-content-sm-end justify-content-end">
                            <nav>
                                <span class="relative z-0 inline-flex rounded-md d-flex new-pagination-section flex-wrap">
                                    <span class="">
                                        <span aria-disabled="true" aria-label="&amp;laquo; Previous">
                                            <button class="border-0 disabled" aria-hidden="true" disabled="">
                                                <i class="iconsax icon-bold fs-16 mr-0 rotate-ar-y d-inline-block" icon-name="chevron-left"></i>
                                            </button>
                                        </span>
                                    </span>

                                    <span wire:key="paginator-page-1-page1">
                                        <button class="border-0 current-page" disabled="">1</button>
                                    </span>
                                    <span wire:key="paginator-page-1-page2">
                                        <button type="button" class="border-0">2</button>
                                    </span>
                                    <span wire:key="paginator-page-1-page3">
                                        <button type="button" class="border-0">3</button>
                                    </span>
                                    <span wire:key="paginator-page-1-page4">
                                        <button type="button" class="border-0">4</button>
                                    </span>
                                    <span wire:key="paginator-page-1-page5">
                                        <button type="button" class="border-0">5</button>
                                    </span>
                                    <span wire:key="paginator-page-1-page6">
                                        <button type="button" class="border-0">6</button>
                                    </span>
                                    <span wire:key="paginator-page-1-page7">
                                        <button type="button" class="border-0">7</button>
                                    </span>
                                    <span wire:key="paginator-page-1-page8">
                                        <button type="button" class="border-0">8</button>
                                    </span>
                                    <span wire:key="paginator-page-1-page9">
                                        <button type="button" class="border-0">9</button>
                                    </span>

                                    <span>
                                        <button type="button" class="border-0">
                                            <i class="iconsax icon-bold fs-16 mr-0 rotate-ar-y d-inline-block" icon-name="chevron-right"></i>
                                        </button>
                                    </span>
                                </span>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
            <div>
                <p class="text-sm text-gray-700 leading-5 mb-0">
                    <span>Showing</span>
                    <span class="font-medium">1</span>
                    <span>to</span>
                    <span class="font-medium">6</span>
                    <span>of</span>
                    <span class="font-medium">52</span>
                    <span>results</span>
                </p>
            </div>
        </div>
    </div>
</div>



  </div>
  <div class="tab-pane fade" id="customer-invoice" role="tabpanel" aria-labelledby="contact-tab">
<div class="card">
    <div class="">
        <div class="card-header py-4 px-3 border-0 d-flex justify-content-between align-items-center">
            <h6 class="text-capitalize text-osool fw-500 mb-3 mb-sm-0">Purchase</h6>
            <div class="d-flex gap-10 table-search">
                <div class="position-relative">
                    <input type="text" class="form-control" placeholder="Search">
                    <i class="iconsax field-icon fs-18 mr-0" icon-name="search-normal-2"></i>
                </div>
                <!-- <button class="btn btn-export text-dark"><i class="iconsax icon fs-22 mr-0" icon-name="upload-1"></i> Export</button> -->
            </div>
        </div>
    </div>
    <div class="card-body px-0 pt-0">
        <div class="userDatatable projectDatatable project-table bg-white w-100 border-0">
            <div class="table-responsive">
                <table class="table mb-0 radius-0 th-osool">
                    <thead>
                        <tr class="userDatatable-header">
                            <th>
                                Vendor
                            </th>
                            <th><i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i> Vendor Date</th>
                            <th><i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i> Due Amount</th>
                            <th><i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i> Status</th>
                            <th><i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i> Action</th>
                        </tr>
                    </thead>
                    <tbody class="sort-table ui-sortable">
                        <tr class="ui-sortable-handle" style="opacity: 1;">
                            <td>
                                <div class="userDatatable-inline-title"><a href=""><span class="fw-400 badge badge-round badge-primary badge-lg badge-outlined">PS-1890999474</span></a></div>
                            </td>
                            <td>
                                <div class="d-flex userDatatable-content mb-0 align-items-center">
                                    <span>09-09-2025</span>
                                </div>
                            </td>
                            <td>
                                <div class="d-flex align-items-center gap-10">
                                    <img src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSx34eO8RbZlsetN1iAQtxuOLnLOlyJbJmU7Q&amp;s" width="15" /><span class="text-new-primary">25000</span>
                                </div>
                            </td>
                            <td>
                                <span class="badge-new bg-opacity-loss text-loss rounded">Drafts</span>
                            </td>
                            <td>
                                <div class="d-inline-block">
                                    <ul class="mb-0 d-flex gap-10">
                                        <!-- <li>
                                            <a href="">
                                                <i class="iconsax icon text-osool fs-18" icon-name="change-shape-1"></i>
                                            </a>
                                        </li> -->
                                        <li>
                                            <a href="">
                                                <i class="iconsax icon text-osool fs-18" icon-name="eye"></i>
                                            </a>
                                        </li>
                                        <li>
                                            <a href="javascript:void(0);" wire:click="openEditModal(588)">
                                                <i class="iconsax icon text-new-primary fs-18" icon-name="edit-1"></i>
                                            </a>
                                        </li>
                                        <li>
                                            <a href="javascript:void(0);" wire:click="openDeleteModal(588, 'Beyond Tech1 Copy test task')">
                                                <i class="iconsax icon text-delete fs-18" icon-name="trash"></i>
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                            </td>
                        </tr>
                    
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <div class="card-body pt-0">
        <div class="d-flex justify-content-md-between flex-column flex-md-row justify-content-center align-items-center mt-4 gap-10">
            <div class="">
                <ul class="atbd-pagination d-flex justify-content-between">
                    <li>
                        <div class="paging-option">
                            <div class="dataTables_length d-flex">
                                <label class="d-flex align-items-center mb-0">
                                    <select aria-controls="workorder_table" wire:model.live.debounce.250ms="perPage" class="custom-select custom-select-sm form-control form-control-sm mx-2" style="min-height: 35px;">
                                        <option value="5">5</option>
                                        <option value="10">10</option>
                                        <option value="25">25</option>
                                        <option value="50">50</option>
                                        <option value="100">100</option>
                                    </select>
                                    <span class="no-wrap"> Entries Per Page </span>
                                </label>
                            </div>
                        </div>
                    </li>
                </ul>
            </div>
            <div class="">
                <div class="user-pagination">
                    <div class="user-pagination new-pagination">
                        <div class="d-flex justify-content-sm-end justify-content-end">
                            <nav>
                                <span class="relative z-0 inline-flex rounded-md d-flex new-pagination-section flex-wrap">
                                    <span class="">
                                        <span aria-disabled="true" aria-label="&amp;laquo; Previous">
                                            <button class="border-0 disabled" aria-hidden="true" disabled="">
                                                <i class="iconsax icon-bold fs-16 mr-0 rotate-ar-y d-inline-block" icon-name="chevron-left"></i>
                                            </button>
                                        </span>
                                    </span>

                                    <span wire:key="paginator-page-1-page1">
                                        <button class="border-0 current-page" disabled="">1</button>
                                    </span>
                                    <span wire:key="paginator-page-1-page2">
                                        <button type="button" class="border-0">2</button>
                                    </span>
                                    <span wire:key="paginator-page-1-page3">
                                        <button type="button" class="border-0">3</button>
                                    </span>
                                    <span wire:key="paginator-page-1-page4">
                                        <button type="button" class="border-0">4</button>
                                    </span>
                                    <span wire:key="paginator-page-1-page5">
                                        <button type="button" class="border-0">5</button>
                                    </span>
                                    <span wire:key="paginator-page-1-page6">
                                        <button type="button" class="border-0">6</button>
                                    </span>
                                    <span wire:key="paginator-page-1-page7">
                                        <button type="button" class="border-0">7</button>
                                    </span>
                                    <span wire:key="paginator-page-1-page8">
                                        <button type="button" class="border-0">8</button>
                                    </span>
                                    <span wire:key="paginator-page-1-page9">
                                        <button type="button" class="border-0">9</button>
                                    </span>

                                    <span>
                                        <button type="button" class="border-0">
                                            <i class="iconsax icon-bold fs-16 mr-0 rotate-ar-y d-inline-block" icon-name="chevron-right"></i>
                                        </button>
                                    </span>
                                </span>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
            <div>
                <p class="text-sm text-gray-700 leading-5 mb-0">
                    <span>Showing</span>
                    <span class="font-medium">1</span>
                    <span>to</span>
                    <span class="font-medium">6</span>
                    <span>of</span>
                    <span class="font-medium">52</span>
                    <span>results</span>
                </p>
            </div>
        </div>
    </div>
</div>
  </div>

  <div class="tab-pane fade" id="customer-revenue" role="tabpanel" aria-labelledby="contact-tab">
<div class="card">
    <div class="">
        <div class="card-header py-4 px-3 border-0 d-flex justify-content-between align-items-center">
            <h6 class="text-capitalize text-osool fw-500 mb-3 mb-sm-0">Payment</h6>
            <div class="d-flex gap-10 table-search">
                <div class="position-relative">
                    <input type="text" class="form-control" placeholder="Search">
                    <i class="iconsax field-icon fs-18 mr-0" icon-name="search-normal-2"></i>
                </div>
                <!-- <button class="btn btn-export text-dark"><i class="iconsax icon fs-22 mr-0" icon-name="upload-1"></i> Export</button> -->
            </div>
        </div>
    </div>
    <div class="card-body px-0 pt-0">
        <div class="userDatatable projectDatatable project-table bg-white w-100 border-0">
            <div class="table-responsive">
                <table class="table mb-0 radius-0 th-osool">
                    <thead>
                        <tr class="userDatatable-header">
                            <th>
                                Date
                            </th>
                            <th><i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i> Ammount</th>
                            <th><i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i> Account</th>
                            <th><i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i> Category</th>
                            <th><i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i> Reference</th>
                            <th><i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i> Description</th>
                            <th><i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i> Payment Receipt</th>
                            <th><i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i> Action</th>
                        </tr>
                    </thead>
                    <tbody class="sort-table ui-sortable">
                        <tr class="ui-sortable-handle" style="opacity: 1;">
                            <td>
                                <div class="d-flex userDatatable-content mb-0 align-items-center">
                                    <span class="no-wrap">09-09-2025</span>
                                </div>
                            </td>
                            <td>
                                <div class="d-flex align-items-center gap-10">
                                    <img src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSx34eO8RbZlsetN1iAQtxuOLnLOlyJbJmU7Q&amp;s" width="15" /><span class="text-new-primary">25000</span>
                                </div>
                            </td>
                            <td>
                                <div class="userDatatable-inline-title">
                                    <span>Mohammad Khadeer</span>
                                </div>
                            </td>
                            <td>
                                <div class="userDatatable-inline-title">
                                    <span>Category 1</span>
                                </div>
                            </td>
                            <td>
                                <div class="userDatatable-inline-title">
                                    <span>Category 1</span>
                                </div>
                            </td>
                            <td>
                                <div class="userDatatable-inline-title">
                                    <span class="max-td">Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam</span>
                                </div>
                            </td>
                            <td>
                                <div class="userDatatable-inline-title">
                                    <a href="">
                                        <span class="fw-400 badge badge-round badge-primary badge-lg badge-outlined">View</span>
                                    </a>
                                </div>
                            </td>
                            <td>
                                <div class="d-inline-block">
                                    <ul class="mb-0 d-flex gap-10">
                                        <li>
                                            <a href="" title="Convert to Invoice">
                                                <i class="iconsax icon text-osool fs-18" icon-name="change-shape-1"></i>
                                            </a>
                                        </li>
                                        <li>
                                            <a href="">
                                                <i class="iconsax icon text-osool fs-18" icon-name="eye"></i>
                                            </a>
                                        </li>
                                        <li>
                                            <a href="javascript:void(0);" wire:click="openEditModal(588)">
                                                <i class="iconsax icon text-new-primary fs-18" icon-name="edit-1"></i>
                                            </a>
                                        </li>
                                        <li>
                                            <a href="javascript:void(0);" wire:click="openDeleteModal(588, 'Beyond Tech1 Copy test task')">
                                                <i class="iconsax icon text-delete fs-18" icon-name="trash"></i>
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                            </td>
                        </tr>
                    
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <div class="card-body pt-0">
        <div class="d-flex justify-content-md-between flex-column flex-md-row justify-content-center align-items-center mt-4 gap-10">
            <div class="">
                <ul class="atbd-pagination d-flex justify-content-between">
                    <li>
                        <div class="paging-option">
                            <div class="dataTables_length d-flex">
                                <label class="d-flex align-items-center mb-0">
                                    <select aria-controls="workorder_table" wire:model.live.debounce.250ms="perPage" class="custom-select custom-select-sm form-control form-control-sm mx-2" style="min-height: 35px;">
                                        <option value="5">5</option>
                                        <option value="10">10</option>
                                        <option value="25">25</option>
                                        <option value="50">50</option>
                                        <option value="100">100</option>
                                    </select>
                                    <span class="no-wrap"> Entries Per Page </span>
                                </label>
                            </div>
                        </div>
                    </li>
                </ul>
            </div>
            <div class="">
                <div class="user-pagination">
                    <div class="user-pagination new-pagination">
                        <div class="d-flex justify-content-sm-end justify-content-end">
                            <nav>
                                <span class="relative z-0 inline-flex rounded-md d-flex new-pagination-section flex-wrap">
                                    <span class="">
                                        <span aria-disabled="true" aria-label="&amp;laquo; Previous">
                                            <button class="border-0 disabled" aria-hidden="true" disabled="">
                                                <i class="iconsax icon-bold fs-16 mr-0 rotate-ar-y d-inline-block" icon-name="chevron-left"></i>
                                            </button>
                                        </span>
                                    </span>

                                    <span wire:key="paginator-page-1-page1">
                                        <button class="border-0 current-page" disabled="">1</button>
                                    </span>
                                    <span wire:key="paginator-page-1-page2">
                                        <button type="button" class="border-0">2</button>
                                    </span>
                                    <span wire:key="paginator-page-1-page3">
                                        <button type="button" class="border-0">3</button>
                                    </span>
                                    <span wire:key="paginator-page-1-page4">
                                        <button type="button" class="border-0">4</button>
                                    </span>
                                    <span wire:key="paginator-page-1-page5">
                                        <button type="button" class="border-0">5</button>
                                    </span>
                                    <span wire:key="paginator-page-1-page6">
                                        <button type="button" class="border-0">6</button>
                                    </span>
                                    <span wire:key="paginator-page-1-page7">
                                        <button type="button" class="border-0">7</button>
                                    </span>
                                    <span wire:key="paginator-page-1-page8">
                                        <button type="button" class="border-0">8</button>
                                    </span>
                                    <span wire:key="paginator-page-1-page9">
                                        <button type="button" class="border-0">9</button>
                                    </span>

                                    <span>
                                        <button type="button" class="border-0">
                                            <i class="iconsax icon-bold fs-16 mr-0 rotate-ar-y d-inline-block" icon-name="chevron-right"></i>
                                        </button>
                                    </span>
                                </span>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
            <div>
                <p class="text-sm text-gray-700 leading-5 mb-0">
                    <span>Showing</span>
                    <span class="font-medium">1</span>
                    <span>to</span>
                    <span class="font-medium">6</span>
                    <span>of</span>
                    <span class="font-medium">52</span>
                    <span>results</span>
                </p>
            </div>
        </div>
    </div>
</div>
  </div>
  <div class="tab-pane fade" id="customer-project" role="tabpanel" aria-labelledby="contact-tab">
<div class="card">
    <div class="">
        <div class="card-header py-4 px-3 border-0 d-flex justify-content-between align-items-center">
            <h6 class="text-capitalize text-osool fw-500 mb-3 mb-sm-0">Projects</h6>
            <div class="d-flex gap-10 table-search">
                <div class="position-relative">
                    <input type="text" class="form-control" placeholder="Search">
                    <i class="iconsax field-icon fs-18 mr-0" icon-name="search-normal-2"></i>
                </div>
                <!-- <button class="btn btn-export text-dark"><i class="iconsax icon fs-22 mr-0" icon-name="upload-1"></i> Export</button> -->
            </div>
        </div>
    </div>
    <div class="card-body px-0 pt-0">
        <div class="userDatatable projectDatatable project-table bg-white w-100 border-0">
            <div class="table-responsive">
                <table class="table mb-0 radius-0 th-osool">
                    <thead>
                        <tr class="userDatatable-header">
                            <th><i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i> Name</th>
                            <th><i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i> Stage</th>
                            <th><i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i> Start Date</th>
                            <th><i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i> End Date</th>
                            <th><i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i> Description</th>
                            <th><i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i> Action</th>
                        </tr>
                    </thead>
                    <tbody class="sort-table ui-sortable">
                        <tr class="ui-sortable-handle" style="opacity: 1;">
                            <td>
                                <div class="userDatatable-inline-title">
                                    <span>Mohammad Khadeer</span>
                                </div>
                            </td>
                            <td>
                                <div class="userDatatable-inline-title">
                                    <span>Stage1</span>
                                </div>
                            </td>
                            <td>
                                <div class="d-flex userDatatable-content mb-0 align-items-center">
                                    <span class="no-wrap">09-09-2025</span>
                                </div>
                            </td>
                            <td>
                                <div class="d-flex userDatatable-content mb-0 align-items-center">
                                    <span class="no-wrap">09-09-2025</span>
                                </div>
                            </td>
                            <td>
                                <div class="userDatatable-inline-title">
                                    <span class="max-td">Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam</span>
                                </div>
                            </td>
                            <td>
                                <div class="d-inline-block">
                                    <ul class="mb-0 d-flex gap-10">
                                        <li>
                                            <a href="">
                                                <i class="iconsax icon text-osool fs-18" icon-name="eye"></i>
                                            </a>
                                        </li>
                                        <li>
                                            <a href="javascript:void(0);" wire:click="openEditModal(588)">
                                                <i class="iconsax icon text-new-primary fs-18" icon-name="edit-1"></i>
                                            </a>
                                        </li>
                                        <li>
                                            <a href="javascript:void(0);" wire:click="openDeleteModal(588, 'Beyond Tech1 Copy test task')">
                                                <i class="iconsax icon text-delete fs-18" icon-name="trash"></i>
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                            </td>
                        </tr>
                    
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <div class="card-body pt-0">
        <div class="d-flex justify-content-md-between flex-column flex-md-row justify-content-center align-items-center mt-4 gap-10">
            <div class="">
                <ul class="atbd-pagination d-flex justify-content-between">
                    <li>
                        <div class="paging-option">
                            <div class="dataTables_length d-flex">
                                <label class="d-flex align-items-center mb-0">
                                    <select aria-controls="workorder_table" wire:model.live.debounce.250ms="perPage" class="custom-select custom-select-sm form-control form-control-sm mx-2" style="min-height: 35px;">
                                        <option value="5">5</option>
                                        <option value="10">10</option>
                                        <option value="25">25</option>
                                        <option value="50">50</option>
                                        <option value="100">100</option>
                                    </select>
                                    <span class="no-wrap"> Entries Per Page </span>
                                </label>
                            </div>
                        </div>
                    </li>
                </ul>
            </div>
            <div class="">
                <div class="user-pagination">
                    <div class="user-pagination new-pagination">
                        <div class="d-flex justify-content-sm-end justify-content-end">
                            <nav>
                                <span class="relative z-0 inline-flex rounded-md d-flex new-pagination-section flex-wrap">
                                    <span class="">
                                        <span aria-disabled="true" aria-label="&amp;laquo; Previous">
                                            <button class="border-0 disabled" aria-hidden="true" disabled="">
                                                <i class="iconsax icon-bold fs-16 mr-0 rotate-ar-y d-inline-block" icon-name="chevron-left"></i>
                                            </button>
                                        </span>
                                    </span>

                                    <span wire:key="paginator-page-1-page1">
                                        <button class="border-0 current-page" disabled="">1</button>
                                    </span>
                                    <span wire:key="paginator-page-1-page2">
                                        <button type="button" class="border-0">2</button>
                                    </span>
                                    <span wire:key="paginator-page-1-page3">
                                        <button type="button" class="border-0">3</button>
                                    </span>
                                    <span wire:key="paginator-page-1-page4">
                                        <button type="button" class="border-0">4</button>
                                    </span>
                                    <span wire:key="paginator-page-1-page5">
                                        <button type="button" class="border-0">5</button>
                                    </span>
                                    <span wire:key="paginator-page-1-page6">
                                        <button type="button" class="border-0">6</button>
                                    </span>
                                    <span wire:key="paginator-page-1-page7">
                                        <button type="button" class="border-0">7</button>
                                    </span>
                                    <span wire:key="paginator-page-1-page8">
                                        <button type="button" class="border-0">8</button>
                                    </span>
                                    <span wire:key="paginator-page-1-page9">
                                        <button type="button" class="border-0">9</button>
                                    </span>

                                    <span>
                                        <button type="button" class="border-0">
                                            <i class="iconsax icon-bold fs-16 mr-0 rotate-ar-y d-inline-block" icon-name="chevron-right"></i>
                                        </button>
                                    </span>
                                </span>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
            <div>
                <p class="text-sm text-gray-700 leading-5 mb-0">
                    <span>Showing</span>
                    <span class="font-medium">1</span>
                    <span>to</span>
                    <span class="font-medium">6</span>
                    <span>of</span>
                    <span class="font-medium">52</span>
                    <span>results</span>
                </p>
            </div>
        </div>
    </div>
</div>
  </div>
  <div class="tab-pane fade" id="customer-statement" role="tabpanel" aria-labelledby="contact-tab">
    <div class="checkout pt-2 pb-20 mb-30 w-100">
        <div class="row">
            <div class="col-lg-3 pr-md-0 mb-3 mb-md-0">
                <div class="card p-3">
                    
            <form class="fs-14">
              <div class="form-group">
                <label for="">From Date</label>
                <div class="position-relative">
                <input type="text" class="form-control datepicker" id="" aria-describedby="emailHelp" placeholder="Enter Start Date">
                <i class="iconsax field-icon text-light" icon-name="calendar-1"></i>
                </div>
                <!-- <small id="emailHelp" class="form-text text-muted">We'll never share your email with anyone else.</small> -->
              </div>
              <div class="form-group">
                <label for="">To Date</label>
                <div class="position-relative">
                <input type="text" class="form-control datepicker" id="" placeholder="Enter End Date">
                <i class="iconsax field-icon text-light" icon-name="calendar-1"></i>
                </div>
              </div>
              <div class="d-flex gap-10 mt-4 justify-content-end">
                <button type="submit" class="btn bg-warning btn-sm text-white radius-xl">Reset</button>
                <button type="submit" class="btn bg-new-primary btn-sm text-white radius-xl">Submit</button>
              </div>
            </form>
            </div>
        </div>
            <div class="col-lg-9 fs-14">
                <div class="card" id="overview-section">
                        <div class="card-header py-3">
                                <div class="">
                                    <img src="https://workdo-dev.osool.cloud/uploads/logo/logo_dark.png" alt="Work Do" class="logo logo-lg" style="max-width: 250px">
                                </div>
                                <div class="">
                                    <strong class="mb-2">My Company</strong><br>
                                    <span class="invoice-number fs-14"><EMAIL></span>
                                </div>
                        </div>
                    <div class="card-header">
                        <h5 class="mb-sm-0 mb-3">Statement of Account</h5>

                        <div class="d-flex flex-wrap align-items-center">
                            <span class="mr-3">2025-06-1 to 2025-06-30</span>
                            <button class="btn btn-xs bg-loss text-white"><i class="iconsax" icon-name="download-1"></i> Download</button>
                        </div>
                    </div>
                    <div class="p-4">

                            <div class="row">
                                <div class="col-md-4 mb-sm-0 mb-3 pr-md-0">
                                    <div class="p-3 address-box radius-xl h-100">
                                        <h6 class="text-dark mb-3 fs-14">Billed To</h6>
                                        <table class="table mb-0 table-borderless new-header td-pl-0 table-address">
                                            <tbody><tr>
                                                <td><span class="fs-12 fw-50 text-dark">Billing Address</span>
                                                </td>
                                                <td>Riyad
                                                </td>
                                            </tr>
                                            <tr>
                                                <td><span class="fs-12 fw-50 text-dark">Billing City                                                    </span>
                                                </td>
                                                <td>riyad
                                                </td>
                                            </tr>
                                            <tr>
                                                <td><span class="fs-12 fw-50 text-dark">Zip Code                                                    </span></td>
                                                <td>101</td>
                                            </tr>
                                            <tr>
                                                <td><span class="fs-12 fw-50 text-dark">Billing Country                                                    </span>
                                                </td>
                                                <td>Saudi Arabia
                                                </td>
                                            </tr>
                                            <tr>
                                                <td><span class="fs-12 fw-50 text-dark">Billing Contact                                                    </span>
                                                </td>
                                                <td>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    <strong>Tax Number  :
                                                   </strong>
                                                </td>
                                                <td>
                                                    TAX1234
                                                </td>
                                            </tr>
                                        </tbody></table>
                                    </div>
                                </div>
                                <div class="col-md-4 mb-sm-0 mb-3 pr-md-0">
                                    <div class="p-3 address-box radius-xl h-100">
                                        <h6 class="text-dark mb-3 fs-14">Shipped To</h6>
                                        <table class="table mb-0 table-borderless new-header td-pl-0 table-address">
                                            <tbody><tr>
                                                <td><span class="fs-12 fw-50 text-dark">Billing Address</span>
                                                </td>
                                                <td>Riyad
                                                </td>
                                            </tr>
                                            <tr>
                                                <td><span class="fs-12 fw-50 text-dark">Billing City                                                    </span>
                                                </td>
                                                <td>riyad
                                                </td>
                                            </tr>
                                            <tr>
                                                <td><span class="fs-12 fw-50 text-dark">Zip Code                                                    </span></td>
                                                <td>101</td>
                                            </tr>
                                            <tr>
                                                <td><span class="fs-12 fw-50 text-dark">Billing Country                                                    </span>
                                                </td>
                                                <td>Saudi Arabia
                                                </td>
                                            </tr>
                                            <tr>
                                                <td><span class="fs-12 fw-50 text-dark">Billing Contact                                                    </span>
                                                </td>
                                                <td>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    <strong>Tax Number  :
                                                   </strong>
                                                </td>
                                                <td>
                                                    TAX1234
                                                </td>
                                            </tr>
                                        </tbody></table>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="p-3 address-box radius-xl h-100">
                                        <h6 class="text-dark mb-3 fs-14">Account summary</h6>
                                        <table class="table mb-0 table-borderless new-header no-wrap tp-0" id="content_table">
                <tbody>
                        <tr>
                            <td><span class="fs-12 fw-50 text-dark"> Balance : </span>  </td>
                            <td><span> 0.00 ﷼</span></td>
                        </tr>
                         <tr>
                            <td><span class="fs-12 fw-50 text-dark"> Invoiced amount :</span>  </td>
                            <td><span> 0.00 ﷼</span></td>
                        </tr>
                         <tr>
                            <td><span class="fs-12 fw-50 text-dark"> Amount Paid :</span> </td>
                            <td><span> 0.00 ﷼</span></td>
                        </tr>
                         <tr>
                            <td> <span class="fs-12 fw-50 text-dark"> Balance Due :</span>   </td>
                            <td><span> 0.00 ﷼</span></td>
                        </tr>
                </tbody>
            </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <div class="card-body px-0 mx-4 address-box radius-xl pb-0 mb-5 overflow-hidden">
                        <h6 class="text-dark ml-4 mb-4">Item List</h6>
                        <div>
    <div class="userDatatable projectDatatable project-table bg-white w-100 border-0">
        <div class="table-responsive">
            <table class="table mb-0 table-borderless new-header" id="content_table">
                <thead>
                    <tr class="userDatatable-header">
                        <th>
                            <span class="projectDatatable-title">Date</span>
                        </th>
                        <th>
                            <span class="projectDatatable-title">Invoice</span>
                        </th>
                        <th>
                            <span class="projectDatatable-title">Payment</span>
                        </th>
                        <th>
                            <div class="">
                                <span class="projectDatatable-title">Amount                                </span>
                            </div>
                        </th>
                    </tr>
                </thead>

                <tbody>
                        <tr>
                            <td>2025-06-1</td>
                            <td><a href=""><span class="fw-400 badge badge-round badge-primary badge-lg badge-outlined">INV-1890999474</span></a></td>
                            <td>Cash</td>
                            <td>
                                <div class="d-flex align-items-center gap-10">
                                    <img src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSx34eO8RbZlsetN1iAQtxuOLnLOlyJbJmU7Q&amp;s" width="15"><span class="text-new-primary">25000</span>
                                </div>
                            </td>
                        </tr>
                </tbody>
                <tfooter>
                    <tr>
                        <td colspan="2"></td>
                            <td><h6>Total</h6></td>
                            <td>
                                <div class="d-flex align-items-center gap-10">
                                    <img src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSx34eO8RbZlsetN1iAQtxuOLnLOlyJbJmU7Q&amp;s" width="15"><span class="text-new-primary">25000</span>
                                </div>
                            </td>
                        </tr>
                </tfooter>
            </table>
            
            
</div>
        <div></div>
    </div>
</div>

<!-- Livewire Component wire-end:vxqzdvCEd0GgkweiPbMf -->                    </div>
                </div>
            </div>
        </div>
    </div>
  </div>
</div>



</div>

           </div>
        </div>


<div class="modal fade" id="delete-vendor" tabindex="-1" role="dialog" aria-labelledby="deleteUserModalLabel" aria-hidden="true">
                    <div class="modal-dialog modal-sm" role="document">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="deleteUserModalLabel">Delete Vendor</h5>
                                <button type="button" class="close" data-dismiss="modal" aria-label="{{ __('user_management_module.modal.close') }}">
                                    <span aria-hidden="true">&times;</span>
                                </button>
                            </div>
                            <div class="modal-body">
                                <div class="text-center">
                                     <i class="iconsax icon text-loss fs-60" icon-name="warning-triangle"></i>
                                    <p class="mt-4">Are you sure you want to delete <br> the vendor <strong id="deleteUserName"> Abdul Rehman ?</strong></p>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                                <form id="deleteUserForm"  method="POST">
                                    <button type="submit" class="btn btn-danger">Yes, Delete It</button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>




@endsection

@section('scripts')
<script type="text/javascript">
    $(".datepicker").datepicker();
    $("#selectUsers,#statusList").select2();
    $(document).ready(function() {
      // Toggle active class and manage dropdown on button click
      $('.filter-button').on('click', function (e) {
        e.stopPropagation(); // Prevent the click from bubbling up to the document
        $(this).toggleClass('active'); // Toggle the 'active' class
        $(this).closest('.dropdown').find(".dropdown-menu").toggleClass('show'); // Toggle the Bootstrap dropdown
      });

      // Remove active class and close dropdown when clicking outside
      $(document).on('click', function (e) {
        if (!$(e.target).closest('.dropdown').length) {
          // If the click is outside the dropdown, remove the 'active' class and close the dropdown
          $('.filter-button').removeClass('active');
          $('.dropdown-menu').removeClass('show');
          $('.filter-button').attr('aria-expanded', 'false');
        }
      });

      // Prevent dropdown from closing when clicking inside the dropdown menu
      $('.dropdown-menu').on('click', function (e) {
        e.stopPropagation();
      });
    });
</script>
@endsection
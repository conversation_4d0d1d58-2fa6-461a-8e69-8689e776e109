@extends('layouts.app')
@section('styles')
@endsection
@section('content')
        <div class="contents crm">
           <div class="container-fluid">
            <div class="col-lg-12">
    <div class="row justify-content-sm-between align-items-center justify-content-center my-3 flex-sm-row flex-column">
        <div class="page-title-wrap p-0">
            <div class="page-title d-flex justify-content-between">
                <div class="page-title__left justify-content-sm-between align-items-center justify-content-center">
                    <div class="user-member__title mr-sm-25 ml-0">
                        <h4 class="text-capitalize fw-500 breadcrumb-title fs-16">
                            {{ __('vendors.common.vendor_details') }}
                        </h4>
                    </div>
                </div>
            </div>
            <div>
                <ul class="atbd-breadcrumb nav">
                    <li class="atbd-breadcrumb__item">
                        <a href="{{ route('admin.dashboard') }}">{{ __('vendors.common.dashboard') }}</a>
                        <span class="breadcrumb__seperator">
                            <span class="la la-angle-right"></span>
                        </span>
                    </li>
                    <li class="atbd-breadcrumb__item">
                        <a href="{{ route('finance.vendors') }}">{{ __('vendors.common.vendors') }}</a>
                    </li>
                </ul>
            </div>
        </div>

        <div class="d-flex gap-10 breadcrumb_right_icons">
            <div class="d-flex gap-10 breadcrumb_right_icons">
                    <button class="btn btn-default btn-primary w-100 no-wrap" type="button" aria-expanded="false" data-toggle="dropdown" aria-expanded="false"><i class="las la-plus fs-16"></i>{{ __('vendors.buttons.create_bill') }}</button>
            </div>
        </div>
        <!--====End Design for Export PDF===-->
    </div>
</div>

<div class="d-flex justify-content-end mb-3">
        <ul class="nav nav-tabs site-pills bg-white p-1 rounded mb-3 mb-sm-0" id="pills-tab" role="tablist">
             <li class="nav-item" role="presentation">
                <button class="nav-link active rounded" id="customer-details-tab" data-toggle="pill" data-target="#customer-details" type="button">{{ __('vendors.common.details') }}</button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link rounded" id="customer-proposal-tab" data-toggle="pill" data-target="#customer-proposal" type="button">{{ __('vendors.common.bill') }}</button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link rounded" id="customer-invoice-tab" data-toggle="pill" data-target="#customer-invoice" type="button">{{ __('vendors.common.purchase') }}</button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link rounded" id="customer-revenue-tab" data-toggle="pill" data-target="#customer-revenue" type="button">{{ __('vendors.common.payment') }}</button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link rounded" id="customer-project-tab" data-toggle="pill" data-target="#customer-project" type="button">{{ __('vendors.common.project') }}</button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link rounded" id="statement-tab" data-toggle="pill" data-target="#customer-statement" type="button">{{ __('vendors.common.statement') }}</button>
            </li>
        </ul>
</div>
<div class="">

<div class="tab-content" id="myTabContent">
  <div class="tab-pane fade show active" id="customer-details" role="tabpanel" aria-labelledby="home-tab">


@if(isset($vendor['details']) && !empty($vendor['details']))

    <div class="col-lg-12 px-0">
    <div class="row mb-3">
        <div class="col-md-4 mb-sm-0 mb-3 pr-md-0">
            <div class="p-3 address-box radius-xl h-100 card">
                <h6 class="text-dark mb-3 fs-14">{{ __('vendors.common.vendor_info') }}</h6>
                <table class="table mb-0 table-borderless new-header td-pl-0 table-address">
                    <tbody>
                        <tr>
                            <td><span class="fs-12 fw-50 text-dark fw-600">{{ __('vendors.common.name') }}</span></td>
                            <td>{{ $vendor['details']['name'] ?? '-' }}</td>
                        </tr>
                        <tr>
                            <td><span class="fs-12 fw-50 text-dark fw-600">{{ __('vendors.common.email') }} </span></td>
                            <td>{{ $vendor['details']['email'] ?? '-' }}</td>
                        </tr>
                        <tr>
                            <td><span class="fs-12 fw-50 text-dark fw-600">{{ __('vendors.common.phone') }} </span></td>
                            <td>{{ $vendor['details']['contact'] ?? '-' }}</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <div class="col-md-4 mb-sm-0 mb-3 pr-md-0">
            <div class="p-3 address-box radius-xl h-100 card">
                <h6 class="text-dark mb-3 fs-14">{{ __('vendors.common.billing_address') }}</h6>
                <table class="table mb-0 table-borderless new-header td-pl-0 table-address">
                    <tbody>
                        <tr>
                            <td><span class="fs-12 fw-50 text-dark fw-600">{{ __('vendors.common.billing_address') }}</span></td>
                            <td>{{ $vendor['details']['billing_address'] ?? '-' }}</td>
                        </tr>
                        <tr>
                            <td><span class="fs-12 fw-50 text-dark fw-600">{{ __('vendors.common.billing_city') }} </span></td>
                            <td>{{ $vendor['details']['billing_city'] ?? '-' }}</td>
                        </tr>
                        <tr>
                            <td><span class="fs-12 fw-50 text-dark fw-600">{{ __('vendors.common.zip_code') }} </span></td>
                            <td>{{ $vendor['details']['billing_zip'] ?? '-' }}</td>
                        </tr>
                        <tr>
                            <td><span class="fs-12 fw-50 text-dark fw-600">{{ __('vendors.common.billing_country') }} </span></td>
                            <td>{{ $vendor['details']['billing_country'] ?? '-' }}</td>
                        </tr>
                        <tr>
                            <td><span class="fs-12 fw-50 text-dark fw-600">{{ __('vendors.common.billing_contact') }} </span></td>
                            <td>{{ $vendor['details']['billing_phone'] ?? '-' }}</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <div class="col-md-4">
            <div class="p-3 address-box radius-xl h-100 card">
                <h6 class="text-dark mb-3 fs-14">{{ __('vendors.common.shipping_address') }}</h6>
                <table class="table mb-0 table-borderless new-header td-pl-0 table-address">
                    <tbody>
                        <tr>
                            <td><span class="fs-12 fw-50 text-dark fw-600">{{ __('vendors.common.shipping_address') }}</span></td>
                            <td>{{ $vendor['details']['shipping_address'] ?? '-' }}</td>
                        </tr>
                        <tr>
                            <td><span class="fs-12 fw-50 text-dark fw-600">{{ __('vendors.common.shipping_city') }} </span></td>
                            <td>{{ $vendor['details']['shipping_city'] ?? '-' }}</td>
                        </tr>
                        <tr>
                            <td><span class="fs-12 fw-50 text-dark fw-600">{{ __('vendors.common.zip_code') }} </span></td>
                            <td>{{ $vendor['details']['shipping_zip'] ?? '-' }}</td>
                        </tr>
                        <tr>
                            <td><span class="fs-12 fw-50 text-dark fw-600">{{ __('vendors.common.shipping_country') }} </span></td>
                            <td>{{ $vendor['details']['shipping_country'] ?? '-' }}</td>
                        </tr>
                        <tr>
                            <td><span class="fs-12 fw-50 text-dark fw-600">{{ __('vendors.common.shipping_contact') }} </span></td>
                            <td>{{ $vendor['details']['shipping_phone'] ?? '-' }}</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>


        <div class="card mb-3" data-select2-id="108">
            <div class="card-header d-flex justify-content-start gap-5">
                <h6>{{ __('vendors.common.company_info') }}</h6>
            </div>
            <div class="card-body" data-select2-id="107">
                <div class="row">
                    <div class="col-md-3 col-6">
                        <label class="fw-600 text-dark">{{ __('vendors.common.vendor_id') }}</label>
                        <p>{{ $vendor['details']['vendor_id'] ?? '-' }}</p>
                    </div>
                    <div class="col-md-3 col-6">
                        <label class="fw-600 text-dark">{{ __('vendors.common.date_of_creation') }}</label>
                        <p>{{ isset($vendor['details']['created_at']) ? date('Y-m-d', strtotime($vendor['details']['created_at'])) : '-' }}</p>
                    </div>
                    <div class="col-md-3 col-6">
                        <label class="fw-600 text-dark">{{ __('vendors.common.balance') }}</label>
                        <div class="d-flex align-items-center gap-10">
                            <img src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSx34eO8RbZlsetN1iAQtxuOLnLOlyJbJmU7Q&s" width="15"><span class="text-new-primary">{{ number_format($vendor['details']['balance'] ?? 0, 2) }}</span>
                        </div>
                    </div>
                    <div class="col-md-3 col-6 mb-3">
                        <label class="fw-600 text-dark">{{ __('vendors.common.overdue') }}</label>
                        <div class="d-flex align-items-center gap-10">
                            <img src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSx34eO8RbZlsetN1iAQtxuOLnLOlyJbJmU7Q&s" width="15"><span class="text-new-primary">{{ number_format($vendor['details']['overdue'] ?? 0, 2) }}</span>
                        </div>
                    </div>
                    <div class="col-md-3 col-6">
                        <label class="fw-600 text-dark">{{ __('vendors.common.total_sum_of_bills') }}</label>
                        <div class="d-flex align-items-center gap-10">
                            <img src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSx34eO8RbZlsetN1iAQtxuOLnLOlyJbJmU7Q&s" width="15"><span class="text-new-primary">{{ number_format($vendor['details']['total_bill_sum'] ?? 0, 2) }}</span>
                        </div>
                    </div>
                    <div class="col-md-3 col-6">
                        <label class="fw-600 text-dark">{{ __('vendors.common.quantity_of_bills') }}</label>
                        <p>{{ $vendor['details']['bill_quantity'] ?? '0' }}</p>
                    </div>
                    <div class="col-md-3 col-6">
                        <label class="fw-600 text-dark">{{ __('vendors.common.average_sales') }}</label>
                        <div class="d-flex align-items-center gap-10">
                            <img src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSx34eO8RbZlsetN1iAQtxuOLnLOlyJbJmU7Q&s" width="15"><span class="text-new-primary">{{ number_format($vendor['details']['avg_sales'] ?? 0, 2) }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
@else
    <div class="text-center py-5">
        <i class="iconsax icon fs-48 text-muted mb-3" icon-name="profile-2user"></i>
        <h6 class="text-muted">{{ __('vendors.messages.vendor_not_found') }}</h6>
        <a href="{{ route('finance.vendors') }}" class="btn btn-primary">
            {{ __('vendors.buttons.back_to_vendors') }}
        </a>
    </div>
@endif


  </div>

  <div class="tab-pane fade" id="customer-proposal" role="tabpanel" aria-labelledby="proposal-tab">
    @livewire('accounting.bill.bill', ['vendorId' => $vendor['details']['id'] ?? request('id'), 'showFullView' => false])
  </div>

  <div class="tab-pane fade" id="customer-invoice" role="tabpanel" aria-labelledby="contact-tab">
    @livewire('purchase-order.purchase-order-list', ['vendorId' => $vendor['details']['id'] ?? request('id'), 'showFullView' => false])
  </div>

  <div class="tab-pane fade" id="customer-revenue" role="tabpanel" aria-labelledby="contact-tab">
    @livewire('accounting.payment', ['vendorId' => $vendor['details']['id'] ?? request('id'), 'showFullView' => false])
  </div>
  <div class="tab-pane fade" id="customer-project" role="tabpanel" aria-labelledby="contact-tab">
    @livewire('accounting.vendor-project-listing', ['vendorId' => $vendor['details']['id'] ?? request('id'), 'showFullView' => false])
  </div>
  <div class="tab-pane fade" id="customer-statement" role="tabpanel" aria-labelledby="contact-tab">
    @livewire('accounting.vendor-statement', ['vendorId' => $vendor['details']['id'] ?? request('id'), 'showFullView' => false])
  </div>

</div>


  </div>
           </div>
        </div>
@endsection

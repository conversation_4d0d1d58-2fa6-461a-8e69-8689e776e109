<?php

namespace App\Http\Livewire\AdvanceContracts;

use Livewire\Component;
use App\Models\Contracts;
use App\Models\Priorities;
use App\Http\Helpers\Helper;
use Livewire\WithPagination;
use App\Models\AssetCategory;
use App\Models\PropertyBuildings;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Models\AdvanceContractDraft;
use Illuminate\Support\Facades\Auth;
use App\Traits\HandlesAdvanceContractDraft;
use App\Services\PerformanceIndicatorService;
use Illuminate\Validation\ValidationException;
use App\Repositories\AdvanceContractRepository;

class DataAgreementKpi extends Component
{
    use HandlesAdvanceContractDraft;
    use WithPagination;

    public $uuid;

    public $contract_name;

    public $client_name;

    public $session_dataid;

    public $filter_regions;

    public $region_id = [];

    public $cities = [];

    public $selected_cities = [];

    public $selected_properties = [];

    public $properties = [];

    public $searchProperty = '';

    public $propertyPage = 1;

    public $perPage = 5;

    public $slaRows = [];

    public $priorities = [];

    public $isOpenModal = false;

    public $isSubcontract = false;

    public $availableKpis = [];

    public array $selectedKpis = [];

    public array $tempSelectedKpis = [];

    public $servicesRows = [];

    public $serviceTypes = [];

    public $checkKpiAll = false;

    public $selectedPriorities = [];

    public $selectedServices = [];

    public $sideFeatures = [
        'allowSub' => false,
        'smartAssign' => false,
        'tenant' => false,
    ];

    public $tenantCategories = [];

    public $selectedTenantCategories = [];

    public $smartAssignCategories = [];

    public $selectedSmartAssignCategories = [];

    public $allowedKpiClasses = PerformanceIndicatorService::ALLOW_LINKING_WITH_SERVICE;

    public $route_prefix;
    public $isVariation = false;
    public $currentStep = 2;
    
    public function updatedSideFeatures($value, $checkboxId)
    {
        $this->sideFeatures[$checkboxId] = $value;

        // When tenant is checked, fetch categories
        if ($checkboxId === 'tenant' && $value) {
            $this->fetchTenantCategories();
        }
        if ($checkboxId === 'smartAssign' && $value) {
            $this->fetchSmartAssignCategories();
        }
    }

    public function fetchTenantCategories()
    {
        $this->tenantCategories = AssetCategory::where('user_id', Auth::user()->project_user_id)
            ->where('is_deleted', 'no')
            ->where('tenant_form_enabled', 1)
            ->get(['id', 'asset_category', 'service_type']);
    }

    public function fetchSmartAssignCategories()
    {
        $this->smartAssignCategories = AssetCategory::where('user_id', Auth::user()->project_user_id)
            ->where('is_deleted', 'no')
            ->get(['id', 'asset_category', 'service_type']);
    }

    // public function updatedCheckKpiAll($value)
    // {
    //     if ($value) {
    //         $this->tempSelectedKpis = $this->availableKpis; // Select all
    //     } else {
    //         // Only clear if all were selected before (i.e., true deselect-all intent)
    //         $currentlySelected = collect($this->tempSelectedKpis)->sort()->values();
    //         $available = collect($this->availableKpis)->sort()->values();

    //         if ($currentlySelected->count() === $available->count() && $currentlySelected->diff($available)->isEmpty()) {
    //             $this->tempSelectedKpis = []; // Unselect all
    //         }
    //     }
    // }

    public function mount(AdvanceContractRepository $advanceContractRepository, $uuid = null, $prefix = null)
    {
        $this->route_prefix = $prefix;
        $this->isVariation = request()->routeIs('variation.*');
        $this->selectedKpis = [];
       
        // If uuid is provided, load existing data
        if ($uuid) {
            $this->uuid = $uuid;

            // Check if the draft with the given UUID exists
            $draft = $advanceContractRepository->findByUuid($this->uuid);

            if ($draft) {
                // Prefill the data if the draft exists
                $draftData = $draft->data_agreement_kpi_data;
                $mainInformationData = $draft->main_information_data;
                $this->isSubcontract = $mainInformationData['is_subcontract'];

                $this->region_id = $draftData['region_id'] ?? [];
                $this->selected_cities = $draftData['selected_cities'] ?? [];
                $this->selected_properties = $draftData['selected_properties'] ?? [];
                $this->slaRows = $draftData['slaRows'] ?? [];
                $this->selectedKpis = $draftData['selectedKpis'] ?? [];
                $this->servicesRows = $draftData['servicesRows'] ?? [];
                $this->sideFeatures = $draftData['sideFeatures'] ?? [
                    'allowSub' => false,
                    'smartAssign' => false,
                    'tenant' => false,
                ];
                if ($this->sideFeatures['smartAssign']) {
                    $this->fetchSmartAssignCategories();
                }

                if ($this->sideFeatures['tenant']) {
                    $this->fetchTenantCategories();
                }
                $this->selectedTenantCategories = $draftData['selectedTenantCategories'] ?? [];
                $this->selectedSmartAssignCategories = $draftData['selectedSmartAssignCategories'] ?? [];

                $this->updateSelectedServices();
                $this->updateSelectedPriorities();

            }
        }

        $this->filter_regions = Helper::getAllRegions();
        $this->availableKpis = PerformanceIndicatorService::getPerformanceIndicators();
        $this->priorities = Priorities::where('user_id',Auth::user()->project_user_id)->get();

        if (! empty($this->region_id)) {
            $this->fetchCities();
        }

        if (! empty($this->selected_cities)) {
            $this->fetchProperties();
        }

        $this->serviceTypes = AssetCategory::where('user_id', Auth::user()->project_user_id)
            ->where('is_deleted', 'no')
            ->where('status', '1')
            ->get(['id', 'asset_category', 'service_type']);
    }

    public function openModal()
    {
        $this->tempSelectedKpis = collect($this->selectedKpis)
            ->pluck('class')
            ->filter(fn ($class) => is_string($class) && class_exists($class))
            ->values()
            ->all();
        $this->isOpenModal = true;
    }

    public function closeModal()
    {

        $this->isOpenModal = false;
    }

    public function submitKpiForm($selectedFromJs = [])
    {
        $this->tempSelectedKpis = is_array($selectedFromJs) ? $selectedFromJs : [];
      
        // Step 1: Remove unchecked KPIs
        $this->selectedKpis = array_filter($this->selectedKpis, function ($kpi) {
            return in_array($kpi['class'], $this->tempSelectedKpis);
        });

        // Step 2: Get existing classes
        $existingClasses = array_column($this->selectedKpis, 'class');

        // Step 3: Add newly selected KPIs
        foreach ($this->tempSelectedKpis as $kpiClass) {
            if (!in_array($kpiClass, $existingClasses)) {
                $this->selectedKpis[] = [
                    'class' => $kpiClass,
                    'percentage_type' => 'percentage',
                    'ranges' => [
                        '100%-95%' => ['penalty' => 0, 'deduction' => 0],
                        '95%-90%' => ['penalty' => 1, 'deduction' => 0],
                        '90%-85%' => ['penalty' => 2, 'deduction' => 0],
                        '85%-80%' => ['penalty' => 3, 'deduction' => 0],
                        '80%-75%' => ['penalty' => 5, 'deduction' => 0],
                        '75%-70%' => ['penalty' => 10, 'deduction' => 0],
                        '70%-65%' => ['penalty' => 20, 'deduction' => 0],
                    ],
                ];
            }
        }

        $this->closeModal();
    }


    public function addSlaRow()
    {
        $this->slaRows[] = [
            'priority_id' => null,
            'service_window_input' => '',
            'service_window_select' => '',
            'response_time_input' => '',
            'response_time_select' => '',
        ];
    }

    public function removeSlaRow($index)
    {
        unset($this->slaRows[$index]);
        $this->slaRows = array_values($this->slaRows);
    }

    public function updateSelectedPriorities()
    {
        // Recalculate selected priorities dynamically from SLA rows
        $this->selectedPriorities = collect($this->slaRows)
            ->pluck('priority_id')
            ->filter()
            ->unique()
            ->values()
            ->toArray();
    }

    public function updatedSlaRows()
    {
        $this->updateSelectedPriorities();
    }

    public function updateSelectedServices()
    {
        // Recalculate selected priorities dynamically from SLA rows
        $this->selectedServices = collect($this->servicesRows)
            ->pluck('service_id')
            ->filter()
            ->unique()
            ->values()
            ->toArray();
    }

    public function updatedServicesRows($value, $key)
    {
        if (str_contains($key, 'service_id')) {
            $this->updateSelectedServices();
        }
    }

    public function addServiceRow()
    {
        $this->servicesRows[] = [
            'service_id' => null,
            'kpi_ids' => '',
            'price' => '',
            'description' => '',
        ];
    }

    public function removeServiceRow($index)
    {
        unset($this->servicesRows[$index]);
        $this->servicesRows = array_values($this->servicesRows);

        $this->updateSelectedServices();
    }

    public function updated($propertyName, $value)
    {
        if ($propertyName == 'region_id') {
            $this->fetchCities();
            $this->selected_cities = [];
            $this->selected_properties = [];
        }
    }

    

    public function updatedSelectedCities()
    {
        $this->propertyPage = 1;
        $this->fetchProperties();
    }

    public function getSelectedPrioritiesProperty()
    {
        return collect($this->slaRows)
            ->pluck('priority_id')
            ->filter()
            ->toArray();
    }

    public function updatedSearchProperty()
    {
        $this->propertyPage = 1;
    }

    private function fetchCities()
    {
        $this->cities = Helper::getCityListRegionIdarray($this->region_id);

    }

    private function fetchProperties()
    {
        if (empty($this->selected_cities)) {
            $this->properties = [];

            return;
        }

        $response = Contracts::getAjaxPropertyList($this->selected_cities, $this->region_id);

        if (isset($response)) {
            $this->properties = json_decode($response, true);
        } else {
            $this->properties = [];
        }
    }

    public function nextPage()
    {
        $totalPages = ceil(count($this->properties) / $this->perPage);
        if ($this->propertyPage < $totalPages) {
            $this->propertyPage++;
        }
    }

    public function previousPage()
    {
        if ($this->propertyPage > 1) {
            $this->propertyPage--;
        }
    }

    public function setPage($page)
    {
        $this->propertyPage = $page;
    }

    public function rules()
    {
        $rules = [
            'region_id' => 'required|array|min:1',
            'selected_cities' => 'required|array|min:1',
            'selected_properties' => 'required|array|min:1',
            'slaRows.*.priority_id' => 'required',
            'slaRows.*.service_window_input' => 'required|numeric',
            'slaRows.*.service_window_select' => 'required',
            'slaRows.*.response_time_input' => 'required|numeric',
            'slaRows.*.response_time_select' => 'required',
            'servicesRows' => 'required|min:1',
            'slaRows'       => 'required|min:1',
            'servicesRows.*.service_id' => 'required|numeric',
            'servicesRows.*.price' => 'required|numeric|min:0',
        ];
        if ($this->sideFeatures['tenant'] ?? false) {
            $rules['selectedTenantCategories'] = 'required|array|min:1';
        }
        
        if ($this->sideFeatures['smartAssign'] ?? false) {
            $rules['selectedSmartAssignCategories'] = 'required|array|min:1';
        }
        return  $rules;
    }
 
    public function messages()
    {
        return [
            'slaRows.required' => __('advance_contracts.validation.slaRows.slaRows_required'),
            'slaRows.min' => __('advance_contracts.validation.slaRows.slaRows_min'),          
            'servicesRows.required' => __('advance_contracts.validation.servicesRows.servicesRows_required'),
            'servicesRows.min' => __('advance_contracts.validation.servicesRows.servicesRows_min'),
          
        ];
    }


    public function getSelectedPropertyDetails(bool $paginate = true)
    {
       $query = PropertyBuildings::with([
            'property' => fn($query) => $query->withTrashed()->select('id', 'complex_name', 'property_type')
        ])
        ->withCount([
            'roomsTypeFloors as units_count',
            'roomsTypeFloors as zones_count' => function ($query) {
                $query->select(DB::raw('COUNT(DISTINCT floor)'));
            },
        ])
        ->when($this->selected_properties, fn ($q) => $q->whereIn('id', $this->selected_properties))
        ->when($this->searchProperty, function ($q) {
            $q->where(function ($query) {
                $query->where('building_name', 'like', '%' . $this->searchProperty . '%')
                    ->orWhereHas('property', function ($subQuery) {
                        $subQuery->withTrashed()->where('complex_name', 'like', '%' . $this->searchProperty . '%');
                    });
            });
        });
        return $paginate
        ? $query->paginate($this->perPage, ['*'], 'propertyPage', $this->propertyPage)
        : $query->get();
    }



    public function submit(AdvanceContractRepository $advanceContractRepository)
    {
        try {
            $this->validate();
        } catch (ValidationException $e) {
            Log::error('Validation failed:', $e->errors());
            throw $e;
        }

        $buildings = $this->getSelectedPropertyDetails(false);
        $propertyDetails = $buildings->map(function ($building) {
            return [
                'building_id' => $building->id,
                'building_name' => $building->building_name,
                'complex_name' => $building->property->complex_name ?? null,
                'property_type' => $building->property->property_type ?? null,
                'units_count' => $building->units_count,
                'zones_count' => $building->zones_count,
            ];
        })->toArray();

        // Prepare the data to be saved in the JSON column
        $dataToSave = [
            'region_id' => $this->region_id,
            'selected_cities' => $this->selected_cities,
            'selected_properties' => $this->selected_properties,
            'slaRows' => $this->slaRows,
            'servicesRows' => $this->servicesRows,
            'selectedKpis' => $this->selectedKpis,
            'sideFeatures' => $this->sideFeatures,
            'selectedTenantCategories' => $this->selectedTenantCategories,
            'selectedSmartAssignCategories' => $this->selectedSmartAssignCategories,
            'property_details' => $propertyDetails,
        ];

        // Check if the draft already exists based on the uuid
         $draft = $advanceContractRepository->findByUuid($this->uuid);

        if ($draft) {
            // Update the existing record
            $draft->update([
                'data_agreement_kpi_data' => $dataToSave,
            ]);
        } else {
             $this->dispatchBrowserEvent('show-toastr', ['type' => 'error', 'message' => 'Invalid request.' ]);

            return redirect()->route('property');
        }

         // Mark current step as saved in session
        $savedSteps = session()->get("contract_saved_steps_{$this->uuid}", []);
        if (!in_array($this->currentStep, $savedSteps)) {
            $savedSteps[] = $this->currentStep;
        }
        session()->put("contract_saved_steps_{$this->uuid}", $savedSteps);


        $this->dispatchBrowserEvent('show-toastr', ['type' => 'success', 'message' => __('advance_contracts.general.data_agreements_saved')]);
        return redirect()->route($this->route_prefix .'asset-ppm', ['uuid' => $this->uuid]);
    }

    public function render()
    {

        $totalZones = 0;
        $totalUnits = 0;
        $totalPages = 0;
        $buildings = [];

        // Properties listing
        if (! empty($this->selected_properties)) {

            $buildings = $this->getSelectedPropertyDetails();
            $totalUnits = $buildings->sum('units_count');
            $totalZones = $buildings->sum('zones_count');
            
            $totalPages = $buildings->lastPage();
        }

        // Properties Listing end
        return view('livewire.advance-contracts.data-agreement-kpi', [
            'performanceIndicators' => $this->selectedKpis,
            'paginatedProperties' => $buildings,
            'totalUnits' => $totalUnits ?? 0,
            'totalZones' => $totalZones ?? 0,
            'totalPages' => $totalPages,

        ]);
    }
}

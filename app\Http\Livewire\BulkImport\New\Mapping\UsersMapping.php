<?php
    namespace App\Http\Livewire\BulkImport\New\Mapping;
    use Livewire\Component;
    use Illuminate\Support\Facades\Log;
    use App\Http\Traits\FunctionsTrait;
    use App\Http\Traits\BulkImportTrait;
    use App\Http\Traits\UserTrait;
    use App\Http\Traits\ServiceProviderProjectMappingTrait;

    class UsersMapping extends Component{
        use FunctionsTrait, BulkImportTrait, UserTrait, ServiceProviderProjectMappingTrait;

        public $showMore;
        public $perPage;
        public $countList;
        public $chunkData;
        public $projectUserId;
        public $bulkImportDetails;

        public function render(){
            $list = $this->getPaginatedUsersList();
            isset($list) && count($list) > 0 ? $this->setCountList($list->total()) : $this->setCountList(0);
            return view('livewire.bulk-import.new.mapping.users-mapping', compact('list'));
        }

        public function initUsersList() {
            try {
                $usersData = isset($this->bulkImportDetails) ? $this->bulkImportDetails->bulkImportTemp->users_data : null;
                $array = json_decode($usersData, true);
                return collect($array);
            } 
            
            catch (\Throwable $th) {
                Log::error("initUsersList error: ".$th);
            }
        }

        public function getPaginatedUsersList() {
            try {
                $usersList = $this->initUsersList();
                return $this->customPagination($usersList, $this->perPage);
            } 
            
            catch (\Throwable $th) {
                Log::error("getPaginatedUsersList error: ".$th);
            }
        }

        public function managePerPage() {
            try {
                $number = $this->additionOperation($this->perPage, $this->showMore);
                $this->setPerPage($number);
            } 
            
            catch (\Throwable $th) {
                Log::error("managePerPage error: ".$th);
            }
        }

        public function setPerPage($value) {
            try {
                $this->perPage = $value;
            } 
            
            catch (\Throwable $th) {
                Log::error("setPerPage error: ".$th);
            }
        }

        public function setCountList($value) {
            try {
                $this->countList = $value;
            } 
            
            catch (\Throwable $th) {
                Log::error("setCountList error: ".$th);
            }
        }

        public function manageLoadAll() {
            try {
                $this->setPerPage($this->countList);
            } 
            
            catch (\Throwable $th) {
                Log::error("manageLoadAll error: ".$th);
            }
        }

        public function manageListStatusData() {
            try {
                $users = $this->initUsersList();
                $acceptedNumber = 0;
                $refusedNumber = 0;

                if(isset($users) && count($users) > 0){
                    collect($users)->chunk($this->chunkData)->each(function($chunk) use(&$acceptedNumber, &$refusedNumber){
                        foreach($chunk as $data){
                            $response = $this->fullUsersValidation($data);

                            if(isset($response) && $response[0]['status'] == 'success'){
                                $acceptedNumber = $acceptedNumber + 1;
                            }

                            else{
                                $refusedNumber = $refusedNumber + 1;
                            }
                        }
                    });
                }

                return [
                    'acceptedNumber' => $acceptedNumber,
                    'refusedNumber' => $refusedNumber
                ];
            } 
            
            catch (\Throwable $th) {
                Log::error("manageListStatusData error: ".$th);
            }
        }
    }
?>
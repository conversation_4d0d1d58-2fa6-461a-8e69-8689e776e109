<?php

namespace App\Http\Livewire\CRMProjects\Modals;

use Livewire\Component;

class MilestoneAdvancedDetails extends Component
{

public $projectId ;
     public $milestone = [
        'created_at' => '',
        'total_task' => '',
        'title' => '',
        'status' => '',
        'start_date' => '',
        'end_date' => '',
        'cost' => '',
        'summary' => '',
        'progress' => 0,
        'tasks' => [],
    ];
     public function mount()
    {
        try {
            $this->projectId = decrypt(request()->id);
        } catch (DecryptException $e) {
            return abort(404);
        }
    }

    protected $listeners = ['showMilestoneAdvancedDetails'];

    public function showMilestoneAdvancedDetails($id)
    {
        $service = app(\App\Services\CRM\Sales\ProjectService::class);
        $response = $service->getTasksByMilstoneId($this->projectId , $id);

        if (data_get($response, 'status') === 'success') {
            $data = data_get($response, 'data', []);
            $this->milestone['created_at'] = data_get($data, 'created_at');
            $this->milestone['total_task'] = data_get($data, 'total_task');
            $this->milestone['status'] = data_get($data, 'status');
            $this->milestone['title'] = data_get($data, 'title');
            $this->milestone['progress'] = data_get($data, 'progress');
            $this->milestone['start_date'] = data_get($data, 'start_date');
            $this->milestone['end_date'] = data_get($data, 'end_date');
            $this->milestone['cost'] = data_get($data, 'cost');
            $this->milestone['summary'] = data_get($data, 'summary');
            $this->milestone['tasks'] = data_get($data, 'tasks');
        }
        $this->dispatchBrowserEvent('open-modal', ['modalId' => 'milestoneAdvancedDetails']);
        $this->dispatchBrowserEvent('hideLoader');
 
    }

    public function render()
    {
        return view('livewire.c-r-m-projects.modals.milestone-advanced-details');
    }
}

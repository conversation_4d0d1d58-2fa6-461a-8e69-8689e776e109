<div>
    @include('livewire.common.loader-1', [
        'target' => 'search,sort_by,sort_dir,loadData,category,account,end_date,start_date',
    ])
    <div class="card">
        <div class="">
            <div class="card-header py-4 px-3 border-0 d-flex justify-content-between align-items-center">
                <div class="dataTable-dropdown d-flex align-center gap-20">

                </div>

                <div class="d-flex gap-10 table-search">
                    <div class="position-relative">
                        <input type="text" class="form-control" wire:model.debounce.1500ms='search' placeholder="Search">
                        <i class="iconsax field-icon fs-18 mr-0" icon-name="search-normal-2"></i>
                    </div>
                </div>
            </div>
        </div>

        <div class="card-body px-0 pt-0 pb-0">
            <div class="userDatatable projectDatatable project-table bg-white w-100 border-0">
                <div class="table-responsive">
                    <table class="table mb-0 radius-0 th-osool">
                        <thead>
                            <tr class="userDatatable-header">
                                <th wire:click='changeSort("date")'><i class="iconsax icon fs-22"
                                        icon-name="swap-vertical-circle"></i>
                                    @lang('accounting.date')
                                </th>
                                <th wire:click='changeSort("amount")'>
                                    <i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i>
                                    @lang('accounting.amount')
                                </th>
                                <th wire:click='changeSort("description")'>
                                    <i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i>
                                    @lang('accounting.description')
                                </th>
                            </tr>
                        </thead>
                        <tbody class="sort-table ui-sortable">
                            @forelse (@$transactions['items'] ?? [] as $transaction)
                                <tr class="ui-sortable-handle" style="opacity: 1;">
                                    <td>
                                        <div class="d-flex userDatatable-content mb-0 align-items-center">
                                            <span>{{ Helper::formatDateForLocale($transaction['date']) }}</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center gap-10">
                                            {!! $currency !!}
                                            <span
                                                class="text-new-primary">{{ Helper::human_readable_number($transaction['amount']) }}</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex userDatatable-content mb-0 align-items-center">
                                            <span>{{ $transaction['description'] }}</span>
                                        </div>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="6">
                                        @include('livewire.sales.common.no-data-tr')
                                    </td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        @livewire('common.paginator-v1', [
            'currentPage' => @$transactions['current_page'],
            'totalRecords' => @$transactions['total'],
            'functionName' => 'loadData',
            'perPage' => 5,
            'updateUrl' => false,
        ])
    </div>
</div>

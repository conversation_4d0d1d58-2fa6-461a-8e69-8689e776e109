<?php
    namespace App\Http\Traits;
    use Illuminate\Support\Facades\Log;
    use App\Models\BulkImportError;

    trait BulkImportErrorTrait{ 
        public function saveBulkImportError($array) {
            try {
                return BulkImportError::insertGetId($array);
            } 
            
            catch (\Throwable $th) {
                Log::error("saveBulkImportErrors error: ".$th);
            }
        }

        public function getBulkImportErrorsListByValues($bulkImportId, $sheet, $type, $typeValue, $perPage) {
            try {
                return BulkImportError::select('map_status', 'backend_status', 'errors', 'sheet', 'identifier', 'value', 'bulk_import_id')
                ->where('bulk_import_id', $bulkImportId)
                ->where('sheet', $sheet)
                ->where($type, $typeValue)
                ->paginate($perPage, ['*'], 'page');   
            } 
            
            catch (\Throwable $th) {
                Log::error("getBulkImportErrorsListByValues error: ".$th);
            }
        }

        public function deleteBulkImportErrorsListByValues($bulkImportId, $sheet) {
            try {
                return BulkImportError::where('bulk_import_id', $bulkImportId)
                ->where('sheet', $sheet)
                ->delete();
            } 
            
            catch (\Throwable $th) {
                Log::error("deleteBulkImportErrorsListByValues error: ".$th);
            }
        }

        public function getBulkImportErrorsCountByValues($bulkImportId, $sheet, $type, $typeValue) {
            try {
                return BulkImportError::where('bulk_import_id', $bulkImportId)
                ->where('sheet', $sheet)
                ->where($type, $typeValue)
                ->count(); 
            } 
            
            catch (\Throwable $th) {
                Log::error("getBulkImportErrorsCountByValues error: ".$th);
            }
        }

        public function getBulkImportErrorsListByArrayValues($bulkImportId, $sheet, $type, $typeValue, $perPage) {
            try {
                return BulkImportError::select('map_status', 'backend_status', 'errors', 'sheet', 'identifier', 'value', 'bulk_import_id')
                ->where('bulk_import_id', $bulkImportId)
                ->where('sheet', $sheet)
                ->whereIn($type, $typeValue)
                ->paginate($perPage, ['*'], 'page');   
            } 
            
            catch (\Throwable $th) {
                Log::error("getBulkImportErrorsListByValues error: ".$th);
            }
        }
    }
?>
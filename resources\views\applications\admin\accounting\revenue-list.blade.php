@extends('layouts.app')
@section('styles')
<style type="text/css">
  
</style>
@endsection
@section('content')
        <div class="contents crm">
           <div class="container-fluid">
            <div class="col-lg-12">
    <div class="row justify-content-sm-between align-items-center justify-content-center my-3 flex-sm-row flex-column">
        <div class="page-title-wrap p-0">
            <div class="page-title d-flex justify-content-between">
                <div class="page-title__left justify-content-sm-between align-items-center justify-content-center">
                    <div class="user-member__title mr-sm-25 ml-0">
                        <h4 class="text-capitalize fw-500 breadcrumb-title fs-16">
                            Manage Revenue
                        </h4>
                    </div>
                </div>
            </div>
            <div>
                <ul class="atbd-breadcrumb nav">
                    <li class="atbd-breadcrumb__item">
                        <a>Dashboard</a>
                        <span class="breadcrumb__seperator">
                            <span class="la la-angle-right"></span>
                        </span>
                    </li>
                    <li class="atbd-breadcrumb__item">
                        <a>Manage Revenue</a>
                    </li>
                </ul>
            </div>
        </div>

        <div class="d-flex gap-10 breadcrumb_right_icons">
            <div class="d-flex gap-10 breadcrumb_right_icons">
                <select class="select2-new form-control">
                    <option>
                        Sales
                    </option>
                </select>
                    <button class="btn btn-white btn-default text-center svg-20 px-2 wh-45">
                        <i class="iconsax icon text-new-primary fs-22 colorRed mr-0" icon-name="folder-add"></i> 
                    </button>

                    <button class="btn btn-white btn-default text-center svg-20 wh-45" wire:click="switchView('cards')">
                        <i class="iconsax icon text-new-primary fs-22 colorRed mr-0" icon-name="layout-3"></i>
                    </button>

                    <button class="btn btn-default btn-primary w-100 no-wrap" type="button" aria-expanded="false"  data-toggle="modal" data-target="#create-revenue"><i class="las la-plus fs-16"></i>Create</button>

            </div>
        </div>
        <!--====End Design for Export PDF===-->
    </div>
</div>


<div class="table-responsive">

        <div class="card mb-3" data-select2-id="108">
            <div class="card-body" data-select2-id="107">
                <form wire:submit.prevent="applyFilters" class="fs-14">
                    <div class="d-flex flex-wrap gap-10">
                        <div class="flex-fill max-w-180">
                            <label for="" class="text-osool">Date</label>
                            <div class="position-relative">
                                <input type="text" class="form-control datepicker" id="start_date" aria-describedby="emailHelp" placeholder="Enter Start Date" />
                                 <i class="iconsax field-icon" icon-name="calendar-search"></i>
                            </div>
                        </div>
                        <div class="flex-fill max-w-150">
                            <label for="" class="text-osool">Account</label>
                            <select class="form-control select2-new">
                                <option value="" data-select2-id="87">Select User</option>
                                <option value="167" data-select2-id="118">Testing</option>
                                <option value="168" data-select2-id="119">Testing</option>
                                <option value="169" data-select2-id="120">Rashed</option>
                                <option value="170" data-select2-id="121">Hisham</option>
                                <option value="172" data-select2-id="122">راشد</option>
                                <option value="173" data-select2-id="123">Rashed</option>
                                <option value="174" data-select2-id="124">nkjkl</option>
                                <option value="175" data-select2-id="125">Lead to Deal</option>
                            </select>
                        </div>
                        <div class="flex-fill max-w-180" data-select2-id="106">
                            <label for="" class="text-osool">Customer</label>
                            <select class="form-control select2-new">
                                <option value="" data-select2-id="93">Select Status</option>
                                <option value="" data-select2-id="113">All Status</option>
                                <option value="Ongoing" data-select2-id="114">Ongoing</option>
                                <option value="Finished" data-select2-id="115">Finished</option>
                                <option value="OnHold" data-select2-id="116">OnHold</option>
                            </select>
                        </div>
                        <div class="flex-fill max-w-150" data-select2-id="106">
                            <label for="" class="text-osool">Category</label>
                            <select class="form-control select2-new">
                                <option value="" data-select2-id="93">Category 1</option>
                                <option value="" data-select2-id="113">Category 2</option>
                                <option value="Ongoing" data-select2-id="114">Category 3</option>
                                <option value="Finished" data-select2-id="115">Category 4</option>
                                <option value="OnHold" data-select2-id="116">Category 5</option>
                            </select>
                        </div>

                        <div class="flex-fill">
                            <label for="" class="d-md-block d-none">&nbsp;</label>
                            <div class="d-flex gap-10">
                                <button type="submit" class="btn bg-opacity-new-primary btn-sm text-new-primary radius-md px-5">
                                    Apply
                                </button>
                                <button type="button" class="btn bg-opacity-loss btn-sm text-loss wh-45 radius-md">
                                    <i class="iconsax mr-0 fs-18" icon-name="rotate-left"></i>
                                    <!-- Reset -->
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    <div class="card">
        <div class="">
            <div class="card-header py-4 px-3 border-0 d-flex justify-content-between align-items-center">
                <h6 class="text-capitalize text-osool fw-500 mb-3 mb-sm-0">Revenue</h6>

                <div class="d-flex gap-10 table-search">
                    <div class="position-relative">
                    <input type="text" class="form-control" placeholder="Search">
                    <i class="iconsax field-icon fs-18 mr-0" icon-name="search-normal-2"></i>
                </div>
                     <button class="btn btn-export text-dark"><i class="iconsax icon fs-22 mr-0" icon-name="upload-1"></i> Export</button>
                </div>
            </div>
        </div>
        <div class="card-body px-0 pt-0">
            <div class="userDatatable projectDatatable project-table bg-white w-100 border-0">
                <div class="table-responsive">
                    <table class="table mb-0 radius-0 th-osool">
                        <thead>
                            <tr class="userDatatable-header">
                                <th>
                                    Date
                                </th>
                                <th>
                                   <i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i>  Amount
                                </th>
                                <th>
                                    <i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i> Account
                                </th>
                                <th>
                                   <i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i>  Customer
                                </th>
                                <th>
                                   <i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i>  Category
                                </th>
                                <th>
                                    <i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i> Reference
                                </th>
                                <th>
                                     <i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i> Description
                                </th>
                                <th>
                                    Action
                                </th>
                            </tr>
                        </thead>
                        <tbody class="sort-table ui-sortable">
                            <tr class="ui-sortable-handle" style="opacity: 1;">
                                <td>
                                    <div class="d-flex userDatatable-content mb-0 align-items-center">
                                        <span>09-09-2025</span>
                                    </div>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center gap-10">
                                    <img src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSx34eO8RbZlsetN1iAQtxuOLnLOlyJbJmU7Q&s" width="15"><span class="text-new-primary">25000</span>
                                </div>
                                </td>
                                <td>
                                    <div class="d-flex userDatatable-content mb-0 align-items-center">
                                        <span>Quantum Trust Bank</span>
                                    </div>
                                </td>
                                <td>
                                    <div class="d-flex userDatatable-content mb-0 align-items-center">
                                        <span>Mohammad</span>
                                    </div>
                                </td>
                                <td>
                                    <div class="d-flex userDatatable-content mb-0 align-items-center">
                                        <span>Product Sales</span>
                                    </div>
                                </td>
                                <td>
                                    <div class="d-flex userDatatable-content mb-0 align-items-center">
                                        <span>self</span>
                                    </div>
                                </td>
                                <td>
                                    <div class="d-flex userDatatable-content mb-0 align-items-center max-td">
                                        <span>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.</span>
                                    </div>
                                </td>
                                <td>
                                    <div class="d-inline-block">
                                        <ul class="mb-0 d-flex flex-wrap gap-10">
                                            <li>
                                                <a href="javascript:void(0);" data-toggle="modal" data-target="#revenue-details">
                                                    <i class="iconsax icon text-osool fs-18" icon-name="eye"></i>
                                                </a>
                                            </li>
                                            <li>
                                               <a href="javascript:void(0);" data-toggle="modal" data-target="#create-revenue">
                                                    <i class="iconsax icon text-new-primary fs-18" icon-name="edit-1"></i>
                                                </a>
                                            </li>
                                            <li>
                                                <a href="javascript:void(0);" wire:click="openDeleteModal(588, 'Beyond Tech1 Copy test task')">
                                                    <i class="iconsax icon text-delete fs-18" icon-name="trash"></i>
                                                </a>
                                            </li>
                                            <li>
                                                <a href="javascript:void(0);">
                                                    <i class="iconsax icon text-osool fs-18 mr-0" icon-name="download-1"></i>
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                </td>
                            </tr>
                            <tr class="ui-sortable-handle" style="opacity: 1;">
                                <td>
                                    <div class="d-flex userDatatable-content mb-0 align-items-center">
                                        <span>09-09-2025</span>
                                    </div>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center gap-10">
                                    <img src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSx34eO8RbZlsetN1iAQtxuOLnLOlyJbJmU7Q&s" width="15"><span class="text-new-primary">25000</span>
                                </div>
                                </td>
                                <td>
                                    <div class="d-flex userDatatable-content mb-0 align-items-center">
                                        <span>Quantum Trust Bank</span>
                                    </div>
                                </td>
                                <td>
                                    <div class="d-flex userDatatable-content mb-0 align-items-center">
                                        <span>Mohammad</span>
                                    </div>
                                </td>
                                <td>
                                    <div class="d-flex userDatatable-content mb-0 align-items-center">
                                        <span>Product Sales</span>
                                    </div>
                                </td>
                                <td>
                                    <div class="d-flex userDatatable-content mb-0 align-items-center">
                                        <span>self</span>
                                    </div>
                                </td>
                                <td>
                                    <div class="d-flex userDatatable-content mb-0 align-items-center max-td">
                                        <span>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.</span>
                                    </div>
                                </td>
                                <td>
                                    <div class="d-inline-block">
                                        <ul class="mb-0 d-flex flex-wrap gap-10">
                                            <li>
                                                <a href="javascript:void(0);" data-toggle="modal" data-target="#revenue-details">
                                                    <i class="iconsax icon text-osool fs-18" icon-name="eye"></i>
                                                </a>
                                            </li>
                                            <li>
                                               <a href="javascript:void(0);" data-toggle="modal" data-target="#create-revenue">
                                                    <i class="iconsax icon text-new-primary fs-18" icon-name="edit-1"></i>
                                                </a>
                                            </li>
                                            <li>
                                                <a href="javascript:void(0);" wire:click="openDeleteModal(588, 'Beyond Tech1 Copy test task')">
                                                    <i class="iconsax icon text-delete fs-18" icon-name="trash"></i>
                                                </a>
                                            </li>
                                            <li>
                                                <a href="javascript:void(0);">
                                                    <i class="iconsax icon text-osool fs-18 mr-0" icon-name="download-1"></i>
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                </td>
                            </tr>
                            <tr class="ui-sortable-handle" style="opacity: 1;">
                                <td>
                                    <div class="d-flex userDatatable-content mb-0 align-items-center">
                                        <span>09-09-2025</span>
                                    </div>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center gap-10">
                                    <img src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSx34eO8RbZlsetN1iAQtxuOLnLOlyJbJmU7Q&s" width="15"><span class="text-new-primary">25000</span>
                                </div>
                                </td>
                                <td>
                                    <div class="d-flex userDatatable-content mb-0 align-items-center">
                                        <span>Quantum Trust Bank</span>
                                    </div>
                                </td>
                                <td>
                                    <div class="d-flex userDatatable-content mb-0 align-items-center">
                                        <span>Mohammad</span>
                                    </div>
                                </td>
                                <td>
                                    <div class="d-flex userDatatable-content mb-0 align-items-center">
                                        <span>Product Sales</span>
                                    </div>
                                </td>
                                <td>
                                    <div class="d-flex userDatatable-content mb-0 align-items-center">
                                        <span>self</span>
                                    </div>
                                </td>
                                <td>
                                    <div class="d-flex userDatatable-content mb-0 align-items-center max-td">
                                        <span>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.</span>
                                    </div>
                                </td>
                                <td>
                                    <div class="d-inline-block">
                                        <ul class="mb-0 d-flex flex-wrap gap-10">
                                           <li>
                                                <a href="javascript:void(0);" data-toggle="modal" data-target="#revenue-details">
                                                    <i class="iconsax icon text-osool fs-18" icon-name="eye"></i>
                                                </a>
                                            </li>
                                            <li>
                                               <a href="javascript:void(0);" data-toggle="modal" data-target="#create-revenue">
                                                    <i class="iconsax icon text-new-primary fs-18" icon-name="edit-1"></i>
                                                </a>
                                            </li>
                                            <li>
                                                <a href="javascript:void(0);" wire:click="openDeleteModal(588, 'Beyond Tech1 Copy test task')">
                                                    <i class="iconsax icon text-delete fs-18" icon-name="trash"></i>
                                                </a>
                                            </li>
                                            <li>
                                                <a href="javascript:void(0);">
                                                    <i class="iconsax icon text-osool fs-18 mr-0" icon-name="download-1"></i>
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>




        <div class="card-body pt-0">
<div class="d-flex justify-content-md-between flex-column flex-md-row justify-content-center align-items-center mt-4 gap-10">
    <div class="">
        <ul class="atbd-pagination d-flex justify-content-between">
            <li>
                <div class="paging-option">
                    <div class="dataTables_length d-flex">
                        <label class="d-flex align-items-center mb-0">
                            <select aria-controls="workorder_table" wire:model.live.debounce.250ms="perPage" class="custom-select custom-select-sm form-control form-control-sm mx-2" style="min-height: 35px;">
                                <option value="5">5</option>
                                <option value="10">10</option>
                                <option value="25">25</option>
                                <option value="50">50</option>
                                <option value="100">100</option>
                            </select>
                           <span class="no-wrap"> Entries Per Page </span>
                        </label>
                    </div>
                </div>
            </li>
        </ul>
    </div>
    <div class="">
        <div class="user-pagination">
            <div class="user-pagination new-pagination">
                <div class="d-flex justify-content-sm-end justify-content-end">
                    <nav>
                        <span class="relative z-0 inline-flex rounded-md d-flex new-pagination-section flex-wrap">
    <span class="">
        <span aria-disabled="true" aria-label="&amp;laquo; Previous">
            <button class="border-0 disabled" aria-hidden="true" disabled="">
                <i class="iconsax icon-bold fs-16 mr-0 rotate-ar-y d-inline-block" icon-name="chevron-left"></i>
            </button>
        </span>
    </span>

    <span wire:key="paginator-page-1-page1">
        <button class="border-0 current-page" disabled="">1</button>
    </span>
    <span wire:key="paginator-page-1-page2">
        <button type="button" class="border-0">2</button>
    </span>
    <span wire:key="paginator-page-1-page3">
        <button type="button" class="border-0">3</button>
    </span>
    <span wire:key="paginator-page-1-page4">
        <button type="button" class="border-0">4</button>
    </span>
    <span wire:key="paginator-page-1-page5">
        <button type="button" class="border-0">5</button>
    </span>
    <span wire:key="paginator-page-1-page6">
        <button type="button" class="border-0">6</button>
    </span>
    <span wire:key="paginator-page-1-page7">
        <button type="button" class="border-0">7</button>
    </span>
    <span wire:key="paginator-page-1-page8">
        <button type="button" class="border-0">8</button>
    </span>
    <span wire:key="paginator-page-1-page9">
        <button type="button" class="border-0"> 9 </button>
    </span>

    <span>
        <button type="button" class="border-0">
            <i class="iconsax icon-bold fs-16 mr-0 rotate-ar-y d-inline-block" icon-name="chevron-right"></i>
        </button>
    </span>
</span>

                    </nav>
                </div>
            </div>
        </div>
    </div>
    <div>
        <p class="text-sm text-gray-700 leading-5 mb-0">
                        <span>Showing</span>
                        <span class="font-medium">1</span>
                        <span>to</span>
                        <span class="font-medium">6</span>
                        <span>of</span>
                        <span class="font-medium">52</span>
                        <span>results</span>
                    </p>
    </div>
</div>
        </div>
    </div>

    
</div>



</div>




<!-- Modal -->
<div class="modal fade" id="create-revenue" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalLabel">Create Revenue</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <div class="row">
    <div class="form-group col-md-6">
        <label for="date" class="form-label">Date</label><span class="text-danger">*</span>
        <div class="form-icon-user">
            <input class="form-control datepicker" required="required" placeholder="Select Date" name="date" type="text" id="date" />
        </div>
    </div>
    <div class="form-group col-md-6">
        <label for="amount" class="form-label">Amount</label><span class="text-danger">*</span>
        <div class="form-icon-user">
            <input class="form-control" required="required" placeholder="Enter Amount" step="0.01" min="0" name="amount" type="number" id="amount" />
        </div>
    </div>
    <div class="form-group col-md-6">
        <label for="account_id" class="form-label">Account</label><span class="text-danger">*</span>
        <select class="form-control select2-new" required="required" id="account_id" name="account_id">
            <option selected="selected" value="">Select Account</option>
            <option value="27">sss cash</option>
            <option value="113">Saudi National Bank Mohammed Firdaus0</option>
        </select>
    </div>
    <div class="form-group col-md-6">
        <label for="customer_id" class="form-label">Customer</label><span class="text-danger">*</span>
        <select class="form-control select2-new" required="required" id="customer_id" name="customer_id">
            <option selected="selected" value="">Select Customer</option>
            <option value="30">Customer 1</option>
            <option value="31">Customer 2</option>
            <option value="32">Customer 3</option>
            <option value="33">Customer 4</option>
            <option value="34">Customer 5</option>
            <option value="35">Customer 6</option>
            <option value="36">Customer 7</option>
            <option value="37">Customer 8</option>
            <option value="38">Customer 9</option>
            <option value="87">Fouzan</option>
        </select>
    </div>
    <div class="form-group col-md-6">
        <label for="category_id" class="form-label">Category</label><span class="text-danger">*</span>
        <select class="form-control select2-new" required="required" id="category_id" name="category_id">
            <option selected="selected" value="">Select Category</option>
            <option value="18">Test cat</option>
        </select>
    </div>
    <div class="form-group col-md-6">
        <label for="reference" class="form-label">Reference</label><span class="text-danger">*</span>
        <div class="form-icon-user">
            <input class="form-control" placeholder="Enter Reference" required="required" name="reference" type="text" id="reference" />
        </div>
    </div>
    <div class="form-group col-md-12">
        <label for="description" class="form-label">Description</label><span class="text-danger">*</span>
        <textarea class="form-control" rows="3" required="required" name="description" cols="50" id="description"></textarea>
    </div>
    <div class="form-group col-md-12">
        <label for="add_receipt" class="form-label">Payment Receipt</label>
        <input type="file" id="imageInput" multiple accept="image/*" class="d-none">
        <label class="btn btn-default bg-new-primary" for="imageInput">
                <i class="las la-upload fs-16"></i> Upload Files </label>
        <div id="imagePreview" class="preview-container file-upload-new"></div>
    </div>
</div>

      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
        <button type="button" class="btn btn-primary">Save changes</button>
      </div>
    </div>
  </div>
</div>




<!-- Modal Bank account Details -->
<div class="modal fade" id="revenue-details" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalLabel">Bank Account Details</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <div class="userDatatable projectDatatable project-table bg-white w-100 border-0">
                <div class="table-responsive">
                    <table class="table mb-0 radius-0 th-borderless">
                        <tbody class="sort-table ui-sortable">
                            <tr>
                                 <th>
                                   Date
                                </th>
                                <td>
                                    <div class="d-flex userDatatable-content mb-0 align-items-center">
                                        <span>Bank Of Baroda</span>
                                    </div>
                                </td>
                            </tr>
                             <tr>
                                 <th>
                                   Amount
                                </th>
                                <td>
                                    <div class="d-flex userDatatable-content mb-0 align-items-center">
                                        <span>Bank Of Baroda</span>
                                    </div>
                                </td>
                            </tr>
                             <tr>
                                 <th>
                                   Account
                                </th>
                                <td>
                                    <div class="d-flex userDatatable-content mb-0 align-items-center">
                                        <span>Bank Of Baroda</span>
                                    </div>
                                </td>
                            </tr>
                             <tr>
                                 <th>
                                   Customer
                                </th>
                                <td>
                                    <div class="d-flex userDatatable-content mb-0 align-items-center">
                                        <span>Cutomer 1</span>
                                    </div>
                                </td>
                            </tr>
                             <tr>
                                 <th>
                                  Category
                                </th>
                                <td>
                                    <div class="d-flex userDatatable-content mb-0 align-items-center">
                                        <span>Bank Of Baroda</span>
                                    </div>
                                </td>
                            </tr>
                             <tr>
                                 <th>
                                   Reference
                                </th>
                                <td>
                                    <div class="d-flex userDatatable-content mb-0 align-items-center">
                                        <span>Bank Of Baroda</span>
                                    </div>
                                </td>
                            </tr>
                             <tr>
                                 <th>
                                  Description
                                </th>
                                <td>
                                    <div class="d-flex userDatatable-content mb-0 align-items-center max-w-360">
                                        <span>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam</span>
                                    </div>
                                </td>
                            </tr>
                             <tr>
                                 <th>
                                   Payment Receipt
                                </th>
                                <td>
                                    <div class="d-flex align-items-center p-2 gap-10">
                                        <div class="view-img wo-img-div rounded" style="background: url('https://images.pexels.com/photos/11181151/pexels-photo-11181151.jpeg'); background-size: cover; background-position: center;">
                                            <img onclick="chatImageClick(this)" src="https://images.pexels.com/photos/11181151/pexels-photo-11181151.jpeg" alt="Maintenance Request Image" class="uploaded-image" width="100%">
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
      </div>
    </div>
  </div>
</div>

           </div>
        </div>







@endsection

@section('scripts')
<script type="text/javascript">
    $(".datepicker").datepicker();
    $("#selectUsers,#statusList").select2();
</script>
<script>
$(document).ready(function () {
  let currentFiles = [];

  const maxFiles = 1;
  const maxSizeMB = 2;
  const allowedTypes = ['image/jpeg', 'image/png', 'image/webp'];

  $('#imageInput').on('change', function () {
    const newFiles = Array.from(this.files);
    let totalFiles = currentFiles.length + newFiles.length;

    if (totalFiles > maxFiles) {
      alert(`Only ${maxFiles} images allowed.`);
      this.value = ''; // reset input
      return;
    }

    newFiles.forEach((file, index) => {
      if (!allowedTypes.includes(file.type)) {
        alert(`Invalid file type: ${file.name}`);
        return;
      }

      if (file.size > maxSizeMB * 1024 * 1024) {
        alert(`File too large: ${file.name}`);
        return;
      }

      currentFiles.push(file); // track only valid files

      const reader = new FileReader();
      reader.onload = function (e) {
        const imgBox = $(`
          <div class="image-box d-flex justify-content-between align-items-center border radius-xl" data-name="${file.name}" data-size="${file.size}">
            <img src="${e.target.result}" alt="Image Preview">
            <button class="remove-btn d-center"><i class="iconsax" icon-name="x"></i></button>
          </div>
        `);
        $('#imagePreview').append(imgBox);
      };
      reader.readAsDataURL(file);
    });

    this.value = ''; // Clear the file input to allow re-upload of same files
  });

  $('#imagePreview').on('click', '.remove-btn', function () {
    const box = $(this).closest('.image-box');
    const name = box.data('name');
    const size = box.data('size');

    // Remove file from tracking array
    currentFiles = currentFiles.filter(file => !(file.name === name && file.size === size));

    box.remove();
  });
});


</script>
@endsection
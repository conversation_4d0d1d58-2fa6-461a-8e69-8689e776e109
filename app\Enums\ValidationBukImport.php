<?php
    namespace App\Enums;

    enum ValidationBukImport: int{
        case EmailTaken = 1;
        case UserCompanyNotSaved = 2;
        case UserNotSaved = 3;
        case NoServiceProviderAdded = 4;
        case UserNotUpdated = 5;
        case PriorityNotSaved = 6;
        case PriorityNotUpdated = 7;
        case ServiceNotSaved = 8;
        case ServiceNotUpdated = 9;
        case PropertyNotExist = 10;
        case BuildingCountIssue = 11;
        case PropertyNotSaved = 12;
        case PropertyNotUpdated = 13;
        case PropertyBuildingNotSaved = 14;
        case RoomTypeNotSaved = 15;
        case RoomTypeFloorNotSaved = 16;
        case RoomTypeFloorNotUpdated = 17;
        case RoomsNotUpdated = 18;
        case PropertyBuildingNotExist = 19;
        case ServiceNotExist = 20;
        case NoQRCodeGenerated = 21;
        case RoomTypeFloorNotExist = 22;
        case AssetNameNotSaved = 23;
        case AssetNameServiceNotSaved = 24;
        case AssetNotSaved = 25;
        case AssetHistoryNotSaved = 26;
        case AssetNotUpdated = 27;
        case UserNotAffected = 28;
        case POEFound = 29;
    }
?>
<div>
  
        <div class="contents crm">
           <div class="container-fluid">
            <div class="col-lg-12">
    <div class="row justify-content-sm-between align-items-center justify-content-center my-3 flex-sm-row flex-column">
        <div class="page-title-wrap p-0">
            <div class="page-title d-flex justify-content-between">
                <div class="page-title__left justify-content-sm-between align-items-center justify-content-center">
                    <div class="user-member__title mr-sm-25 ml-0">
                        <h4 class="text-capitalize fw-500 breadcrumb-title fs-16">
                            @lang('accounting.customer_details.title')
                        </h4>

                    </div>
                </div>
            </div>
            <div>
                <ul class="atbd-breadcrumb nav">
                    <li class="atbd-breadcrumb__item">
                        <a>@lang('accounting.customer_details.breadcrumb_dashboard')</a>
                        <span class="breadcrumb__seperator">
                            <span class="la la-angle-right"></span>
                        </span>
                    </li>
                    <li class="atbd-breadcrumb__item">
                        <a>@lang('accounting.customer_details.breadcrumb_customer')</a>
                    </li>
                </ul>
            </div>
        </div>



        <div class="d-flex gap-10 breadcrumb_right_icons">
            <div class="d-flex gap-10 breadcrumb_right_icons">
                <div class="dropdown">
                    <button class="btn btn-default btn-primary w-100 no-wrap" type="button" aria-expanded="false" data-toggle="dropdown" aria-expanded="false"><i class="las la-plus fs-16"></i>@lang('accounting.customer_details.create')</button>
                      <div class="dropdown-menu">
                        <a class="dropdown-item" href="{{url('finance/invoice/create')}}">@lang('accounting.customer_details.create_invoice')</a>
                        <a class="dropdown-item" href="{{route('finance.proposal.create')}}">@lang('accounting.customer_details.create_proposal')</a>
                        <a class="dropdown-item" href="#">@lang('accounting.customer_details.create_retainer')</a>
                    </div>

                </div>
            </div>
        </div>
        <!--====End Design for Export PDF===-->
    </div>
</div>

<div class="d-flex justify-content-end mb-3">
        <ul class="nav nav-tabs site-pills bg-white p-1 rounded mb-3 mb-sm-0" id="pills-tab" role="tablist">
             
            <li class="nav-item" role="presentation">
                <button class="nav-link rounded {{ $activeTab === 'details' ? 'active' : '' }}" 
                    type="button" wire:click="$set('activeTab', 'details')">
                    @lang('accounting.customer_details.tabs.details')
                </button>
            </li>

            <li class="nav-item" role="presentation">
                <button class="nav-link rounded {{ $activeTab === 'proposals' ? 'active' : '' }}" 
                    type="button" wire:click="$set('activeTab', 'proposals')">
                    @lang('accounting.customer_details.tabs.proposals')
                </button>
            </li>

            <li class="nav-item" role="presentation">
                <button class="nav-link rounded {{ $activeTab === 'invoices' ? 'active' : '' }}" 
                    type="button" wire:click="$set('activeTab', 'invoices')">
                    @lang('accounting.customer_details.tabs.invoices')
                </button>
            </li>

            <li class="nav-item" role="presentation">
                <button class="nav-link rounded {{ $activeTab === 'retainer' ? 'active' : '' }}" 
                    type="button" wire:click="$set('activeTab', 'retainer')">
                    @lang('accounting.customer_details.tabs.retainer')
                </button>
            </li>

            <li class="nav-item" role="presentation">
                <button class="nav-link rounded {{ $activeTab === 'revenue' ? 'active' : '' }}" 
                    type="button" wire:click="$set('activeTab', 'revenue')">
                    @lang('accounting.customer_details.tabs.revenue')
                </button>
            </li>

            <li class="nav-item" role="presentation">
                <button class="nav-link rounded {{ $activeTab === 'project' ? 'active' : '' }}" 
                    type="button" wire:click="$set('activeTab', 'project')">
                    @lang('accounting.customer_details.tabs.project')
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link rounded {{ $activeTab === 'statement' ? 'active' : '' }}" 
                    type="button" wire:click="$set('activeTab', 'statement')">
                    @lang('accounting.customer_details.tabs.statement')
                </button>
            </li>

         
        
        </ul>
</div>
<div class="">

<div class="tab-content">
    @if ($activeTab === 'details')
        @include('livewire.accounting.customers.include.detail')
    @elseif ($activeTab === 'proposals')
        @livewire('accounting.customers.customer-proposal', ['customerId' => $customerId, 'showFullView' => false])
    @elseif ($activeTab === 'invoices')
        @livewire('accounting.invoice.invoice', ['showFullView' => false,'searchCustomer' => $customerId])
    @elseif ($activeTab === 'retainer')
        @include('livewire.accounting.customers.include.retainer')
    @elseif ($activeTab === 'revenue')
        @livewire('accounting.finance-revenue', ['showFullView' => false,'searchCustomer' => $customerId])
    @elseif ($activeTab === 'project')
        @livewire('accounting.customer-project-listing', ['customerId' => $customerId, 'showFullView' => false])
    @elseif ($activeTab === 'statement')
        @livewire('accounting.customers.components.customer-statement', ['customerId' => $customerId, 'showFullView' => false])
    @endif
</div>



</div>

           </div>
        </div>


</div>

@extends('layouts.app')

@section('styles')
<style>
    .view-toggle-btn.active {
        background-color: #007bff !important;
        color: white !important;
    }
    
    .new-shadow {
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        transition: box-shadow 0.3s ease;
    }
    
    .new-shadow:hover {
        box-shadow: 0 4px 16px rgba(0,0,0,0.15);
    }
    
    .avatar-colors {
        background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
    }
</style>
@endsection

@section('content')
@livewire('rfx.rfx-edit', ['rfxId' => decrypt($id)])
@endsection

@section('scripts')
<script type="text/javascript">
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize select2 if needed
        if (typeof $.fn.select2 !== 'undefined') {
            $('.select2-new').select2({
                minimumResultsForSearch: Infinity
            });
        }
        
        // Handle toast notifications
        window.addEventListener('show-toast', event => {
            if (typeof toastr !== 'undefined') {
                toastr[event.detail.type](event.detail.message);
            } else {
                alert(event.detail.message);
            }
        });
    });
    
    // Livewire hooks
    document.addEventListener('livewire:load', function () {
        // Any additional initialization when Livewire loads
    });
    
    document.addEventListener('livewire:update', function () {
        // Reinitialize components after Livewire updates
        if (typeof $.fn.select2 !== 'undefined') {
            $('.select2-new').select2({
                minimumResultsForSearch: Infinity
            });
        }
    });
</script>
@endsection

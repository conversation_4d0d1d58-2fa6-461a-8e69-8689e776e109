<div>
    @if($showFullView)
    <div class="contents crm">
        <div class="container-fluid">
            <div class="col-lg-12">
                <div class="row justify-content-sm-between align-items-center justify-content-center my-3 flex-sm-row flex-column">
                    <div class="page-title-wrap p-0">
                        <div class="page-title d-flex justify-content-between">
                            <div class="page-title__left justify-content-sm-between align-items-center justify-content-center">
                                <div class="user-member__title mr-sm-25 ml-0">
                                    <h4 class="text-capitalize fw-500 breadcrumb-title fs-16">
                                        {{ __('vendors.statement.vendor_statement') }}
                                    </h4>
                                </div>
                            </div>
                        </div>
                        <div>
                            <ul class="atbd-breadcrumb nav">
                                <li class="atbd-breadcrumb__item">
                                    <a href="{{ route('admin.dashboard') }}">{{ __('vendors.common.dashboard') }}</a>
                                    <span class="breadcrumb__seperator">
                                        <span class="la la-angle-right"></span>
                                    </span>
                                </li>
                                <li class="atbd-breadcrumb__item">
                                    <a href="{{ route('finance.vendors') }}">{{ __('vendors.common.vendors') }}</a>
                                    <span class="breadcrumb__seperator">
                                        <span class="la la-angle-right"></span>
                                    </span>
                                </li>
                                <li class="atbd-breadcrumb__item">
                                    <a>{{ __('vendors.statement.statement') }}</a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endif

    <div class="checkout pt-2 pb-20 mb-30 w-100">
        <div class="row">
            {{-- Date Filter Sidebar --}}
            <div class="col-lg-3 pr-md-0 mb-3 mb-md-0">
                <div class="card p-3 radius-xl">
                    <form class="fs-14" wire:submit.prevent="applyFilters">
                        <div class="form-group">
                            <label for="fromDate">{{ __('vendors.statement.from_date') }}</label>
                            <div class="position-relative">
                                <input type="date" class="form-control" id="fromDate" wire:model="fromDate" placeholder="{{ __('vendors.statement.enter_start_date') }}">
                                <i class="iconsax field-icon text-light" icon-name="calendar-1"></i>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="toDate">{{ __('vendors.statement.to_date') }}</label>
                            <div class="position-relative">
                                <input type="date" class="form-control" id="toDate" wire:model="toDate" placeholder="{{ __('vendors.statement.enter_end_date') }}">
                                <i class="iconsax field-icon text-light" icon-name="calendar-1"></i>
                            </div>
                        </div>
                        <div class="d-flex gap-10 mt-4 justify-content-end">
                            <button type="button" wire:click="resetFilters" class="btn bg-warning btn-sm text-white radius-xl">{{ __('vendors.statement.reset') }}</button>
                            <button type="submit" class="btn bg-new-primary btn-sm text-white radius-xl">{{ __('vendors.statement.submit') }}</button>
                        </div>
                    </form>
                </div>
            </div>

            {{-- Statement Content --}}
            <div class="col-lg-9 fs-14">
                <div class="card radius-xl" id="overview-section">
                    {{-- Header --}}
                    <div class="card-header py-3">
                        <div class="">
                            <img src="https://workdo-dev.osool.cloud/uploads/logo/logo_dark.png" alt="Work Do" class="logo logo-lg" style="max-width: 250px">
                        </div>
                        <div class="">
                            <strong class="mb-2">{{ __('vendors.statement.my_company') }}</strong><br>
                            <span class="invoice-number fs-14">{{ config('app.name', 'Company') }}</span>
                        </div>
                    </div>

                    {{-- Statement Header --}}
                    <div class="card-header">
                        <h5 class="mb-sm-0 mb-3">{{ __('vendors.statement.statement_of_account') }}</h5>
                        <div class="d-flex flex-wrap align-items-center">
                            <span class="mr-3">{{ $fromDate }} {{ __('vendors.statement.to') }} {{ $toDate }}</span>
                            <button wire:click="downloadStatement" class="btn btn-xs bg-loss text-white">
                                <i class="iconsax" icon-name="download-1"></i> {{ __('vendors.statement.download') }}
                            </button>
                        </div>
                    </div>

                    {{-- Loading State --}}
                    @if($loading)
                        <div class="text-center py-5">
                            <div class="spinner-border text-primary" role="status">
                                <span class="sr-only">{{ __('vendors.common.loading') }}</span>
                            </div>
                        </div>
                    @elseif($error)
                        <div class="text-center py-5">
                            <i class="iconsax icon fs-48 text-danger mb-3" icon-name="info-circle"></i>
                            <h6 class="text-danger">{{ $error }}</h6>
                            <button wire:click="fetchStatements" class="btn btn-primary mt-3">
                                {{ __('vendors.buttons.retry') }}
                            </button>
                        </div>
                    @else
                        {{-- Address Information --}}
                        <div class="p-4">
                            <div class="row">
                                <div class="col-md-4 mb-sm-0 mb-3 pr-md-0">
                                    <div class="p-3 address-box radius-xl h-100">
                                        <h6 class="text-dark mb-3 fs-14">{{ __('vendors.statement.billed_to') }}</h6>
                                        <table class="table mb-0 table-borderless new-header td-pl-0 table-address">
                                            <tbody>
                                                <tr>
                                                    <td><span class="fs-12 fw-50 text-dark">{{ __('vendors.common.billing_address') }}</span></td>
                                                    <td>{{ $vendorDetails['billing_address'] ?? '-' }}</td>
                                                </tr>
                                                <tr>
                                                    <td><span class="fs-12 fw-50 text-dark">{{ __('vendors.common.billing_city') }}</span></td>
                                                    <td>{{ $vendorDetails['billing_city'] ?? '-' }}</td>
                                                </tr>
                                                <tr>
                                                    <td><span class="fs-12 fw-50 text-dark">{{ __('vendors.common.zip_code') }}</span></td>
                                                    <td>{{ $vendorDetails['billing_zip'] ?? '-' }}</td>
                                                </tr>
                                                <tr>
                                                    <td><span class="fs-12 fw-50 text-dark">{{ __('vendors.common.billing_country') }}</span></td>
                                                    <td>{{ $vendorDetails['billing_country'] ?? '-' }}</td>
                                                </tr>
                                                <tr>
                                                    <td><span class="fs-12 fw-50 text-dark">{{ __('vendors.common.billing_contact') }}</span></td>
                                                    <td>{{ $vendorDetails['billing_phone'] ?? '-' }}</td>
                                                </tr>
                                                <tr>
                                                    <td><strong>{{ __('vendors.statement.tax_number') }} :</strong></td>
                                                    <td>{{ $vendorDetails['tax_number'] ?? '-' }}</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                                <div class="col-md-4 mb-sm-0 mb-3 pr-md-0">
                                    <div class="p-3 address-box radius-xl h-100">
                                        <h6 class="text-dark mb-3 fs-14">{{ __('vendors.statement.shipped_to') }}</h6>
                                        <table class="table mb-0 table-borderless new-header td-pl-0 table-address">
                                            <tbody>
                                                <tr>
                                                    <td><span class="fs-12 fw-50 text-dark">{{ __('vendors.common.shipping_address') }}</span></td>
                                                    <td>{{ $vendorDetails['shipping_address'] ?? '-' }}</td>
                                                </tr>
                                                <tr>
                                                    <td><span class="fs-12 fw-50 text-dark">{{ __('vendors.common.shipping_city') }}</span></td>
                                                    <td>{{ $vendorDetails['shipping_city'] ?? '-' }}</td>
                                                </tr>
                                                <tr>
                                                    <td><span class="fs-12 fw-50 text-dark">{{ __('vendors.common.zip_code') }}</span></td>
                                                    <td>{{ $vendorDetails['shipping_zip'] ?? '-' }}</td>
                                                </tr>
                                                <tr>
                                                    <td><span class="fs-12 fw-50 text-dark">{{ __('vendors.common.shipping_country') }}</span></td>
                                                    <td>{{ $vendorDetails['shipping_country'] ?? '-' }}</td>
                                                </tr>
                                                <tr>
                                                    <td><span class="fs-12 fw-50 text-dark">{{ __('vendors.common.shipping_contact') }}</span></td>
                                                    <td>{{ $vendorDetails['shipping_phone'] ?? '-' }}</td>
                                                </tr>
                                                <tr>
                                                    <td><strong>{{ __('vendors.statement.tax_number') }} :</strong></td>
                                                    <td>{{ $vendorDetails['tax_number'] ?? '-' }}</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="p-3 address-box radius-xl h-100">
                                        <h6 class="text-dark mb-3 fs-14">{{ __('vendors.statement.account_summary') }}</h6>
                                        <table class="table mb-0 table-borderless new-header no-wrap tp-0" id="content_table">
                                            <tbody>
                                                <tr>
                                                    <td><span class="fs-12 fw-50 text-dark">{{ __('vendors.statement.balance') }} :</span></td>
                                                    <td><span>{{ number_format($accountSummary['balance'] ?? 0, 2) }} {{ __('vendors.statement.currency') }}</span></td>
                                                </tr>
                                                <tr>
                                                    <td><span class="fs-12 fw-50 text-dark">{{ __('vendors.statement.invoiced_amount') }} :</span></td>
                                                    <td><span>{{ number_format($accountSummary['invoiced_amount'] ?? 0, 2) }} {{ __('vendors.statement.currency') }}</span></td>
                                                </tr>
                                                <tr>
                                                    <td><span class="fs-12 fw-50 text-dark">{{ __('vendors.statement.amount_paid') }} :</span></td>
                                                    <td><span>{{ number_format($accountSummary['amount_paid'] ?? 0, 2) }} {{ __('vendors.statement.currency') }}</span></td>
                                                </tr>
                                                <tr>
                                                    <td><span class="fs-12 fw-50 text-dark">{{ __('vendors.statement.balance_due') }} :</span></td>
                                                    <td><span>{{ number_format($accountSummary['balance_due'] ?? 0, 2) }} {{ __('vendors.statement.currency') }}</span></td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {{-- Statement Items Table --}}
                        <div class="card-body px-0 mx-4 address-box radius-xl pb-0 mb-5 overflow-hidden">
                            <h6 class="text-dark ml-4 mb-4">{{ __('vendors.statement.item_list') }}</h6>
                            <div class="userDatatable projectDatatable project-table bg-white w-100 border-0">
                                <div class="table-responsive">
                                    <table class="table mb-0 table-borderless new-header" id="content_table">
                                        <thead>
                                            <tr class="userDatatable-header">
                                                <th wire:click="sortBy('date')" style="cursor: pointer;">
                                                    <span class="projectDatatable-title">{{ __('vendors.statement.date') }}</span>
                                                    @if($sortField === 'date')
                                                        <i class="las la-sort-{{ $sortDirection === 'asc' ? 'up' : 'down' }}"></i>
                                                    @endif
                                                </th>
                                                <th wire:click="sortBy('invoice')" style="cursor: pointer;">
                                                    <span class="projectDatatable-title">{{ __('vendors.statement.invoice') }}</span>
                                                    @if($sortField === 'invoice')
                                                        <i class="las la-sort-{{ $sortDirection === 'asc' ? 'up' : 'down' }}"></i>
                                                    @endif
                                                </th>
                                                <th wire:click="sortBy('payment')" style="cursor: pointer;">
                                                    <span class="projectDatatable-title">{{ __('vendors.statement.payment') }}</span>
                                                    @if($sortField === 'payment')
                                                        <i class="las la-sort-{{ $sortDirection === 'asc' ? 'up' : 'down' }}"></i>
                                                    @endif
                                                </th>
                                                <th wire:click="sortBy('amount')" style="cursor: pointer;">
                                                    <div class="">
                                                        <span class="projectDatatable-title">{{ __('vendors.statement.amount') }}</span>
                                                        @if($sortField === 'amount')
                                                            <i class="las la-sort-{{ $sortDirection === 'asc' ? 'up' : 'down' }}"></i>
                                                        @endif
                                                    </div>
                                                </th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @forelse($statements as $statement)
                                            <tr>
                                                <td>{{ isset($statement['date']) ? date('Y-m-d', strtotime($statement['date'])) : '-' }}</td>
                                                <td>
                                                    @if(isset($statement['invoice_number']))
                                                        <a href="{{ $statement['invoice_url'] ?? '#' }}">
                                                            <span class="fw-400 badge badge-round badge-primary badge-lg badge-outlined">
                                                                {{ $statement['invoice_number'] }}
                                                            </span>
                                                        </a>
                                                    @else
                                                        -
                                                    @endif
                                                </td>
                                                <td>{{ $statement['payment_method'] ?? '-' }}</td>
                                                <td>
                                                    <div class="d-flex align-items-center gap-10">
                                                        <img src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSx34eO8RbZlsetN1iAQtxuOLnLOlyJbJmU7Q&s" width="15">
                                                        <span class="text-new-primary">{{ number_format($statement['amount'] ?? 0, 2) }}</span>
                                                    </div>
                                                </td>
                                            </tr>
                                            @empty
                                            <tr>
                                                <td colspan="4" class="text-center py-5">
                                                    <div class="d-flex flex-column align-items-center">
                                                        <i class="iconsax icon fs-48 text-muted mb-3" icon-name="folder-2"></i>
                                                        <h6 class="text-muted">{{ __('vendors.statement.no_statements_found') }}</h6>
                                                    </div>
                                                </td>
                                            </tr>
                                            @endforelse
                                        </tbody>
                                        @if($statements->count() > 0)
                                        <tfooter>
                                            <tr>
                                                <td colspan="2"></td>
                                                <td><h6>{{ __('vendors.statement.total') }}</h6></td>
                                                <td>
                                                    <div class="d-flex align-items-center gap-10">
                                                        <img src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSx34eO8RbZlsetN1iAQtxuOLnLOlyJbJmU7Q&s" width="15">
                                                        <span class="text-new-primary">{{ number_format($accountSummary['total_amount'] ?? 0, 2) }}</span>
                                                    </div>
                                                </td>
                                            </tr>
                                        </tfooter>
                                        @endif
                                    </table>
                                </div>
                            </div>
                        </div>

                        {{-- Pagination --}}
                        @if($total > 0)
                        <div class="card-body pt-0">
                            <div class="d-flex justify-content-md-between flex-column flex-md-row justify-content-center align-items-center mt-4 gap-10">
                                <div class="">
                                    <ul class="atbd-pagination d-flex justify-content-between">
                                        <li>
                                            <div class="paging-option">
                                                <div class="dataTables_length d-flex">
                                                    <label class="d-flex align-items-center mb-0">
                                                        <select wire:model="perPage" class="custom-select custom-select-sm form-control form-control-sm mx-2" style="min-height: 35px;">
                                                            <option value="5">5</option>
                                                            <option value="10">10</option>
                                                            <option value="25">25</option>
                                                            <option value="50">50</option>
                                                            <option value="100">100</option>
                                                        </select>
                                                        <span class="no-wrap"> {{ __('vendors.pagination.entries_per_page') }} </span>
                                                    </label>
                                                </div>
                                            </div>
                                        </li>
                                    </ul>
                                </div>

                                <div class="">
                                    <div class="user-pagination">
                                        <div class="user-pagination new-pagination">
                                            <div class="d-flex justify-content-sm-end justify-content-end">
                                                <nav>
                                                    <span class="relative z-0 inline-flex rounded-md d-flex new-pagination-section flex-wrap">
                                                        @if($pagination->current_page > 1)
                                                            <span>
                                                                <button type="button" class="border-0" wire:click="previousPage">
                                                                    <i class="iconsax icon-bold fs-16 mr-0 rotate-ar-y d-inline-block" icon-name="chevron-left"></i>
                                                                </button>
                                                            </span>
                                                        @else
                                                            <span>
                                                                <button class="border-0 disabled" disabled>
                                                                    <i class="iconsax icon-bold fs-16 mr-0 rotate-ar-y d-inline-block" icon-name="chevron-left"></i>
                                                                </button>
                                                            </span>
                                                        @endif

                                                        @php
                                                            $start = max(1, $pagination->current_page - 2);
                                                            $end = min($pagination->last_page, $pagination->current_page + 2);
                                                        @endphp

                                                        @if($start > 1)
                                                            <span>
                                                                <button type="button" class="border-0" wire:click="gotoPage(1)">1</button>
                                                            </span>
                                                            @if($start > 2)
                                                                <span>
                                                                    <button class="border-0 disabled" disabled>...</button>
                                                                </span>
                                                            @endif
                                                        @endif

                                                        @for($i = $start; $i <= $end; $i++)
                                                            @if($i == $pagination->current_page)
                                                                <span>
                                                                    <button class="border-0 current-page" disabled>{{ $i }}</button>
                                                                </span>
                                                            @else
                                                                <span>
                                                                    <button type="button" class="border-0" wire:click="gotoPage({{ $i }})">{{ $i }}</button>
                                                                </span>
                                                            @endif
                                                        @endfor

                                                        @if($end < $pagination->last_page)
                                                            @if($end < $pagination->last_page - 1)
                                                                <span>
                                                                    <button class="border-0 disabled" disabled>...</button>
                                                                </span>
                                                            @endif
                                                            <span>
                                                                <button type="button" class="border-0" wire:click="gotoPage({{ $pagination->last_page }})">{{ $pagination->last_page }}</button>
                                                            </span>
                                                        @endif

                                                        @if($pagination->current_page < $pagination->last_page)
                                                            <span>
                                                                <button type="button" class="border-0" wire:click="nextPage">
                                                                    <i class="iconsax icon-bold fs-16 mr-0 rotate-ar-y d-inline-block" icon-name="chevron-right"></i>
                                                                </button>
                                                            </span>
                                                        @else
                                                            <span>
                                                                <button class="border-0 disabled" disabled>
                                                                    <i class="iconsax icon-bold fs-16 mr-0 rotate-ar-y d-inline-block" icon-name="chevron-right"></i>
                                                                </button>
                                                            </span>
                                                        @endif
                                                    </span>
                                                </nav>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div>
                                    <p class="text-sm text-gray-700 leading-5 mb-0">
                                        <span>{{ __('vendors.pagination.showing') }}</span>
                                        <span class="font-medium">{{ ($pagination->current_page - 1) * $pagination->per_page + 1 }}</span>
                                        <span>{{ __('vendors.pagination.to') }}</span>
                                        <span class="font-medium">{{ min($pagination->current_page * $pagination->per_page, $pagination->total) }}</span>
                                        <span>{{ __('vendors.pagination.of') }}</span>
                                        <span class="font-medium">{{ $pagination->total }}</span>
                                        <span>{{ __('vendors.pagination.results') }}</span>
                                    </p>
                                </div>
                            </div>
                        </div>
                        @endif
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
    // Toast notification handler
    window.addEventListener('show-toastr', event => {
        toastr.options = {
            "closeButton": true,
            "progressBar": true,
            "positionClass": "toast-top-right",
            "timeOut": "3000"
        };

        if (event.detail.type === 'success') {
            toastr.success(event.detail.message);
        } else if (event.detail.type === 'error') {
            toastr.error(event.detail.message);
        } else if (event.detail.type === 'info') {
            toastr.info(event.detail.message);
        }
    });

    // Download handler
    window.addEventListener('download-start', event => {
        // Handle download start if needed
    });
</script>
@endpush

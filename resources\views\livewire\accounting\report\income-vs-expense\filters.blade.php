<div>
    <div class="card mb-3">
        <div class="card-body">
            <form class="fs-14">
                <div class="d-flex flex-wrap gap-10">
                    <div class="flex-fill max-w-150">
                        <label for="" class="text-osool">@lang('accounting.year')</label>
                        <select class="form-control" wire:model='year' data-allow-clear="false">
                            @foreach ($years as $item)
                                <option value="{{ $item }}">{{ $item }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="flex-fill max-w-180">
                        <label for="" class="text-osool">@lang('accounting.customer')</label>
                        <select class="form-control" wire:model='customer'>
                            <option value=""></option>
                            @foreach ($customers as $item)
                                <option value="{{ $item['id'] }}">{{ $item['name'] }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="flex-fill max-w-180">
                        <label for="" class="text-osool">@lang('accounting.vendor')</label>
                        <select class="form-control" wire:model='vendor'>
                            <option value=""></option>
                            @foreach ($vendors as $item)
                                <option value="{{ $item['id'] }}">{{ $item['name'] }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="flex-fill max-w-150">
                        <label for="" class="text-osool">@lang('accounting.category')</label>
                        <select class="form-control" wire:model='category'>
                            <option value=""></option>
                            @foreach ($categories as $item)
                                <option value="{{ $item['id'] }}">{{ $item['name'] }}</option>
                            @endforeach
                        </select>
                    </div>

                    <div class="flex-fill">
                        <label for="" class="d-md-block d-none">&nbsp;</label>
                        <div class="d-flex gap-10">
                            <button data-toggle="tooltip" data-placement="top" title="@lang('accounting.apply')"
                                onclick="showLoader()" wire:click.prevent='filter' type="button"
                                class="btn bg-opacity-new-primary btn-sm text-new-primary radius-md px-5">
                                @lang('accounting.apply')
                            </button>
                            <button data-toggle="tooltip" data-placement="top" title="@lang('accounting.reset')"
                                onclick="showLoader()" wire:click.prevent='resetFilter' type="button"
                                class="btn bg-opacity-loss btn-sm text-loss wh-45 radius-md">
                                <i class="iconsax mr-0 fs-18" icon-name="rotate-left"></i>
                                <!-- Reset -->
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <div class="row mt-3">
        <div class="col-sm-6">
            <div class="card p-4">
                <h5 class="text-dark mb-0">@lang('accounting.report') :</h5>
                <p class="mb-0">@lang('accounting.income_vs_expense_sum')</p>
            </div>
        </div>
        <div class="col-sm-6">
            <div class="card p-4 mb-sm-0 mb-3">
                <h5 class="report-text gray-text mb-0">@lang('accounting.duration') :</h5>
                <p class="mb-0">Jan-{{ $year }} to Dec-{{ $year }}</p>
            </div>
        </div>
    </div>
</div>

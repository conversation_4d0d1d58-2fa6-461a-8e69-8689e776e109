<?php
    namespace App\Http\Traits;
    use App\Models\Contracts;
    use App\Models\WorkerContractMapping;
    use Illuminate\Support\Facades\Log;
    use App\Http\Helpers\ReportQueryHelper;
    use App\Http\Traits\FunctionsTrait;
    use App\Http\Traits\PriorityTrait;
    use App\Http\Traits\TempBulkImportTrait;
    use App\Http\Traits\ConfigurationTrait;
    use App\Http\Traits\ProjectCompanyTrait;
    use App\Http\Traits\UserCompanyTrait;
    use App\Models\User;
    use App\Enums\ModelAction;
    use App\Enums\ConfigurationCode;

    trait UserTrait{
        use FunctionsTrait, PriorityTrait, TempBulkImportTrait, ConfigurationTrait, ProjectCompanyTrait, UserCompanyTrait;

        public function saveUser($array) {
            try {
                return User::insertGetId($array);
            } 
            
            catch (\Throwable $th) {
                Log::error("saveUser error: ".$th);
            }
        }

        public function getUserInformationsById($user_id) {

            try {
                return User::where('id', '=', $user_id)->withTrashed()->first();
            } 
            
            catch (\Throwable $th) {
                Log::error("getUserInformationsById Error: ".$th);
            }
        }
        
        public function getSpeceficUserByValues($projectUserId, $key, $value) {
            try {
                return User::where('project_user_id', $projectUserId) 
                ->where($key, $value)        
                ->where('is_deleted', 'no')                          
                ->first();
            } 
            
            catch (\Throwable $th) {
                Log::error("getSpeceficUserByValues Error: ".$th);
            }
        }

        public function manageEntredUser($email, $projectUserId) {
            try {
                $user = $this->getSpeceficUserByValues($projectUserId, 'email', $email);

                if(isset($user)){
                    return ModelAction::Update->value;
                }

                else{
                    return ModelAction::Insert->value;  
                }
            } 
            
            catch (\Throwable $th) {
                Log::error("manageEntredUser error: ".$th);
            }
        }

        public function updateUserByValues($id, $array) {
            try {
                return User::where('id', $id)->update($array);
            } 
            
            catch (\Throwable $th) {
                Log::error("updateUserByValues error: ".$th);
            }
        }

        public function getUserByvalues($projectId) {
            try {
                return User::where('project_id', $projectId)
                    ->where('user_type', 'admin')
                    ->where('status', 1)
                    ->select('id', 'created_at')
                    ->first();
            } 
            
            catch (\Throwable $th) {
                Log::error("getUserByvalues Error: ".$th);
            }
        }

        public function manageGetUser($projectId, $userType) {
            try {
                if(in_array($userType, array('super_admin', 'osool_admin', 'admin', 'admin_employee'))){
                    $data = $this->getUserByvalues($projectId);

                    if(!is_null($data)){
                        return $data->id;
                    }

                    else{
                        return 0;
                    }
                }

                else{
                    return 1;
                }
            } 
            
            catch (\Throwable $th) {
                Log::error("manageGetUser Error: ".$th);
            }
        }

        public function getUserInformationsByUserId($userId) { 
            try {
                return User::where('id', $userId)
                ->where('status', 1)
                ->where('is_deleted', 'no')
                ->select('building_ids', 'project_id')
                ->first();
            } 
            
            catch (\Throwable $th) {
                Log::error("getUserInformationsByUserId Error: ".$th);
            }
        }

        public function deleteUserByValues($key, $value) {
            try {
                return User::whereIn($key, $value)
                ->delete();
            } 
            
            catch (\Throwable $th) {
                Log::error("deleteUserByValues error: ".$th);
            }
        }

        public function getUserServiceProviderListByValues($key, $value) {
            try {
                return User::where($key, $value)
                ->select('service_provider')
                ->where('user_type', 'sp_admin' )
                ->where('is_deleted','no')
                ->where('status', 1)
                ->get();
            } 
            
            catch (\Throwable $th) {
                Log::error("getUserServiceProviderListByValues error: ".$th);
            }
        }

        public function getSupervisors($loggedInUser, $serviceProviderId = null, $search) {
            try {
                $supervisors = null;

                if(!is_array($serviceProviderId)){
                    $serviceProviderId = $this->explodeDataFromField($serviceProviderId);
                }

                if ($loggedInUser->isBuildingManager() || $loggedInUser->isBuildingManagerEmployee()) {
                    $newBuildingsId = $this->explodeDataFromField($loggedInUser->building_ids);
                    $supervisors = $this->getSupervisorsForBuildingManager($newBuildingsId, $search);
                }

                elseif ($loggedInUser->isServiceProviderAdmin()) {
                    $supervisors = $this->getSupervisorsForSuperProviderAdmin($loggedInUser, $search);
                }

                else{
                    $supervisors = $this->getSupervisorsForRest($serviceProviderId, $search);
                }

                return $supervisors;
            } 
            
            catch (\Throwable $th) {
                Log::error("getSupervisors error: ".$th);
            }
        }

        public function getSupervisorsForBuildingManager($buildingIds, $search) {
            try {
                $supervisorsData = User::select('users.id', 'users.name', 'users.sp_admin_id', 'spu.id as sp_id', 'spu.name as sp_name')
                ->join('users as spu', 'spu.id', '=', 'users.sp_admin_id')
                ->where('users.user_type', 'supervisor')
                ->when(!empty($search), function ($query) use ($search) {
                    $query->where('users.name', 'LIKE', '%' . $search . '%');
                })
                ->where(function ($query) use ($buildingIds) {
                    foreach ($buildingIds as $buildingId) {
                        $query->orWhereRaw("FIND_IN_SET($buildingId, users.building_ids)");
                    }
                })
                ->get();

                $supervisors = [];
                $spAdmins = [];

                foreach ($supervisorsData as $supervisor) {
                    $supervisors[] = [
                        'id' => $supervisor['id'],
                        'name' => $supervisor['name']
                    ];

                    $spAdmins[] = [
                        'id' => $supervisor['sp_id'],
                        'name' => $supervisor['sp_name']
                    ];
                }
    
                $uniqueData = array_merge($supervisors, $spAdmins);
                return collect($uniqueData)->unique('id')->values()->all();
            } 
            
            catch (\Throwable $th) {
                Log::error("getSupervisorsForBuildingManager error: ".$th);
            }
        }  

        public function getSupervisorsForSuperProviderAdmin($loggedInUser, $search) {
            try {
                return $loggedInUser->supervisors()
                ->when(!empty($search), function ($query) use ($search) {
                    $query->where('name', 'LIKE', '%' . $search . '%');
                })
                ->where('user_type', 'supervisor')
                ->select('id', 'name')
                ->get()
                ->toArray();
            } 
            
            catch (\Throwable $th) {
                Log::error("getSupervisorsForSuperProviderAdmin error: ".$th);
            }
        }

        public function getSupervisorsForRest($serviceProviderId, $search) {
            try {
                return User::select('id', 'name')
                ->when(!empty($search), function ($query) use ($search) {
                    $query->where('name', 'LIKE', '%' . $search . '%');
                })
                ->whereIn('service_provider', $serviceProviderId)
                ->whereIn('user_type', ['supervisor', 'sp_admin'])
                ->get()
                ->toArray();
            } 
            
            catch (\Throwable $th) {
                Log::error("getSupervisorsForRest error: ".$th);
            }
        }

        public function getWorkersListArrayByValues($contractId, $userId, $serviceId) {
            try {
                return User::select('id')
                ->where('user_type', 'sp_worker')
                ->whereRaw("FIND_IN_SET(?, `contract_ids`) > 0", [$contractId])
                ->whereRaw("FIND_IN_SET(?, `asset_categories`) > 0", [$serviceId])
                ->where('id', '<>', $userId)
                ->where('is_deleted', 'no')
                ->whereNull('deleted_at')
                ->pluck('id')
                ->toArray();
            } 
            
            catch (\Throwable $th) {
                Log::error("getWorkersListArrayByValues error: ".$th);
            }
        }

        public function getUsersListData($users) {
            try {
                return User::select('id', 'created_at')
                ->where('user_type', 'sp_worker')
                ->whereIn('id', $users)
                ->where('is_deleted', 'no')
                ->orderBy('created_at', 'ASC')
                ->first();
            } 
            
            catch (\Throwable $th) {
                Log::error("getUsersListData error: ".$th);
            }
        }

        public function manageProjectId($userType, $projectId, $projectUserId) {
            try {
                if(!in_array($userType, array('super_admin', 'osool_admin'))){
                    if(!empty($projectId)){
                        return $projectId;
                    }

                    else{
                        $userData = $this->getUserInformationsByUserId($projectUserId);
                        $result = isset($userData) && !is_null($userData->project_id) ? $userData->project_id : 0;
                        return $result;
                    }
                }

                elseif(in_array($userType, array('super_admin', 'osool_admin'))){
                    $result = $this->hasDataSession('entered_project_id') ? $this->getDataSession('entered_project_id') : 0;
                    return $result;
                }
            } 
            
            catch (\Throwable $th) {
                Log::error("manageProjectId error: ".$th);
            }
        }

        public function getUserId($userType, $projectId) {
            try {
                $result = 1;

                if(in_array($userType, array('super_admin','osool_admin','admin','admin_employee','building_manager','building_manager_employee'))){
                    $userData = $this->getUserByvalues($projectId);
                    $result = isset($userData) ? $userData->id : 0;
                }

                return $result;
            } 
            
            catch (\Throwable $th) {
                Log::error("getUserId error: ".$th);
            }
        }

        public function getHiddenColumnsListForUser($user) {
            try {
                return $user->workOrderColumns()
                ->where('page','work-order-list')
                ->pluck('columns_name')
                ->unique()
                ->toArray();
            } 
            
            catch (\Throwable $th) {
                Log::error("getHiddenColumnsListForUser error: ".$th);
            }
        }

        public function getFiltredWorkersList($userType, $serviceProvider, $userId, $contracts, $projectUserId) {
            try {
                $query = User::query()->select('id', 'name')
                ->where('user_type', 'sp_worker')
                ->where('is_deleted', 'no')
                ->whereNull('deleted_at')
                ->where('status', 1);

                if($userType == "sp_admin"){
                    $query = $this->getWorkersListForSpAdmin($query, $serviceProvider);
                }

                elseif($userType == "supervisor"){
                    $query = $this->getWorkersListForSupervisor($query, $userId);
                }

                elseif($userType == "sp_worker"){
                    $query = $this->getWorkersListForWorker($query, $userId);
                }

                elseif(in_array($userType, array('building_manager_employee', 'building_manager'))){
                    $query = $this->getWorkersListForBuildingManager($query, $contracts);
                }

                else{
                    $query = $this->getWorkersListForOtherUsers($query, $projectUserId);
                }
        
                return $query->get()->toArray();
            } 
            
            catch (\Throwable $th) {
                Log::error("getFiltredWorkersList error: ".$th);
            }
        }

        public function getBuildingManagersList($user) {
            return User::select('users.id','users.name')->leftJoin('service_providers','service_providers.id','=','users.service_provider')
              ->whereRaw("( `service_providers`.`global_sp` = '0' or `service_providers`.`global_sp` IS NULL ) ")
              ->whereRaw("( `users`.`user_type` = 'building_manager')")
              ->where([['users.deleted_at',NULL],['users.is_deleted', 'no']])
              ->where('users.project_user_id', $user->project_user_id)
              ->orderBy('users.user_type')
              ->get();
        }

        public function getWorkersListForSpAdmin($query, $serviceProvider) {
            try {
                return $query->where('service_provider', $serviceProvider);
            } 
            
            catch (\Throwable $th) {
                Log::error("getWorkersListForSpAdmin error: ".$th);
            }
        }

        public function getWorkersListForSupervisor($query, $userId) {
            try {
                return $query->whereRaw("find_in_set(?, supervisor_id)", [$userId]);
            } 
            
            catch (\Throwable $th) {
                Log::error("getWorkersListForSupervisor error: ".$th);
            }
        }

        public function getWorkersListForWorker($query, $userId) {
            try {
                return $query->where('id', $userId);
            } 
            
            catch (\Throwable $th) {
                Log::error("getWorkersListForWorker error: ".$th);
            } 
        }

        public function getWorkersListForBuildingManager($query, $contracts) {
            try {
                return $query->whereIn('contract_ids', $contracts);
            } 
            
            catch (\Throwable $th) {
                Log::error("getWorkersListForBuildingManager error: ".$th);
            }
        }

        public function getWorkersListForOtherUsers($query, $projectUserId) {
            try {
                return $query->where('project_user_id', $projectUserId);
            } 
            
            catch (\Throwable $th) {
                Log::error("getWorkersListForOtherUsers error: ".$th);
            }
        }

        public function getProjectIdForWorkOrdersList($userType, $projectId, $projectUserId) {
            try {
                $result = 0;

                if(in_array($userType, array('super_admin', 'osool_admin'))){
                    $result = session()->has('entered_project_id') ? session()->get('entered_project_id') : 0;
                }

                else{
                    if(!empty($projectId)){
                        $result = $projectId;
                    }

                    else{
                        $user = $this->getUserInformationsByUserId($projectUserId);
                        $result = isset($user) && $user->project_id > 0 ? $user->project_id : 0;
                    }
                }

                return $result;
            } 
            
            catch (\Throwable $th) {
                Log::error("getProjectIdForWorkOrdersList error: ".$th);
            }
        }

        public function getPluckUserIdByValues($key, $value) {
            try {
                return User::where($key, $value)
                ->select('id')
                ->where('user_type', 'sp_admin' )
                ->where('is_deleted','no')
                ->where('status', 1)
                ->withTrashed()
                ->pluck('id');
            } 
            
            catch (\Throwable $th) {
                Log::error("getPluckUserIdByValues error: ".$th);
            }
        }

        public function getUserInformationsByValues($key, $value) {

            try {
                return User::where($key, $value)
                ->select('apartment', 'id', 'name')
                ->first();
            } 
            
            catch (\Throwable $th) {
                Log::error("getUserInformationsByValues error: ".$th);
            }
        }

        public function updateWorkerContracts($newContractIds,$workerId,$actionType)
        {
            if ($actionType == 'update') 
            {
                $oldContracts = WorkerContractMapping::where('worker_id', $workerId)
                ->whereNull('end_date') // Only consider active contracts
                ->pluck('contract_id')->toArray();
                $newContracts = array_map('trim', $newContractIds);

                $contractsToRemove = array_diff($oldContracts, $newContracts);
                $contractsToAdd = array_diff($newContracts, $oldContracts);

                // Mark removed contracts with end_date
                if (!empty($contractsToRemove)) {
                    WorkerContractMapping::where('worker_id', $workerId)
                        ->whereIn('contract_id', $contractsToRemove)
                        ->whereNull('end_date') // Ensure only active contracts are updated
                        ->update(['end_date' => now(),'updated_at' => now()]);
                }

                // Insert new contracts
                $insertData = [];
                foreach ($contractsToAdd as $contractId) {
                    $contract = Contracts::find($contractId);
                    if ($contract) {
                        $insertData[] = [
                            'worker_id' => $workerId,
                            'contract_id' => $contract->id,
                            'start_date' => now(),
                            'created_at' => now()
                        ];
                    }
                }

                if (!empty($insertData)) {
                    WorkerContractMapping::insert($insertData);
                }
            }
            else
            {
                WorkerContractMapping::where('worker_id', $workerId)->delete();
                    // Insert new contracts
                $insertData = [];
                foreach ($newContractIds as $contractId) {
                    $contract = Contracts::find($contractId);
                    if ($contract) {
                        $insertData[] = [
                            'worker_id' => $workerId,
                            'contract_id' => $contract->id,
                            'start_date' => now(),
                            'created_at' => now()
                        ];
                    }
                }

                if (!empty($insertData)) {
                    WorkerContractMapping::insert($insertData);
                }
            }
            
        }

        public function getUsersListByValues($key, $value, $perPage) {
            try {
                return User::with(['serviceProvider', 'adminEmployee', 'adminSupervisor', 'adminSupervisor'])
                ->select('id', 'name', 'email', 'phone', 'user_type', 'emp_dept', 'service_provider', 'sp_admin_id', 'supervisor_id')
                ->whereIn($key, $value)
                ->where('is_deleted', 'no')
                ->paginate($perPage, ['*'], 'page'); 
            } 
            
            catch (\Throwable $th) {
                Log::error("getUsersListByValues error: ".$th);
            }
        }

        public function getUsersListByUserType($userType, $projectId) {
            try {
                return User::select('id', 'name')
                ->where('user_type', $userType)
                ->where('project_id', $projectId) 
                ->where('is_deleted', 'no')
                ->get();
            } 
            
            catch (\Throwable $th) {
                Log::error("getUsersListByUserType error: ".$th);
            }
        }

        public function getSpListByValues($userType, $company, $status) {
            try {
                return User::select('id', 'name')
                ->where('user_type', $userType)
                ->where('service_provider', $company) 
                ->where('is_deleted', 'no')
                ->where('status', $status)
                ->get();
            } 
            
            catch (\Throwable $th) {
                Log::error("getSpListByValues error: ".$th);
            }
        }

        public function getCustodiansListByMultipleValues($key1, $value1, $key2, $value2) {
            try {
                return User::select('id', 'name', 'email', 'user_type', 'phone')
                ->whereIn($key1, $value1)
                ->where($key2, $value2)
                ->orderBy('user_type', 'ASC')
                ->get();
            } 
            
            catch (\Throwable $th) {
                Log::error("getCustodiansListByMultipleValues error: ".$th);
            }
        }

        public function checkExistUserByMultipleValues($key1, $value1, $key2, $value2) {
            try {
                return User::where($key1, $value1)
                ->where($key2, $value2)
                ->exists();
            } 
            
            catch (\Throwable $th) {
                Log::error("checkExistUserByMultipleValues error: ".$th);
            }
        }
    }
?>
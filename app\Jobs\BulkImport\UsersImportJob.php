<?php
    namespace App\Jobs\BulkImport;
    use Illuminate\Bus\Queueable;
    use Illuminate\Bus\Batchable;
    use Illuminate\Contracts\Queue\ShouldBeUnique;
    use Illuminate\Contracts\Queue\ShouldQueue;
    use Illuminate\Foundation\Bus\Dispatchable;
    use Illuminate\Queue\InteractsWithQueue;
    use Illuminate\Queue\SerializesModels;
    use Illuminate\Support\Facades\Log;
    use App\Http\Traits\ProjectDetailTrait;
    use App\Http\Traits\ConfigurationTrait;
    use App\Http\Traits\BulkImportTrait;
    use App\Http\Traits\BulkImportErrorTrait;
    use App\Http\Traits\UserTrait;
    use App\Http\Traits\UserCompanyTrait;
    use App\Http\Traits\BulkImportListTrait;
    use App\Enums\ConfigurationCode;
    use App\Enums\ResultType;
    use App\Enums\ModelAction;
    use App\Enums\Status;
    use App\Enums\ValidationBukImport;

    class UsersImportJob implements ShouldQueue{
        use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, Batchable, ProjectDetailTrait, ConfigurationTrait, BulkImportTrait, BulkImportErrorTrait, UserTrait, UserCompanyTrait, BulkImportListTrait;
        public $list;
        public $projectId;
        public $bulkImportDetailsId;
        public $projectUserId;
        public $userId;

        /**
         * Create a new job instance.
         *
         * @return void
         */
        public function __construct($list, $projectId, $bulkImportDetailsId, $projectUserId, $userId){
            $this->list = $list;
            $this->projectId = $projectId;
            $this->bulkImportDetailsId = $bulkImportDetailsId;
            $this->projectUserId = $projectUserId;
            $this->userId = $userId;
        }

        /**
         * Execute the job.
         *
         * @return void
         */
        public function handle(){
            try {
                $bulkArray = [];
                $project = $this->getProjectDetailInformationByProjectId($this->projectId);
     
                if(is_null($project)){
                    Log::info("UsersImportJob error: No project found with this projectId : ".$this->projectId); 
                }

                elseif(!isset($this->list)){
                    Log::info("UsersImportJob error: The users list is empty"); 
                }

                else{
                    $configDefaultPassword = $this->getConfigurationByValue('code', ConfigurationCode::DefaultPassword->value);
                    $defaultPassword = isset($configDefaultPassword) ? $configDefaultPassword->value : '0000';
    
                    foreach($this->list as $data){
                        $mapChecking = $this->fullUsersValidation($data);

                        if(!is_null($mapChecking) && $mapChecking[0]['status'] <> 'success'){
                            $bulkArrayErrors = ['map_status' => true, 'backend_status' => false, 'project_id' => $this->projectId, 'user_id' => $this->userId, 'bulk_import_id' => $this->bulkImportDetailsId, 'sheet' => ResultType::Users->value, 'identifier' => $data['email'], 'row_status' => ModelAction::NotInserted->value, 'created_by' => $this->userId];
                            $bulkImportErrorId = $this->saveBulkImportError($bulkArrayErrors);

                            if(!$bulkImportErrorId){
                                Log::info("UsersImportJob error: Cannot save the bulk import error row for Users sheet : Project ID: ".$this->projectId.", Email: ".$data['email']); 
                            }
                        }

                        else{
                            $user = $this->getUserInformationsByValues('email', $data['email']);
                            $checkedUserExist = false;

                            if(isset($user)){
                                $speceficUser = $this->getSpeceficUserByValues($this->projectUserId, 'email', $data['email']);  
                                $checkedUserExist = isset($speceficUser) ? false : true;
                            }

                            if($checkedUserExist){
                                $bulkArrayErrors = ['map_status' => false, 'backend_status' => true, 'project_id' => $this->projectId, 'user_id' => $this->userId, 'bulk_import_id' => $this->bulkImportDetailsId, 'sheet' => ResultType::Users->value, 'identifier' => $data['email'], 'row_status' => ModelAction::NotInserted->value, 'errors' => ValidationBukImport::EmailTaken->value, 'value' => $data['email'], 'created_by' => $this->userId];
                                $bulkImportErrorId = $this->saveBulkImportError($bulkArrayErrors);

                                if(!$bulkImportErrorId){
                                    Log::info("UsersImportJob error: Cannot save the bulk import error row for Users sheet : Project ID: ".$this->projectId.", Email: ".$data['email']); 
                                }
                            }

                            else{
                                $userRow = $this->getSpeceficUserByValues($this->projectUserId, 'email', $data['email']);
                                $userType = null;
                                $checkAffectation = true;

                                switch (strtolower($data['user_type'])) {
                                    case 'project owner admin':
                                        $userType = 'admin';
                                    break;
 
                                    case 'project owner employee':
                                        $userType = 'admin_employee';
                                    break;
                                    
                                    case 'building manager admin':
                                        $userType = 'building_manager';
                                    break;

                                    case 'building manager employee':
                                        $userType = 'building_manager_employee';
                                    break;

                                    case 'service provider admin':
                                        $userType = 'sp_admin';
                                    break;

                                    case 'service provider supervisor':
                                        $userType = 'supervisor';
                                    break;

                                    case 'service provider worker':
                                        $userType = 'sp_worker';
                                    break;

                                    case 'tenant':
                                        $userType = 'tenant';
                                    break;
                                    
                                    default:
                                        $userType = 'user_draft';
                                    break;
                                }

                                if($userType == 'building_manager_employee' && !isset($data['bma'])){
                                    $checkAffectation = false;  
                                }

                                if($userType == 'sp_admin' && !isset($data['company'])){
                                    $checkAffectation = false;  
                                }

                                if($userType == 'supervisor' && (!isset($data['company']) || !isset($data['spa']))){
                                    $checkAffectation = false;  
                                }

                                if($userType == 'sp_worker' && (!isset($data['company']) || !isset($data['sps']))){
                                    $checkAffectation = false;  
                                }

                                if(!$checkAffectation){
                                    $bulkArrayErrors = ['map_status' => false, 'backend_status' => true, 'project_id' => $this->projectId, 'user_id' => $this->userId, 'bulk_import_id' => $this->bulkImportDetailsId, 'sheet' => ResultType::Users->value, 'identifier' => $data['email'], 'row_status' => ModelAction::NotInserted->value, 'errors' => ValidationBukImport::UserNotAffected->value, 'value' => $data['email'], 'created_by' => $this->userId];
                                    $bulkImportErrorId = $this->saveBulkImportError($bulkArrayErrors);

                                    if(!$bulkImportErrorId){
                                        Log::info("UsersImportJob error: Cannot save the bulk import error row for Users sheet : Project ID: ".$this->projectId.", Email: ".$data['email']); 
                                    }
                                }

                                else{
                                    if($userType == 'admin' && $this->checkExistUserByMultipleValues('user_type', 'admin', 'project_user_id', $this->projectUserId)){
                                        $bulkArrayErrors = ['map_status' => false, 'backend_status' => true, 'project_id' => $this->projectId, 'user_id' => $this->userId, 'bulk_import_id' => $this->bulkImportDetailsId, 'sheet' => ResultType::Users->value, 'identifier' => $data['email'], 'row_status' => ModelAction::NotInserted->value, 'errors' => ValidationBukImport::POEFound->value, 'value' => $data['email'], 'created_by' => $this->userId];
                                        $bulkImportErrorId = $this->saveBulkImportError($bulkArrayErrors);

                                        if(!$bulkImportErrorId){
                                            Log::info("UsersImportJob error: Cannot save the bulk import error row for Users sheet : Project ID: ".$this->projectId.", Email: ".$data['email']); 
                                        }
                                    }

                                    else{
                                        if(!isset($userRow)){
                                            $array = [
                                                'name' => $data['user_name'] ?? null,
                                                'user_type' => $userType ?? null,
                                                'email' => $data['email'] ?? null,
                                                'phone' => $data['phone_number'] ?? null,
                                                'password' => $defaultPassword ?? null,
                                                'status' => Status::Active->value ?? null,
                                                'project_id' => $this->projectId ?? null,
                                                'emp_dept' => $data['department'] ?? null,
                                                'project_user_id' => $this->projectUserId ?? null,
                                                'service_provider' => $data['company'] ?? null,
                                                'sp_admin_id' => $userType == 'building_manager_employee' ? $data['bma'] : ($userType == 'supervisor' ? $data['spa'] : null),
                                                'supervisor_id' => $data['sps'] ?? null,
                                                'created_by' => $this->userId ?? null
                                            ];

                                            $newUserId = $this->saveUser($array);

                                            if($newUserId){
                                                array_push($bulkArray, $newUserId);
                                            }

                                            else{
                                                $bulkArrayErrors = ['map_status' => false, 'backend_status' => true, 'project_id' => $this->projectId, 'user_id' => $this->userId, 'bulk_import_id' => $this->bulkImportDetailsId, 'sheet' => ResultType::Users->value, 'identifier' => $data['email'], 'row_status' => ModelAction::NotInserted->value, 'errors' => ValidationBukImport::UserNotSaved->value, 'value' => $data['email'], 'created_by' => $this->userId];
                                                $bulkImportErrorId = $this->saveBulkImportError($bulkArrayErrors);

                                                if(!$bulkImportErrorId){
                                                    Log::info("UsersImportJob error: Cannot save the bulk import error row for Users sheet : Project ID: ".$this->projectId.", Email: ".$data['email']); 
                                                }
                                            }
                                        }

                                        else{
                                            $userRowId = $userRow->id;
                                            $existSuperVisors = isset($userRow->supervisor_id) ? $this->explodeDataFromField($userRow->supervisor_id) : [];
                                            $entredSuperVisor = $data['sps'] ?? null;
                                            isset($entredSuperVisor) ? array_push($existSuperVisors, $entredSuperVisor) : $existSuperVisors;
                                            $existSuperVisors = $this->implodeDataFromField(array_unique($existSuperVisors));

                                            $array = [
                                                'name' => $data['user_name'] ?? null,
                                                'user_type' => $userType ?? null,
                                                'email' => $data['email'] ?? null,
                                                'phone' => $data['phone_number'] ?? null,
                                                'password' => $defaultPassword ?? null,
                                                'status' => Status::Active->value ?? null,
                                                'project_id' => $this->projectId ?? null,
                                                'emp_dept' => $data['department'] ?? null,
                                                'project_user_id' => $this->projectUserId ?? null,
                                                'service_provider' => $data['company'] ?? null,
                                                'sp_admin_id' => $userType == 'building_manager_employee' ? $data['bma'] : ($userType == 'supervisor' ? $data['spa'] : null),
                                                'supervisor_id' => $existSuperVisors ?? null
                                            ];

                                            $newUpdatedUser = $this->updateUserByValues($userRowId, $array);

                                            if($newUpdatedUser){
                                                array_push($bulkArray, $userRowId);
                                            }

                                            else{
                                                $bulkArrayErrors = ['map_status' => false, 'backend_status' => true, 'project_id' => $this->projectId, 'user_id' => $this->userId, 'bulk_import_id' => $this->bulkImportDetailsId, 'sheet' => ResultType::Users->value, 'identifier' => $data['email'], 'row_status' => ModelAction::NotInserted->value, 'errors' => ValidationBukImport::UserNotUpdated->value, 'value' => $data['email'], 'created_by' => $this->userId];
                                                $bulkImportErrorId = $this->saveBulkImportError($bulkArrayErrors);

                                                if(!$bulkImportErrorId){
                                                    Log::info("UsersImportJob error: Cannot save the bulk import error row for Users sheet : Project ID: ".$this->projectId.", Email: ".$data['email']); 
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                $implodedValue = isset($bulkArray) && count($bulkArray) > 0 ? $this->implodeDataFromField($bulkArray) : null;
                $bulkImportList = $this->getBulkImportListByValues('bulk_import_id', $this->bulkImportDetailsId);
                $result = null;

                $array = [
                    'users' => $implodedValue,
                    'project_id' => $this->projectId,
                    'bulk_import_id' => $this->bulkImportDetailsId,
                    'created_by' => !isset($bulkImportList) ? $this->userId : $bulkImportList->created_by,
                    'updated_by' => isset($bulkImportList) ? $this->userId : null,
                ];
                
                if(isset($bulkImportList)){
                    $result = $this->updateBulkImportListByValues('bulk_import_id', $this->bulkImportDetailsId, $array);
                }

                else{
                    $result = $this->saveBulkImportList($array);
                }

                if(!$result){
                    Log::info("UsersImportJob error: We cannot do any action on bulk import table for users column (Same data)"); 
                }
            } 
            
            catch (\Throwable $th) {
                Log::error("UsersImportJob error: ".$th);
            }
        }
    }
?>
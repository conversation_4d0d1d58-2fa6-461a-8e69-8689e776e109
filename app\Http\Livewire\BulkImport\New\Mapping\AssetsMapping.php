<?php
    namespace App\Http\Livewire\BulkImport\New\Mapping;
    use Livewire\Component;
    use Illuminate\Support\Facades\Log;
    use App\Http\Traits\FunctionsTrait;
    use App\Http\Traits\BulkImportTrait;
    use App\Http\Traits\AssetTrait;

    class AssetsMapping extends Component{
        use FunctionsTrait, BulkImportTrait, AssetTrait;

        public $showMore;
        public $perPage;
        public $countList;
        public $chunkData;
        public $projectUserId;
        public $bulkImportDetails;

        public function render(){
            $list = $this->getPaginatedAssetsList();
            isset($list) && count($list) > 0 ? $this->setCountList($list->total()) : $this->setCountList(0);
            return view('livewire.bulk-import.new.mapping.assets-mapping', compact('list'));
        }

        public function initAssets() {
            try {
                $assetsData = isset($this->bulkImportDetails) ? $this->bulkImportDetails->bulkImportTemp->assets_data : null;
                $array = json_decode($assetsData, true);
                return collect($array);
            } 
            
            catch (\Throwable $th) {
                Log::error("initAssets error: ".$th);
            }
        }

        public function getPaginatedAssetsList() {
            try {
                $assets = $this->initAssets();
                return $this->customPagination($assets, $this->perPage);
            } 
            
            catch (\Throwable $th) {
                Log::error("getPaginatedAssetsList error: ".$th);
            }
        }

        public function managePerPage() {
            try {
                $number = $this->additionOperation($this->perPage, $this->showMore);
                $this->setPerPage($number);
            } 
            
            catch (\Throwable $th) {
                Log::error("managePerPage error: ".$th);
            }
        }

        public function setPerPage($value) {
            try {
                $this->perPage = $value;
            } 
            
            catch (\Throwable $th) {
                Log::error("setPerPage error: ".$th);
            }
        }

        public function setCountList($value) {
            try {
                $this->countList = $value;
            } 
            
            catch (\Throwable $th) {
                Log::error("setCountList error: ".$th);
            }
        }

        public function manageLoadAll() {
            try {
                $this->setPerPage($this->countList);
            } 
            
            catch (\Throwable $th) {
                Log::error("manageLoadAll error: ".$th);
            }
        }

        public function manageListStatusData() {
            try {
                $assets = $this->initAssets();
                $acceptedNumber = 0;
                $refusedNumber = 0;

                if(isset($assets) && count($assets) > 0){
                    collect($assets)->chunk($this->chunkData)->each(function($chunk) use(&$acceptedNumber, &$refusedNumber){
                        foreach($chunk as $data){
                            $response = $this->fullAssetsValidation($data);

                            if(isset($response) && $response[0]['status'] == 'success'){
                                $acceptedNumber = $acceptedNumber + 1;
                            }

                            else{
                                $refusedNumber = $refusedNumber + 1;
                            }
                        }
                    });
                }

                return [
                    'acceptedNumber' => $acceptedNumber,
                    'refusedNumber' => $refusedNumber
                ];
            } 
            
            catch (\Throwable $th) {
                Log::error("manageListStatusData error: ".$th);
            }
        }
    }
?>
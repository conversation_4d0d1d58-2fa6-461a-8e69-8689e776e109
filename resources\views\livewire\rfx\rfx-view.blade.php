<div>
   <div class="contents crm">
      <div class="container-fluid">
         <div class="col-lg-12">
            <div class="row justify-content-sm-between align-items-center justify-content-center my-3 flex-sm-row flex-column">
                <div class="page-title-wrap p-0">
                    <div class="page-title d-flex justify-content-between">
                        <div class="page-title__left justify-content-sm-between align-items-center justify-content-center">
                            <div class="user-member__title mr-sm-25 ml-0">
                                <h4 class="text-capitalize fw-500 breadcrumb-title fs-16">
                                    @lang('rfx.manage_rfx')
                                </h4>
                            </div>
                        </div>
                    </div>
                    <div>
                        <ul class="atbd-breadcrumb nav">
                            <li class="atbd-breadcrumb__item">
                                <a>@lang('rfx.dashboard')</a>
                                <span class="breadcrumb__seperator">
                                    <span class="la la-angle-right"></span>
                                </span>
                            </li>
                            <li class="atbd-breadcrumb__item">
                                <a>@lang('rfx.manage_rfx')</a>
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="d-flex gap-10 breadcrumb_right_icons">
                    <div class="d-flex gap-10 breadcrumb_right_icons">
                        <button class="btn btn-default btn-primary wh-45 no-wrap" type="button" aria-expanded="false" data-toggle="modal" data-target=""  wire:click="goToEdit('{{ Crypt::encrypt($response['rfx']['id']) }}')">
                            <i class="iconsax mr-0" icon-name="edit-1"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>

         <div class="">
            <div class="row">
               <div class="col-xxl-6 col-lg-6 col-md-6 mb-md-0 mb-30">
                    <div class="card h-100">
                        <div class="card-header py-sm-20 py-3 px-sm-25 px-3">
                            <h6>@lang('rfx.details')</h6>
                        </div>
                        <div class="card-body pt-0">
                            <div class="about-projects">
                                <div class="landing-pages-table table-responsive">
                                    <table class="table table--default align-left">
                                        <thead>
                                            <tr>
                                                <th style="width:35%;">@lang('rfx.title')</th>
                                                <th>@lang('rfx.information')</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr><td>@lang('rfx.rfx_title')</td><td>{{$response['rfx']['title']}}</td></tr>
                                            <tr><td>@lang('rfx.rfx_category')</td><td>{{$response['rfx']['category']}}</td></tr>
                                            <tr><td>@lang('rfx.rfx_type')</td><td>{{$response['rfx']['rfx_type']}}</td></tr>
                                            <tr><td>@lang('rfx.positions')</td><td>{{$response['rfx']['position']}}</td></tr>
                                            <tr><td>@lang('rfx.status')</td><td>

                                                <span class="px-3 py-2 bg-win radius-xl text-white">{{$response['rfx']['status']}}</span>

                                            </td></tr>
                                            <tr><td>@lang('rfx.budget_from')</td><td>{{$response['rfx']['budget_from']}}﷼</td></tr>
                                            <tr><td>@lang('rfx.budget_to')</td><td>{{$response['rfx']['budget_to']}}﷼</td></tr>
                                            <tr><td>@lang('rfx.created_date')</td><td>{{date('d/m/Y',strtotime($response['rfx']['created_by']))}}</td></tr>
                                            <tr><td>@lang('rfx.start_date')</td><td>{{date('d/m/Y',strtotime($response['rfx']['start_date']))}}</td></tr>
                                            <tr><td>@lang('rfx.end_date')</td><td>{{date('d/m/Y',strtotime($response['rfx']['end_date']))}}</td></tr>
                                            <tr>
                                                <td>@lang('rfx.skills')</td>
                                                <td>
                                                    @foreach($response['rfx']['skill'] as $this_data)
                                                    <div class="userDatatable-content d-flex flex-wrap d-inline-block gap-10">
                                                        <span class="bg-opacity-gray color-gray rounded-pill userDatatable-content-status">{{$this_data}}</span>
                                                        <!-- ...other regions -->
                                                    </div>
                                                    @endforeach
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>@lang('rfx.need_to_ask')</td>
                                                <td>
                                                    @foreach($response['rfx']['applicant'] as $this_data)
                                                    <div class="userDatatable-content d-flex flex-wrap d-inline-block gap-10">
                                                        <span class="bg-opacity-gray color-gray rounded-pill userDatatable-content-status">{{$this_data}}</span>
                                                        <!-- ...other regions -->
                                                    </div>
                                                    @endforeach
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>@lang('rfx.need_to_show')</td>
                                                <td>
                                                    @foreach($response['rfx']['visibility'] as $this_data)
                                                    <div class="userDatatable-content d-flex flex-wrap d-inline-block gap-10">
                                                        <span class="bg-opacity-gray color-gray rounded-pill userDatatable-content-status">{{$this_data}}</span>
                                                        <!-- ...other regions -->
                                                    </div>
                                                    @endforeach
                                                </td>
                                            </tr>
                                            <tr><td>@lang('rfx.location')</td><td>{{$response['rfx']['location']}}</td></tr>
                                            <tr><td>@lang('rfx.billing_type')</td><td>{{$response['rfx']['billing_type']}}</td></tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

              <div class="col-xxl-6 col-lg-6 col-md-6 mb-md-0 mb-30">
                    <div class="card">
                        <div>
                            <div class="card-header py-sm-20 py-3 px-sm-25 px-3">
                                <h6>@lang('rfx.description')</h6>
                            </div>
                            <div class="card-body">
                                <p class="mb-0">
                                    {!! $response['rfx']['description'] !!}
                                </p>
                            </div>

                            <div class="card-header py-sm-20 py-3 px-sm-25 px-3">
                                <h6>@lang('rfx.requirement')</h6>
                            </div>
                            <div class="card-body">
                                <p class="mb-0">
                                    {!! $response['rfx']['requirement'] !!}
                                </p>
                            </div>
                            <div class="card-header py-sm-20 py-3 px-sm-25 px-3">
                                <h6>@lang('rfx.terms_conditions')</h6>
                            </div>
                            <div class="card-body">
                                <p class="mb-0">
                                    {!! $response['rfx']['terms_and_conditions'] !!}
                                </p>
                            </div>
                        </div>
                    </div>

                    <div class="card mt-3">
                        <div>
                            <div class="card-header py-sm-20 py-3 px-sm-25 px-3">
                                <h6>@lang('rfx.items')</h6>
                            </div>
                            <div class="card-body px-0">
                                <div class="userDatatable projectDatatable project-table bg-white w-100 border-0">
                                    <div class="table-responsive">

                                        @if($response['rfx']['billing_type'] === 'rfx')
                                        <table class="table mb-0 radius-0">
                                            <thead>
                                                <tr class="userDatatable-header">
                                                    <th>@lang('rfx.item_name')</th>
                                                    <th>@lang('rfx.description')</th>
                                                </tr>
                                            </thead>
                                            <tbody class="sort-table ui-sortable">
                                                @foreach($response['rfxItemData'] as $this_data)
                                                <tr class="ui-sortable-handle">
                                                    <td>
                                                        <div class="d-flex userDatatable-content mb-0 align-items-center">
                                                            <span>{{$this_data['rfx_task']}}</span>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <div class="d-flex userDatatable-content mb-0 align-items-center">
                                                            <span>{{$this_data['rfx_description']}}</span>
                                                        </div>
                                                    </td>
                                                </tr>
                                                @endforeach
                                            </tbody>
                                        </table>
                                        @endif


                                        @if($response['rfx']['billing_type'] === 'items')
                                        <table class="table mb-0 radius-0">
                                            <thead>
                                                <tr class="userDatatable-header">
                                                    <th>@lang('rfx.items')</th>
                                                    <th>@lang('rfx.quantity')</th>
                                                    <th>@lang('rfx.price')</th>
                                                </tr>
                                            </thead>
                                            <tbody class="sort-table ui-sortable">
                                                @foreach($response['rfxItemData'] as $this_data)
                                                <tr class="ui-sortable-handle">
                                                    <td>
                                                        <div class="d-flex userDatatable-content mb-0 align-items-center">
                                                            <span>{{$this_data['product']['name']}}</span>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <div class="d-flex userDatatable-content mb-0 align-items-center">
                                                            <span>{{$this_data['product_quantity']}}</span>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <div class="d-flex userDatatable-content mb-0 align-items-center">
                                                            <span>{{$this_data['product_price']}}</span>
                                                        </div>
                                                    </td>
                                                </tr>
                                                @endforeach
                                            </tbody>
                                        </table>
                                        @endif



                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
         </div>
      </div>
      
   </div>
</div>
</div>
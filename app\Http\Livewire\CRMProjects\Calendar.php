<?php

namespace App\Http\Livewire\CRMProjects;

use App\Services\CRM\Sales\ProjectService;
use Carbon\Carbon;
use Illuminate\Contracts\Encryption\DecryptException;
use Livewire\Component;

class Calendar extends Component
 {
    public $milestone = [
        'title' => '',
        'status' => 'incomplete',
        'start_date' => '',
        'end_date' => '',
        'cost' => '',
        'summary' => '',
        'progress' => '',

    ];
    public $start_date, $end_date;
    public $data = [];
    public $events = [];
    public $itemId;

    protected $listeners = [ 'resetDate', 'taskUpdated' => 'fetchData', 'monthChanged' ];

    public function resetDate()
 {
        $this->start_date = Carbon::now()->startOfMonth()->format( 'd-m-Y' );
        $this->end_date  = Carbon::now()->endOfMonth()->format( 'd-m-Y' );

        $this->fetchData();
        $this->dispatchBrowserEvent( 'change-calendar-month', [
            'start_date' => Carbon::parse( $this->start_date )->format( 'Y-m-d' ),
        ] );
    }

    public function monthChanged( $start, $end )
 {
        $this->start_date = Carbon::parse( $start )->format( 'd-m-Y' );
        $this->end_date = Carbon::parse( $end )->format( 'd-m-Y' );
        $this->fetchData();
    }

    public function filterCalendarData()
 {
        $this->fetchData();
        $this->dispatchBrowserEvent( 'change-calendar-month', [
            'start_date' => Carbon::parse( $this->start_date )->format( 'Y-m-d' ),
        ] );
    }

    public function mount()
 {
        try {
            $this->start_date = Carbon::now()->startOfMonth()->format( 'd-m-Y' );
            $this->end_date  = Carbon::now()->endOfMonth()->format( 'd-m-Y' );
            $this->itemId = decrypt( request()->id );
            $this->fetchData();
        } catch ( DecryptException $e ) {
            return abort( 404 );
        }
    }

    public function fetchData()
 {
        $start_date = $this->start_date ? Carbon::createFromFormat( 'd-m-Y', $this->start_date )->format( 'Y-m-d' ) : '';
        $end_date = $this->end_date ? Carbon::createFromFormat( 'd-m-Y', $this->end_date )->format( 'Y-m-d' ) : '';
        $service = app( ProjectService::class );
        $response = $service->calendarVersionOne( $this->itemId, [ 'start_date' => $start_date, 'due_date' => $end_date ] );

        if ( @$response[ 'status' ] == 'success' ) {
            $this->data = $response[ 'data' ] ?? [];

            foreach ( $response[ 'data' ][ 'arrTask' ] as &$item ) {
                $item[ 'start' ] = Carbon::parse( $item[ 'start' ] )->format( 'Y-m-d\TH:i:s' );
                $item[ 'end' ] = Carbon::parse( $item[ 'end' ] )->format( 'Y-m-d\TH:i:s' );
                unset( $item[ 'url' ], $item[ 'className' ] );
                $item[ 'allDay' ] = true;
                $item[ 'className' ] = 'task-event';
                $item[ 'type' ] = 'task';
            }

            $eventTasks = $response[ 'data' ][ 'arrTask' ];

            $eventsMilestones = [];
            foreach ( $response[ 'data' ][ 'milestones' ] as $milestone ) {
                $progress = isset( $milestone[ 'progress' ] ) && $milestone[ 'progress' ] !== '' ? ( int ) $milestone[ 'progress' ] : 0;

                if ( $progress <= 25 ) {
                    $classSuffix = 0;
                    // danger
                } elseif ( $progress <= 50 ) {
                    $classSuffix = 1;
                    // warning
                } elseif ( $progress <= 75 ) {
                    $classSuffix = 2;
                    // info
                } else {
                    $classSuffix = 3;
                    // success
                }

                $eventsMilestones[] = [
                    'id' => $milestone[ 'id' ],
                    'title' => $milestone[ 'name' ],
                    'start' => Carbon::parse( $milestone[ 'start_date' ] )->format( 'Y-m-d\TH:i:s' ),
                    'end' => Carbon::parse( $milestone[ 'end_date' ] )->format( 'Y-m-d\TH:i:s' ),
                    'allDay' => true,
                    'className' =>   'milestone-event milestone-progress-' . $classSuffix,
                    'type' => 'milestone',
                    'progress' => $milestone[ 'progress' ] ?? 0,
                    'tasks_count' => $milestone[ 'tasks_count' ],
                    'status' => $milestone[ 'status' ],

                ];
            }

            $this->events = array_merge( $eventTasks, $eventsMilestones );

            $this->dispatchBrowserEvent( 'calendar-events', [
                'events' => $this->events,
            ] );
        }

    }

    public function render()
 {
        return view( 'livewire.c-r-m-projects.calendar' );
    }
}

<?php
    namespace App\Http\Traits;
    use Illuminate\Support\Facades\Log;
    use App\Http\Helpers\ReportQueryHelper;
    use App\Http\Traits\PropertyTrait;
    use App\Http\Traits\ServiceTrait;
    use App\Http\Traits\FunctionsTrait;
    use App\Http\Traits\AssetNameTrait;
    use App\Http\Traits\PropertyBuildingTrait;
    use App\Http\Traits\TempBulkImportTrait;
    use App\Http\Traits\RoomsTypeFloorTrait;
    use App\Http\Traits\AssetHistoryTrait;
    use App\Http\Traits\AssetNameAssetCategoryTrait;
    use App\Models\Asset;
    use App\Enums\ModelAction;

    trait AssetTrait{
       use PropertyTrait, ServiceTrait, FunctionsTrait, AssetNameTrait, PropertyBuildingTrait, TempBulkImportTrait, RoomsTypeFloorTrait, AssetHistoryTrait, AssetNameAssetCategoryTrait;

        public function manageEntredAsset($assetName, $assetNumber, $projectUserId) {
            try {
                $asset = $this->getAssetInformationsByValues($assetName, $assetNumber, $projectUserId);

                if(isset($asset)){
                    return ModelAction::Update->value;
                }

                else{
                    return ModelAction::Insert->value;
                }
            } 
            
            catch (\Throwable $th) {
                Log::error("manageEntredAsset error: ".$th);
            }
        }

        public function getAssetInformationsByValues($assetName, $assetNumber, $projectUserId) {
            try {
                return Asset::leftjoin('asset_names', 'asset_names.id', 'assets.asset_name_id')
                ->where('assets.user_id', $projectUserId) 
                ->where('assets.asset_number', $assetNumber)     
                ->where('asset_names.asset_name', $assetName)     
                ->where('assets.is_deleted', 'no')   
                ->select('assets.*', 'asset_names.asset_name')                       
                ->first();
            } 
            
            catch (\Throwable $th) {
                Log::error("getAssetInformationsByValues error: ".$th);
            }
        }

        public function saveAsset($array) {
            try {
                return Asset::insertGetId($array);
            } 
            
            catch (\Throwable $th) {
                Log::error("saveAsset error: ".$th);
            }
        }

        public function updateAssetInformationsById($assetId, $array) {
            try {
                return Asset::where('id', $assetId)                       
                ->update($array);
            } 
            
            catch (\Throwable $th) {
                Log::error("updateAssetInformationsById error: ".$th);
            }
        }

        public function getAssetInformationsById($assetId) {
            try {
                return Asset::where('id', $assetId)
                ->where('is_deleted', 'no')
                ->select('building_id', 'property_id', 'asset_category_id', 'asset_name_id', 'asset_number', 'purchase_date', 'model_number', 'manufacturer_name', 'asset_status', 'asset_damage_date', 'room', 'floor', 'asset_name_id', 'barcode_value', 'id')
                ->first();
            } 
            
            catch (\Throwable $th) {
                Log::error("getAssetInformationsById error: ".$th);
            }
        }    

        public function deleteAssetByValue($key, $value) {
            try {
                return Asset::whereIn($key, $value)
                ->delete();
            } 
            
            catch (\Throwable $th) {
                Log::error("deleteAssetByValue error: ".$th);
            }
        } 

        public function getFiltredAssetsList($services) {
            try {
                $assetCatIds = [];
                $data = [];

                if(isset($services) && count($services) > 0){
                    $assetCatIds = array_column($services, 'id');
                    $data = $this->getAssetListByValues('asset_category_id', $assetCatIds)->toArray();
                }

                return $data;
            } 
            
            catch (\Throwable $th) {
                Log::error("getFiltredAssetsList Error: ".$th);
            }
        }

        public function getAssetListByValues($key, $value) {
            try {
                return Asset::where('is_deleted', 'no')
                ->whereIn($key, $value)
                ->get();
            } 
            
            catch (\Throwable $th) {
                Log::error("getAssetListByValues Error: ".$th);
            }
        }

        public function getPaginatedAssetsListByValues($key, $value, $perPage) {
            try {
                return Asset::select('building_id', 'property_id', 'asset_category_id', 'floor', 'room', 'asset_name_id', 'asset_number', 'purchase_date', 'manufacturer_name', 'model_number', 'asset_status', 'asset_damage_date')
                ->whereIn($key, $value)
                ->paginate($perPage, ['*'], 'page');
            } 
            
            catch (\Throwable $th) {
                Log::error("getPaginatedAssetsListByValues error: ".$th);
            }
        }

        public function getAssetInformationsByBuildingIdAndValues($projectUserId, $buildingId, $assetNumber, $assetName) {
            try {
                return Asset::leftjoin('asset_names', 'asset_names.id', 'assets.asset_name_id')
                ->select('assets.id')
                ->where('assets.user_id', $projectUserId) 
                ->where('assets.building_id', $buildingId)
                ->where('assets.asset_number', $assetNumber)
                ->where('asset_names.asset_name', $assetName)    
                ->first();
            } 
            
            catch (\Throwable $th) {
                Log::error("getAssetInformationsByBuildingIdAndValues error: ".$th);
            }
        }
    }
?>
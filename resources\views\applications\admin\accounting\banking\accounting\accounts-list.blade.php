@extends('layouts.app')
@section('styles')
@endsection
@section('content')
        <div class="contents crm">
           <div class="container-fluid">
            <div class="col-lg-12">
    <div class="row justify-content-sm-between align-items-center justify-content-center my-3 flex-sm-row flex-column">
        <div class="page-title-wrap p-0">
            <div class="page-title d-flex justify-content-between">
                <div class="page-title__left justify-content-sm-between align-items-center justify-content-center">
                    <div class="user-member__title mr-sm-25 ml-0">
                        <h4 class="text-capitalize fw-500 breadcrumb-title fs-16">
                            Manage Bank Accounts
                        </h4>
                    </div>
                </div>
            </div>
            <div>
                <ul class="atbd-breadcrumb nav">
                    <li class="atbd-breadcrumb__item">
                        <a>Dashboard</a>
                        <span class="breadcrumb__seperator">
                            <span class="la la-angle-right"></span>
                        </span>
                    </li>
                    <li class="atbd-breadcrumb__item">
                        <a>Manage Bank Accounts</a>
                    </li>
                </ul>
            </div>
        </div>

        <div class="d-flex gap-10 breadcrumb_right_icons">
            <div class="d-flex gap-10 breadcrumb_right_icons">
                    <button class="btn btn-default btn-primary w-100 no-wrap" data-toggle="modal" data-target="#create-account" type="button" aria-expanded="false"><i class="las la-plus fs-16"></i>Create</button>

            </div>
        </div>
        <!--====End Design for Export PDF===-->
    </div>
</div>


<div class="table-responsive">
    <div class="card">
        <div class="">
            <div class="card-header py-4 px-3 border-0 d-flex justify-content-between align-items-center">
                <h6 class="text-capitalize text-osool fw-500 mb-3 mb-sm-0"> Bank Accounts</h6>

                <div class="d-flex gap-10 table-search">
                    <div class="position-relative">
                    <input type="text" class="form-control" placeholder="Search">
                    <i class="iconsax field-icon fs-18 mr-0" icon-name="search-normal-2"></i>
                </div>
                     <button class="btn btn-export text-dark"><i class="iconsax icon fs-22 mr-0" icon-name="upload-1"></i> Export</button>
                </div>
            </div>
        </div>
        <div class="card-body px-0 pt-0">
            <div class="userDatatable projectDatatable project-table bg-white w-100 border-0">
                <div class="table-responsive">
                    <table class="table mb-0 radius-0 th-osool">
                        <thead>
                            <tr class="userDatatable-header">
                                <th>
                                   <i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i>  Bank Name
                                </th>
                                <th>
                                    <i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i> Account Number
                                </th>
                                <th>
                                   <i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i>  Current Balance
                                </th>
                                <th>
                                   <i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i>  Contact Number
                                </th>
                                <th>
                                   <i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i> Bank Branch
                                </th>
                                <th>
                                   <i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i>  Swift Code
                                </th>
                                <th>
                                   <i class="iconsax icon fs-22" icon-name="swap-vertical-circle"></i>  Bank Address
                                </th>
                                 <th>
                                   COA ( Current Assets)
                                </th>
                                <th>
                                    Action
                                </th>
                            </tr>
                        </thead>
                        <tbody class="sort-table ui-sortable">
                            <tr class="ui-sortable-handle">
                                <td>
                                    <div class="d-flex userDatatable-content mb-0 align-items-center">
                                        <span>Bank Of Baroda</span>
                                    </div>
                                </td>
                                 <td>
                                    <div class="d-flex userDatatable-content mb-0 align-items-center">
                                        <span>BOB123456789</span>
                                    </div>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center gap-10">
                                    <img src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSx34eO8RbZlsetN1iAQtxuOLnLOlyJbJmU7Q&s" width="15"><span class="text-new-primary">25000</span>
                                </td>
                                <td>
                                    <div class="d-flex userDatatable-content mb-0 align-items-center">
                                        <span>+966-*********</span>
                                    </div>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center gap-10"><span class="">Riyadh</span>
                                </div>
                                </td>
                                <td>
                                    <div class="d-flex userDatatable-content mb-0 align-items-center">
                                        <span>SWFT1234</span>
                                    </div>
                                </td>
                                <td>
                                    <div class="d-flex userDatatable-content mb-0 align-items-center"><span>Jeddah,Saudi Arabia</span>
                                </td>
                                 <td>
                                    <div class="d-flex userDatatable-content mb-0 align-items-center">
                                        <span>-</span>
                                    </div>
                                </td>
                                <td>
                                    <div class="d-inline-block">
                                        <ul class="mb-0 d-flex gap-10">
                                            <li>
                                                <a href="javascript:void(0);" data-toggle="modal" data-target="#bank-details">
                                                    <i class="iconsax icon text-osool fs-18" icon-name="eye"></i>
                                                </a>
                                            </li>
                                            <li>
                                                <a href="javascript:void(0);" data-toggle="modal" data-target="#create-account">
                                                    <i class="iconsax icon text-new-primary fs-18" icon-name="edit-1"></i>
                                                </a>
                                            </li>
                                            <li>
                                                <a href="javascript:void(0);" data-toggle="modal" data-target="#delete-vendor">
                                                    <i class="iconsax icon text-delete fs-18" icon-name="trash"></i>
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>




        <div class="card-body pt-0">
<div class="d-flex justify-content-md-between flex-column flex-md-row justify-content-center align-items-center mt-4 gap-10">
    <div class="">
        <ul class="atbd-pagination d-flex justify-content-between">
            <li>
                <div class="paging-option">
                    <div class="dataTables_length d-flex">
                        <label class="d-flex align-items-center mb-0">
                            <select aria-controls="workorder_table" wire:model.live.debounce.250ms="perPage" class="custom-select custom-select-sm form-control form-control-sm mx-2" style="min-height: 35px;">
                                <option value="5">5</option>
                                <option value="10">10</option>
                                <option value="25">25</option>
                                <option value="50">50</option>
                                <option value="100">100</option>
                            </select>
                           <span class="no-wrap"> Entries Per Page </span>
                        </label>
                    </div>
                </div>
            </li>
        </ul>
    </div>
    <div class="">
        <div class="user-pagination">
            <div class="user-pagination new-pagination">
                <div class="d-flex justify-content-sm-end justify-content-end">
                    <nav>
                        <span class="relative z-0 inline-flex rounded-md d-flex new-pagination-section flex-wrap">
    <span class="">
        <span aria-disabled="true" aria-label="&amp;laquo; Previous">
            <button class="border-0 disabled" aria-hidden="true" disabled="">
                <i class="iconsax icon-bold fs-16 mr-0 rotate-ar-y d-inline-block" icon-name="chevron-left"></i>
            </button>
        </span>
    </span>

    <span wire:key="paginator-page-1-page1">
        <button class="border-0 current-page" disabled="">1</button>
    </span>
    <span wire:key="paginator-page-1-page2">
        <button type="button" class="border-0">2</button>
    </span>
    <span wire:key="paginator-page-1-page3">
        <button type="button" class="border-0">3</button>
    </span>
    <span wire:key="paginator-page-1-page4">
        <button type="button" class="border-0">4</button>
    </span>
    <span wire:key="paginator-page-1-page5">
        <button type="button" class="border-0">5</button>
    </span>
    <span wire:key="paginator-page-1-page6">
        <button type="button" class="border-0">6</button>
    </span>
    <span wire:key="paginator-page-1-page7">
        <button type="button" class="border-0">7</button>
    </span>
    <span wire:key="paginator-page-1-page8">
        <button type="button" class="border-0">8</button>
    </span>
    <span wire:key="paginator-page-1-page9">
        <button type="button" class="border-0"> 9 </button>
    </span>

    <span>
        <button type="button" class="border-0">
            <i class="iconsax icon-bold fs-16 mr-0 rotate-ar-y d-inline-block" icon-name="chevron-right"></i>
        </button>
    </span>
</span>

                    </nav>
                </div>
            </div>
        </div>
    </div>
    <div>
        <p class="text-sm text-gray-700 leading-5 mb-0">
                        <span>Showing</span>
                        <span class="font-medium">1</span>
                        <span>to</span>
                        <span class="font-medium">6</span>
                        <span>of</span>
                        <span class="font-medium">52</span>
                        <span>results</span>
                    </p>
    </div>
</div>
        </div>
    </div>

    
</div>
</div>



<!-- Modal -->
<div class="modal fade new-modal" id="create-account" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalLabel">Create New Account</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true"><i class="iconsax" icon-name="x"></i></span>
        </button>
      </div>
      <div class="modal-body">
    <div class="row">
        <div class="form-group col-md-12">
            <label class="require form-label">Bank Type</label>
            <select class="form-control" name="bank_type" id="bank_type">
                <option value="">Select Type</option>
                <option value="bank">Bank</option>
                <option value="wallet">Wallet</option>
            </select>
        </div>
    </div>
    <div class="row bank_type_wallet d-none">
        <div class="form-group col-md-12">
            <label class="require form-label">Wallet</label>
            <select class="form-control" name="wallet_type" id="wallet_type">
                <option value="">Select Type</option>
                <option value="paypal">Paypal</option>
                <option value="stripe">Stripe</option>
            </select>
        </div>
    </div>
    <div class="row">
        <div class="form-group col-md-6">
            <label for="chart_account_id" class="form-label">Account</label>
            <select name="chart_account_id" id="account_type" class="form-control" required="required">
                <option value="0" class="subAccount">Select Account</option>
                <option value="2801" class="subAccount">1060 - Checking Account</option>
                <option value="2802" class="subAccount">1065 - Petty Cash</option>
            </select>
        </div>
        <div class="col-md-6">
            <div class="form-group">
                <label for="holder_name" class="form-label">Bank Holder Name</label><span class="text-danger">*</span>
                <input class="form-control" required="required" placeholder="Enter Bank Holder Name" name="holder_name" type="text" id="holder_name" />
            </div>
        </div>
        <div class="col-md-6">
            <div class="form-group">
                <label for="bank_name" class="form-label">Bank Name</label><span class="text-danger">*</span>
                <input class="form-control" placeholder="Enter Bank Name" required="required" name="bank_name" type="text" id="bank_name" />
            </div>
        </div>
        <div class="col-md-6 bank">
            <div class="form-group">
                <label for="account_number" class="form-label">Account Number</label><span class="text-danger">*</span>
                <input class="form-control" required="required" placeholder="Enter Account Number" name="account_number" type="text" id="account_number" />
            </div>
        </div>
        <div class="col-md-6">
            <div class="form-group">
                <label for="opening_balance" class="form-label">Opening Balance</label><span class="text-danger">*</span>
                <input class="form-control" required="required" min="0" placeholder="Enter Opening Balance" name="opening_balance" type="number" value="" id="opening_balance" />
            </div>
        </div>
        <div class="col-md-6 bank">
            <div class="form-group">
                <label for="contact_number" class="form-label">Contact Number</label>
                <input class="form-control" placeholder="Enter Contact Number" name="contact_number" type="text" value="" id="contact_number" />
            </div>
        </div>
        <div class="col-md-6 bank">
            <div class="form-group">
                <label for="bank_branch" class="form-label">Bank Branch</label><span class="text-danger">*</span>
                <input class="form-control" required="required" min="0" placeholder="Enter Bank Branch" name="bank_branch" type="text" value="" id="bank_branch" />
            </div>
        </div>
        <div class="col-md-6 bank">
            <div class="form-group">
                <label for="swift" class="form-label">SWIFT code</label>
                <input class="form-control" id="swift" placeholder="Enter Swift Number" name="swift" type="text" value="" />
            </div>
        </div>
        <div class="col-md-12">
            <div class="form-group">
                <label for="bank_address" class="form-label">Bank Address</label><span class="text-danger">*</span>
                <textarea class="form-control" placeholder="Enter Bank Address" rows="3" required="required" name="bank_address" cols="50" id="bank_address"></textarea>
            </div>
        </div>
    </div>
</div>

      <div class="modal-footer">
        <button type="button" class="btn bg-hold-light text-white" data-dismiss="modal">Close</button>
        <button type="button" class="btn bg-new-primary">Save</button>
      </div>
    </div>
  </div>
</div>

<div class="modal fade" id="delete-vendor" tabindex="-1" role="dialog" aria-labelledby="deleteUserModalLabel" aria-hidden="true">
                    <div class="modal-dialog modal-sm" role="document">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="deleteUserModalLabel">Delete Vendor</h5>
                                <button type="button" class="close" data-dismiss="modal" aria-label="{{ __('user_management_module.modal.close') }}">
                                    <span aria-hidden="true"><i class="iconsax" icon-name="x"></i></span>
                                </button>
                            </div>
                            <div class="modal-body">
                                <div class="text-center">
                                     <i class="iconsax icon text-loss fs-60" icon-name="warning-triangle"></i>
                                    <p class="mt-4">Are you sure you want to delete <br> the account <strong id="deleteUserName"> Abdul Rehman ?</strong></p>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn bg-hold-light" data-dismiss="modal">Cancel</button>
                                <form id="deleteUserForm"  method="POST">
                                    <button type="submit" class="btn btn-danger">Yes, Delete It</button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>



<!-- Modal Bank account Details -->
<div class="modal fade" id="bank-details" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalLabel">Bank Account Details</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <div class="userDatatable projectDatatable project-table bg-white w-100 border-0">
                <div class="table-responsive">
                    <table class="table mb-0 radius-0 th-borderless">
                        <tbody class="sort-table ui-sortable">
                            <tr>
                                 <th>
                                   Bank Name
                                </th>
                                <td>
                                    <div class="d-flex userDatatable-content mb-0 align-items-center">
                                        <span>Bank Of Baroda</span>
                                    </div>
                                </td>
                            </tr>
                             <tr>
                                 <th>
                                   Account
                                </th>
                                <td>
                                    <div class="d-flex userDatatable-content mb-0 align-items-center">
                                        <span>Bank Of Baroda</span>
                                    </div>
                                </td>
                            </tr>
                             <tr>
                                 <th>
                                   Bank Holder Name
                                </th>
                                <td>
                                    <div class="d-flex userDatatable-content mb-0 align-items-center">
                                        <span>Bank Of Baroda</span>
                                    </div>
                                </td>
                            </tr>
                             <tr>
                                 <th>
                                   Account Number
                                </th>
                                <td>
                                    <div class="d-flex userDatatable-content mb-0 align-items-center">
                                        <span>Bank Of Baroda</span>
                                    </div>
                                </td>
                            </tr>
                             <tr>
                                 <th>
                                   Opening Balance
                                </th>
                                <td>
                                    <div class="d-flex userDatatable-content mb-0 align-items-center">
                                        <span>Bank Of Baroda</span>
                                    </div>
                                </td>
                            </tr>
                             <tr>
                                 <th>
                                  Bank Type
                                </th>
                                <td>
                                    <div class="d-flex userDatatable-content mb-0 align-items-center">
                                        <span>Bank Of Baroda</span>
                                    </div>
                                </td>
                            </tr>
                             <tr>
                                 <th>
                                   Contact Number
                                </th>
                                <td>
                                    <div class="d-flex userDatatable-content mb-0 align-items-center">
                                        <span>Bank Of Baroda</span>
                                    </div>
                                </td>
                            </tr>
                             <tr>
                                 <th>
                                   Bank Branch
                                </th>
                                <td>
                                    <div class="d-flex userDatatable-content mb-0 align-items-center">
                                        <span>Bank Of Baroda</span>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                 <th>
                                   SWIFT Code
                                </th>
                                <td>
                                    <div class="d-flex userDatatable-content mb-0 align-items-center">
                                        <span>Bank Of Baroda</span>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                 <th>
                                   Bank Address
                                </th>
                                <td>
                                    <div class="d-flex userDatatable-content mb-0 align-items-center">
                                        <span>Bank Of Baroda</span>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
      </div>
    </div>
  </div>
</div>

           </div>




@endsection

@section('scripts')
<script type="text/javascript">
    $(".datepicker").datepicker();
    $("#bank_type, #account_type, #wallet_type").select2();
</script>
@endsection
@extends('layouts.app')
@section('styles')
<style>
    .daterangepicker {
        top: 219px !important;
    }
    .dropdown-item-checked::before {
    position: absolute;
    left: .4rem;
    content: '✓';
    color: black;
    font-weight: 600;
}
</style>
@endsection 
@section('content')
    <livewire:work-orders.pm-list :decryptedServiceProviderId="$decryptedServiceProviderId" :propertiesList="$propertiesList" :servicesList="$servicesList" :explodedBmEmployees="$explodedBmEmployees" :spAdminId="$spAdminId" :serviceProviderName="$serviceProviderName" :viewPm="$viewPm" :editPm="$editPm" :deletePm="$deletePm" :countPmWorkOrders="$countPmWorkOrders" :propertiesCount="$propertiesCount"/>
@endsection
@section('scripts')
    <script src = "{{ asset('new_theme/js/functions.js')}}"></script>
    <script>
        document.addEventListener('feather', (event) => {
            feather.replace();
        });

        $("#filter_id_section .dropdown-item").click(function() {
            filterByFrequencyList(this);
        });

        $('#searchbarservice').on('input', function() {
            searchWOPropertiesList(this);
        });

        $('#searchbar').on('input', function() {
            searchWOPropertiesList(this);
        });

        $('#all_properties').on('click', function() {
            selectProperties(this);
        }); 
        
        $('#service_all').on('click', function() {
            selectServices(this);
        }); 

        $('.prop_field').on('click', function() {
            selectSingleProperties();
        });
        
        $('.prop_field_service').on('click', function() {
            selectSingleProperties();
        });

        $('#property_filter_submit').on('click', function() {
            filterByProperty();
        });

        $('#property_filter_reset').on('click', function() {
            resetProperty();
        });

        $('#service_filter_submit').on('click', function() {
            filterByService();
        });

        $('#service_filter_reset').on('click', function() {
            resetService();
        });

        $('.delete_workorderp').on('click', function() {
            confirmDestroyPmWorkOrder(this);
        });

        document.addEventListener('result', (event) => {
            event.detail === 1 ? openSuccessModalForDeleteWorkOrder() : openFailedModalForDeleteWorkOrder();
        });

        $(".complex > div > .prop_field").on("change", function () {
            let isChecked = $(this).is(":checked");
            $(this).closest("li").find("ul .property").prop("checked", isChecked);
        });
    </script>

    <script>
        function bindDeleteWorkOrder() {
            $('.delete_workorderp').off('click').on('click', function () {
                confirmDestroyPmWorkOrder(this);
            });
        }

        // Initial bind
        document.addEventListener('DOMContentLoaded', function () {
            bindDeleteWorkOrder();
        });

        // Re-bind after every Livewire update
        Livewire.hook('message.processed', (message, component) => {
            bindDeleteWorkOrder();
        });
    </script>

@endsection  
<?php
    namespace App\Imports;
    use Illuminate\Support\Collection;
    use Maatwebsite\Excel\Concerns\ToCollection;
    use Maatwebsite\Excel\Concerns\WithCalculatedFormulas;
    use Maatwebsite\Excel\Concerns\WithHeadingRow;
    use Maatwebsite\Excel\Concerns\SkipsOnError;
    use Maatwebsite\Excel\Concerns\WithBatchInserts;
    use Maatwebsite\Excel\Concerns\WithChunkReading;
    use Maatwebsite\Excel\Concerns\Importable;
    use Maatwebsite\Excel\Concerns\SkipsErrors;
    use Illuminate\Support\Facades\Log;
    use App\Http\Traits\FunctionsTrait;
    use App\Http\Traits\BulkImportTempTrait;

    class UserImport implements ToCollection, WithCalculatedFormulas, WithHeadingRow, SkipsOnError, WithBatchInserts, WithChunkReading{
       use Importable, SkipsErrors, FunctionsTrait, BulkImportTempTrait;
       
        protected $bulkImportTempId;

        public function __construct($bulkImportTempId){
            $this->bulkImportTempId = $bulkImportTempId;
        }

        /**
        * @param Collection $collection
        */
        public function collection(Collection $dataRows){
            try {
                $collection = collect();

                if($this->valueIsRequired($dataRows)){
                    Log::info("UserImport error: No users sheet found in this file!");
                }

                else{
                    $dataRows->chunk(500)->each(function ($chunk) use ($collection, $dataRows) {
                        $chunk->each(function ($row) use ($collection, $dataRows) {
                            $row['user_type'] = isset($row['user_type']) ? trim(strtolower($row['user_type'])) : null;
                            $row['user_name'] = isset($row['user_name']) ? trim($row['user_name']) : null;
                            $row['email'] = isset($row['email']) ? trim($row['email']) : null;
                            $row['phone_number'] = isset($row['phone_number']) ? trim($row['phone_number']) : null;
                            $row['department'] = isset($row['department']) ? trim($row['department']) : null;
                            $collection->push($row);
                        });
                    });

                    $filteredCollection = $collection->filter(function ($item) {
                        return collect($item)->filter()->isNotEmpty();
                    });

                    $jsonData = $filteredCollection->toJson();
                    $updatedBulkImportTemp = $this->updateBulkImportTempByValues('id', $this->bulkImportTempId, ['users_data' => $jsonData]);

                    if($updatedBulkImportTemp){
                        Log::info("UserImport: Users data processed successfully: ".$filteredCollection->count()." records found.");
                    }

                    else{
                        Log::info("UserImport: Unable to import and save the users sheet.");
                    }
                }
            } 
            
            catch (\Throwable $th) {
                Log::error("UserImport error: ".$th);
            }
        }

        /**
         * uniqueBy
         *
         * @return void
         */
        public function uniqueBy(){
            return 'email';
        }

        /**
         * headingRow
         *
         * @return int
         */
        public function headingRow(): int{
            return 1;
        } 

         /**
         * onError
         *
         * @param  mixed $e
         * @return void
         */
        public function onError(\Throwable $e){
            Log::error("UserImport (onError) error: ".$e->getMessage());
        }

        /**
         * batchSize
         *
         * @return int
         */
        public function batchSize(): int{
            return 500;
        }

         /**
         * chunkSize
         *
         * @return int
         */
        public function chunkSize(): int{
            return 500;
        }
    }
?>
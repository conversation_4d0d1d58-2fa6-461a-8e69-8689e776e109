<?php

namespace App\Http\Livewire\AdvanceContracts;

use App\Models\User;
use Livewire\Component;
use App\Models\Contracts;
use App\Http\Helpers\Helper;
use App\Models\ContractMonth;
use App\Models\WorkforceAndTeam;
use App\Services\ContractService;
use App\Models\ContractServiceKpi;
use App\Models\ContractUsableItem;
use App\Services\AkauntingService;
use Illuminate\Support\Facades\DB;
use App\Http\Traits\FunctionsTrait;
use Illuminate\Support\Facades\Log;
use App\Models\AdvanceContractDraft;
use Illuminate\Support\Facades\Auth;
use App\Repositories\AdvanceContractRepository;
use App\Http\Traits\SmartAssigningContractTrait;
use App\Notifications\AdvanceContracts\VariationOrderApprovalNotification;

class StepSix extends Component
{
    use  FunctionsTrait, SmartAssigningContractTrait;
    public $uuid;
    public $type =   'confirmation';
    public $route_prefix;
    public $isVariation = false; 

    public function mount(AdvanceContractRepository $advanceContractRepository, $uuid = null, $prefix = null)
    {
        $this->uuid = $uuid;
        $this->route_prefix = $prefix;
        $this->isVariation = request()->routeIs('variation.*');
        if($this->isVariation ){
            $this->type  =   'variation_order';
        }
    }

    public function submit()
    {
        try {
            DB::beginTransaction();
            $successMessage = __('advance_contracts.general.contract_created');
            $errorMessage = __('advance_contracts.general.contract_created_error');
            $draft = AdvanceContractDraft::where('uuid', $this->uuid)->first();
            $message_en = 'A new Variation Order request has been submitted and requires your approval.';
            $message_ar = 'تم تقديم طلب أمر تغيير جديد ويحتاج إلى موافقتك.';
            $notificationSubType = 'variation_order_created';

            if (!$draft) {
                throw new \Exception('Draft not found.');
            }
       
            if($draft->is_variation_order == 0){
                $akauntingService = new AkauntingService();
                $contractService = new ContractService();
                $mainInformation = $draft->main_information_data;
                $extraData = $draft->extra_data;
                $team = $draft->workforce_team_data;
                $kpiData = $draft->data_agreement_kpi_data; // <-- KPI data
                $assetData = $draft->assets_ppm_data;
                $sideFeatures = $kpiData['sideFeatures'] ?? [];

                $allowSub    =  !empty($sideFeatures['allowSub']) ? 1 : 0;
                $smartAssign =  !empty($sideFeatures['smartAssign']) ? 1 : 0;
                $tenant      =  !empty($sideFeatures['tenant']) ? 1 : 0;

                $user   =   Auth::user();
                $serviceIds = implode(',', array_map(function ($serviceRow) {
                    return $serviceRow['service_id'];
                }, $kpiData['servicesRows']));

            
                $contract = Contracts::create([
                    'service_provider_id' => $mainInformation['contract_with'],
                    'reference_uuid'      => $draft->uuid,
                    'contract_number'     => $mainInformation['contract_name'],
                    'contract_value'      => $mainInformation['amount'],
                    'payment_interval'    => $mainInformation['interval'],
                    'start_date'          => date('Y-m-d',strtotime($mainInformation['start_date'])),
                    'end_date'            =>  date('Y-m-d',strtotime($mainInformation['end_date'])),
                    'is_subcontract'      => $mainInformation['is_subcontract'],
                    'parent_contract_id'  => $mainInformation['selected_subcontract_id'] ?? null,
                    'contract_type_id'    => $mainInformation['contract_type_id'],
                    'comment'             => $extraData['comments'] ?? null,
                    'picture'             => $extraData['file_path'] ?? null,
                    'region_id'           => isset($kpiData['region_id']) ? implode(',', $kpiData['region_id']) : null,
                    'city_id'             => isset($kpiData['selected_cities']) ? implode(',', $kpiData['selected_cities']) : null,
                    'properties'          => isset($kpiData['selected_properties']) ? implode(',', $kpiData['selected_properties']) : null,
                    'warehouse_owner'     => $assetData['ownership'],
                    'asset_categories'    =>   $serviceIds,
                    'asset_names'         =>  isset($assetData['assets']) ? implode(',', $assetData['assets']) : null,
                    'use_smart_assigning'   => $smartAssign,
                    'allow_subcontract'     => $allowSub,
                    'use_form_of_unit_receival' =>   $tenant,
                    'user_id'               =>  $user->project_user_id,
                    'created_by'            =>  $user->id,
                    'status'                =>  1,
                    'is_deleted'            => 'no',
                ]);

                $draft->contract_id = $contract->id;
                $draft->status = 'pending_approval';
                $draft->save();

                foreach($kpiData['property_details'] as $property)
                {
                    $data = array(
                        'contract_id' => $contract->id,
                        'property_building_id' => $property['building_id'],
                        'building_name' => $property['building_name'],
                        'complex_name' => $property['complex_name'],
                        'property_type' => $property['property_type'],
                        'units_count' => $property['units_count'],
                        'zones_count' => $property['zones_count'],
                    );
                    DB::table('contract_property_buildings')->insert($data);
                }
                // Insert stock data into contract_usable_items table
                if (isset($assetData['stocks'][$assetData['ownership']])) {
                    $itemCompanyId = $akauntingService->getCurrentCompanyId();
                    if($assetData['ownership'] == 'service_provider'){
                        $serviceProviderCompany = $contract->serviceProvider->serviceProviderAdmin->userCompany->getCompany();
                        $itemCompanyId = $serviceProviderCompany->id;
                    }
                    foreach ($assetData['stocks'][$assetData['ownership']] as $stock) {
                        ContractUsableItem::create([
                            'contract_id'    => $contract->id,
                            'company_id'      => $itemCompanyId,
                            'item_id'         => $stock['id'],
                            'price'           => $stock['price'],
                            'penalty'         => $stock['penalty'],
                            'approval'        => $stock['approval'],
                            'low_stock'       => $stock['low_stock'],
                            'mandatory'       => $stock['mandatory'],
                            'open_stock'      => $stock['open_stock'],
                            'penalty_type'    => $stock['penalty_type'],
                        ]);
                    }
                }

                if($smartAssign== 1){
                    $data = $this->implodeDataFromField($kpiData['selectedSmartAssignCategories']);
                    $this->saveSmartAssigningContract($contract->id, $data, $user->id);
                }

                if(isset($tenant) && $tenant == 1)
                {
                    $contract->contractTenantServiceCategories()->attach($kpiData['selectedTenantCategories']);
                }

                foreach ($mainInformation['months'] ?? [] as $monthData) {
                    ContractMonth::create([
                        'contract_id' => $contract->id,
                        'month'       => $monthData['month'],
                        'amount'      => $monthData['amount'],
                        'is_paid'     => false,
                        'user_id'     => auth()->id(),
                    ]);
                }

                foreach ($team ?? [] as $member) {
                    WorkforceAndTeam::create([
                        'contract_id'          => $contract->id,
                        'role'                 => $member['role'],
                        'proficiency'          => $member['proficiency'],
                        'quantity'             => $member['quantity'],
                        'deduction_rate'       => $member['deduction_rate'],
                        'working_days'         => $member['working_days'],
                        'localization_target'  => $member['localization_target'],
                        'working_hours'        => $member['working_hours'],
                        'attendance_mandatory' => $member['attendance_mandatory'],
                        'minimum_wage'        => $member['minimum_wage'],
                        'uniform_and_tools_mandatory' => $member['uniform_and_tools'],
                        'user_id'              => auth()->id(),
                    ]);
                }

                // Insert KPI data into contract_performance_indicators
                foreach ($kpiData['selectedKpis'] ?? [] as $kpi) {
                    $performanceClass = $kpi['class'] ?? null;
                    $deductionType = $kpi['percentage_type'] ?? null; // fixed or percentage
                    $ranges = $kpi['ranges'] ?? [];

                    foreach ($ranges as $range => $data) {
                        $penalty = $data['penalty'] ?? null;
                        $rangeId = self::getRangeIdFromLabel($range); // Map 100%-95% => 1, etc.

                        if ($rangeId && $penalty !== null) {
                            DB::table('contract_performance_indicators')->insert([
                                'contract_id'           => $contract->id,
                                'range_id'              => $rangeId,
                                'penalty'               => $penalty,
                                'performance_indicator' => $performanceClass,
                                'deduction_value'       => $data['deduction'] ?? 0,
                                'deduction_type'        => $deductionType ?? 'fixed',
                                'created_at'            => now(),
                                'updated_at'            => now(),
                            ]);
                        }
                    }
                }

                // ✅ Insert Performance Indicatoe Rows into contract_priorities
                foreach ($kpiData['slaRows'] ?? [] as $row) {
                    DB::table('contract_priorities')->insert([
                        'user_id'             => auth()->id(),
                        'contract_number'     =>  $mainInformation['contract_name'],
                        'contract_id'         => $contract->id,
                        'priority_id'         => $row['priority_id'],
                        'service_window'      => $row['service_window_input'],
                        'response_time'       => $row['response_time_input'],
                        'service_window_type' => $row['service_window_select'],
                        'response_time_type'  => $row['response_time_select'],
                        'created_at'          => now(),
                    ]);
                }

                if (isset($kpiData['servicesRows']) && !empty($kpiData['servicesRows'])) {
                    foreach ($kpiData['servicesRows'] as $serviceRow) {
                        
                        $serviceKpi = ContractServiceKpi::create([
                            'contract_id' => $contract->id,
                            'service_id' => $serviceRow['service_id'],
                            'performance_indicator' => json_encode($serviceRow['kpi_ids'] ?? null),
                            'price' => $serviceRow['price'] ?? 0,
                            'description' => $serviceRow['description'] ?? null,
                            'created_by' => $user->id,
                            'updated_by' => $user->id,
                        ]);
                    }
                }

                if(AkauntingService::allow() && $assetData['ownership'] != 'no_inventory'){
                        $contractService->createWarehouseForContract( $contract, $assetData['ownership']);
                        $contractService->createCustomerForServiceProvider( $contract);
                    }
            }else{
                $draft->status = 'pending';
                $draft->save();

                $errorMessage = __('advance_contracts.general.variation_order_error');
                $successMessage = __('advance_contracts.general.variation_order_created');

                $mainInformation = $draft->main_information_data;
                $kpiData = $draft->data_agreement_kpi_data; // <-- KPI data
                $initiatorRole = $draft->initiator_role; // example, adjust as per your system

                // Fetch the roles that need to approve this draft
                $approvalRoles = Helper::getApprovalRolesFor($initiatorRole);

                foreach ($approvalRoles as $level => $roleName) {
                    $approverId = null;
                    if($roleName == 'sp_admin'){
                        $approverId = $mainInformation['contract_with']; 
                    }
                    if($roleName == 'admin'){
                        $contract = Contracts::findOrFail($draft->contract_id);
                        $approverId = $contract->user_id; 
                    }
                    if ($roleName == 'building_manager') {
                        $buildingIds = collect($kpiData['property_details'])->pluck('building_id')->unique()->toArray();
                        $buildingManagers = User::where('status', 1)
                                                ->where('is_deleted', 'no')
                                                ->where('user_type', 'building_manager')
                                                ->where(function ($query) use ($buildingIds) {
                                                    foreach ($buildingIds as $id) {
                                                        $query->orWhereRaw("FIND_IN_SET(?, building_ids)", [$id]);
                                                    }
                                                })->get();

                        foreach ($buildingManagers as $manager) {
                            DB::table('advance_contract_draft_approvals')->insert([
                                'advance_contract_draft_id' => $draft->id,
                                'approver_id' => $manager->id,
                                'role' => $roleName,
                                'status' => 'pending',
                                'approval_sequence' => $level + 1,
                                'comment' => null,
                                'acted_at' => null,
                                'created_at' => now(),
                                'updated_at' => now(),
                            ]);
                            $manager->notify(new VariationOrderApprovalNotification($draft, $manager,$message_en,$message_ar,$notificationSubType));
                        }
                    }else{
                         DB::table('advance_contract_draft_approvals')->insert([
                            'advance_contract_draft_id' => $draft->id,
                            'approver_id' =>  $approverId,
                            'role' => $roleName,
                            'status' => 'pending',        // initial status
                            'approval_sequence' => $level + 1,        // approval sequence level (1-based)
                            'comment' => null,
                            'acted_at' => null,
                            'created_at' => now(),
                            'updated_at' => now(),
                        ]);
                        $user = User::find($approverId);
                        $user->notify(new VariationOrderApprovalNotification($draft, $user,$message_en,$message_ar,$notificationSubType));
                    }
                }
            }
        
            DB::commit();
            session()->forget("contract_saved_steps_{$this->uuid}");

            $this->dispatchBrowserEvent('show-toastr', ['type' => 'success', 'message' => $successMessage]);
            return redirect()->to('/data/contracts');

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Contract creation failed', [
                                'uuid' => $this->uuid,
                                'error' => $e->getMessage(),
                                'trace' => $e->getTraceAsString(),
                            ]);
            $this->dispatchBrowserEvent('show-toastr', ['type' => 'error', 'message' => $errorMessage ]);
        }
    }


    private static function getRangeIdFromLabel(string $label): ?int
    {
        $map = [
            '100%-95%' => 1,
            '94%-90%'  => 2,
            '89%-85%'  => 3,
            '85%-80%' => 4,
            '80%-75%' => 5,
            '75%-70%' => 6,
            '70%-65%' => 7,
        ];
    
        return $map[$label] ?? null;
    }
    
    public function render()
    {
        return view('livewire.advance-contracts.step-six');
    }
}

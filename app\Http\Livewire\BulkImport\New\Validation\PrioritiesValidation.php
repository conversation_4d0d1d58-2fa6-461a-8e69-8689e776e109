<?php
    namespace App\Http\Livewire\BulkImport\New\Validation;
    use Livewire\Component;
    use Illuminate\Support\Facades\Log;
    use App\Http\Traits\BulkImportErrorTrait;
    use App\Http\Traits\FunctionsTrait;
    use App\Http\Traits\PriorityTrait;
    use App\Http\Traits\BulkImportListTrait;
    use App\Enums\ResultType;

    class PrioritiesValidation extends Component{
        use BulkImportErrorTrait, FunctionsTrait, PriorityTrait, BulkImportListTrait;

        public $showMore;
        public $perPage;
        public $decryptedToken;
        public $perPageList;
        public $prioritiesArray;

        public function render(){
            $errorsList = $this->getBulkImportErrorsListByArrayValues($this->decryptedToken, ResultType::PrioritiesLevels->value, 'backend_status', [true, false], $this->perPage);
            $prioritiesList = $this->getPaginatedPrioritiesListVByValues('id', $this->prioritiesArray, $this->perPageList);
            $dataErrorsCount = $this->getBulkImportErrorsCountByValues($this->decryptedToken, ResultType::PrioritiesLevels->value, 'backend_status', false);
            $systemErrorsCount = $this->getBulkImportErrorsCountByValues($this->decryptedToken, ResultType::PrioritiesLevels->value, 'backend_status', true);
            return view('livewire.bulk-import.new.validation.priorities-validation', compact('errorsList', 'prioritiesList', 'dataErrorsCount', 'systemErrorsCount'));
        }

        public function mount() {
            try {
                $this->setPerPageList($this->perPage);
            } 
            
            catch (\Throwable $th) {
                Log::error("mount error: ".$th);
            }
        }

         public function setPerPage($value) {
            try {
                $this->perPage = $value;
            } 
            
            catch (\Throwable $th) {
                Log::error("setPerPage error: ".$th);
            }
        }

        public function manageLoadAll($countList) {
            try {
                $this->setPerPage($countList);
            } 
            
            catch (\Throwable $th) {
                Log::error("manageLoadAll error: ".$th);
            }
        }

        public function managePerPage() {
            try {
                $number = $this->additionOperation($this->perPage, $this->showMore);
                $this->setPerPage($number);
            } 
            
            catch (\Throwable $th) {
                Log::error("managePerPage error: ".$th);
            }
        }

        public function setPerPageList($value) {
            try {
                $this->perPageList = $value;
            } 
            
            catch (\Throwable $th) {
                Log::error("setPerPageList error: ".$th);
            }
        }

        public function managePerPageList() {
            try {
                $number = $this->additionOperation($this->perPageList, $this->showMore);
                $this->setPerPageList($number);
            } 
            
            catch (\Throwable $th) {
                Log::error("managePerPageList error: ".$th);
            }
        }

        public function manageLoadAllList($countList) {
            try {
                $this->setPerPageList($countList);
            } 
            
            catch (\Throwable $th) {
                Log::error("manageLoadAllList error: ".$th);
            }
        }

        public function destroyPrioritiesList() {
            try {
                $deletedPriorties = $this->deletePriorityByValue('id', $this->prioritiesArray);

                if($deletedPriorties){
                    $deletedBulkImportErrors = $this->deleteBulkImportErrorsListByValues($this->decryptedToken, ResultType::PrioritiesLevels->value);

                    $array = [
                        'priorities' => null
                    ];

                    $updatedBulkImportList = $this->updateBulkImportListByValues('bulk_import_id', $this->decryptedToken, $array);

                    if($updatedBulkImportList){
                        $this->callJsFunctionByValues('show-toastr', 'success', __('import.priorities_deleted_successfully'));
                    }

                    else{
                        $this->callJsFunctionByValues('show-toastr', 'error', __('import.inserted_list_not_updated'));
                    }
                }

                else{
                    $this->callJsFunctionByValues('show-toastr', 'error', __('import.priorities_not_deleted'));
                }

                $this->closeModalByValue('closeDeleteModal');
            } 
            
            catch (\Throwable $th) {
                Log::error("destroyPrioritiesList error: ".$th);
            }
        }

        public function callJsFunctionByValues($key, $type, $message) {
            try {
                return $this->dispatchBrowserEvent($key, [
                    'type' => $type,
                    'message' => $message
                ]);
            } 
            
            catch (\Throwable $th) {
                Log::error('callJsFunctionByValues error: '.$th);
            }
        }

        public function closeModalByValue($key) {
            try {
                return $this->dispatchBrowserEvent($key);
            } 
            
            catch (\Throwable $th) {
                Log::error('closeModalByValue error: '.$th);
            }
        }
    }
?>
<div>
    @php
        function initials($name) {
    $words = explode(' ', $name);
    $initials = '';

    foreach ($words as $word) {
        $initials .= strtoupper(substr($word, 0, 1));
        if (strlen($initials) >= 2) break;
    }

    return $initials;
}

        @endphp
        @if($showFullView)
    <div class="contents crm">
        <div class="container-fluid">
            <div class="col-lg-12">
                <div class="row justify-content-sm-between align-items-center justify-content-center my-3 flex-sm-row flex-column">
                    <div class="page-title-wrap p-0">
                        <div class="page-title d-flex justify-content-between">
                            <div class="page-title__left justify-content-sm-between align-items-center justify-content-center">
                                <div class="user-member__title mr-sm-25 ml-0">
                                    <h4 class="text-capitalize fw-500 breadcrumb-title fs-16">
                                        @lang('purchase.title')
                                    </h4>
                                </div>
                            </div>
                        </div>
                        <div>
                            <ul class="atbd-breadcrumb nav">
                                <li class="atbd-breadcrumb__item">
                                    <a>@lang('purchase.breadcrumb.dashboard')</a>
                                    <span class="breadcrumb__seperator">
                                        <span class="la la-angle-right"></span>
                                    </span>
                                </li>
                                <li class="atbd-breadcrumb__item">
                                    <a>@lang('purchase.breadcrumb.accounting')</a>
                                </li>
                            </ul>
                        </div>
                    </div>

                    <div class="d-flex gap-10 breadcrumb_right_icons">
                        <div class="d-flex gap-10 breadcrumb_right_icons">
{{--                            <select class="select2-new form-control">--}}
{{--                                <option>Sales</option>--}}
{{--                            </select>--}}
{{--                            <button class="btn btn-white btn-default text-center svg-20 px-2 wh-45">--}}
{{--                                <i class="iconsax icon text-new-primary fs-22 colorRed mr-0" icon-name="folder-add"></i>--}}
{{--                            </button>--}}

                            <button class="btn btn-white btn-default text-center svg-20 wh-45"
                                    wire:click="switchView('{{ $viewMode === 'table' ? 'cards' : 'table' }}')">
                                <i class="iconsax icon text-new-primary fs-22 colorRed mr-0"
                                   icon-name="{{ $viewMode === 'table' ? 'layout-3' : 'task-list' }}"></i>
                            </button>


                            <a class="btn btn-default btn-primary w-100 no-wrap" type="button" aria-expanded="false" href="{{route('purchase.create')}}">
                                <i class="las la-plus fs-16"></i>@lang('purchase.buttons.create')
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            @endif

            <div class="table-responsive">
                <div class="card">
                    <div class="">
                        <div class="card-header py-4 px-3 border-0 d-flex justify-content-between align-items-center">
                            <h6 class="text-capitalize text-osool fw-500 mb-3 mb-sm-0">@lang('purchase.title')</h6>

                            <div class="d-flex gap-10 table-search">
                                <div class="position-relative">
                                    <input type="text" class="form-control" placeholder="@lang('purchase.common.search')" wire:model.live.debounce.250ms="search">
                                    <i class="iconsax field-icon fs-18 mr-0" icon-name="search-normal-2"></i>
                                </div>
                                <button class="btn btn-export text-dark" wire:click="export()">
                                    <i class="iconsax icon fs-22 mr-0" icon-name="upload-1"></i> @lang('purchase.buttons.export')
                                </button>
                            </div>
                        </div>
                    </div>
                    @if (session()->has('message'))
                        <div class="alert alert-success">
                            {{ session('message') }}
                        </div>
                    @endif
                    @if ($errors->has('form_error'))
                        <div class="alert alert-danger">
                            {{ $errors->first('form_error') }}
                        </div>
                    @endif
                    @if($viewMode === 'table')
                    <div class="card-body px-0 pt-0">
                        <div class="userDatatable projectDatatable project-table bg-white w-100 border-0">
                            <div class="table-responsive">
                                <table class="table mb-0 radius-0 th-osool">
                                    <thead>
                                    <tr class="userDatatable-header">
                                        <th>@lang('purchase.table.headers.number')</th>
                                        <th wire:click="sortBy('purchase_id')">
                                            @lang('purchase.table.headers.po_number')
                                            <i class="iconsax icon fs-20" icon-name="swap-vertical-circle"></i>
                                        </th>
                                        <th wire:click="sortBy('vendor')">
                                            @lang('purchase.table.headers.vendor_name')
                                            <i class="iconsax icon fs-20" icon-name="swap-vertical-circle"></i>
                                        </th>
                                        <th wire:click="sortBy('account_type')">
                                            @lang('purchase.table.headers.account_type')
                                            <i class="iconsax icon fs-20" icon-name="swap-vertical-circle"></i>
                                        </th>
                                        <th wire:click="sortBy('category')">
                                            @lang('purchase.table.headers.category')
                                            <i class="iconsax icon fs-20" icon-name="swap-vertical-circle"></i>
                                        </th>
                                        <th wire:click="sortBy('purchase_date')">
                                            @lang('purchase.table.headers.purchase_date')
                                            <i class="iconsax icon fs-20" icon-name="swap-vertical-circle"></i>
                                        </th>
                                        <th wire:click="sortBy('status')">
                                            @lang('purchase.table.headers.status')
                                            <i class="iconsax icon fs-20" icon-name="swap-vertical-circle"></i>
                                        </th>
                                        <th>@lang('purchase.table.headers.actions')</th>
                                    </tr>
                                    </thead>
                                    <tbody >
                                    @forelse($purchases as $index => $purchase)
                                        <tr>
                                            <td>
                                                <div class="d-flex userDatatable-content mb-0 align-items-center">
                                                    <span>{{ $index + 1 }}</span>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="d-flex userDatatable-content mb-0 align-items-center">
                                                    <span>{{ $purchase['purchase_id'] }}</span>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="d-flex align-items-center gap-10">
                                                    <span>{{ $purchase['vendor'] }}</span>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="d-flex userDatatable-content mb-0 align-items-center">
                                                    <span>{{ $purchase['account_type'] }}</span>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="d-flex align-items-center gap-10">
                                                    {{ $purchase['category'] }}
                                                </div>
                                            </td>
                                            <td>
                                                <div class="d-flex align-items-center gap-10">
                                                    {{ \Carbon\Carbon::parse($purchase['purchase_date'])->format('d-m-Y') }}
                                                </div>
                                            </td>
                                            <td>
                                                @php
                                                    $statusClass = match(strtolower($purchase['status'])) {
                                                        'fully received' => 'badge-completed',
                                                        'partially received' => 'badge-received',
                                                        'sent' => 'badge-sent',
                                                        default => 'badge-draft'
                                                    };
                                                @endphp
                                                <div class="{{ $statusClass }} d-inline-block py-1 px-3 fw-600 rounded text-center">
                                                    {{$purchase['status']}}
                                                </div>
                                            </td>
                                            <td>
                                                <div class="d-inline-block">
                                                    <ul class="mb-0 d-flex gap-10">
                                                        <li>
                                                            <a href="{{route('purchase.details', $purchase['id'])}}">
                                                                <i class="iconsax icon text-osool fs-18" icon-name="eye"></i>
                                                            </a>
                                                        </li>
                                                        <li>
                                                            <a href="{{route('purchase.edit', $purchase['id'])}}">
                                                                <i class="iconsax icon text-new-primary fs-18" icon-name="edit-1"></i>
                                                            </a>
                                                        </li>
                                                        <li>
                                                            <a href="javascript:void(0);" data-toggle="modal" data-target="#delete-vendor" wire:click="setDeleteId({{ $purchase['id'] }}, '{{ $purchase['vendor'] }}')">
                                                                <i class="iconsax icon text-delete fs-18" icon-name="trash"></i>
                                                            </a>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </td>
                                        </tr>
                                    @empty
                                        <tr>
                                            <td colspan="8" class="text-center">@lang('purchase.common.no_records_found')</td>
                                        </tr>
                                    @endforelse
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    @else
                        <div class="card-body px-0 pt-0">
                        <div class="row">
                            @foreach($purchases as $purchase)
                                <div class="col-md-4 mb-3">
                                    <div class="radius-xl new-shadow">
                                        <div class="d-flex p-3 align-items-center">
                                            <div class="mr-3 wh-60 fs-16 d-center
                            @switch($purchase['status'])
                                @case('Fully Received') bg-success @break
                                @case('Partially Received') bg-warning @break
                                @case('Sent') bg-primary @break
                                @default bg-secondary @break
                            @endswitch
                            radius-xl text-white">
                                                {{ initials($purchase['vendor']) }}
                                            </div>
                                            <div>
                                                <h2 class="fw-500 mb-1 fs-14 text-new-primary">
                                                    {{ $purchase['vendor'] }}
                                                </h2>
                                                <p class="mb-0">
                                                    {{ $purchase['purchase_id'] }}
                                                </p>
                                            </div>
                                        </div>
                                        <p class="mb-1 px-2 border-top py-3 d-flex">
                        <span class="d-center gap-10 flex-fill">
                            <i class="iconsax icon fs-22 mr-0 text-sool" icon-name="calendar"></i>
                            <span class="fs-15 text-dark fw-600">
                                {{ \Carbon\Carbon::parse($purchase['purchase_date'])->format('d M Y') }}
                            </span>
                        </span>
                                            <span class="d-center gap-10 flex-fill border-right border-left">
                            <i class="iconsax icon fs-22 mr-0 text-sool" icon-name="wallet-open-tick"></i>
                            <span class="fs-15 text-dark fw-600">
                         {{$purchase['status']}}
                            </span>
                        </span>
                                        </p>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                        </div>
                    @endif
                    <div class="card-body pt-0">
                        <div class="d-flex justify-content-md-between flex-column flex-md-row justify-content-center align-items-center mt-4 gap-10">
                            <div class="">
                                <ul class="atbd-pagination d-flex justify-content-between">
                                    <li>
                                        <div class="paging-option">
                                            <div class="dataTables_length d-flex">
                                                <label class="d-flex align-items-center mb-0">
                                                    <select aria-controls="workorder_table" wire:model.live="perPage" class="custom-select custom-select-sm form-control form-control-sm mx-2" style="min-height: 35px;">
                                                        <option value="5">5</option>
                                                        <option value="10">10</option>
                                                        <option value="25">25</option>
                                                        <option value="50">50</option>
                                                        <option value="100">100</option>
                                                    </select>
                                                    <span class="no-wrap"> @lang('purchase.table.pagination.entries_per_page') </span>
                                                </label>
                                            </div>
                                        </div>
                                    </li>
                                </ul>
                            </div>
                            <div class="">
                                <div class="user-pagination">
                                    <div class="user-pagination new-pagination">
                                        <div class="d-flex justify-content-sm-end justify-content-end">
                                            {{ $purchases->links() }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div>
                                <p class="text-sm text-gray-700 leading-5 mb-0">
                                    <span>@lang('purchase.table.pagination.showing')</span>
                                    <span class="font-medium">{{ $purchases->firstItem() }}</span>
                                    <span>@lang('purchase.table.pagination.to')</span>
                                    <span class="font-medium">{{ $purchases->lastItem() }}</span>
                                    <span>@lang('purchase.table.pagination.of')</span>
                                    <span class="font-medium">{{ $purchases->total() }}</span>
                                    <span>@lang('purchase.table.pagination.results')</span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        @if($showFullView)
        </div>
    </div>
    @endif

@if($showFullView)
    <div class="modal fade" id="delete-vendor" tabindex="-1" role="dialog" aria-labelledby="deleteUserModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-sm" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="deleteUserModalLabel">@lang('purchase.delete_modal.title')</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="text-center">
                        <i class="iconsax icon text-loss fs-60" icon-name="warning-triangle"></i>
                        <p class="mt-4">{!! __('purchase.delete_modal.message', ['name' => $deleteName ?? '']) !!}</p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">@lang('purchase.buttons.cancel')</button>
                    <form id="deleteUserForm" method="POST" wire:submit.prevent="deletePurchase">
                        <button type="submit" class="btn btn-danger">@lang('purchase.buttons.confirm_delete')</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
    @endif
</div>

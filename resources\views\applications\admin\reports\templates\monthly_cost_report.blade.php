
<table border="1" cellpadding="2" cellspacing="0" style="border-collapse: collapse; text-align: left;">
    <tr>
        <th colspan="1">@lang('user_management_module.user_forms.label.report_period')</th>
        <td colspan="1">@lang('user_management_module.user_forms.label.from') {{ date('d/M/Y', strtotime($start_date)) }} @lang('user_management_module.user_forms.label.to') {{ date('d/M/Y', strtotime($end_date)) }}</td>
    </tr>
</table>

<br>
@foreach($contracts as $contract)
<table border="1" cellpadding="2" cellspacing="0" style="border-collapse: collapse; text-align: left;">
     <tr>
        <th colspan="1">@lang('user_management_module.user_forms.label.contract_name')</th>
        <td colspan="1">{{ $contract->contract_number }}</td>
    </tr>
    <tr>
        <th colspan="1">@lang('user_management_module.user_forms.label.service_provider')</th>
        <td colspan="1">{{ $contract->serviceProvider->name }}</td>
    </tr>
    <tr>
        <th colspan="1">@lang('user_management_module.user_forms.label.number_of_services')</th>
        <td colspan="1">{{ count($contract->contractServiceKpis) }}</td>
    </tr>
</table>

<table border="1" cellpadding="8" cellspacing="0" style="border-collapse: collapse; width: 100%;">
    <thead>
            <tr>
                <th>@lang('user_management_module.user_forms.label.serial')</th>
                <th>@lang('user_management_module.user_forms.label.service_name')</th>
                <th>@lang('user_management_module.user_forms.label.total_wo_count')</th>
                <th>@lang('user_management_module.user_forms.label.pm_work_orders')</th>
                <th>@lang('user_management_module.user_forms.label.rm_work_orders')</th>
                <th>@lang('user_management_module.user_forms.label.price_of_service')</th>

                @foreach (\Carbon\Carbon::parse($start_date)->daysUntil($end_date) as $date)
                    <th>{{ $date->format('d-M') }}</th>
                @endforeach

                <th>@lang('user_management_module.user_forms.label.total_costs')</th>
            </tr>
    </thead>
     <tbody>
            @foreach($contract->contractServiceKpis as $key => $this_service)
                @php
                    $total_costs = 0;
                    $breakdown = $this_service->getServiceBreakDown($start_date, $end_date);
                @endphp
                <tr>
                    <td>{{ $key + 1 }}</td>
                    <td>{{ $this_service->assetCategory->asset_category }}</td>
                    <td>{{ $this_service->getTotalWorkordersCount() }}</td>
                    <td>{{ $this_service->getTotalPMWorkordersCount() }}</td>
                    <td>{{ $this_service->getTotalRMWorkordersCount() }}</td>
                    <td>{{ $this_service->getTotalWorkordersCount() * $this_service->price }}</td>
                    @foreach (\Carbon\Carbon::parse($start_date)->daysUntil($end_date) as $date)
                        @php
                            $date_key = $date->format('Y-m-d');
                            $date_service_price = $breakdown[$date_key] ?? 0;
                            $total_costs += $date_service_price;
                        @endphp
                        <td>{{ $date_service_price }}</td>
                    @endforeach
                    <td>{{ $total_costs }}</td>
                </tr>
            @endforeach
        </tbody>
</table>

@endforeach




<!-- 
<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>

<script>
// function exportAllTablesToServer() {
    const tables = document.querySelectorAll("table");
    let mergedData = [];

    tables.forEach((table, index) => {
        const tempData = XLSX.utils.sheet_to_json(
            XLSX.utils.table_to_sheet(table),
            { header: 1 }
        );

        if (index !== 0) {
            mergedData.push([]); // add space between tables
        }

        mergedData = mergedData.concat(tempData);
    });

    const wb = XLSX.utils.book_new();
    const ws = XLSX.utils.aoa_to_sheet(mergedData);
    XLSX.utils.book_append_sheet(wb, ws, "Full Report");

    // ✅ Use 'array' instead of 'blob'
    const wbout = XLSX.write(wb, { bookType: "xlsx", type: "array" });

    // ✅ Convert to Blob manually
    const blob = new Blob([wbout], {
        type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    });

    // Prepare form data
    const formData = new FormData();
    formData.append("file", blob, "{{ $filename}}");

    // POST to Laravel
    fetch("/reports/upload-advacecontractreport/{{ $report_id}}/{{ $filename}}", {
        method: "POST",
        body: formData,
        headers: {
            "X-CSRF-TOKEN": document.querySelector('meta[name="csrf-token"]').content
        }
    })
    .then(res => res.json())
    .then(data => {
        if (data.path) {
            alert("File saved successfully: " + data.path);
            // Optional: open/download
            // window.open(data.path, "_blank");
        } else {
           // alert("Upload failed.");
        }
    })
    .catch(err => {
        //console.error("Export failed", err);
        //alert("Something went wrong.");
    });
// }
</script>
-->